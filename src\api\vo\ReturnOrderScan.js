import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_VerifyOrder}/returnOrderScan/`

//获取扫码记录 PageGetScanRecord
export const pageGetScanRecord = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetScanRecord', params, config)
}

//物流单号扫码 ReturnOrderScan
export const returnOrderScan = (params, config = {}) => {
    return request.post(apiPrefix + 'ReturnOrderScan', params, config)
}

//手动打印 Print
export const print = (params, config = {}) => {
    return request.post(apiPrefix + 'Print', params, config)
}

//获取汇总信息 GetScanPrintStat
export const getScanPrintStat = (params, config = {}) => {
    return request.post(apiPrefix + 'GetScanPrintStat', params, config)
}

//统计每天工作量趋势图 GetStatTrendChart
export const getStatTrendChart = (params, config = {}) => {
    return request.post(apiPrefix + 'GetStatTrendChart', params, config)
}

//导出 ExportScanRecord
export const exportScanRecord = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportScanRecord', params, config)
}

//撤销 Reject
export const reject = (params, config = {}) => {
    return request.post(apiPrefix + 'Reject', params, config)
}

//多次退件扫码查询 PageGetRepeatedlys
export const pageGetRepeatedlys = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetRepeatedlys', params, config)
}

//多次退件扫码表头 GetColumns
export const getRepeatedlyColumns = (params, config = {}) => {
    return request.post(apiPrefix + 'GetRepeatedlyColumns', params, config)
}

//ExportRepeatedlys
export const exportRepeatedlys = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportRepeatedlys', params, config)
}


//GetColumns 拆包件表头
export const unpackingGetColumns = (params, config = {}) => {
    return request.post(apiPrefix + 'Unpacking/' + 'GetColumns', params, config)
}

//查询数据 拆包件 PageGetData
export const unpackingPageGetData = (params, config = {}) => {
    return request.post(apiPrefix + 'Unpacking/' + 'PageGetData', params, config)
}

//数据导出 拆包件 ExportData
export const unpackingExportData = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'Unpacking/' + 'ExportData', params, config)
}


//扫码退件工作量统计-导入列表
export const importScanReturnOrderWorkload = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportScanReturnOrderWorkload', params, config)
}

//扫码退件工作量统计-导入售后明细
export const importScanReturnOrderWorkloadAfterSaleDetail = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportScanReturnOrderWorkloadAfterSaleDetail', params, config)
}

//扫码退件工作量统计-导入销退上架明细
export const importScanReturnOrderWorkloadCancelReturnDetail = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportScanReturnOrderWorkloadCancelReturnDetail', params, config)
}

//扫码退件工作量统计-查询列表
export const getScanReturnOrderWorkload = (params, config = {}) => {
    return request.post(apiPrefix + 'GetScanReturnOrderWorkload', params, config)
}

//扫码退件工作量统计-导出列表
export const exportScanReturnOrderWorkload = (params, config = { responseType: 'blob' }) => {
    params = params || {}
    return request.post(apiPrefix + 'ExportScanReturnOrderWorkload', params, config)
}

//扫码退件工作量统计-查询售后明细
export const getScanReturnOrderWorkloadAfterSaleDetail = (params, config = {}) => {
    return request.post(apiPrefix + 'GetScanReturnOrderWorkloadAfterSaleDetail', params, config)
}

//扫码退件工作量统计-导出售后明细
export const exportScanReturnOrderWorkloadAfterSaleDetail = (params, config = { responseType: 'blob' }) => {
    params = params || {}
    return request.post(apiPrefix + 'ExportScanReturnOrderWorkloadAfterSaleDetail', params, config)
}

//扫码退件工作量统计-导出售后合计明细
export const exportScanReturnOrderWorkloadWarehouseDetail = (params, config = { responseType: 'blob' }) => {
    params = params || {}
    return request.post(apiPrefix + 'ExportScanReturnOrderWorkloadWarehouseDetail', params, config)
}

//扫码退件工作量统计-查询销退上架明细
export const getScanReturnOrderWorkloadCancelReturnDetail = (params, config = {}) => {
    return request.post(apiPrefix + 'GetScanReturnOrderWorkloadCancelReturnDetail', params, config)
}

//扫码退件工作量统计-导出销退上架明细
export const exportScanReturnOrderWorkloadCancelReturnDetail = (params, config = { responseType: 'blob' }) => {
    params = params || {}
    return request.post(apiPrefix + 'ExportScanReturnOrderWorkloadCancelReturnDetail', params, config)
}

//导入售后导入
export const  ImportJdAfters = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportJdAfters', params, config)
}

//京东编码映射导入
export const ImportJdCodeMappers = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportJdCodeMappers', params, config)
}