import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/NewOrderNodes/`

export const getTbWarehouseList =(params, config = {}) => { return request.get(apiPrefix + 'GetTbWarehouseList',{ params: params, ...config })}

export const getNewOrderList = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderAnalysisList', params, config) }

export const getOrderRepairList = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderRepairList', params, config) }
export const importOrderBh = (params, config = {}) => { return request.post(apiPrefix + 'ImportSupplementaryGoods', params, config) }
export const importOrderPc = (params, config = {}) => { return request.post(apiPrefix + 'ImportNewOrderNodesEntityAsync', params, config) }
export const importOrderNodes = (params, config = {}) => { return request.post(apiPrefix + 'ImportNewOrderNodesAsync', params, config) }

//全部订单趋势图取数
export const getNewAllOrderNotesTypeListMapNew = (params, config = {}) => { return request.post(apiPrefix + 'GetNewAllOrderNotesTypeListMapNew', params, config) }
export const getNewOrderNotesTypeListMap = (params, config = {}) => { return request.post(apiPrefix + 'GetNewOrderNotesTypeListMap', params, config) }
export const getNewOrderNotesRepairListMap = (params, config = {}) => { return request.post(apiPrefix + 'GetNewOrderNotesRepairListMap', params, config) }

export const exportOrderNoteAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportOrderNoteAsync', params, config) }
export const exportOrderRepairAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportOrderRepairAsync', params, config) }

export const getNewOrderNodeDialogList = (params, config = {}) => { return request.post(apiPrefix + 'GetNewOrderNodeDialogList', params, config) }
export const exportOrderNodeDialogAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportOrderNodeDialogAsync', params, config) }
//export const exportOrderRepairAsync =(params, config = {}) => { return request.get(apiPrefix + 'ExportOrderRepairAsync',{ params: params, ...config })}
//export const exportOrderNoteAsync =(params, config = {}) => { return request.get(apiPrefix + 'ExportOrderNoteAsync',{ params: params, ...config })}

export const getNewAllOrderNotesChat = (params, config = {}) => { return request.post(apiPrefix + 'GetNewAllOrderNotesChat', params, config) }

