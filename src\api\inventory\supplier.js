import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/supplier/`

export const editSupplier = (params, config) => { return request.post(apiPrefix + 'EditSupplierAsync', params, config) }
export const pageSupplier = (params, config) => { return request.post(apiPrefix + 'PageSupplierAsync',  params, config ) }
export const pageSupplierAll = (params, config) => { return request.get(apiPrefix + 'PageSupplierAllAsync', { params: params, ...config }) }
export const getSupplierDetail = (params, config = {}) => { return request.get(apiPrefix + 'GetSupplierDetailAsync', { params: params, ...config }) }
export const getSupplierPurchaseList = (params, config = {}) => { return request.get(apiPrefix + 'GetSupplierPurchaseListAsync', { params: params, ...config }) }
export const exportSupplier = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportSupplierAsync', params, config) }
export const getAllSupplier = (params, config = {}) => { return request.get(apiPrefix + 'GetAllSupplierAsync', { params: params, ...config }) }
export const SupplierAccountsDetail = (params, config = {}) => { return request.get(apiPrefix + 'SupplierAccountsDetailAsync', { params: params, ...config }) }

export const getSupplierAccountsDetailBuyNo = (params, config = {}) => { return request.get(apiPrefix + 'GetSupplierAccountsDetailBuyNoAsync', { params: params, ...config }) }
export const editSupplierAccountsDetail = (params, config) => { return request.post(apiPrefix + 'EditSupplierAccountsDetailAsync', params, config) }
export const exportSupplierAccountsDetail = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportSupplierAccountsDetailAsync', params, config) }
export const addSupplier = (params, config) => { return request.post(apiPrefix + 'AddSupplierAsync', params, config) }
export const getNameSimilarity = (params, config = {}) => { return request.get(apiPrefix + 'GetNameSimilarityAsync', { params: params, ...config }) }
export const getPurNameSimilarity = (params, config = {}) => { return request.get(apiPrefix + 'GetPurNameSimilarityAsync', { params: params, ...config }) }
export const syncJstSupplier = (params, config) => { return request.post(apiPrefix + 'SyncJstSupplier', params, config) }
export const GetSupplierAccountBankList = (params, config) => { return request.post(apiPrefix + 'GetSupplierAccountBankList', params, config ) }
export const AddSupplierAccountBank = (params, config) => { return request.post(apiPrefix + 'AddSupplierAccountBank', params, config ) }
export const DeleteSupplierAccountBank = (params, config) => { return request.post(apiPrefix + 'DeleteSupplierAccountBank', params, config ) }

//获取供应商特殊符号白名单
export const getSupplierNameWhitelist = (params, config) => { return request.post(apiPrefix + 'GetSupplierNameWhitelist', params, config) }
//保存供应商特殊符号报名单
export const saveSupplierNameWhitelist = (params, config) => { return request.post(apiPrefix + 'SaveSupplierNameWhitelist', params, config) }
//删除供应商特殊符号报名单
export const delSupplierNameWhitelist = (params, config) => { return request.post(apiPrefix + 'DelSupplierNameWhitelist', params, config) }

//GetSupplierMapInfos
export const getSupplierMapInfos = (params, config) => { return request.post(apiPrefix + 'GetSupplierMapInfos', params, config) }

//ExportSupplierTagGoodsInfosByMap
export const ExportSupplierTagGoodsInfosByMap = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportSupplierTagGoodsInfosByMap', params, config) }

//GetSupplierTagGoodsInfos
export const getSupplierTagGoodsInfos = (params, config) => { return request.post(apiPrefix + 'GetSupplierTagGoodsInfos', params, config) }

//GetSupplierIsOfPublicLogList
export const getSupplierIsOfPublicLogList = (params, config) => { return request.post(apiPrefix + 'GetSupplierIsOfPublicLogList', params, config) }
