import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/ztccreativity/`

export const getPageList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPageList', { params, ...config })
  }

export const importZTCCreativityAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportZTCCreativityAsync', params, config)
}
export const exportdata =(params, config = {responseType: 'blob'}) => {
  return request.get(apiPrefix + 'ExportdataList', { params, ...config })
}
