<template>
  <el-popover :width="width" trigger="hover" @show="init">
    <div class="header_btn">
      <div class="logic">
        <el-radio-group v-model="logic">
          <el-radio label="And">并且</el-radio>
          <el-radio label="Or">或者</el-radio>
        </el-radio-group>
      </div>
      <div>
        <el-button type="info" @click="clear">清空</el-button>
        <el-button type="primary" @click="sure">确定</el-button>
      </div>
    </div>
    <div class="tags">
      <div v-for="item in data" :key="item.id">
        <div v-if="item.groupName" class="header">{{ item.groupName }}</div>
        <div v-for="subItem in item.children" :key="subItem.propName" class="tagContainer">
          <template v-if="subItem.cdtType == 'Text'">
            <el-tooltip effect="dark" :content="subItem.toolTip" placement="right"> <span class="groupName">{{
              subItem.propName }}</span> </el-tooltip>
            <div class="tags">
              <el-tag v-for="(tag, i) in subItem.conditionData" :key="i" :type="tag.isInfo ? 'info' : 'success'"
                size="medium" class="tag" @click="tag.isInfo = !tag.isInfo">{{ tag.name }}</el-tag>
            </div>
          </template>
          <template v-else-if="subItem.cdtType == 'Range'">
            <el-tooltip effect="dark" :content="subItem.toolTip" placement="right"> <span class="groupName">{{
              subItem.propName }}</span> </el-tooltip>
            <div class="tags">
              <el-tag v-for="(tag, i) in subItem.conditionData" :key="i" :type="tag.isInfo ? 'info' : 'success'"
                size="medium" class="tag" @click="tag.isInfo = !tag.isInfo">{{ tag.name }}</el-tag>
            </div>
            <number-range class="range" :min.sync="subItem.min" :max.sync="subItem.max" />
          </template>
        </div>
      </div>
    </div>
    <slot slot="reference" />
  </el-popover>
</template>
<script>
import { pageGetData } from '@/api/vo/dynamicProp.js'
import numberRange from '@/components/number-range/index.vue'
export default {
  components: { numberRange },
  props: {
    scene: {
      type: String,
      require: true,
      default: null
    },
    width: {
      type: Number,
      default: 850
    }
  },
  data() {
    return {
      query: {
        total: 0,
        currentPage: 1,
        pageSize: 500,
        orderBy: null,
        isAsc: false
      },
      data: [],
      tags: [], // 选中的标签
      filterSuccess: [],
      filterRangeValue: [],
      logic: 'And'
    }
  },
  mounted() { },
  methods: {
    clear() {
      this.data.forEach(item => {
        item.children.forEach(subItem => {
          if (subItem.cdtType == 'Range') {
            this.$set(subItem, 'min', null)
            this.$set(subItem, 'max', null)
          }
          subItem.conditionData.forEach(tag => {
            if (!tag.isInfo) {
              tag.isInfo = true
            }
          })
        })
      })
      this.$emit('init', {})
    },
    sure() {
      this.filterSuccess = this.data.map(item => {
        return item.children.map(subItem => {
          return subItem.conditionData.filter(tag => tag.isInfo == false)
        }).flat()
      }).flat()
      // 找出this.data.children中有min或者max的数据
      this.data.forEach(item => {
        item.children.forEach(subItem => {
          subItem.conditionData.forEach(tag => {
            if (!tag.isInfo) {
              this.filterRangeValue.push(tag)
            }
          })
          if (subItem.cdtType == 'Range' && (subItem.min || subItem.max)) {
            const resItem = JSON.parse(JSON.stringify(subItem))
            const filterItem = {
              groupName: resItem.groupName,
              propName: resItem.propName,
              propValueMin: resItem.min,
              propValueMax: resItem.max,
              cdtType: 'Range',
              scene: resItem.scene,
              name: resItem.propFormat.replace('{0}', `${resItem.min} - ${resItem.max ?? '...'}`),
              isInfo: (resItem.min || resItem.max) ? false : true
            }
            this.filterRangeValue.push(filterItem)
          }
        })
      })
      this.tags = [...this.filterSuccess, ...this.filterRangeValue]
      this.tags = this.tags.reduce((acc, cur) => {
        if (cur.cdtType === 'Range') {
          const index = acc.findIndex(item => item.groupName === cur.groupName && item.propName === cur.propName && item.propValueMin === cur.propValueMin && item.propValueMax === cur.propValueMax && cur.isInfo == false)
          if (index === -1) {
            acc.push(cur)
          }
        } else if (cur.cdtType === 'Text') {
          const index = acc.findIndex(item => item.groupName === cur.groupName && item.propName === cur.propName && item.propValue === cur.propValue && cur.isInfo == false)
          if (index === -1) {
            acc.push(cur)
          }
        }
        return acc
      }, [])
      this.tags = this.tags.filter(tag => tag.isInfo == false)
      this.$emit('init', { logic: this.logic, filters: this.tags })
    },
    init() {
      if (this.data.length === 0) { this.getList() }
    },
    async getList(type) {
      if (type === 'search') {
        this.query.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      try {
        this.query.goodsCodes = this.goodsCodes
        const { data, success } = await pageGetData(this.query)
        if (success) {
          for (const group of data) {
            for (const item of group.children) {
              if (item.condition && item.cdtType == 'Text') {
                item.conditionData = JSON.parse(item.condition).map(tag => {
                  return {
                    name: tag,
                    value: tag,
                    isInfo: true,
                    cdtType: 'Text',
                    groupName: group.groupName ? group.groupName : '',
                    propName: item.propName ? item.propName : '',
                    propValue: tag,
                    scene: item.scene
                  }
                })
              } else {
                item.conditionData = JSON.parse(item.condition).map(tag => {
                  return {
                    name: tag.min == tag.max ? item.propFormat.replace('{0}', tag.min) : item.propFormat.replace('{0}', `${tag.min} - ${tag.max ?? '...'}`),
                    value: {
                      min: tag.min,
                      max: tag.max
                    },
                    groupName: group.groupName ? group.groupName : '',
                    propName: item.propName ? item.propName : '',
                    propValueMin: tag.min,
                    propValueMax: tag.max,
                    isInfo: true,
                    cdtType: 'Range',
                    scene: item.scene
                  }
                })
              }
            }
          }
          this.data = data
        } else {
          this.$message.error('获取数据失败')
        }
      } catch (error) {
        this.$message.error('获取数据失败')
      }
    }
  }
}
</script>
<style scoped lang="scss">
.tags {
  line-height: 30px;

  .header {
    line-height: 40px;
    font-weight: bold;
    background-color: #f2f2f2;
    text-indent: 10px;
  }

  .tagContainer {
    display: flex;
    margin-top: 5px;
    margin-bottom: 5px;
    align-items: start;
    justify-content: space-between;

    .groupName {
      width: 80px;
    }

    .tags {
      flex: 1;
      cursor: pointer;
      display: flex;
      gap: 6px;
      flex-wrap: wrap;

      .tag {
        padding-left: 5px;
        padding-right: 5px;
        text-align: center;
      }
    }

    .range {
      width: 200px
    }
  }
}

.header_btn {
  display: flex;
  justify-content: space-between;
  padding: 0 0 10px 0;
}
</style>
