import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Media}/vediotask/`

 
export const pageViewTaskAsync = (params, config = {}) => 
{ return request.post(apiPrefix + 'PageViewTaskAsync', params, config ) }


export const getVideoTaskPdList = (params, config = {}) => { return request.get(apiPrefix + 'GetVideoTaskPdList', { params: params, ...config }) }

export const getTaskUrgencyList = (params, config = {}) => { return request.get(apiPrefix + 'GetTaskUrgencyList', { params: params, ...config }) }
export const getSceneList = (params, config = {}) => { return request.get(apiPrefix + 'GetSceneList', { params: params, ...config }) }
export const getSceneListTree = (params, config = {}) => { return request.get(apiPrefix + 'GetSceneListTree', { params: params, ...config }) }


//获取拍摄上传片段字段剪切
export const uplodPdCuteVideoAsync = (params, config = {}) => { return request.post(apiPrefix + 'UplodPdCuteVideoAsync', { params: params, config }) }

export const addOrUpdateVideoTaskAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'AddOrUpdateVideoTaskAsync', params, config)
}
//视频任务删除
export const deleteVideoTaskAsync = (params, config = {}) => {
  return request.delete(apiPrefix + 'DeleteVideoTaskAsync', { params: params, ...config })
}
//拍摄领取
export const complatVideoTask = (params, config = {}) => {
  return request.get(apiPrefix + 'ComplatVideoTaskAsync', { params: params, ...config })
}

//拍摄领取
export const pickVideoTaskAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'PickVideoTaskAsync', { params: params, ...config })
}

//取消拍摄领取
export const unPickVideoTaskAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'UnPickVideoTaskAsync', { params: params, ...config })
}

//剪切领取
export const cutPickVideoTaskAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'CutPickVideoTaskAsync', { params: params, ...config })
}

//取消剪切领取
export const unCutPickVideoTaskAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'UnCutPickVideoTaskAsync', { params: params, ...config })
}

export const saveUploadTeskVideoAsync = (params, config = {}) => { return request.post(apiPrefix + 'SaveUploadTeskVideoAsync', params, config) }

export const uplodPdVideoAsync = (params, config = {}) => { return request.post(apiPrefix + 'UplodPdVideoAsync', params, config) }


export const moveTaskToShop = (params, config = {}) => { return request.post(apiPrefix + 'MoveTaskToShop', params, config) }

export const moveTaskToShopOut = (params, config = {}) => { return request.post(apiPrefix + 'MoveTaskToShopOut', params, config) }

export const moveTaskToOver = (params, config = {}) => { return request.post(apiPrefix + 'MoveTaskToOver', params, config) }

//导出
export const exportVedioTaskReport=
 (params, config = { responseType: 'blob' }) => { return  request.get(apiPrefix + 'ExportVedioTaskReport', { params: params, ...config }) }



export const assignshootingSave = (params, config = {}) => { return request.post(apiPrefix + 'AssignshootingSave', params, config) }
export const uploadOutcomeVideo = (params, config = {}) => { return request.post(apiPrefix + 'UploadOutcomeVideo', params, config) }
export const delOutcomeVideo = (params, config = {}) => { return request.get(apiPrefix + 'DelOutcomeVideo', { params: params, ...config }) }
export const getOutcomeVideo = (params, config = {}) => { return request.get(apiPrefix + 'GetOutcomeVideo', { params: params, ...config }) }

export const deleteUploadVideoByIdAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteUploadVideoByIdAsync', { params: params, ...config }) }

export const getVedioTaskOrderListById = (params, config = {}) => { return request.get(apiPrefix + 'GetVedioTaskOrderListById', { params: params, ...config }) }
export const vedioTaskAddOrderSaveCheckTaskIds = (params, config = {}) => { return request.post(apiPrefix + 'VedioTaskAddOrderSaveCheckTaskIds', params, config) }
export const getCityAllData = (params, config = {}) => { return request.get(apiPrefix + 'GetCityAllData', { params: params, ...config }) }
export const vedioTaskAddOrderSave = (params, config = {}) => { return request.post(apiPrefix + 'VedioTaskAddOrderSave', params, config) }
export const getVedioTaskOrderAddressList = (params, config = {}) => { return request.get(apiPrefix + 'GetVedioTaskOrderAddressList', { params: params, ...config }) }
export const saveVedioTaskOrderAddress = (params, config = {}) => { return request.post(apiPrefix + 'SaveVedioTaskOrderAddress', params, config) }
export const deleteVedioTaskOrderAddress = (params, config = {}) => { return request.get(apiPrefix + 'DeleteVedioTaskOrderAddress', { params: params, ...config }) }


//--------------------------2323//--------------------------
export const getVideoTask2PdListInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetVideoTask2PdListInfo', { params: params, ...config }) }

//删除参考前校验
export const delVideoTaskPdInfo = (params, config = {}) => { return request.get(apiPrefix + 'DelVideoTaskPdInfo', { params: params, ...config }) }



export const getVideoTasCuteInfo  = (params, config = {}) => { return request.get(apiPrefix + 'GetVideoTasCuteInfo', { params: params, ...config }) }

export const getVideoTaskApprovedInfo  = (params, config = {}) => { return request.get(apiPrefix + 'GetVideoTaskApprovedInfo', { params: params, ...config }) }
export const rejectUploadVideoByIdAsync = (params, config = {}) => { return request.post(apiPrefix + 'RejectUploadVideoByIdAsync', params, config) }
export const replyCommentInfo = (params, config = {}) => { return request.post(apiPrefix + 'ReplyCommentInfo', params, config) }

export const approveUploadVideoByIdAsync  = (params, config = {}) => { return request.get(apiPrefix + 'ApproveUploadVideoByIdAsync', { params: params, ...config }) }

export const unEndTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'UnEndTaskActionAsync', params, config) }
export const endTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'EndTaskActionAsync', params, config) }
export const signTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'SignTaskActionAsync', params, config) }
export const unSignTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'UnSignTaskActionAsync', params, config) }
export const deleteTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteTaskActionAsync', params, config) }
export const caclTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'CaclTaskActionAsync', params, config) }
export const unCaclTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'UnCaclTaskActionAsync', params, config) }
export const overTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'OverTaskActionAsync', params, config) }
 
export const shopTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'ShopTaskActionAsync', params, config) }
export const unShopTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'UnShopTaskActionAsync', params, config) }
export const setUploadVideoDefault = (params, config = {}) => {return request.get(apiPrefix + 'SetUploadVideoDefault', { params: params, ...config }) }


export const checkVideoTaskCanChange  = (params, config = {}) => { return request.get(apiPrefix + 'CheckVideoTaskCanChange', { params: params, ...config }) }


export const shootUrgencyTaskSqAsync  = (params, config = {}) => { return request.get(apiPrefix + 'ShootUrgencyTaskSqAsync', { params: params, ...config }) }

export const shootUrgencyTaskTgAsync  = (params, config = {}) => { return request.get(apiPrefix + 'ShootUrgencyTaskTgAsync', { params: params, ...config }) }
export const getTaskChangeAsync  = (params, config = {}) => { return request.get(apiPrefix + 'GetTaskChangeAsync', { params: params, ...config }) }

//保存详情备注
export const saveVideoTaskMarkAsync= (params, config = {}) => 
{   return request.post(apiPrefix + 'SaveVideoTaskMarkAsync',params, config)  }
//删除详情备注
export const delVideoTaskMarkAsync= (params, config = {}) => 
{   return request.get(apiPrefix + 'DelVideoTaskMarkAsync',{ params: params, ...config }) }

//获取备注信息
export const getVideoTaskMarkAsync= (params, config = {}) => 
{   return request.get(apiPrefix + 'GetVideoTaskMarkAsync', { params: params, ...config }) }

//删除附件
export const delVideoTploadFileTaskAsync= (params, config = {}) => 
{   return request.get(apiPrefix + 'DelVideoTploadFileTaskAsync', { params: params, ...config }) }

//批量到货
export const arrivalShootingSave = (params, config = {}) => { return request.post(apiPrefix + 'ArrivalShootingSaveTaskAsync', params, config) }