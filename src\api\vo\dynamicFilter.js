import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_VerifyOrder}/dynamicFilter/`

//GetColumns
export const getColumns = (params, config = {}) => {
    return request.post(apiPrefix + 'GetColumns', params, config)
}

//查询数据 PageGetData
export const pageGetData = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetData', params, config)
}

//数据导出 ExportData
export const exportData = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportData', params, config)
}

//查询单条数据 Get
export const get = (params, config = {}) => {
    return request.post(apiPrefix + 'Get', params, config)
}

//新增或编辑 Merge
export const merge = (params, config = {}) => {
    return request.post(apiPrefix + 'Merge', params, config)
}

//删除 Delete
export const deleteData = (params, config = {}) => {
    return request.post(apiPrefix + 'Delete', params, config)
}