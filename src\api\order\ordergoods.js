import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/ordergoods/`
 
export const pageOrders = (params, config = {}) => {    return request.get(apiPrefix + 'PageOrdersAsync', { params: params, ...config })}
export const importOrderData = (params, config = {}) => {    return request.post(apiPrefix + 'ImportOrderDataAsync', params, config)}
export const pageOrderGoods = (params, config = {}) => {    return request.get(apiPrefix + 'PageOrderGoodsAsync', { params: params, ...config })}
export const pageOrdersMonit = (params, config = {}) => {    return request.get(apiPrefix + 'PageOrdersMonitAsync', { params: params, ...config })}
export const syncNewOrderMonit = (params, config = {}) => {    return request.post(apiPrefix + 'SyncNewOrderMonitAsync', { params: params, ...config })}
export const importGoodsOrderData = (params, config = {}) => {    return request.post(apiPrefix + 'ImportGoodsOrderDataAsync', params, config)}
export const pageMonitorOrderGoods = (params, config = {}) => {    return request.post(apiPrefix + 'PageMonitorOrderGoodsAsync', params, config)}
export const monitorOrderGoodsAnalysis = (params, config = {}) => {    return request.post(apiPrefix + 'MonitorOrderGoodsAnalysisAsync', params, config)}
// 订单导入
export const importOrderAndGoodsData = (params,config ={}) =>{    return request.post(apiPrefix + 'ImportOrderAndGoodsDataAsync', params, config)}
// 发货时间订单导入
export const importOrderAndGoodsDataTX = (params,config ={}) =>{    return request.post(apiPrefix + 'ImportOrderAndGoodsDataTXAsync', params, config)}
export const syncNewOrderMonitAsync =(params,config ={}) =>{    return request.post(apiPrefix + 'SyncNewOrderMonitAsync',{params,config})}
export const exportOrder =(params,config ={responseType: 'blob'}) =>{    return request.get(apiPrefix + 'ExportOrderAsync',{params: params, ...config})}
export const exportOrderAnalysis =(params,config ={responseType: 'blob'}) =>{    return request.get(apiPrefix + 'ExportOrderAnalysisAsync',{params: params, ...config})}
export const queryOrderGoodsRefund =(params,config ={}) =>{    return request.post(apiPrefix + 'QueryOrderGoodsRefundAsync',params,config)}
export const queryOrdersSplit =(params,config ={}) =>{    return request.post(apiPrefix + 'QueryOrdersSplitAsync',params,config)}
//获取订单图表原始数据
export const getOrderChartsList = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderChartsListAsync', params, config)}
//获取订单统计数据
export const getOrderSum = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderSumAsync', params, config)}
//获取订单图表数据
export const getOrderCharts = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderChartsAsync', params, config)}
//导出订单整体看板数据
export const exportOrderChartsList =(params,config ={responseType: 'blob'}) =>{    return request.get(apiPrefix + 'ExportOrderChartsListAsync',{params: params, ...config})}
//获取订单未发货数据
export const getOrderNotSend = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderNotSendAsync', params, config)}
//导出订单整体看板数据
export const exportOrderNotSend =(params,config ={responseType: 'blob'}) =>{    return request.get(apiPrefix + 'ExportOrderNotSendAsync',{params: params, ...config})}
//获取未发货宝贝的订单明细
export const getOrderCountNotSendDetail = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderCountNotSendDetailAsync', params, config)}
//导出未发货宝贝的订单明细
export const exportOrderCountNotSendDetail =(params,config ={responseType: 'blob'}) =>{    return request.get(apiPrefix + 'ExportOrderCountNotSendDetailAsync',{params: params, ...config})}
//分页获取商品关联分析
export const pageOrderGoodsRelevance = (params, config = {}) => {    return request.post(apiPrefix + 'PageOrderGoodsRelevanceAsync', params, config)}
//导出商品关联分析
export const exportOrderGoodsRelevance =(params,config ={responseType: 'blob'}) =>{    return request.get(apiPrefix + 'ExportOrderGoodsRelevanceAsync',{params: params, ...config})}
//获取商品关联分析仓库统计数据
export const getOrderGoodsRelevancePie = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderGoodsRelevancePieAsync', params, config)}
//订单数，已发货，未发货
export const getOrderNoCount = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderNoCountAsync', params, config)}
//延迟发货扣款
export const getOrderDeductMoneySum = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderDeductMoneySumAsync', params, config)}
//总快递费，分摊平均快递费，总续重费, 毛利率，面单费
export const getOrderExpressSum = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderExpressSumAsync', params, config)}
//总平均快递费
export const getOrderAvgFreightMoney = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderAvgFreightMoneyAsync', params, config)}
//编码退货率
export const getOrderReturnRate = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderReturnRateAsync', params, config)}
//Id退货率
export const getOrderProCodeReturnRate = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderProCodeReturnRateAsync', params, config)}
//发货时长
export const getOrderTimeRange = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderTimeRangeAsync', params, config)}
//快递罚款，违禁品罚款
export const getOrderDeductMoneyTaboo = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderDeductMoneyTabooAsync', params, config)}
//获取未发货宝贝的订单明细 的订单号
export const getOrderCountNotSendDetailOrderNos = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderCountNotSendDetailOrderNosAsync', params, config)}
 
//============================商品销售分析 STart
//获取商品销售统计，组合编码拆分为具体的子编码
export const pageOrderGoodsSales = (params, config = {}) => {    return request.post(apiPrefix + 'PageOrderGoodsSalesAsync', params, config)}
//导出商品关联分析 -明细统计导出
export const exportOrderGoodsSalesDetail =(params,config ={responseType: 'blob'}) =>{    return request.get(apiPrefix + 'ExportOrderGoodsSalesDetailAsync',{params: params, ...config})}
//获取商品销售统计 -明细统计
export const getOrderGoodsSalesDetail = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderGoodsSalesDetailAsync', params, config)}
//获取商品销售统计 -宝贝ID明细统计
export const getOrderGoodsSalesProDetail = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderGoodsSalesProDetailAsync', params, config)}
//导出商品关联分析 -宝贝ID明细统计导出
export const exportOrderGoodsSalesProDetail =(params,config ={responseType: 'blob'}) =>{return request.get(apiPrefix + 'ExportOrderGoodsSalesProDetailAsync',{params: params, ...config})}
//获取商品销售统计 - 饼图
export const getOrderGoodsSalesPie = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderGoodsSalesPieAsync', params, config)}
//获取商品销售统计 - 运营组周趋势折线图
export const getOrderGoodsSalesGroupWeek = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderGoodsSalesGroupWeekAsync', params, config)}
//获取商品销售统计 - 运营组库存资金统计汇总柱形图
export const getOrderGoodsSalesGroupStockFinance = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderGoodsSalesGroupStockFinanceAsync', params, config)}
//============================商品销售分析 End

//============================代拍订单 Start
export const importOrderAgent =(params, config = {}) => {    return request.post(apiPrefix + 'ImportOrderAgentAsync',params,config)}
export const pageOrderAgent = (params, config = {}) => {    return request.post(apiPrefix + 'PageOrderAgentAsync', params, config)}
export const getOrderAgentAmountSum = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderAgentAmountSumAsync', params, config)}
//获取代拍订单统计
export const getOrderReportAgentAmount = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderReportAgentAmountAsync', params, config)}
//导出代拍订单统计
export const exportOrderReportAgentAmount =(params,config ={responseType: 'blob'}) =>{    return request.get(apiPrefix + 'ExportOrderReportAgentAmountAsync',{params: params, ...config})}
//获取代拍订单统计明细
export const getOrderReportAgentAmountDetail = (params, config = {}) => {  return request.post(apiPrefix + 'GetOrderReportAgentAmountDetailAsync', params, config)}
//导出代拍订单统计明细
export const exportOrderReportAgentAmountDetail =(params,config ={responseType: 'blob'}) =>{return request.get(apiPrefix + 'ExportOrderReportAgentAmountDetailAsync',{params: params, ...config})}
//获取代拍订单统计明细的订单号
export const getOrderReportAgentAmountDetailOrderNos = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderReportAgentAmountDetailOrderNosAsync', params, config)}
//============================代拍订单 End
//快递分析
export const getOrderExpressAnalysis = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderExpressAnalysisAsync', params, config)}
/*订单明细分页查询*/
export const getOrderDetail = (params, config = {}) => {    return request.post(apiPrefix + 'GetOrderDetailAsync', params,config)}
export const pageOrderGitDetail = (params, config = {}) => { return request.get(apiPrefix + 'PageOrderGitDetailAsync', { params: params, ...config })}
export const pageOrderGiftRule = (params, config = {}) => { return request.get(apiPrefix + 'PageOrderGiftRuleAsync', { params: params, ...config })}
export const importOrderGiftRule =(params, config = {}) => { return request.post(apiPrefix + 'ImportOrderGiftRuleAsync',params,config)}

//订单明细导出
export const exportOrderDetail = (params, config = {responseType: 'blob'}) => {    return request.post(apiPrefix + 'ExportOrderDetailAsync', params, config)}

//获取跨境订单数据 GetCrossBorderOrderPage
export const getCrossBorderOrderPage = (params, config = {}) => {    return request.post(apiPrefix + 'GetCrossBorderOrderPage', params, config)}

//获取跨境订单导出
export const exportCrossBorderOrder = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportCrossBorderOrder', params, config) }


