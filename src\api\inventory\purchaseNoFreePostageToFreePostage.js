import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/PurchaseOrderGoodsPostage/`

export const getLoginInfo = (params, config = {}) => {  return request.get(apiPrefix + 'getUserInfo', { params: params, ...config })}

//不包邮转包邮：查询
export const GetPurchaseNoFreeTransferPageList=(params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'GetPurchaseNoFreeTransferPageList', params, config)
}

//不包邮转包邮：新增、编辑
export const AddPurchaseNoFreeTransferAsync=(params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'AddPurchaseNoFreeTransferAsync', params, config)
}

//不包邮转包邮：删除
export const DeletePurchaseNoFreeTransferAsync=(params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'DeletePurchaseNoFreeTransferAsync', params, config)
}
