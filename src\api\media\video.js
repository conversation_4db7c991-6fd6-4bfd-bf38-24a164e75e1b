import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Media}/video/`

export const getList = (params, config = {}) => { return request.get(apiPrefix + 'GetUploadVideoList', { params: params, ...config }) }
export const getCuteVideoList = (params, config = {}) => { return request.get(apiPrefix + 'GetCuteVideoList', { params: params, ...config }) }
export const getTaskCuteVideoList = (params, config = {}) => { return request.get(apiPrefix + 'GetTaskCuteVideoList', { params: params, ...config }) }
//export const cuteVideo = (params, config = {}) => { return request.get(apiPrefix + 'CuteVideoAsync', { params: params, ...config }) }
//获取拍摄上传的任务
export const getTaskUploadVideoList = (params, config = {}) => { return request.get(apiPrefix + 'GetTaskUploadVideoList', { params: params, ...config }) }

//审核任务
export const videoTaskAudioAsync = (params, config = {}) => { return request.post(apiPrefix + 'VideoTaskAudioAsync', params, config) }


//审核任务
export const getVideoTaskCommenList = (params, config = {}) => { return request.get(apiPrefix + 'GetVideoTaskCommenList', { params: params, ...config }) }

//视频剪切
export const cuteVideo = (params, config = {}) => { return request.post(apiPrefix + 'CuteVideoAsync', params, config) }

//未剪切视频 通过不通过
export const processVideoPassStatus = (params, config = {}) => {
    return request.get(apiPrefix + 'ProcessVideoPassStatusAsync', { params: params, ...config })
}
//已剪切视频通过不通过
export const processCutePassStatusAsync = (params, config = {}) => {
    return request.get(apiPrefix + 'ProcessCutePassStatusAsync', { params: params, ...config })
}
//删除剪切的视频
export const delTaskCuteVideo = (params, config = {}) => {
    return request.get(apiPrefix + 'DelTaskCuteVideo', { params: params, ...config })
}
export const downVideoAsync = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportdownVideoAsync', { params: params, ...config }) }
//获取统计视频
export const getVideoViewAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetVideoViewAsync', params, config)
}
//获取月份完成数据
export const getMonthVideoView = (params, config = {}) => {
    return request.post(apiPrefix + 'GetMonthVideoView', params, config)
}

//获取月份完成数据
export const getCuteVideoAsyncByTaskIdList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetCuteVideoAsyncByTaskIdList', { params: params, ...config })
}
//保存聊天记录
export const saveReplyCommentInfo = (params, config = {}) => { return request.post(apiPrefix + 'SaveReplyCommentInfo', params, config) }

//保存聊天记录
export const getstatisticalperformance = (params, config = {}) => {  return request.post(apiPrefix + 'getstatisticalperformance', params, config) }


export const reUpdateCuteVideoAsync = (params, config = {}) => {  return request.post(apiPrefix + 'ReUpdateCuteVideoAsync', params, config) }

export const cuteProgressStatistics = (params, config = {}) => {  return request.post(apiPrefix + 'CuteProgressStatistics', params, config) }

export const shootingProgressStatistics = (params, config = {}) => {  return request.post(apiPrefix + 'ShootingProgressStatistics', params, config) }

export const taskStatusStatistics = (params, config = {}) => {  return request.post(apiPrefix + 'TaskStatusStatistics', params, config) }


export const allProgressStatisticsDetail = (params, config = {}) => {  return request.post(apiPrefix + 'AllProgressStatisticsDetail', params, config) }
export const cuteProgressStatisticsDetail = (params, config = {}) => {  return request.post(apiPrefix + 'CuteProgressStatisticsDetail', params, config) }
export const shootingProgressStatisticsDetail = (params, config = {}) => {  return request.post(apiPrefix + 'ShootingProgressStatisticsDetail', params, config) }


//任务统计-总数
export const getMainTaskStatistics= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainTaskStatistics', params, config) }

//任务统计-任务汇总统计
export const getMainTaskTotalStatistics= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainTaskTotalStatistics', params, config) }

//任务统计-任务明细
export const getMainShootingTaskStatistics= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainShootingTaskStatistics', params, config) }

//任务数弹窗-数据统计-小任务统计
export const getMainStatTaskPopLittleTaskList= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatTaskPopLittleTaskList', params, config) }

//平台汇总
export const getMainStatPlatStatistics= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatPlatStatistics', params, config) }

//平台汇总-趋势图
export const getMainStatPlatStatisticsChat= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatPlatStatisticsChat', params, config) }

//平台汇总-运营小组趋势图
export const getMainStatPlatGroupStatisticsChat= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatPlatGroupStatisticsChat', params, config) }

//平台汇总-弹窗
export const getMainStatPlatGroupPopStatisticsChat= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatPlatGroupPopStatisticsChat', params, config) }

//任务数弹窗-数据统计
export const getMainStatTaskPopList= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatTaskPopList', params, config) }
