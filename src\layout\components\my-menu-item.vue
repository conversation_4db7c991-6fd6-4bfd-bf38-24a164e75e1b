<template>
  <div v-if="!item.hidden">
    <el-submenu v-if="item.children && item.children.length > 0&&!isCollapse" :index="item.id + ''">
      <template #title>
        <i v-if="item.icon" :class="item.icon" />
        <span>{{ item.label }}</span>
      </template>
      <my-menu-item v-for="child in item.children" :key="child.id" :item="child" />
    </el-submenu>

    <el-submenu v-if="item.children && item.children.length > 0&&isCollapse" :index="item.id + ''">
      <template #title>
        <div class="flex">
          <i v-if="item.icon" :class="item.icon" />
          <span style="margin-top: -15px; margin-left: -20px;" v-show="isCollapse">{{ item.label.slice(0,2) }}</span>
        </div>
      </template>
      <my-menu-item v-for="child in item.children" :key="child.id" :item="child" />
    </el-submenu>
    <template v-else-if="item.path">
      <a v-if="item.isNewSiteMenu" href="javascript:void(0);" rel="noopener" @click="toNewSite(item)">
        <el-menu-item :index="item.path">
          <i v-if="item.icon" :class="item.icon" />
          <span>{{ item.label }}</span>
        </el-menu-item>
      </a>

      <a v-else-if="item.newWindow" @click="openwindows(item.path)" target="_blank" rel="noopener">
        <el-menu-item :index="item.path">
          <i v-if="item.icon" :class="item.icon" />
          <span>{{ item.label }}</span>
        </el-menu-item>
      </a>

      <router-link v-else-if="item.pathh" :to="item.path">
        <el-menu-item :index="item.path">
          <i v-if="item.icon" :class="item.icon" />
          <span>{{ item.label }}</span>
        </el-menu-item>
      </router-link>

      <router-link v-else :to="item.path" tag="div">
        <el-menu-item :index="item.path">
          <i v-if="item.icon" :class="item.icon" />
          <span>{{ item.label }}</span>
        </el-menu-item>
      </router-link>
    </template>
  </div>
</template>

<script>
import { authUShield } from '@/utils/ushieldauth'
import { getAccessToken } from '@/api/admin/auth.js'
export default {
  name: 'MyMenuItem',
  props: {
    item: {
      type: Object,
      required: true
    },
    isCollapse: {
      type: Boolean,
    }
  },
    methods: {
    async openwindows(path) {
        let resp = await getAccessToken();
        if (resp) {
            path = path + `?accessToken=${resp}`
            window.open(path);
        } else {
            window.open(path);
        }
    },
    async toNewSite(item){
      // 如果是需要U盾权限的视图
      if(item.isNeedUShield){
        var result = await authUShield(item.viewId);
        if(!result) return;
      }

      var code = item.code;
      // 判断是否开发环境、测试环境
      var port = window.location.port;
      var isDevelopment = port=="8002"||port=="8000";

      let user = `{"username":"${this.$store.getters.token}"}`;
      let url = '';
      if(code=='teamwork'){
        url = isDevelopment?'http://*************:30006/rebotlogin':'http://*************:30005/rebotlogin';
      } else if (code=='send') {
        url = isDevelopment?'http://*************:30272/rebotlogin':'http://*************:30574/rebotlogin';
      } else if (code=='finance') {
        url = isDevelopment?'http://*************:31491/rebotlogin':'http://*************:30695/rebotlogin';
      }
      
      if(url && url!=''){
        window.open(`${url}?parm=${encodeURIComponent(user)}`);
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.flex{
  display: flex;
  flex-direction: column;
  text-align: center;
  width: 40px;
  margin-top: 5px;
}
</style>
