import request from "@/utils/request";
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/EvaluationFormManage/`;

//分页获取评价单 GetEvaluationFormManagePage
export const getEvaluationFormManagePage = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetEvaluationFormManagePage",
    params,
    config
  );
};

//导出评价单数据 ExportEvaluationFormManage
export const exportEvaluationFormManage = (
  params,
  config = { responseType: "blob" }
) => {
  return request.post(apiPrefix + "ExportEvaluationFormManage", params, config);
};

//保存评价单 SaveEvaluationFormManage
export const saveEvaluationFormManage = (params, config = {}) => {
  return request.post(apiPrefix + "SaveEvaluationFormManage", params, config);
};

//认领评价单 ClaimEvaluationFormManage
export const claimEvaluationFormManage = (params, config = {}) => {
  return request.post(apiPrefix + "ClaimEvaluationFormManage", params, config);
};

//撤销评价单 RevokeEvaluationFormManage
export const revokeEvaluationFormManage = (params, config = {}) => {
  return request.post(apiPrefix + "RevokeEvaluationFormManage", params, config);
};

//分页获取评价单日志 GetEvaluationFormManageLogPage
export const GetEvaluationFormManageLogPage = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetEvaluationFormManageLogPage",
    params,
    config
  );
};

//分页获取评价收集 GetEvaluationFormManageLogPage
export const getEvaluationCollectionPage = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetEvaluationCollectionPage",
    params,
    config
  );
};

//删除评价收集 DelEvaluationCollection
export const delEvaluationCollection = (params, config = {}) => {
  return request.post(apiPrefix + "DelEvaluationCollection", params, config);
};

//批量删除评价收集 BatchDelEvaluationCollection
export const batchDelEvaluationCollection = (params, config = {}) => {
  return request.post(
    apiPrefix + "BatchDelEvaluationCollection",
    params,
    config
  );
};

//获取运营中台 GetOperationsCenterList
export const getOperationsCenterList = (params, config = {}) => {
  return request.post(apiPrefix + "GetOperationsCenterList", params, config);
};

//获取店铺数据 GetShopList
export const getShopList = (params, config = {}) => {
  return request.get(apiPrefix + "GetShopList", {
    params,
    ...config,
  });
};

//导入评价单数据 ImportEvaluationFormManageData
export const importEvaluationFormManageData = (params, config = {}) => {
  return request.post(
    apiPrefix + "ImportEvaluationFormManageData",
    params,
    config
  );
};

//获取评价单明细 GetEvaluationFormManageDtl
export const getEvaluationFormManageDtl = (params, config = {}) => {
  return request.post(apiPrefix + "GetEvaluationFormManageDtl", params, config);
};

//获取放单人 GetReleaseWorkerList
export const getReleaseWorkerList = (params, config = {}) => {
  return request.get(apiPrefix + "GetReleaseWorkerList", {
    params,
    ...config,
  });
};

//获取日志操作人列表 GetLogOperateUserList
export const getLogOperateUserList = (params, config = {}) => {
  return request.get(apiPrefix + "GetLogOperateUserList", {
    params,
    ...config,
  });
};

//获取主持列表 GetOperationDeptAllWorker
export const getOperationDeptAllWorker = (params, config = {}) => {
  return request.get(apiPrefix + "GetOperationDeptAllWorker", {
    params,
    ...config,
  });
};

//下载评价收集数据 ExportEvaluationCollectionData
export const exportEvaluationCollectionData = (params, config = {}) => {
  return request.post(apiPrefix + "ExportEvaluationCollectionData", params, config);
};

//保存主持人名称 EditEvaluationCollectionParallelHost
export const editEvaluationCollectionParallelHost = (params, config = {}) => {
  return request.post(apiPrefix + "EditEvaluationCollectionParallelHost", params, config);
};

//导入ImportEvaluationCollectionAsync
export const ImportEvaluationCollectionAsync = (params, config = {}) => {
    return request.post(apiPrefix + "ImportEvaluationCollectionAsync", params, config);
  };
