import Vue from "vue"; // 引入 Vue 是因为要用到 Vue.extend() 这个方法
import dialogform from "./dialogform.vue"; // 引入组件

let dialogformConstructor = Vue.extend(dialogform);  //创建构造器
let instance;


const showDialogform = function (options = {
    path:"@/views/****.vue",
    title:'',
    args:{oid:'',mode:3},    
    callOk,
    callCancel,   
    width:'80%',
    top: '100px',
    height:'650px',    
}) {

    let opt={
        ...{width:'80%',height:'650px',top: '100px'},
        autoTitle:true,
        title:'',
        ...options};    

    if(opt.autoTitle && opt.args && opt.args.mode){
        if(opt.args.mode===3){
            opt.title+="-详情";
        }else if(opt.args.mode===2){
            opt.title+="-编辑";
        }else if(opt.args.mode===1){
            opt.title="新增-"+opt.title;
        }
    }

    let data={
        dialogFormVisible:true,
        currentView:null,
        cmptPath:'',
        title:opt.title,
        cpmtArgs:{},
        width:opt.width,
        top:opt.top,
        height:opt.height
    }

    if(opt && opt.path && opt.path.length>4){
        let path=opt.path;
        if(path.substring(path.length-4).toLowerCase()!=".vue")
            path+=".vue";

        if(path.indexOf("@/views/")==0)
            path=path.replace("@/views/","");
        if(path.indexOf("/views/")==0)
            path=path.replace("/views/","");
        if(path.indexOf("/")==0)
            path=path.replace("/","");
            
        data.cmptPath=path;
    }else{
        alert('错误的窗体参数！options.path');
        return;
    }

    if(opt.args){
        data.cpmtArgs={...opt.args};
    }

    let methods={        };

    if(options.callOk && typeof options.callOk ==="function"){
        methods.callOk=options.callOk;
    }

    if(options.callCancel && typeof options.callCancel ==="function"){
        methods.callCancel=options.callCancel;
    }

    instance = new dialogformConstructor ({      
       data() {     
            return {...data};
        },
        methods:methods,
    }).$mount(); // 创建实例
    //debugger;
   // document.body.appendChild(instance.$el); // 将元素挂载到 body 下
};


export default showDialogform;