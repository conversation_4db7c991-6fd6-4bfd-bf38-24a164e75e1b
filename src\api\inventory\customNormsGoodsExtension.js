import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/yggrmmodule/`

///获取阳光隔热膜常规设置列表 GetListData
export const getListData = (params, config = {}) => { return request.post(apiPrefix + 'GetListData', params, config) }

//导出 阳光隔热膜常规设置 ExportYGGRMRecordDtl
export const exportYGGRMRecordDtl = (params, config = {responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportYGGRMRecordDtl', params, config) }

//添加 阳光隔热膜常规设置 SaveYGGRMRoutineSetAsync
export const saveYGGRMRoutineSetAsync = (params, config = {}) => { return request.post(apiPrefix + 'SaveYGGRMRoutineSetAsync', params, config) }

//导入 常规设置 ImportYGGRMRoutineSet
export const importYGGRMRoutineSet = (params, config = {}) => { return request.post(apiPrefix + 'ImportYGGRMRoutineSet', params, config) }
