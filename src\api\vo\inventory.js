import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_VerifyOrder}/inventory/`

//GetColumns 预包数据表头
export const getColumns = (params, config = {}) => {
    return request.post(apiPrefix + 'GetColumns', params, config)
}

//查询数据 PageGetData
export const pageGetData = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetData', params, config)
}

//数据导出 ExportData
export const exportData = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportData', params, config)
}