import request from '@/utils/request'

//const apiPrefix = `http://**************:9000/yunhan-gis-lift/dayWeatherOrderStatistic/`
const apiPrefix = `${process.env.VUE_APP_JAVA_API_BLADEGATEWAY}/yunhan-gis-lift/dayWeatherOrderStatistic/`

//const apiPrefixStyleCode = `http://**************:9000/yunhan-gis-lift/dayWeatherStyleCodeStatistic/`
const apiPrefixStyleCode = `${process.env.VUE_APP_JAVA_API_BLADEGATEWAY}/yunhan-gis-lift/dayWeatherStyleCodeStatistic/`

const apiOrderQuantityCode = `${process.env.VUE_APP_JAVA_API_BLADEGATEWAY}/yunhan-gis-lift/`
// const apiOrderQuantityCode = `http://**************:8000/api/bladegateway/yunhan-gis-lift/`


// const apiOrderQuantityCode = `/yunhan-gis-lift/`



//分页查询 每天城市天气订单统计
export const pageData = (params,config ={}) =>{
    return request.post(apiPrefix+'page', params, config)
}

//查询所有天气类型
export const getWeatherList = (params,config ={}) =>{
    return request.post(apiPrefix + 'weatherList', params, config)
}

//分页查询 每天城市天气订单统计导出
export const orderExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'export', params, config)
}

//分页查询 每天城市天气序列编号订单统计
export const pageStyleCodeOrderData = (params,config ={}) =>{
    return request.post(apiPrefixStyleCode+'page', params, config)
}

//分页查询 每天城市天气序列编号订单统计导出
export const styleCodeOrderExport = (params,config ={responseType: 'blob'}) =>{
    return request.post(apiPrefixStyleCode+'export', params, config)
}

//商品波动分析-当天时段订单量统计
export const todayOrderTimeFrame = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'orderTimeFrameStatistic' + '/' + 'todayOrderTimeFrame', params, config)
}

//商品波动分析-当天时段sku订单量统计
export const todaySkuTimeFrame = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'skuTimeFrameStatistic' + '/' + 'todaySkuTimeFrame', params, config)
}

//商品波动分析-当天时段sku每个城市订单量统计
export const todaySkuTimeFrameByCity = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'skuTimeFrameStatistic' + '/' + 'todaySkuTimeFrameByCity', params, config)
}

//商品波动分析-sku当前仓库数据
export const skuInventory = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'skuTimeFrameStatistic' + '/' + 'skuInventory', params, config)
}

//昀晗知识库-标签列表
export const pageList = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'kbsTag' + '/' + 'list', params, config)
}

//昀晗知识库-标签列表保存
export const batchSubmit = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'kbsTag' + '/' + 'batchSubmit', params, config)
}

//昀晗知识库-标签列表删除
export const remove = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'kbsTag' + '/' + 'remove?ids=' + params.ids, params, config)
}

//昀晗知识库-标签获取跳转url路径
export const kbsTaglist = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'kbsTag' + '/' + 'list?id=' + params.id, params, config)
}

//商品波动分析-聚水潭订单最近一次同步时间
export const lastImportTime = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'jstOrder' + '/' + 'lastImportTime', params, config)
}

//昀晗知识库-统计饼状图
export const statistic = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'kbsUserStatistic' + '/' + 'statistic', params, config)
}

//昀晗知识库-查看详情-统计列表分页
export const kbsUserStatisticPage = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'kbsUserStatistic' + '/' + 'page', params, config)
}

//提问饼状图-统计饼状图
export const questionStatistic = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'kbsUserStatistic' + '/' + 'questionStatistic', params, config)
}

//已回复/待回复饼状图-统计饼状图
export const replyStatistic = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'kbsUserStatistic' + '/' + 'replyStatistic', params, config)
}

//已解决/未解决饼状图-统计饼状图
export const solvedStatistic = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'kbsUserStatistic' + '/' + 'solvedStatistic', params, config)
}

//集包设备分页 /collectingEquipment/page
export const collectingEquipmentPage = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'collectingEquipment' + '/' + 'page', params, config)
}

//快递重量分页 electronicScale/page
export const electronicScalePage = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'electronicScale' + '/' + 'page', params, config)
}
//升级列表分页查询
export const getUpgradeList = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'upgradeListPage' , params, config)
}

// 升级列表状态更新
export const upgradeListStatus = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'upgradeListUpdateStatus' , params, config)
}

// 升级列表删除
export const deleteUpgrade = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'upgradeListRemove?ids='+params.ids , config)
}

// 升级列表新增
export const upgradeListSubmit = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'upgradeListSubmit' , params, { ...config })
}

//发送指令 /collectingEquipment/sendOrder
export const sendOrder = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'collectingEquipment' + '/' + 'sendOrder', params, config)
}

//开启发送升级请求 /collectingEquipment/sendStartUpgrade
export const sendStartUpgrade = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'collectingEquipment' + '/' + 'sendStartUpgrade', params, config)
}


//日志 collectingEquipment/logPage
export const logPage = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'collectingEquipment' + '/' + 'logPage', params, config)
}

export const collectingEquipmentSubmit = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'collectingEquipment' + '/' + 'submit', params, config)
}

// 集包揽收

export const shippingDataPage = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'shippingData' + '/' + 'page', params, config)
}

// 集包人员
export const packagePersonPage = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'packagePersonPage', params, config)
}

// 集包工作量
export const packageWorkloadPage = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'packageWorkloadPage', params, config)
}

// 新增导入接口
export const shippingDataImport = (file, config = {}) => {
  const formData = new FormData();
  formData.append('file', file);
  return request.post(apiOrderQuantityCode + 'shippingData' + '/' + 'import', formData, { ...config, headers: { 'Content-Type': 'multipart/form-data' } });
}

// 导出
export const packageWorkloadExport = (params, config = { responseType: 'blob' }) => {
  return request.post(apiOrderQuantityCode + 'packageWorkloadExport', params, config)
}

// 设备数据日志
export const getDeviceLogPage = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'collectingEquipment' + '/' + 'getDeviceLogpage', params, config)
}

// 获取集包揽收趋势图数据
export const getShippingDataTrend = (params, config = {}) => {
  return request.post(apiOrderQuantityCode + 'shippingData' + '/' + 'trend', params, config)
}
