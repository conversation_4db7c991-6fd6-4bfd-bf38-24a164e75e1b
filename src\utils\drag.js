
const drag = {
  data() {
    return {
        menuOpen:false,     //  菜单展开状态
        mouseDownState:false,   //  鼠标点击状态
        iX:500, iY:0,
        dX:500, dY:500,   //  初始定位
        lastMoveIndex:0,    //  拖拽计数
        curMoveIndex:0, //  历史计数
    }
  },
 methods: {
        //  鼠标按下
        demo_down(event){
          //  如果打开了菜单，则不做响应
          if(this.menuOpen){
            this.mouseDownState = false;
            return
          }
          console.log("demo_down",event);
          /* 此处判断  pc 或 移动端 得到 event 事件 */
          var touch;
          if (event.touches) {
            touch = event.touches[0];
          } else {
            touch = event;
          }
          // 鼠标点击 面向页面 的 x坐标 y坐标
          let { clientX, clientY } = touch;
          // 鼠标x坐标 - 拖拽按钮x坐标  得到鼠标 距离 拖拽按钮 的间距
          this.iX = clientX - this.$refs.actionMgr.offsetRight;
          // 鼠标y坐标 - 拖拽按钮y坐标  得到鼠标 距离 拖拽按钮 的间距
          this.iY = clientY - this.$refs.actionMgr.offsetTop;
          // 设置当前 状态为 鼠标按下
          this.mouseDownState = true;
        },
        //  鼠标拖拽
        demo_move(event){
          //鼠标按下 切移动中
          if (this.mouseDownState) {
            console.log("demo_move",event);
            /* 此处判断  pc 或 移动端 得到 event 事件 */
            var touch;
            if (event.touches) {
              touch = event.touches[0];
            } else {
              touch = event;
            }
            // 鼠标移动时 面向页面 的 x坐标 y坐标
            let { clientX, clientY } = touch;
            //当前页面全局容器 dom 元素  获取容器 宽高
            // let {
            //   clientHeight: pageDivY,
            //   clientWidth: pageDivX
            // } = this.$refs.pageDiv;
            /* 鼠标坐标 - 鼠标与拖拽按钮的 间距坐标  得到 拖拽按钮的 左上角 x轴y轴坐标 */
            let [x, y] = [clientX - this.iX, clientY - this.iY];

            //拖拽按钮 dom 元素  获取 宽高 style 对象
            let {
              clientHeight: actionMgrY,
              clientWidth: actionMgrX,
              style: actionMgrStyle
            } = this.$refs.actionMgr;
            /* 此处判断 拖拽按钮 如果超出 屏幕宽高 或者 小于
               设置 屏幕最大 x=全局容器x y=全局容器y 否则 设置 为 x=0 y=0
            */
            let pageDivX = document.body.clientWidth;
            let pageDivY = document.body.clientHeight;

            if (x > pageDivX - actionMgrX) x = pageDivX - actionMgrX;
            else if (x < 0) x = 0;
            if (y > pageDivY - actionMgrY) y = pageDivY - actionMgrY;
            else if (y < 0) y = 0;
            this.dX =x;this.dY = y;
            // 计算后坐标  设置 按钮位置
            actionMgrStyle.right = `${x}px`;
            actionMgrStyle.top = `${y}px`;
            actionMgrStyle.bottom = "auto";
            actionMgrStyle.left = "auto";
            //  move Index
            this.lastMoveIndex++;
            //  当按下键滑动时， 阻止屏幕滑动事件
            event.preventDefault();
          }
        },
        //    鼠标抬起
        demo_up(event){
          this.mouseDownState = false;
        },

        //    点击空白关闭菜单
        closeOpenModal(){}
      
 }
}
export default drag;
