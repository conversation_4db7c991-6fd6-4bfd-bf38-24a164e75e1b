import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/prePack/`
// 基础数据查询
export const pageGetBaseDataAsync = (params, config = {}) => { return request.get(apiPrefix + 'PageGetBaseDataAsync',  { params: params, ...config })}
//创建基础数据
export const createBaseDataAsync = (params, config = {}) => { return request.post(apiPrefix + 'CreateBaseDataAsync', params, config) }
//编辑基础数据
export const updateBaseDataAsync = (params, config = {}) => { return request.post(apiPrefix + 'UpdateBaseDataAsync', params, config) }
//删除基础数据
export const deleteBaseDataAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteBaseDataAsync/'+params, config) }

//第一tab筛选条件
export const pageGetPrePackStatAsync = (params, config = {}) => {return request.post(apiPrefix + 'PageGetPrePackStatAsync', params, config)}

//趋势图
export const showPrePackStatCharAsync =(params, config = {}) => {return request.post(apiPrefix + 'ShowPrePackStatCharAsync', params, config)}

//第一页面导入
// export const importGoodsSaleAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ImportGoodsSaleAsync', params, config) }
export const importGoodsSaleAsync = (params, config = {}) => {return request.post(apiPrefix + 'ImportGoodsSaleAsync', params, config)}


//第二页面导入
// export const importBaseDatasAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ImportBaseDatasAsync', params, config) }
export const importBaseDatasAsync = (params, config = {}) => {return request.post(apiPrefix + 'ImportBaseDatasAsync', params, config)}


//导出
export const exportPrePackStatAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPrePackStatAsync', params, config) }

// export const getAllocateList = (params, config = {}) => { return request.get(apiPrefix + 'GetAllocateListAsync',  { params: params, ...config })}
// export const creatAllocate = (params, config = {}) => { return request.post(apiPrefix + 'CreatAllocateAsync', params, config) }
// export const warehouseTransferDetails = (params, config = {}) => { return request.get(apiPrefix + 'WarehouseTransferDetails',  { params: params, ...config })}
// export const exportAllocateList = (params, config = {responseType: 'blob'}) => { return request.get(apiPrefix + 'ExportAllocateListAsync',  { params: params, ...config })}
// export const getUnBindRecord = (params, config = {}) => { return request.get(apiPrefix + 'GetUnBindRecord',  { params: params, ...config })}

//获取一键加工数据
export const getTreateInfoByGoodsCodeAsync= (params, config = {}) => {return request.post(apiPrefix + 'GetTreateInfoByGoodsCodeAsync', params, config)}

//保存一键加工
export const savePrePackGoodsTreateAsync= (params, config = {}) => {return request.post(apiPrefix + 'SavePrePackGoodsTreateAsync', params, config)}

//加工记录查询
export const pageGetPrePackGoodsTreateRecordAsync= (params, config = {}) => {return request.post(apiPrefix + 'PageGetPrePackGoodsTreateRecordAsync', params, config)}

//仓库列表
export const pageGetTbWarehouseAsync= (params, config = {}) => {return request.post(apiPrefix + 'PageGetTbWarehouseAsync', params, config)}

//编辑仓库信息
export const setTbWarehouseRateAsync= (params, config = {}) => {return request.post(apiPrefix + 'SetTbWarehouseRateAsync', params, config)}

//分页获取预包库存
export const pageGetGoodsPrePackInventoryAsync= (params, config = {}) => {return request.post(apiPrefix + 'PageGetGoodsPrePackInventoryAsync', params, config)}

//分页获取预包库存
export const getLastPrePackInventoryUpdateTime= (params, config = {}) => {return request.post(apiPrefix + 'GetLastPrePackInventoryUpdateTime', params, config)}

//加工仓
export const changePrePackWarehouse= (params, config = {}) => {return request.post(apiPrefix + 'ChangePrePackWarehouse', params, config)}

//发送仓
export const changeSendWarehouse= (params, config = {}) => {return request.post(apiPrefix + 'ChangeSendWarehouse', params, config)}

//一键生成预包编码
export const batchCreatePrePackCode= (params, config = {}) => {return request.post(apiPrefix + 'BatchCreatePrePackCode', params, config)}

//打印指定组合编码的加工清单GetManifestsByCombineCodes
export const getManifestsByCombineCodes= (params, config = {}) => {return request.post(apiPrefix + 'GetManifestsByCombineCodes', params, config)}

//保存预包加工清单 SaveManifests
export const saveManifests= (params, config = {}) => {return request.post(apiPrefix + 'SaveManifests', params, config)}

//查询单条数据加工清单 GetManifestInfo
export const getManifestInfo= (params, config = {}) => {return request.post(apiPrefix + 'GetManifestInfo', params, config)}

//CptManifestInfo 完成加工清单
export const cptManifestInfo= (params, config = {}) => {return request.post(apiPrefix + 'CptManifestInfo', params, config)}

//领取审单加工清单 ReceiveManifestInfo
export const receiveManifestInfo= (params, config = {}) => {return request.post(apiPrefix + 'ReceiveManifestInfo', params, config)}

//完成审单加工清单
export const finishManifestInfo= (params, config = {}) => {return request.post(apiPrefix + 'FinishManifestInfo', params, config)}

//分页查询加工清单数据 PageGetManifests
export const pageGetManifests= (params, config = {}) => {return request.post(apiPrefix + 'PageGetManifests', params, config)}

//打印指定组合编码的加工清单
export const printManifests= (params, config = {}) => {return request.post(apiPrefix + 'PrintManifests', params, config)}

//批量修改加工仓、发货仓 BatchUpdateWarehouse
export const batchUpdateWarehouse= (params, config = {}) => {return request.post(apiPrefix + 'BatchUpdateWarehouse', params, config)}

//编码未称重管理-分页查询 GetNoWeightGoodsList
export const getNoWeightGoodsList = (params, config = {}) => { return request.post(apiPrefix + 'GetNoWeightGoodsList', params, config) }
//编码未称重管理-导出 ExportNoWeightGoodsList
export const exportNoWeightGoodsList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportNoWeightGoodsList', params, config) }
