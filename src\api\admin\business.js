import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API}/business/`

export const getAllBuModule = (params, config = {}) => {return request.get(apiPrefix + 'GetAllBuModuleAsync', { params: params, ...config })}
export const getRegion = (params, config = {}) => {return request.get(apiPrefix + 'GetRegionAsync', { params: params, ...config })}

export const getTableColumnCache = (params, config = {}) => {return request.get(apiPrefix + 'GetTableColumnCacheAsync', { params: params, ...config })}
export const setTableColumnCache = (params, config = {}) => {return request.post(apiPrefix + 'SetTableColumnCacheAsync',  params,  config )}

export const queryImportModuleByDay = (params, config = {}) => {return request.get(apiPrefix + 'QueryImportModuleByDayAsync', { params: params, ...config })}



export const GetVxeTableColumnCacheAsync = (params, config = {}) => {return request.get(apiPrefix + 'GetVxeTableColumnCacheAsync', { params: params, ...config })}
export const SetVxeTableColumnCacheAsync = (params, config = {}) => {return request.post(apiPrefix + 'SetVxeTableColumnCacheAsync',  params,  config )}






