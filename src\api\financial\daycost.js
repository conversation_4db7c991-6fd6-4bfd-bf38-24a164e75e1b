import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Financial}/DayCost/`

export const pageDayCuiShouFee = (params, config = {}) => { return request.get(apiPrefix + 'PageDayCuiShouFeeAsync', { params: params, ...config }) }
export const pageDaySampleFee = (params, config = {}) => { return request.get(apiPrefix + 'PageDaySampleFeeAsync', { params: params, ...config }) }
export const pageDayReport = (params, config = {}) => { return request.get(apiPrefix + 'PageDayReportAsync', { params: params, ...config }) }
export const pageDayPurchasePickUp = (params, config = {}) => { return request.get(apiPrefix + 'PageDayPurchasePickUpAsync', { params: params, ...config }) }
export const pageDayCashRed = (params, config = {}) => { return request.get(apiPrefix + 'PageDayCashRedAsync', { params: params, ...config }) }
//export const getDayCashRedAnalysis = (params, config = {}) => {return request.get(apiPrefix + 'GetDayCashRedAnalysisResponse', { params: params, ...config })}
export const getDayCashRedResonLevel1Analysis = (params, config = {}) => { return request.get(apiPrefix + 'GetDayCashRedResonLevel1AnalysisResponse', { params: params, ...config }) }
export const getDayCashRedResonLevel2Analysis = (params, config = {}) => { return request.get(apiPrefix + 'GetDayCashRedResonLevel2AnalysisResponse', { params: params, ...config }) }
export const deleteDayCost = (params, config = {}) => { return request.post(apiPrefix + 'DeleteDayCostAsync', params, config) }
export const importDayCuiShouFee = (params, config = {}) => { return request.post(apiPrefix + 'ImportDayCuiShouFeeAsync', params, config) }
export const importDaySampleFee = (params, config = {}) => { return request.post(apiPrefix + 'ImportDaySampleFeeAsync', params, config) }
export const importDayPurchasePickUp = (params, config = {}) => { return request.post(apiPrefix + 'ImportDayPurchasePickUpAsync', params, config) }
export const importDayCashRed = (params, config = {}) => { return request.post(apiPrefix + 'ImportDayCashRedAsync', params, config) }
export const pageCashRedTXDetail = (params, config = {}) => { return request.get(apiPrefix + 'PageCashRedTXDetailAsync', { params: params, ...config }) }
export const exportDayCashRed = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportDayCashRedAsync', { params: params, ...config }) }
export const pageDayCashRedAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'PageDayCashRedAnalysisAsync', { params: params, ...config }) }
export const pageDaySpecialOrder = (params, config = {}) => { return request.get(apiPrefix + 'PageDaySpecialOrderAsync', { params: params, ...config }) }
export const importDaySpecialOrder = (params, config = {}) => { return request.post(apiPrefix + 'ImportDaySpecialOrderAsync', params, config) }
export const computDayCalCGFareAsync = (params, config = {}) => { return request.get(apiPrefix + 'ComputDayCalCGFareAsync', { params: params, ...config }) }
export const deleteSpecialOrderBatch = (params, config = {}) => { return request.delete(apiPrefix + 'DeleteSpecialOrderBatchAsync', { params: params, ...config }) }

//分销代拍
export const ExportDayReportProxyPhotography_FenXiao = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportDayReportProxyPhotography_FenXiaoAsync', { params: params, ...config }) }
export const PageDayReportProxyPhotography_FenXiao = (params, config = {}) => { return request.get(apiPrefix + 'PageDayReportProxyPhotography_FenXiaoAsync', { params: params, ...config }) }
export const ImportDayReportProxyPhotography_FenXiao = (params, config = {}) => { return request.post(apiPrefix + 'ImportDayReportProxyPhotography_FenXiaoAsync', params, config) }
export const DeleteDayReportProxyPhotography_FenXiaoBatch = (params, config = {}) => { return request.delete(apiPrefix + 'DeleteDayReportProxyPhotography_FenXiaoBatchAsync', { params: params, ...config }) }

//淘宝客
export const importDayTBVistorData = (params, config = {}) => { return request.post(apiPrefix + 'ImportDayTBVistorDataAsync', params, config) }
export const pageDayTBVistorData = (params, config = {}) => { return request.get(apiPrefix + 'PageDayTBVistorDataAsync', { params: params, ...config }) }
//计算淘系运营费用
export const calcTXyyfy = (params, config = {}) => { return request.get(apiPrefix + 'CalcTXyyfyAsync', { params, ...config }) }
export const calcEstimatedCost = (params, config = {}) => { return request.get(apiPrefix + 'CalcEstimatedCostAsync', { params, ...config }) }
export const calcTxCashRed = (params, config = {}) => { return request.get(apiPrefix + 'ComputDayCalCashRedAsync', { params, ...config }) }
export const CalcOperatingWage = (params, config = {}) => { return request.get(apiPrefix + 'CalcOperatingWageAsync', { params, ...config }) }

export const exportDaySpecialOrder = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportDaySpecialOrderAsync', { params: params, ...config }) }
