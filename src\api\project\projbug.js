import request from '@/utils/request'
import { exp } from 'mathjs'
const apiPrefix = `${process.env.VUE_APP_BASE_API_ProjMng}/projbug/`

//工单列表
export const GetMyWorkOrderList = (params, config = {}) => {
  return request.get(apiPrefix + 'GetMyWorkOrderList', { params: params, ...config })
}

//工单详情
export const GetWorkOrderDtl = (params, config = {}) => {
  return request.get(apiPrefix + 'GetWorkOrderDtl', { params: params, ...config })
}

//新增工单
export const AddWorkOrderAsync = (params,config ={}) =>{
  return request.post(apiPrefix + 'AddWorkOrderAsync', params, config)
}


//回复工单
export const ReplyWorkOrder = (params,config ={}) =>{
  return request.post(apiPrefix + 'ReplyWorkOrder', params, config)
}