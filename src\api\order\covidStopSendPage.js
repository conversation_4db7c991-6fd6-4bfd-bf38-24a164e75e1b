import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/CovidStopSend/`
export const getPageCovidStopSendDataAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageCovidStopSendDataAsync', params, config) }
export const importCovidStopSendAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportCovidStopSendAsync', params, config) }
export const getExceptionTypeAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetExceptionTypeAsync', params, config) }
export const getOrderStatusListAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderStatusListAsync', params, config) }
export const getExpressStopSendDataAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetExpressStopSendDataAsync', params, config) }
export const importExpressStopSendAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportExpressStopSendAsync', params, config) }
export const setExpressEnabledAsync = (params, config = {}) => {
    return request.get(apiPrefix + 'SetExpressEnabled', { params: params, ...config })
}
export const exportStopExpressDataAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportStopExpressDataAsync', params, config) }
export const exportCovidStopSendDataAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportCovidStopSendDataAsync', params, config) }

export const getExpressStopSendDetailDataAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageExpressStopSendDetailDataAsync', params, config) }

export const getAllExpStopArea = (params, config = {}) => {
    return request.get(apiPrefix + 'QueryAllExpStopArea', { params: params, ...config })
}

export const calculExpStopAreaNow = (params, config = {}) => { return request.post(apiPrefix + 'CalculExpStopAreaNow', params, config) }

export const exportStopExpressMergeDataAsync = (params, config = { responseType: 'blob' }) => {
    return request.get(apiPrefix + 'ExportStopExpressMergeDataAsync', { params: params, ...config })
}