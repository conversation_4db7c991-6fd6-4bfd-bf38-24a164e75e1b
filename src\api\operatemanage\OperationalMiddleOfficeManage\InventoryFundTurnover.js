import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/pddMiddlePlatform/`

// 查询库存资金周转
export const getInventoryFundTurnover = (params, config = {}) => {
    return request.post(apiPrefix + 'GetInventoryFundTurnover', params, config)
}

// 导出库存资金周转
export const exportInventoryFundTurnover = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportInventoryFundTurnover', params, config)
}


// 查询
export const PagePDDCommentListAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'PagePDDCommentListAsync', params, config)
}

// 导出
export const ExportPDDCommentListAsync = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportPDDCommentListAsync', params, config)
}

