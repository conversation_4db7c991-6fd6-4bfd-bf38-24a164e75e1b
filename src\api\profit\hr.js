import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API}/hr/`


// //导入菜单
// export const importBaseMenuAsync = (params,config ={}) =>{
//     return request.post(apiPrefix + 'ImportBaseMenuAsync', params, config)
// }
//获取招聘计划
export const getPageRecruitmentPlan = (params,config ={}) =>{
    return request.post(apiPrefix + 'PageRecruitmentPlan', params, config)
}
//新增招聘计划
export const createRecruitmentPlan = (params,config ={}) =>{
    return request.post(apiPrefix + 'CreateRecruitmentPlan', params, config)
}
//查看招聘计划详情
export const getRecruitmentPlan = (params,config ={}) =>{
    return request.get(apiPrefix+'GetRecruitmentPlan', {params: params, ...config})
}
//修改招聘计划
export const saveRecruitmentPlan = (params,config ={}) =>{
    return request.post(apiPrefix + 'SaveRecruitmentPlan', params, config)
}
//关闭招聘计划
export const closeRecruitmentPlan = (params,config ={}) =>{
    return request.post(apiPrefix + 'closeRecruitmentPlan', params, config)
}
// 关闭招聘计划-批量
export const closeRecruitmentPlanBatch = (params,config ={}) =>{
    return request.post(apiPrefix + 'CloseRecruitmentPlanBatch', params, config)
}
//删除招聘计划
export const delRecruitmentPlan = (params,config ={}) =>{
    return request.get(apiPrefix+'DelRecruitmentPlan', {params: params, ...config})
}
//分页查询岗位招聘人数列表
export const pagePositionCandidate = (params,config ={}) =>{
    return request.post(apiPrefix + 'PagePositionCandidate', params, config)
}
//分页查询全部人才列表
export const pageCandidate = (params,config ={}) =>{
    return request.post(apiPrefix + 'PageCandidate', params, config)
}
//添加人才档案
export const addCandidate = (params,config ={}) =>{
    return request.post(apiPrefix + 'AddCandidate', params, config)
}

//导入待入职
export const importHrCandidate = (params,config ={}) =>{
    return request.post(apiPrefix + 'ImportHrCandidate', params, config)
}
// 分页查询人才列表
export const pageCandidateRc = (params,config ={}) =>{
    return request.post(apiPrefix + 'PageCandidateRc', params, config)
}
//获取人才档案详情
export const getCandidateInfo = (params,config ={}) =>{
    return request.get(apiPrefix+'GetCandidateInfo', {params: params, ...config})
}
// 修改人才档案
export const editCandidate = (params,config ={}) =>{
    return request.post(apiPrefix + 'EditCandidate', params, config)
}
// 人才流失
export const candidateLostBatch = (params,config ={}) =>{
    return request.post(apiPrefix + 'CandidateLostBatch', params, config)
}
// 分页查询预备人才列表
export const pageCandidateYb = (params,config ={}) =>{
    return request.post(apiPrefix + 'PageCandidateYb', params, config)
}
// 分页查询正式人才列表
export const pageCandidateZs = (params,config ={}) =>{
    return request.post(apiPrefix + 'PageCandidateZs', params, config)
}
// 分页查询试用人才列表
export const pageCandidateSy = (params,config ={}) =>{
    return request.post(apiPrefix + 'PageCandidateSy', params, config)
}
// 分页查询离职人才列表
export const pageCandidateLz = (params,config ={}) =>{
    return request.post(apiPrefix + 'PageCandidateLz', params, config)
}
// 流失人才回到人才库
export const candidateLostBackBatch = (params,config ={}) =>{
    return request.post(apiPrefix + 'CandidateLostBackBatch', params, config)
}
// 批量入职
export const joinEmployees = (params,config ={}) =>{
    return request.post(apiPrefix + 'JoinEmployees', params, config)
}
// 单独入职
export const joinEmployee = (params,config ={}) =>{
    return request.get(apiPrefix+'JoinEmployee', {params: params, ...config})
}
//人才导出
export const exportCandidateRc =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportCandidateRc',{params: params, ...config})
}
//预备人才导出
export const exportCandidateYb =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportCandidateYb',{params: params, ...config})
}
//试用人才导出
export const exportCandidateEmployeeSy =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportCandidateEmployeeSy',{params: params, ...config})
}
//正式员工花名册导出
export const exportCandidateEmployeeZs =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportCandidateEmployeeZs',{params: params, ...config})
}
//在职员工花名册导出
export const exportCandidateEmployee =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportCandidateEmployee',{params: params, ...config})
}
//离职员工花名册导出
export const exportCandidateLz =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportCandidateLz',{params: params, ...config})
}
//新增面谈记录
export const addHrInterviewMeeting = (params,config ={}) =>{
    return request.post(apiPrefix + 'AddHrInterviewMeeting', params, config)
}
//修改面谈记录
export const editHrInterviewMeeting = (params,config ={}) =>{
    return request.post(apiPrefix + 'EditHrInterviewMeeting', params, config)
}
//员工绑定钉钉id
export const bindEmployeeDDUserId = (params,config ={}) =>{
    return request.get(apiPrefix+'BindEmployeeDDUserId', {params: params, ...config})
}
//导入员工花名册
export const importCandidateEmp = (params,config ={}) =>{
    return request.post(apiPrefix + 'ImportCandidateEmp', params, config)
}
//新增薪资模板
export const createSalaryTemplate = (params,config ={}) =>{
    return request.post(apiPrefix + 'CreateSalaryTemplate', params, config)
}
//分页查询薪模板
export const pageSalaryTemplate = (params,config ={}) =>{
    return request.post(apiPrefix + 'PageSalaryTemplate', params, config)
}
//修改薪资模板
export const editSalaryTemplate = (params,config ={}) =>{
    return request.post(apiPrefix + 'EditSalaryTemplate', params, config)
}
//删除薪资模板
export const deleteSalaryTemplate = (params,config ={}) =>{
    return request.get(apiPrefix+'DeleteSalaryTemplate', {params: params, ...config})
}
//薪资模板详情
export const getSalaryTemplate = (params,config ={}) =>{
    return request.get(apiPrefix+'GetSalaryTemplate', {params: params, ...config})
}
//分页查询员工薪资
export const pageEmployeeSalary = (params,config ={}) =>{
    return request.post(apiPrefix + 'PageEmployeeSalary', params, config)
}
//修订员工薪资
export const editEmployeeSalary = (params,config ={}) =>{
    return request.post(apiPrefix + 'EditEmployeeSalary', params, config)
}
//删除员工薪资
export const deleteEmployeeSalary = (params,config ={}) =>{
    return request.get(apiPrefix+'DeleteEmployeeSalary', {params: params, ...config})
}
//获取员工薪资详情
export const getEmployeeSalary = (params,config ={}) =>{
    return request.get(apiPrefix+'GetEmployeeSalary', {params: params, ...config})
}
//HR首页-统计数量
export const chartsHrHomePageTotal = (params,config ={}) =>{
    return request.post(apiPrefix + 'ChartsHrHomePageTotal', params, config)
}
//HR首页-入职离职人数曲线图
export const employeeDimissionLineCharts = (params,config ={}) =>{
    return request.post(apiPrefix + 'EmployeeDimissionLineCharts', params, config)
}
//HR首页统计数对象-部门
export const hrHomePageDeptList = (params,config ={}) =>{
    return request.post(apiPrefix + 'HrHomePageDeptList', params, config)
}
//导出HR首页统计数对象-部门
export const exportHrHomePageDeptList =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportHrHomePageDeptList',{params: params, ...config})
}
//HR首页统计数对象-岗位
export const hrHomePagePositionList = (params,config ={}) =>{
    return request.post(apiPrefix + 'HrHomePagePositionList', params, config)
}
//导出HR首页统计数对象-岗位
export const exportHrHomePagePositionList =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportHrHomePagePositionList',{params: params, ...config})
}
//HR首页统计数对象-招聘专员
export const hrHomePageRecruiterList = (params,config ={}) =>{
    return request.post(apiPrefix + 'HrHomePageRecruiterList', params, config)
}
//导出HR首页统计数对象-招聘专员
export const exportHrHomePageRecruiterList =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportHrHomePageRecruiterList',{params: params, ...config})
}
//HR首页统计数对象-招聘申请人
export const hrHomePageApplicantList = (params,config ={}) =>{
    return request.post(apiPrefix + 'HrHomePageApplicantList', params, config)
}
//导出HR首页统计数对象-招聘申请人
export const exportHrHomePageApplicantList =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportHrHomePageApplicantList',{params: params, ...config})
}
//HR首页统计数对象-人才流失离职环图
export const hrHomePageLostDimissionPieCharts = (params,config ={}) =>{
    return request.post(apiPrefix + 'HrHomePageLostDimissionPieCharts', params, config)
}
// HR首页统计数对象-人群特征
export const hrHomePageCandidateAttrCharts = (params,config ={}) =>{
    return request.post(apiPrefix + 'HrHomePageCandidateAttrCharts', params, config)
}
//员工薪资修订记录
export const employeeSalaryChgLogs = (params,config ={}) =>{
    return request.get(apiPrefix+'EmployeeSalaryChgLogs', {params: params, ...config})
}
//删除人才
export const delCandidate = (params,config ={}) =>{
    return request.get(apiPrefix+'DelCandidate', {params: params, ...config})
}
//导入薪资模板数据
export const importSalaryTemplate = (params,config ={}) =>{
    return request.post(apiPrefix + 'ImportSalaryTemplate', params, config)
}
//导出薪资模板数据
export const exportSalaryTemplate =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportSalaryTemplate',{params: params, ...config})
}

//导出薪资模板数据
export const ExportEmployeeSalary =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportEmployeeSalary',{params: params, ...config})
}
//获取员工部门、岗位调整记录
export const getEmpDeptPositionChgLog = (params,config ={}) =>{
    return request.get(apiPrefix+'GetEmpDeptPositionChgLog', {params: params, ...config})
}
//重新入职
export const againJoinEmployee = (params,config ={}) =>{
    return request.post(apiPrefix+'AgainJoinEmployee', params, config)
}
//同步钉钉花名册信息
export const syncDingding = (params,config ={}) =>{
    return request.post(apiPrefix+'SyncDingding', params, config)
}
//HR首页统计数对象-部门（合计）
export const hrHomePageDeptList_Stat = (params,config ={}) =>{
  return request.post(apiPrefix+'HrHomePageDeptList_Stat', params, config)
}
//HR首页统计数对象-岗位（合计）
export const hrHomePagePositionList_Stat = (params,config ={}) =>{
  return request.post(apiPrefix+'HrHomePagePositionList_Stat', params, config)
}
//HR首页统计数对象-招聘专员（合计）
export const hrHomePageRecruiterList_Stat = (params,config ={}) =>{
  return request.post(apiPrefix+'HrHomePageRecruiterList_Stat', params, config)
}
//HR首页统计数对象-招聘专员（合计）
export const hrHomePageApplicantList_Stat = (params,config ={}) =>{
  return request.post(apiPrefix+'HrHomePageApplicantList_Stat', params, config)
}
