<template>
  <div>
    <el-select
      v-model="selectId"
      placeholder="动态查询"
      class="publicCss"
      clearable
      @change="selecterChange"
    >
      <el-option label="新增" value="新增">
        <div style="text-align: center">
          新增 <i class="el-icon-circle-plus" />
        </div>
      </el-option>
      <el-option
        v-for="item in dynamicFilters"
        :key="item.id"
        :label="item.filterName"
        :value="item.id"
      >
        <div class="filterItem">
          {{ item.filterName }}
          <div class="filterItemHover">
            <i class="el-icon-s-tools" @click.stop="edit(item.id)" />
            <i class="el-icon-delete-solid" @click.stop="del(item.id)" />
          </div>
        </div>
      </el-option>
    </el-select>

    <el-dialog
      v-dialogDrag
      title="自定义查询"
      :visible.sync="queryVisable"
      width="50%"
    >
      <customQuery
        v-if="dynamic && queryVisable"
        v-model="dynamic"
        :fields="fields"
        is-root
        style="height: 400px; overflow: auto"
      />
      <div class="bottomGroup">
        <el-button type="primary" @click="queryVisable = false">关闭</el-button>
        <div class="bottom_right">
          <el-input
            v-model.trim="dynamicFilter.filterName"
            placeholder="过滤器名称"
            maxlength="50"
            clearable
            class="bottom_right_title"
          />
          <el-button
            type="primary"
            @click="submitQueryVisable"
          >保存</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import customQuery from './index.vue'
import {
  merge,
  pageGetData as dynamicFilterPageGetData,
  deleteData,
  get
} from '@/api/vo/dynamicFilter'

import { getBasic } from '@/api/admin/user'
export default {
  components: { customQuery },
  model: {
    prop: 'value',
    event: 'update'
  },
  props: {
    value: {
      type: Object,
      default: null
    },
    scene: { type: String, require: true },
    fields: {
      type: Array,
      default: null
    }
  },
  data() {
    return {
      dynamicFilters: [],
      dynamic: {
        logic: 'And',
        filters: []
      },
      dynamicFilter: {
        scene: null, // 场景
        filterValue: null, // 过滤器值
        filterName: null // 过滤器名称
      },
      queryVisable: false,
      selectId: null
    }
  },
  async mounted() {
    this.dynamicFilter.scene = this.scene
    this.dynamicFilter.createBy = await this.getUserId()
    await this.pageGet()
  },
  methods: {
    async getUserId() {
      const { data: { id }, success } = await getBasic()
      if (success) {
        return id
      }
    },
    async submitQueryVisable() {
      if (!this.dynamicFilter.filterName) {
        return this.$message.error('过滤器名称不能为空')
      }
      this.dynamicFilter.filterValue = JSON.stringify(this.dynamic)
      const { data, success } = await merge(this.dynamicFilter)
      if (success) {
        await this.pageGet()
        this.$message.success('保存成功')
        this.queryVisable = false
        this.selectId = data
        this.change()
      } else {
        this.$message.error('保存失败')
      }
    },
    async pageGet() {
      const params = {
        currentPage: 1,
        pageSize: 50,
        createBy: this.dynamicFilter.createBy,
        scene: this.scene
      }
      const { data, success } = await dynamicFilterPageGetData(params)
      if (success && data.list.length > 0) {
        data.list.forEach((item) => {
          item.filterValue = JSON.parse(item.filterValue)
        })
      }
      this.dynamicFilters = data.list
    },
    async edit(id) {
      const { data, success } = await get({ id })
      if (success) {
        data.filterValue = JSON.parse(data.filterValue)
        this.dynamicFilter = data
        this.dynamic = data.filterValue
        this.queryVisable = true
      }
    },
    // 删除动态查询条件
    del(id) {
      this.$confirm('此操作将永久删除数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async() => {
          const { success } = await deleteData([id])
          if (success) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            await this.pageGet()
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async selecterChange(e) {
      if (e === '新增') {
        this.dynamic = {
          logic: 'And',
          filters: [{ field: null, operator: null, value: null }]
        }
        this.selectId = null
        this.queryVisable = true
        return
      } else {
        this.change()
      }
    },
    change() {
      if (this.selectId) {
        var filterValue = this.dynamicFilters.find(a => a.id === this.selectId).filterValue
        this.$emit('update', filterValue)
      } else {
        this.$emit('update', null)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
  flex-wrap: wrap;

  .publicCss {
    width: 100%;
    margin: 0 10px 5px 0;
  }
}

.bottomGroup {
  display: flex;
  justify-content: space-between;
}

.bottom_right {
  display: flex;
  flex: 1;
  justify-content: end;

  .bottom_right_title {
    width: 220px;
    margin-right: 10px;
  }
}
.filterItem {
  position: relative;
  color:#555;
  font-weight: normal;

  .filterItemHover {
    display: none;
    position: absolute;
    top: 0px;
    right: 0px;

    i {
      padding: 5px;
      background: white;
      margin-left: 5px;
    }
  }

  &:hover .filterItemHover {
    display: block;
  }
}
</style>
