import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/GoodsRapidRetire/`

// 分页商品下架信息
export const getGoodsRapidRetireAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGoodsRapidRetireAsync', params, config)
}

// 聚水潭店铺商品信息
export const getJstShopGoodsListAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetJstShopGoodsListAsync', params, config)
}

// 添加下架日志信息
export const createGoodsRapidRetireLogAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'CreateGoodsRapidRetireLogAsync', params, config)
}

// 获取日志记录
export const getGoodsRapidRetireLogAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGoodsRapidRetireLogAsync', params, config)
}

// 获取日志记录详情
export const getGoodsRapidRetireLogDetailAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGoodsRapidRetireLogDetailAsync', params, config)
}