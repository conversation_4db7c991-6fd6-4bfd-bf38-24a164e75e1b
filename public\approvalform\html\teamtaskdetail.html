<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <!-- elment样式 -->
    <link rel="stylesheet" href="./api/elment.css">
    <!-- vue -->
    <script src="./api/vue.min.js"></script>
    <!-- elment脚本 -->
    <script src="./api/elment.js"></script>
    <!-- jquery -->
    <script src="./api/jquery.min.js"></script>
    <style>
        .mb10 {
            margin-bottom: 20px;
        }

        label {
            display: inline-block;
            width: 100px;
            text-align: right;
            font-weight: 700;
        }

        .rich {
            vertical-align: top;
            display: inline-block;
            width: 800px;
        }
    </style>
</head>

<body>
    <div id="app" style="margin:0 auto;width: 1000px; padding: 20px;border:1px solid #ccc">
        <div class="mb10">
            <label for="">任务标题：</label>
            <span>{{info.title}}</span>
        </div>
        <div class="mb10">
            <label for="">负责部门：</label>
            <span>{{info.departName}}</span>
        </div>
        <div class="mb10">
            <label for="">负责人：</label>
            <span>{{info.directorName}}</span>
        </div>
        <div class="mb10">
            <label for="">任务周期：</label>
            <span>{{info.tastTime}}</span>
        </div>
        <div class="mb10">
            <label for="">优先级：</label>
            <span>{{info.priority}}</span>
        </div>
        <div class="mb10">
            <label for="">关注人：</label>
            <span>{{info.followerNames}}</span>
        </div>
        <div class="mb10">
            <label for="">任务描述：</label>
            <span v-html="info.description" class="rich"></span>
        </div>
        <div class="mb10">
            <label for="">任务进度：</label>
            <span>{{info.progress}}</span>
        </div>
    </div>
    <script>
        var vm = new Vue({
            el: '#app',
            data: function () {
                return {
                    info: {}
                }
            },
            created() {

            },
            mounted() {
                this.getInfo()
            },
            methods: {
                getInfo() {
                    let that = this
                    let searchURL = window.location.search;
                    searchURL = searchURL.substring(1, searchURL.length);
                    let targetPageId = searchURL.split("&")[0].split("=")[1];
                    $.ajax({
                        type: 'GET',
                        url: `/api/teamwork/teamwork/GetTaskDetailInfo/${targetPageId}`,
                        // paramas: {
                        //     id: ''
                        // },
                        success: function (result) {
                            that.info = result.data
                        }
                    })
                }

            }
        });
    </script>


</body>


</html>