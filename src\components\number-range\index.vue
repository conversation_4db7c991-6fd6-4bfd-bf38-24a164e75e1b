<template>
  <div :class="['range', isfocus ? 'focus' : '']">
    <el-input-number v-model="range.min" :max="range.max !== null && range.max !== undefined ? range.max : maxNumber"
      :min="minNumber" class="item" :placeholder="minLabel" :controls="false" @change="update" @focus="isfocus = true"
      @blur="isfocus = false" :precision="precision" />
    至
    <el-input-number v-model="range.max" :min="range.min !== null && range.min !== undefined ? range.min : minNumber"
      :max="maxNumber" class="item" :placeholder="maxLabel" :controls="false" @change="update" @focus="isfocus = true"
      @blur="isfocus = false" :precision="precision" />
  </div>
</template>
<script>
export default {
  props: {
    min: {
      type: Number,
      default: null,
    },
    max: {
      type: Number,
      default: null,
    },

    minLabel: {
      type: String,
      default: "最小值",
    },
    minNumber: {
      type: Number,
      default: 0,
    },
    maxNumber: {
      type: Number,
      default: 999999999,
    },
    maxLabel: {
      type: String,
      default: "最大值",
    },
    precision: {
      type: Number,
    },
  },
  data() {
    return {
      range: {
        min: undefined,
        max: undefined,
      },
      isfocus: false,
    };
  },
  watch: {
    value: {
      handler() {
        this.valueChange();
      },
      deep: true,
    },
    min: "valueChange",
    max: "valueChange",
  },
  mounted() {
    this.valueChange();
  },
  methods: {
    valueChange() {
      if (this.min == null || this.min == undefined) {
        this.range.min = undefined
      } else {
        this.range.min = this.min;
      }
      if (this.max == null || this.max == undefined) {
        this.range.max = undefined
      } else {
        this.range.max = this.max;
      }
    },
    update() {
      this.$emit("update:min", this.range.min);
      this.$emit("update:max", this.range.max);
    },
  },
};
</script>
<style scoped lang="scss">
.range {
  color: #ccc;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 12px;
  transition: border-color 0.3s;
  width: 100%;
  display: flex;
  align-items: center;

  &.focus {
    border-color: #409eff;
  }

  .item {
    flex: 1;

    ::v-deep input {
      border: 0px !important;
    }
  }
}
</style>
