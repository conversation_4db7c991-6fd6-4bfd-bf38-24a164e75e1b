
import request from '@/utils/request'
const GroupPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/JingDongInquirs/`

//获取组distinct
export const getJingDongGroup = (params, config = {}) => { return request.get(GroupPrefix + 'GetJingDongGroup', { params: params, ...config }) }
//分页获取分组
export const getJingDongGroupPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetJingDongGroupPageList', params, config) }
//新增-组
export const addJingDongGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'AddJingDongGroupAsync', params, config) }
//删除-组
export const deleteJingDongGroupAsync = (params, config = {}) => { return request.get(GroupPrefix + 'DeleteJingDongGroupAsync', { params: params, ...config }) }
//修改-组
export const updateJingDongGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'UpdateJingDongGroupAsync', params, config) }
//导入-组
export const importJingDongGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportJingDongGroupAsync', params, config) }
//获取分组修改日志
export const GetGroupLogList = (params, config = {}) => { return request.post(GroupPrefix + 'GetGroupLogList', params, config) }
//咨询数据没有匹配到分组的
export const GetJingDongInquirsNotExistsList = (params, config = {}) => { return request.post(GroupPrefix + 'GetJingDongInquirsNotExistsList', params, config) }


//分页获取咨询数据
export const getJingDongInquirsPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetJingDongInquirsPageList', params, config) }
//删除咨询数据
export const deleteJingDongInquirsAsync = (params, config = {}) => { return request.get(GroupPrefix + 'DeleteJingDongInquirsAsync', { params: params, ...config }) }
//导入-咨询数据
export const importJingDongInquirsAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportJingDongInquirsAsync', params, config) }


//个人效率统计-售前
export const getJingDongPersonalEfficiencyPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetJingDongPersonalEfficiencyPageList', params, config) }
//个人效率统计-店铺个人效率-售前
export const getJingDongShopPersonalEfficiencyPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetJingDongShopPersonalEfficiencyPageList', params, config) }
//个人效率统计-个人趋势图-售前
export const getJingDongPersonalEfficiencyChat = (params, config = {}) => { return request.post(GroupPrefix + 'GetJingDongPersonalEfficiencyChat', params, config) }
//个人效率统计导出
export const exportJingDongPersonalEfficiencyList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportJingDongPersonalEfficiencyList', params, config)
}

//组效率统计-分页查询
export const getJingDongGroupEfficiencyPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetJingDongGroupEfficiencyPageList', params, config) }
//组效率统计-组趋势图
export const getJingDongGroupEfficiencyChat = (params, config = {}) => { return request.post(GroupPrefix + 'GetJingDongGroupEfficiencyChat', params, config) }
//组效率统计导出
export const exportJingDongGroupEfficiencyList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportJingDongGroupEfficiencyList', params, config)
}

//店效率统计-分页查询
export const getJingDongShopEfficiencyPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetJingDongShopEfficiencyPageList', params, config) }
//店效率统计-铺趋势图
export const getJingDongShopEfficiencyChat = (params, config = {}) => { return request.post(GroupPrefix + 'GetJingDongShopEfficiencyChat', params, config) }
//店效率统计导出
export const exportJingDongShopEfficiencyList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportJingDongShopEfficiencyList', params, config)
}

//店铺组效率
export const getJdInquirsStatisticsByShopListMonth = (params, config = {}) => { return request.post(GroupPrefix + 'GetJdInquirsStatisticsByShopListMonth', params, config) }
