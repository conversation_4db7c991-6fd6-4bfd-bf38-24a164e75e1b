<template>
    <!-- :style="isborder?{border: '1px solid red'}:{}"    @click="imgclickk" onfocus='alert("得到焦点");' -->
    <section v-if="divshow&&!bigimg&&ispaste" :style="hownum=='one'?{minHeight: '520px'}:hownum=='tworow'?{minHeight: '258px'}:hownum=='three'?{minHeight: '173px'}:{height: '100%'}" style="width: 100%; display: flex; flex-wrap: wrap; background-color: #eee;  position: relative;"  @blur="onblurtext" ref="replyInput"  @input="onDivInput($event,i)" tabindex="0" :id="keyarr[1]" @paste="changeContent($event,i)"  :contenteditable="ispaste" spellcheck="false"
         :placeholder="placetext" class="pastimgg">
        <!-- <img :src="bigimg" class="imgsty" alt=""  /> -->
        <!-- @click.stop="imgclickk('img')" -->

        <!-- <div style="width: 100%; height: 100%; background-color: #eee; color: white; font-size: 30px; justify-content: center; align-items: center;">
            <span>{{ textnow }}</span>
        </div> -->
        <div>
        </div>
    </section>
    <div v-else-if="(!divshow||bigimg)&&ispaste" style="width: 100%; height: 100%; background: #eee;  position: relative;" class="flexcenter">
        <img :src="bigimg" class="imgsty" alt="" @click="imgclick"/>
    </div>
    <div v-else style="width: 100%; height: 100%; justify-content: center; background: #eee; align-items: center; display: flex;  position: relative;">
        <img :src="bigimg" class="imgsty" alt="" />
    </div>
</template>

<script>
 const clickoutside = {
        bind(el, binding, vnode) {
            function documentHandler(e) {
                if (el.contains(e.target)) {
                    return false;
                }
                if (binding.expression) {
                    binding.value(e);
                }
            }

            el.vueClickOutside = documentHandler;
            let a = document.addEventListener('click', documentHandler);
        },
        // update() {
        // },
        unbind(el, binding) {
            document.removeEventListener('click', el.vueClickOutside);
            delete el.vueClickOutside;
        },
    };
export default {
    name: 'DEMOPastimg',
    props: {
        ispaste: {
            type: Boolean,
            default: true,
        },
        i: {
            type: Number,
            default: 1,
        },
        keyy: {type: Array,default: function(){
            return [];
        }},
        toimage: {type: Array,default: function(){
            return [];
        }},
        name: {
            type: String,
            default: '',
        },
        hownum: {
            type: String,
            default: '',
        },
        inputedit: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            placetext:'',
            canvasimg: [],
            bigimg: '',
            isborder: false,
            showborder: true,
            keyarr: [],
            divshow: true,
            textshow: '',
            longto: false,
        };
    },
    directives: {clickoutside},
    mounted() {
        this.keyarr = this.keyy;
        this.namenum = this.hownum;   
    },

    methods: {
        created () {
            var e = 10;
            var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
                a = t.length,
                n = "";
            for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
            this.randrom = n;
        },
        moudleclick(){
            this.isborder = true
        },
        ischange(val){
            if(val){
                this.$emit("ischange",val)
            }
        },
        uploadToServer(file, callback) {
            var xhr = new XMLHttpRequest()
            var formData = new FormData()
            formData.append('file', file)
            xhr.open('post', process.env.VUE_APP_BASE_API_UpLoadNew_Domin+'/api/uploadnew/file/UploadCommonFileAsync')
            xhr.withCredentials = true
            xhr.responseType = 'json'
            xhr.send(formData)
            xhr.onreadystatechange = () => {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    // debugger;
                    callback(xhr.response)
                }
            }
        },
        changeContent(e,index){
            let _this = this;
            _this.draw = true;
            _this.ischange(true);
            const dataTransferItemList = e.clipboardData.items;
            const items = [].slice.call(dataTransferItemList).filter(function (item) {
                return item.type.indexOf('image') !== -1;
            });
            
            if (items.length === 0&&this.inputedit) {
                const ee = document.getElementById('quanmodule'+_this.keyarr[0]).children;
                let a = ee[0].children[0].children[1].children[0].children[_this.keyy[1]].children[0];
                if(a.innerText.length>=50){
                    setTimeout(() => {
                        a.innerText =  a.innerText.slice(0,50); 
                        a.style.fontSize = '20px';
                        a.style.wordBreak = 'break-all';
                        _this.textshow = a.innerText;
                    }, 10);
                    
                    this.$message('已为你限制字数为50'); 
                }
                return;
            }
                // _this.divshow = false;

                const dataTransferItem = items[0];
                const file = dataTransferItem?.getAsFile();
                if(file){
                    _this.uploadToServer(file, (res) => {
                        // _this.canvasimg.push(res.data.url);
                        if(res.success)
                        _this.bigimg = res.data.url;
                        _this.$emit("childtoimg",this.name,[_this.keyy,res.data])
                        _this.$emit("callback",res)
                    })
                
                    var event = event || window.event;
                    // var file = event.target.files[0];
                    var reader = new FileReader(); 
                    reader.onload = function(e) {
                        _this.canvasimg[index] = e.target.result
                    }
                    reader.readAsDataURL(file);
                }
                

                setTimeout(()=>{
                    this.imgclick()
                },300)
            
        },
        imgclick(val){
            this.divshow = true;
            this.bigimg = false;
        },
        onblurtext(){
            let res = {
                url: this.textshow,
                filePath: '',
                fileName: '',
            }
            this.$emit("childtoimg",this.name,[this.keyy,res])
        },
        onDivInput(e,index) {
                let _this = this;
                _this.$emit('ischange',true);
                if(!this.inputedit){
                    if(e.target.innerText){
                        e.target.innerText=''
                        _this.divshow = true;
                        return;
                    }
                }
                _this.divshow = false;        
        },
        qulist(index){
            let DomList=document.getElementById('replyInput'+index)
            let img = document.getElementsByName('img');
        }
    },
};
</script>

<style lang="scss" scoped>
.pastimgg{
    // width: 100%;
    // background-color: #eee;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
.imgsty{
    max-width: 100%;
    max-height: 100%;
    height: auto;
    width: auto;
    // top: 0;
    // left: 0;
    // position: absolute;
}
.flexcenter{
    display: flex;
    justify-content: center;
    align-items: center;
}
.pastimgg{
    cursor:pointer;
    color: transparent;
}
</style>