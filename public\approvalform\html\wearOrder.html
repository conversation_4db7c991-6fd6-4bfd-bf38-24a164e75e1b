<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <link rel="stylesheet" href="/approvalform/html/api/elment.css">
    <script src="/approvalform/html/api/vue.min.js"></script>
    <script src="/approvalform/html/api/elment.js"></script>
    <script src="/approvalform/html/api/jquery.min.js"></script>
    <script src="/approvalform/html/api/html2canvas.js"></script>
    <title>损耗订单</title>
    <style type="text/css">
        .linebreak {
            overflow: hidden;
            /*超出部分隐藏*/
            text-overflow: ellipsis;
            /* 超出部分显示省略号 */
            white-space: normal;
            /*规定段落中的文本不进行换行 */
            width: 100%;
        }
        .el-pagination__jump {
            margin-left:0px;
        }
    </style>
</head>

<body>
    <div id="app" style="margin:0 auto;overflow-y: hidden;">
        <el-container direction="vertical" style=" border: 1px #ccc solid;">
            {{title}}
            <span style="color: red;">*该表展示明细数据,实际扣款会订单合并，请忽略同个订单多次定责通知</span>
            <template>
                <!-- <el-table v-if="typeId == 0" ref="tableBox" :data="list" style="width: 100%;" row-key="id" border
                    :max-height="tableHeight">
                    <el-table-column type="index" min-width="20" fixed></el-table-column>
                    <el-table-column prop="goodsCode" label="商品编码" min-width="70"></el-table-column>
                    <el-table-column prop="goodsCodeName" label="商品名称" min-width="100"></el-table-column>
                    <el-table-column prop="qty" label="调拨数量" min-width="50"></el-table-column>
                    <el-table-column prop="cwarehouseName" label="调出仓/库存/周转天数" min-width="100">
                        <template slot-scope="scope">
                            <span>{{scope.row["cwarehouseName"] }}/{{scope.row["cwarehouseWarehouseStock"]
                                }}/{{scope.row["cwarehouseInventoryDay"] }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="rwarehouseName" label="调入仓/库存/周转天数" min-width="100">
                        <template slot-scope="scope">
                            <span>{{scope.row["rwarehouseName"] }}/{{scope.row["rwarehouseWarehouseStock"]
                                }}/{{scope.row["rwarehouseInventoryDay"] }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="brandName" label="采购" min-width="50"></el-table-column>
                    <el-table-column prop="createTime" label="创建时间" min-width="90"></el-table-column>
                </el-table> -->
                <el-table ref="tableBox" :data="list" align="center" style="width: 100%;" row-key="id" border
                    :max-height="tableHeight">
                    <el-table-column type="index" min-width="20" fixed></el-table-column>
                    <el-table-column prop="orderNo" label="线上订单号" min-width="100"></el-table-column>
                    <el-table-column prop="orderInnerNo" label="内部订单号" min-width="100"></el-table-column>

                    <el-table-column prop="afterSaleApproveDate" label="售后发起时间" min-width="100"></el-table-column>
                    <!-- <el-table-column prop="damagedAmount" label="损耗金额" min-width="50"></el-table-column> -->
                    <el-table-column prop="damagedReason" label="损耗具体原因" min-width="100"></el-table-column>
                    <el-table-column prop="fileUrls" label="客服上传附件" min-width="100">
                        <template slot-scope="scope">
                            <el-image
                            style="width: 100px; height: 100px"
                            :src="scope.row.fileUrls[0]"
                            :preview-src-list="scope.row.fileUrls"
                            :fit="'fill'"></el-image>
                          </template>
                    </el-table-column>
                    <!-- <el-table-column prop="receiver_name" label="申请人" min-width="80"></el-table-column>
                    <el-table-column prop="expiration_date" label="申请时间" min-width="90"></el-table-column> -->
                </el-table>
                <!-- <div style="text-align: right;">
                    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :current-page="currentPage" :page-sizes="[20, 50, 100, 150]" :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper" :total="total">
                    </el-pagination>
                </div> -->
            </template>
        </el-container>
    </div>
    <script>
        var vm = new Vue({
            el: '#app',
            data: function () {
                return {
                    //正式上线时，请切换为正式地址，末尾不许带 /
                    //测试：http://**************
                    //正式：http://************* 
                    // thisInterfaceUrl: "http://**************",
                    thisLonding: true,
                    list: [{}],
                    tableHeight: null,
                    typeId: null,
                    currentPage: 1,
                    pageSize: 20,
                    total: 0,
                    title: ''
                }
            },
            created () {

            },
            async mounted () {
                this.getStyleSheetInfo();
                this.beginShowing()
            },
            methods: {
                handleSizeChange (val) {
                    this.pageSize = val;
                    this.getStyleSheetInfo();
                },
                handleCurrentChange (val) {
                    this.currentPage = val;
                    this.getStyleSheetInfo();
                },
                beginShowing () {
                    this.$nextTick(function () {
                        // 文档显示区域的高度 - 
                        if (this.$refs.tableBox) {
                            this.tableHeight = 835;
                            this.$refs.tableBox.doLayout()
                        }
                    })
                },
                async getStyleSheetInfo () {
                    var me = this;
                    let searchURL = window.location.search;
                    searchURL = searchURL.substring(1, searchURL.length);
                    this.typeId = searchURL.split("&")[0].split("=")[1];
                    let targetPageId = searchURL.split("&")[1].split("=")[1];
                    me.thisLonding = true;
                    let targetUrl = '';
                    let parm = {};
                    // if (this.typeId == 0) {
                    //     targetUrl = '/api/Inventory/Allocate/WarehouseTransferDetails';
                    //     parm.transferId = targetPageId;
                    // } else {
                    //     targetUrl = '/api/Inventory/PackagesProcessing/GetApproveDetialList';
                    //     parm.id = targetPageId;
                    // }
                    // parm.pageSize = this.pageSize;
                    // parm.currentPage = this.currentPage;
                    // parm.GetNoticesInfo = targetPageId;
                    // parm.id = this.typeId;

                    $.ajax({
                        url: '/api/customerservice/DamagedOrders/GetNoticesInfo?signGuid='+targetPageId+'&id='+this.typeId,
                        type: 'POST',
                        dataType: 'json',
                        data: parm,
                        success: function (response) {
                            console.log("返回数据",response)
                            response.data.list.map((item)=>{
                            if(!item.fileUrls){
                                item.fileUrls = [];
                            }else if(item.fileUrls != ""||item.fileUrls){
                                console.log("打印返回的数据222",item.fileUrls)
                                if(item.fileUrls.indexOf(',')!=-1){
                                item.fileUrls = item.fileUrls.split(',')
                                }else{
                                item.fileUrls = [item.fileUrls]
                                }
                            }
                            })
                            me.list = response.data.list;
                            // me.total = response.data.total;
                            me.title = response.data.title;
                        },
                        error: function (xhr, textStatus, errorThrown) {
                            console.log('Error: ', errorThrown);
                        }
                    });
                },
            }
        });
    </script>
</body>

</html>