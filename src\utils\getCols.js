import dayjs from "dayjs";
import { cloneDeep } from 'lodash';
//第一个参数是选中的值，第二个参数是checkBox的所有值，第三个参数是table的所有列
export const getCols = (tmp, checkGroup, tableCols) => {
  //第一步：通过tmp和checkGroup找出不同的项
  const result = checkGroup
    .filter((item) => !tmp.includes(item.value))
    .map((item) => item.label);
  //第二步：判断result的长度是否大于0，如果大于0，就将tableCols中的对应项的istrue改为false
  if (result.length > 0) {
    tableCols.forEach((item) => {
      if (result.includes(item.label)) {
        //这里会将tableCols中的对应项的istrue改为false，如果我第二次修改的话，那么第一次修改的结果还在，所以会遇到第一个bug，
        //bug1：就是修改一次之后，第二次修改的时候，第一次的结果还在
        item.istrue = false;
      } else {
        //所以我们将tableCols中的其他项全部变为true
        item.istrue = true;
      }
    });
  } else {
    //bug2：全选的话，会漏掉最后一个的istrue
    //这里如果不写else就会遇到第二个bug，当你全部选中的时候，实际上result是空的，所以我们要将所有的istrue变为true
    tableCols.forEach((item) => {
      item.istrue = true;
    });
  }
  return tableCols;
};

export const filterStatus = (status, arr, bindProp) => {
  //判断是否是一个数组
  const isArr = Array.isArray(status);
  if (isArr) {
    bindProp = arr
      .filter((item) => !status.includes(item.label))
      .map((item1) => item1.value);
  } else {
    bindProp = arr
      .filter((item) => item.label != status)
      .map((item1) => item1.label);
  }
  return bindProp;
};

//清除空格的方法,第一个参数是要清除的值组成的数组如[goodsCode],第二个参数是查询条件整个对象,如this.filter
export const replaceSpace = (replaceArr, queryInfo) => {
  replaceArr.forEach((item) => {
    const isArray = Array.isArray(queryInfo[item]);
    if (!isArray) {
      queryInfo[item] = queryInfo[item]
        ? queryInfo[item].replace(/\s*/g, "")
        : null;
    }
  });
  return queryInfo;
};

//erp趋势图时间逻辑
export const getTime = (oldTime) => {
  //oldTime是一个数组,是外面列表的时间
  //先将oldTime序列化
  oldTime = [
    dayjs(oldTime[0]).format("YYYY-MM-DD"),
    dayjs(oldTime[1]).format("YYYY-MM-DD"),
  ];
  let newTime = [];
  let days = dayjs(oldTime[1]).diff(dayjs(oldTime[0]), "day"); //判断时间的间隔是多少天
  if (days < 30) {
    newTime = [
      dayjs(oldTime[1]).subtract(30, "day").format("YYYY-MM-DD"),
      oldTime[1],
    ];
    return newTime;
  } else {
    return oldTime;
  }
};
//以下四个选择器的数据,温度和天气本包含'无'选项,当前日报页面不需要'无'所以将其去掉.若使用该数据且其他页面需要'无',则将{ label: '无', value: '无' }写死在其他页面选项数据中
//季节/节日
const seasonList = [
  { label: "四季款（常年）", value: "四季款（常年）" },
  { label: "春季款（1月1日-4月底）", value: "春季款（1月1日-4月底）" },
  { label: "夏季款（3月15日-9月底）", value: "夏季款（3月15日-9月底）" },
  { label: "秋季款（8月15日-10月底）", value: "秋季款（8月15日-10月底）" },
  { label: "冬季款（9月15日-1月底）", value: "冬季款（9月15日-1月底）" },
  { label: "开学季（1月1日至2月底）", value: "开学季（1月1日至2月底）" },
  { label: "开学季（7月1日至8月底）", value: "开学季（7月1日至8月底）" },
  { label: "清明节（3月1日至3月底）", value: "清明节（3月1日至3月底）" },
  {
    label: "端午节（农历四月初五至四月底）",
    value: "端午节（农历四月初五至四月底）",
  },
  {
    label: "中秋节（农历七月十五至八月初十）",
    value: "中秋节（农历七月十五至八月初十）",
  },
  { label: "国庆节（9月1日至9月25日）", value: "国庆节（9月1日至9月25日）" },
  { label: "圣诞节（不允许进货）", value: "圣诞节（不允许进货）" },
  {
    label: "元旦节（农历十一月初一至腊月十五）",
    value: "元旦节（农历十一月初一至腊月十五）",
  },
  {
    label: "春节（农历十一月初一至腊月十五）",
    value: "春节（农历十一月初一至腊月十五）",
  },
];
//温度
const temperatureList = [
  { label: "酷热（38度以上）", value: "酷热（38度以上）" },
  { label: "炎热（35到37度）", value: "炎热（35到37度）" },
  { label: "闷热（28到35度）", value: "闷热（28到35度）" },
  { label: "温暖（10到27度）", value: "温暖（10到27度）" },
  { label: "凉爽（0到9度）", value: "凉爽（0到9度）" },
  { label: "寒冷（-1到-9度）", value: "寒冷（-1到-9度）" },
  { label: "严寒（低于-10度）", value: "严寒（低于-10度）" },
];
//属性
const statsList = [
  { label: "常规款", value: "常规款" },
  { label: "定制款", value: "定制款" },
];
//天气
const weatherList = [
  { label: "阳光", value: "阳光" },
  { label: "雨水", value: "雨水" },
  { label: "冰雪", value: "冰雪" },
  { label: "台风", value: "台风" },
];

/*宝贝ID加链接*/
const formatLink = (platform, proCode) => {
  //是否跨境
  let proCodeLink = proCode;
  if (proCode && proCode.toLowerCase().indexOf("kj") == 0) {
    proCodeLink = proCode.substring(2);
  }

  var proBaseUrl = "";
  switch (platform) {
    case 1: //淘系
    case "淘系": //淘系
    case "天猫": //天猫
      proBaseUrl = "https://detail.tmall.com/item.htm?id=" + proCodeLink;
      break;
    case 2: //拼多多
    case "拼多多": //拼多多
      proBaseUrl =
        "https://mobile.yangkeduo.com/goods2.html?goods_id=" + proCodeLink;
      break;
    case 8: //淘系
    case "淘工厂": //淘系
      proBaseUrl = "https://detail.tmall.com/item.htm?id=" + proCodeLink;
      break;
    case 9: //淘系
    case "淘宝": //淘系
      proBaseUrl = "https://detail.tmall.com/item.htm?id=" + proCodeLink;
      break;
    case 7: //京东
    case "京东": //京东
      proBaseUrl = "https://item.jd.com/" + proCodeLink + ".html";
      break;
    case 4: //阿里巴巴
    case "阿里巴巴": //阿里巴巴
      proBaseUrl =
        "https://detail.1688.com/offer/" +
        proCodeLink +
        ".html?spm=a26286.8251493.description.2.221425b2kIBGkR";
      break;
    case 6: //抖音
    case "抖音": //抖音
      proBaseUrl =
        "https://haohuo.jinritemai.com/views/product/detail?id=" + proCodeLink;
      break;
  }
  if (proCode && proBaseUrl) return window.open(proBaseUrl);
  return "";
};

//计算两个时间间隔多少天,例如2024-6-10和2024-6-15间隔5天,传入一个数组
const getTimeDiff = (TimeRanges) => {
  let timeDiff = 0;
  if (TimeRanges && TimeRanges.length > 0 && TimeRanges[0] && TimeRanges[1]) {
    let startTime = dayjs(TimeRanges[0]).format("YYYY-MM-DD");
    let endTime = dayjs(TimeRanges[1]).format("YYYY-MM-DD");
    timeDiff = dayjs(endTime).diff(dayjs(startTime), "day");
    return timeDiff;
  } else {
    return "";
  }
};
//运营人员业绩统计快捷选择器
const fastTimePickerOptions = {
  shortcuts: [
    {
      text: "前一天",
      onClick(picker) {
        const start = dayjs().subtract(1, "day").toDate();
        const end = dayjs().subtract(1, "day").toDate();
        picker.$emit("pick", [start, end]);
        window.setshowprogress(false);
      },
    },
    {
      text: "近一周",
      onClick(picker) {
        const start = dayjs().subtract(7, "day").toDate();
        const end = dayjs().subtract(1, "day").toDate();
        picker.$emit("pick", [start, end]);
        window.setshowprogress(false);
      },
    },
    {
      text: "近一个月",
      onClick(picker) {
        const start = dayjs().subtract(1, "month").subtract(1, "day").toDate();
        const end = dayjs().subtract(1, "day").toDate();
        picker.$emit("pick", [start, end]);
        window.setshowprogress(false);
      },
    },
    {
      text: "近三个月",
      onClick(picker) {
        const start = dayjs().subtract(3, "month").subtract(1, "day").toDate();
        const end = dayjs().subtract(1, "day").toDate();
        picker.$emit("pick", [start, end]);
        window.setshowprogress(true);
      },
    },
  ],
};

const timePickerOptions = {
  //昨天 三天前 七天前 15天前 30天前 YYYY-MM-DD HH:mm:ss 使用dayjs 开始结束时间
  shortcuts: [
    {
      text: "24小时",
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24);
        picker.$emit("pick", [start, end]);
      },
    },
    {
      text: "3天",
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
        picker.$emit("pick", [start, end]);
      },
    },
    {
      text: "7天",
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        picker.$emit("pick", [start, end]);
      },
    },
    {
      text: "15天",
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
        picker.$emit("pick", [start, end]);
      },
    },
    {
      text: "30天",
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
        picker.$emit("pick", [start, end]);
      },
    },
  ],
};

//系列编码报表趋势图时间选择,快捷选择器 自然周、自然月、近7天、近30天
const seriesTimePickerOptions = {
  shortcuts: [
    {
      text: "自然周",
      onClick(picker) {
        const start = dayjs().startOf("week").toDate();
        const end = dayjs().endOf("week").toDate();
        picker.$emit("pick", [start, end]);
      },
    },
    {
      text: "自然月",
      onClick(picker) {
        const start = dayjs().startOf("month").toDate();
        const end = dayjs().endOf("month").toDate();
        picker.$emit("pick", [start, end]);
      },
    },
    {
      text: "近7天",
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        picker.$emit("pick", [start, end]);
      },
    },
    {
      text: "近30天",
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
        picker.$emit("pick", [start, end]);
      },
    },
  ],
};

//配置列格式化合并列表格
const mergeTableCols = (tableCols) => {
  const res = cloneDeep(tableCols).filter(
    (item) => item.enabled || item.type == "checkbox"
  );
  const result = [];
  for (let i = 0; i < res.length; i++) {
    //如果没有mergeName，就不合并
    if (
      res[i].mergeName == null ||
      res[i].mergeName == "" ||
      res[i].mergeName == undefined
    ) {
      result.push(res[i]);
    } else if (res[i].mergeName) {
      //如果有mergeName，就判断下一个的mergeName是否和当前的一样，一样就合并,直到mergeName不一样
      let j = i + 1;
      let item = {
        label: res[i].mergeName,
        cols: [],
        merge: true,
        tipmesg: res[i].mergeHeaderTip ? res[i].mergeHeaderTip : null,
        width: res[i].width,
        prop: res[i] + "_merge",
        headerBgColor: res[i].headerBgColor,
      };
      item.cols.push(res[i]);
      while (j < res.length && res[j].mergeName == res[i].mergeName) {
        item.cols.push(res[j]);
        j++;
      }
      i = j - 1;
      result.push(item);
    }
  }
  return result;
};
const matchArr = [
  "https://nanc.yunhanmy.com:10010",
  "https://nanc.yunhanmy.com:10020",
];
const matchImg = (value) => {
  if (!value) return value;
  let image = JSON.parse(JSON.stringify(value));
  let res;
  if (value[0] != "[" && !Array.isArray(value)) {
    res = image.split(",").map((item) => {
      return matchUrl(item);
    });
  } else if (Array.isArray(image) && image.length > 0) {
    res = image.map((item) => {
      return matchUrl(item);
    });
  } else if (image[0] == "[" && image && !Array.isArray(image)) {
    res = JSON.parse(image).map((item) => {
      item.url = matchUrl(item.url);
      return item;
    });
  }
  return res;
};

const matchUrl = (value) => {
  let res = JSON.parse(JSON.stringify(value));
  if (!res) return res;
  res = matchArr.some((i) => res.includes(i))
    ? res.replace(/(\.\w+)$/, "_50x50$1")
    : res;
  return res;
};

export {
  seasonList,
  temperatureList,
  statsList,
  weatherList,
  formatLink,
  getTimeDiff,
  timePickerOptions,
  fastTimePickerOptions,
  seriesTimePickerOptions,
  mergeTableCols,
  matchImg,
};
