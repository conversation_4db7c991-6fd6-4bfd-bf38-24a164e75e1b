<template>
  <div v-if="condition" class="item">
    <div><field v-model="condition.field" :fields="fields" @update="updateField" /></div>
    <div><operator v-model="condition" @update="change" /></div>
    <div><value v-model="condition" @update="change" /></div>
  </div>
</template>
<script>

import field from './field.vue'
import operator from './operator.vue'
import value from './value.vue'
export default {
  name: 'Item',
  components: {
    field, operator, value
  },
  model: {
    prop: 'value',
    event: 'update'
  },
  props: {
    value: {
      type: Object,
      default: {}
    },
    fields: {
      type: Array,
      default: []
    }
  },
  data() {
    return { condition: null }
  },
  mounted() {
    this.condition = this.value
  },
  methods: {
    updateField(field) {
      var fieldType = this.fields.find(a => a.field === field)?.propType
      this.condition.fieldType = fieldType
      this.condition.operator = 'Equal'
      this.change()
    },
    change() {
      this.$emit('update', this.condition)
    }
  }
}
</script>
<style scoped lang="scss">
.item {
    flex: 1;
    line-height: 30px;
    margin-bottom: 10px;
    display: flex;
    gap: 20px;
}
</style>
