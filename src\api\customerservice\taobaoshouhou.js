import request from '@/utils/request'
const GroupPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/TaoBaoShouHou/`

//获取组distinct
export const getTaoBaoShouHouGroup = (params, config = {}) => { return request.get(GroupPrefix + 'GetTaoBaoShouHouGroup', { params: params, ...config }) }

//淘宝客服人员效率统计-售后组--分页获取分组
export const getTaoBaoShouHouGroupList = (params, config = {}) => { return request.post(GroupPrefix + 'GetTaoBaoShouHouGroupList', params, config) }
//新增-淘宝客服人员效率统计-售后组
export const addTaoBaoShouHouGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'AddTaoBaoShouHouGroupAsync', params, config) }
//删除-淘宝客服人员效率统计-售后组
export const deleteTaoBaoShouHouGroupAsync = (params, config = {}) => { return request.get(GroupPrefix + 'DeleteTaoBaoShouHouGroupAsync', { params: params, ...config }) }
//修改-淘宝客服人员效率统计-售后组
export const updateTaoBaoShouHouGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'UpdateTaoBaoShouHouGroupAsync', params, config) }
//淘宝客服人员效率统计-售后组--导入组
export const importTaoBaoShouHouGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportTaoBaoShouHouGroupAsync', params, config) }


//淘宝客服人员效率统计-售后组--分页获取分组
export const getTaoBaoShouHouInquirsList = (params, config = {}) => { return request.post(GroupPrefix + 'GetTaoBaoShouHouInquirsList', params, config) }
//删除-淘宝客服人员效率统计-售后咨询数据
export const deleteTaoBaoShouHouInquirsAsync = (params, config = {}) => { return request.get(GroupPrefix + 'DeleteTaoBaoShouHouInquirsAsync', { params: params, ...config }) }
//淘宝客服人员效率统计-售后组--导入咨询数据
export const importTaoBaoShouHouInquirsAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportTaoBaoShouHouInquirsAsync', params, config) }

export const GetTaoBaoShouHouInquirsNotExistsList = (params, config = {}) => { return request.post(GroupPrefix + 'GetTaoBaoShouHouInquirsNotExistsList', params, config) }


//个人效率统计-分页查询
export const getTaoBaoShouHouPersonalEfficiencyList = (params, config = {}) => { return request.post(GroupPrefix + 'GetTaoBaoShouHouPersonalEfficiencyList', params, config) }
//个人效率统计-店铺个人效率
export const getTaoBaoShouHouShopPersonalEfficiencyList = (params, config = {}) => { return request.post(GroupPrefix + 'GetTaoBaoShouHouShopPersonalEfficiencyList', params, config) }
//个人效率统计-个人趋势图
export const getTaoBaoShouHouPersonalEfficiencyChar = (params, config = {}) => { return request.post(GroupPrefix + 'GetTaoBaoShouHouPersonalEfficiencyChar', params, config) }
//淘系个人效率统计导出
export const exportTaoBaoShouHouPersonalEfficiencyList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportTaoBaoShouHouPersonalEfficiencyList', params, config)
}



//组效率统计-分页查询
export const getTaoBaoShouHouGroupEfficiencyList = (params, config = {}) => { return request.post(GroupPrefix + 'GetTaoBaoShouHouGroupEfficiencyList', params, config) }
//组效率统计-组趋势图
export const getTaoBaoShouHouGroupEfficiencyChar = (params, config = {}) => { return request.post(GroupPrefix + 'GetTaoBaoShouHouGroupEfficiencyChar', params, config) }
//淘系售后组效率统计导出
export const exportTaoBaoShouHouGroupEfficiencyList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportTaoBaoShouHouGroupEfficiencyList', params, config)
}


//店效率统计-分页查询
export const getTaoBaoShouHouShopEfficiencyList = (params, config = {}) => { return request.post(GroupPrefix + 'GetTaoBaoShouHouShopEfficiencyList', params, config) }
//店效率统计-铺趋势图
export const getTaoBaoShouHouShopEfficiencyChar = (params, config = {}) => { return request.post(GroupPrefix + 'GetTaoBaoShouHouShopEfficiencyChar', params, config) }
//店效率统计-分页查询
export const exportTaoBaoShouHouShopEfficiencyList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportTaoBaoShouHouShopEfficiencyList', params, config)
}

//店铺组效率
export const getTbShInquirsStatisticsByShopListMonth = (params, config = {}) => { return request.post(GroupPrefix + 'GetTbShInquirsStatisticsByShopListMonth', params, config) }

//天猫投诉--导入咨询数据
export const importTianMaoComplainAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportTianMaoComplainAsync', params, config) }
//分页查询-天猫投诉
export const getTianMaoComplainList = (params, config = {}) => { return request.post(GroupPrefix + 'GetTianMaoComplainList', params, config) }
//修改-天猫投诉
export const updateTianMaoComplainAsync = (params, config = {}) => { return request.post(GroupPrefix + 'UpdateTianMaoComplainAsync', params, config) }
//导出-天猫投诉
export const exportTianMaoComplainAsync = (params, config = { responseType: 'blob' }) => { return request.post(GroupPrefix + 'ExportTianMaoComplainAsync', params, config) }
//天猫投诉--物流轨迹-查询
export const getTianMaoLogisticsTrackList = (params, config = {}) => { return request.post(GroupPrefix + 'GetTianMaoLogisticsTrackList', params, config) }
//天猫投诉--导入快递维护数据
export const importTianMaoComplainExpressAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportTianMaoComplainExpressAsync', params, config) }
//分页查询-天猫投诉快递维护数据
export const getTianMaoComplainExpressList = (params, config = {}) => { return request.post(GroupPrefix + 'GetTianMaoComplainExpressList', params, config) }

//天猫投诉--导入顺丰快递维护数据
export const importTianMaoComplainExpress_ShunFengAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportTianMaoComplainExpress_ShunFengAsync', params, config) }
//分页查询-天猫投诉顺丰快递维护数据
export const getTianMaoComplainExpress_ShunFengList = (params, config = {}) => { return request.post(GroupPrefix + 'GetTianMaoComplainExpress_ShunFengList', params, config) }
