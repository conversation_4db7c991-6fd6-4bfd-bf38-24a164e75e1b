
import request from '@/utils/request'
const GroupPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/AlbbInquirs/`

//获取组distinct
export const getAlbbGroup = (params, config = {}) => { return request.get(GroupPrefix + 'GetAlbbGroup', { params: params, ...config }) }
//分页获取分组
export const getAlbbGroupPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetAlbbGroupPageList', params, config) }
//新增-组
export const addAlbbGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'AddAlbbGroupAsync', params, config) }
//删除-组
export const deleteAlbbGroupAsync = (params, config = {}) => { return request.get(GroupPrefix + 'DeleteAlbbGroupAsync', { params: params, ...config }) }
//修改-组
export const updateAlbbGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'UpdateAlbbGroupAsync', params, config) }
//导入-组
export const importAlbbGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportAlbbGroupAsync', params, config) }
//获取分组修改日志
export const GetGroupLogList = (params, config = {}) => { return request.post(GroupPrefix + 'GetGroupLogList', params, config) }
//咨询数据没有匹配到分组的
export const GetAlbbInquirsNotExistsList = (params, config = {}) => { return request.post(GroupPrefix + 'GetAlbbInquirsNotExistsList', params, config) }


//分页获取咨询数据
export const getAlbbInquirsPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetAlbbInquirsPageList', params, config) }
//删除咨询数据
export const deleteAlbbInquirsAsync = (params, config = {}) => { return request.get(GroupPrefix + 'DeleteAlbbInquirsAsync', { params: params, ...config }) }
//导入-咨询数据
export const importAlbbInquirsAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportAlbbInquirsAsync', params, config) }


//个人效率统计-售前
export const getAlbbPersonalEfficiencyPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetAlbbPersonalEfficiencyPageList', params, config) }
//个人效率统计-店铺个人效率-售前
export const getAlbbShopPersonalEfficiencyPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetAlbbShopPersonalEfficiencyPageList', params, config) }
//个人效率统计-个人趋势图-售前
export const getAlbbPersonalEfficiencyChat = (params, config = {}) => { return request.post(GroupPrefix + 'GetAlbbPersonalEfficiencyChat', params, config) }
//个人效率统计导出
export const exportAlbbPersonalEfficiencyList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportAlbbPersonalEfficiencyList', params, config)
}

//组效率统计-分页查询
export const getAlbbGroupEfficiencyPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetAlbbGroupEfficiencyPageList', params, config) }
//组效率统计-组趋势图
export const getAlbbGroupEfficiencyChat = (params, config = {}) => { return request.post(GroupPrefix + 'GetAlbbGroupEfficiencyChat', params, config) }
//组效率统计导出
export const exportAlbbGroupEfficiencyList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportAlbbGroupEfficiencyList', params, config)
}

//店效率统计-分页查询
export const getAlbbShopEfficiencyPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetAlbbShopEfficiencyPageList', params, config) }
//店效率统计-铺趋势图
export const getAlbbShopEfficiencyChat = (params, config = {}) => { return request.post(GroupPrefix + 'GetAlbbShopEfficiencyChat', params, config) }
//店效率统计导出
export const exportAlbbShopEfficiencyList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportAlbbShopEfficiencyList', params, config)
}


//店铺组效率
export const getAlbbInquirsStatisticsByShopListMonth = (params, config = {}) => { return request.post(GroupPrefix + 'GetAlbbInquirsStatisticsByShopListMonth', params, config) }

//ImportAlbbSupplierExhibitionAsync 导入阿里巴巴展会供应商列表
export const ImportAlbbSupplierExhibitionAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportAlbbSupplierExhibitionAsync', params, config) }

//AddOrEditAlbbSupplierExhibitionAsync 新增或编辑阿里巴巴展会供应商列表
export const AddOrEditAlbbSupplierExhibitionAsync = (params, config = {}) => { return request.post(GroupPrefix + 'AddOrEditAlbbSupplierExhibitionAsync', params, config) }

//GetAlbbSupplierExhibition  阿里巴巴展会供应商列表查询
export const GetAlbbSupplierExhibition = (params, config = {}) => { return request.get(GroupPrefix + 'GetAlbbSupplierExhibition', { params: params, ...config }) }

//ExportAlbbSupplierExhibition 阿里巴巴展会供应商列表导出
export const ExportAlbbSupplierExhibition = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportAlbbSupplierExhibition', params, config)
}

//GetSampleGoodsList  阿里巴巴展会供应商选品列表查询
export const getSampleGoodsList = (params, config = {}) => { return request.post(GroupPrefix + 'GetSampleGoodsList', params, config) }

//getAlbbSupplierExhibitionlist  阿里巴巴展会供供应商列表
export const getAlbbSupplierExhibitionlist = (params, config = {}) => { return request.get(GroupPrefix + 'GetAlbbSupplierExhibitionlist', { params: params, ...config }) }

//UpdateSampleGoods 新增或编辑阿里巴巴展会供应商选品列表
export const updateSampleGoods = (params, config = {}) => { return request.post(GroupPrefix + 'UpdateSampleGoods', params, config) }
//saveSampleGoods 保存普通商品资料
export const saveSampleGoods = (params, config = {}) => { return request.post(GroupPrefix + 'SaveSampleGoods', params, config)  }

//GetSampleGoodPriceRangList  阿里巴巴展查询样品起订列表
export const getSampleGoodPriceRangList = (params, config = {}) => { return request.get(GroupPrefix + 'GetSampleGoodPriceRangList', { params: params, ...config }) }

//InitSampleGoods  初始化
export const initSampleGoods = (params, config = {}) => { return request.get(GroupPrefix + 'InitSampleGoods', { params: params, ...config }) }

// 导入阿里巴巴展会供应商商品列表
export const ImportSampleGoods = (params, config = {}) => { return request.post(GroupPrefix + 'ImportSampleGoodsAsync', params, config) }

// 阿里巴巴展会供应商商品列表导出
export const ExportSampleGoods = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportSampleGoodsAsync', params, config)
}

//GetSampleProCodeImages  展会ID图片获取查询
export const getSampleProCodeImages = (params, config = {}) => { return request.get(GroupPrefix + 'GetSampleProCodeImages', { params: params, ...config }) }

// 打印页-是否打印修改
export const updateSampleGoods2DaYin = (params, config = {}) => { return request.post(GroupPrefix + 'UpdateSampleGoods2DaYin', params, config) }

//GetSuiJiGoodsCodeBath  生成二维码初始化
export const getSuiJiGoodsCodeBath = (params, config = {}) => { return request.get(GroupPrefix + 'GetSuiJiGoodsCodeBath', { params: params, ...config }) }

// 批量设置选品中心链接运营
export const batchUpdateSampleGoodsToOperateId = (params, config = {}) => { return request.post(GroupPrefix + 'BatchUpdateSampleGoodsToOperateId', params, config) }

//GetSampleGoodsLog  样品换绑列表查询
export const getSampleGoodsLog = (params, config = {}) => { return request.post(GroupPrefix + 'GetSampleGoodsLog', params, config) }

//PageProductList
export const PageProductList = (params, config = {}) => { return request.post(GroupPrefix + 'PageProductList', params, config) }

//SaveSampleStyleAsync
export const SaveSampleStyleAsync = (params, config = {}) => { return request.post(GroupPrefix + 'SaveSampleStyleAsync', params, config) }

//GetSampleGoodsLogList  1688选品日志列表查询
export const getSampleGoodsLogList = (params, config = {}) => { return request.post(GroupPrefix + 'GetSampleGoodsLogList', params, config) }

//GetErpSampleGoodsChooseRecordListAsync  运营选品列表查询
export const getErpSampleGoodsChooseRecordListAsync = (params, config = {}) => { return request.post(GroupPrefix + 'GetErpSampleGoodsChooseRecordListAsync', params, config) }

//GetErpSampleGoodsChooseRecordListAsync  运营选品列表查询驳回记录
export const getErpSampleGoodsChooseRecordList2Async = (params, config = {}) => { return request.post(GroupPrefix + 'GetErpSampleGoodsChooseRecordList2Async', params, config) }

//ExportErpSampleGoodsChooseRecordListAsync 运营选品列表导出
export const exportErpSampleGoodsChooseRecordListAsync = (params, config = { responseType: 'blob' }) => { return request.post(GroupPrefix + 'ExportErpSampleGoodsChooseRecordListAsync', params, config) }

//BatchUpdateSampleGoodsToBoothInformation  批量解绑货架
export const batchUpdateSampleGoodsToBoothInformation = (params, config = {}) => { return request.post(GroupPrefix + 'BatchUpdateSampleGoodsToBoothInformation', params, config) }

//BatchDeleteSampleGoods  批量删除1688展会商品
export const batchDeleteSampleGoods = (params, config = {}) => { return request.post(GroupPrefix + 'BatchDeleteSampleGoods', params, config) }

//同步新增商品到微信小程序
export const initProductList = (params, config = {}) => { return request.get(GroupPrefix + 'InitProductList', { params: params, ...config }) }

// 阿里巴巴展会供应商商品列表导出
export const exportSampleGoodsLog = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportSampleGoodsLog', params, config)
}

//GetCrmEfficiencyDept  获取1688选品运营数据
export const getCrmEfficiencyDept = (params, config = {}) => { return request.post(GroupPrefix + 'GetCrmEfficiencyDept', params, config) }
