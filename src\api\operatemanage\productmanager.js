import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/productmanager/`

//跨境产品管理导出列表
export const ExportProductTemuList = (params, config ={responseType: 'blob'}) => {
  return request.post(apiPrefix + 'ExportProductTemuList',  params, config)
}

// 获取批量上下架列表
export const getProductUpDownList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProductUpDownList', params, config)
}
export const ExportProductUpDownList = (params, config = {responseType: 'blob'}) => {
  return request.post(apiPrefix + 'ExportProductUpDownList',  params, config)
}

// 批量上下架操作
export const batchProductUpDown = (params, config = {}) => {
  return request.post(apiPrefix + 'BatchProductUpDown', params, config)
}

// 批量上下架日志
export const GetProductUpDownLogPageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProductUpDownLogPageList', params, config)
}

// 导出批量上下架日志
export const getProductUpDownLogExport = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProductUpDownLogExport', params, config)
}
export const ExportProductUpDownLogList = (params, config = {responseType: 'blob'}) => {
  return request.post(apiPrefix + 'ExportProductUpDownLogList',  params, config)
}

// 获取拼多多跨境产品列表
export const getProductTemuList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProductTemuList', params, config)
}

// 拼多多跨境产品列表导入
export const importProductInfo_PddAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'ImportProductInfo_PddAsync', params, config)
}

// 批量修改拼多多跨境产品
export const batchUpdateProductTemu = (params, config = {}) => {
  return request.post(apiPrefix + 'BatchUpdateProductTemu', params, config)
}

// 删除批次拼多多跨境产品
export const batchDeleteProductTemuAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'BatchDeleteProductTemuAsync?batchNumber=' + params.batchNumber, params, config) }

// 拼多多跨境产品日志
export const getProductTemuLogList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProductTemuLogList', params, config)
}

// 产品管理-获取项目选择器数据
export const getProductProjectList = (params, config = {}) => {
  return request.post(apiPrefix + 'getProductProjectList', params, config)
}

// 产品管理-新增项目
export const addProductProject = (params, config = {}) => {
  return request.post(apiPrefix + 'AddProductProject', params, config)
}

// 产品管理-获取备注选择器数据
export const getProductRemarkList = (params, config = {}) => {
  return request.post(apiPrefix + 'getProductRemarkList', params, config)
}


// 删除链接
export const deletePDDProduct = (params, config = {}) => {
  return request.post(apiPrefix + 'DeletePDDProduct', params, config)
}


//添加运营同步宝贝记录
export const addOperateSyncProductRecord = (params, config = {}) => {
  return request.post(apiPrefix + 'AddOperateSyncProductRecord', params, config)
}

//获取运营同步宝贝列表
export const getOperateSyncProductRecord = (params, config = {}) => {
  return request.post(apiPrefix + 'GetOperateSyncProductRecord', params, config)
}

// 批量导入修改
export const importBatchUpdateProduct = (params, config = {}) => {
  return request.post(apiPrefix + 'ImportBatchUpdateProduct', params, config)
}
