export function download(resp) {
  if (!resp) return;
  var data = resp.data;
  var type = data.type;

  var fileName = resp.headers["content-disposition"];

  // 使用正则表达式匹配filename*字段
  var match = /filename\*=UTF-8''([^;]*)/.exec(fileName);

  var name = null;

  if (match) {
    // 如果找到filename*字段，则解码文件名
    name = decodeURIComponent(match[1]);
  } else {
    // 如果未找到filename*字段，则匹配普通的filename字段
    match = /filename=(.*?)(;|$)/.exec(fileName);
    name = match[1];
  }

  // 如果无法从content-disposition获取文件名，基于content-type生成一个通用的文件名
  if (!name) {
    var contentType = data.type;
    var extension = "." + contentType.split("/")[1]; // 获取文件扩展名

    // 使用当前时间戳生成一个唯一的文件名
    var timestamp = new Date()
      .toISOString()
      .replace(/[-:]/g, "")
      .replace("T", "")
      .slice(0, -5);
    name = "file_" + timestamp + extension;
  }

  if (data) {
    const aLink = document.createElement("a");
    const blob = new Blob([data], { type: type });
    aLink.href = URL.createObjectURL(blob);
    aLink.setAttribute("download", name);
    aLink.click();
  }
}
