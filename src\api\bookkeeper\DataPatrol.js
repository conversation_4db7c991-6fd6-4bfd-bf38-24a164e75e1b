import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/DataPatrol/`

//获取数据
export const QueryData =(params, config = {}) => { return request.get(apiPrefix + 'QueryData',{ params: params, ...config })}

//获取状态
export const QueryStatus =(params, config = {}) => { return request.get(apiPrefix + 'QueryStatus',{ params: params, ...config })}

//获取Sql
export const  QuerySql= (params, config = {}) => {return request.post(apiPrefix + 'QuerySql', params, config)}

//新增或更新Sql
export const  InsertOrUpdateSql= (params, config = {}) => {return request.post(apiPrefix + 'InsertOrUpdateSql', params, config)}

//删除Sql
export const  DeleteSql= (params, config = {}) => {return request.post(apiPrefix + 'DeleteSql', params, config)}