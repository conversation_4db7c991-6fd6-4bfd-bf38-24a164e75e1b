<template>
  <div v-if="condition">
    <template v-if="condition.operator=='Any' || condition.operator=='NotAny'">
      <el-input v-model="multiValue" type="textarea" :autosize="{ minRows: 2}" placeholder="请输入内容[,]或者换行切割多个内容" class="value" clearable @change="multiValueChange" />
    </template>
    <template v-else-if="condition.operator=='Range'">
      <el-input-number v-model="min" placeholder="最小值" class="valueMin" clearable @change="minMaxChange" />
      -
      <el-input-number v-model="max" placeholder="最大值" class="valueMax" clearable @change="minMaxChange" />
    </template>
    <template v-else-if="condition.operator=='DateRange'">
      <el-date-picker
        v-model="singleValue"
        type="datetimerange"
        class="value"
        :picker-options="pickerOptions"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        clearable
        @change="changeSingleValue"
      />
    </template>
    <template v-else>
      <template v-if="condition.fieldType=='String'">
        <el-input v-model="singleValue" placeholder="请输入" class="value" clearable @change="changeSingleValue" />
      </template>
      <template v-if="condition.fieldType=='Single' || condition.fieldType=='Decimal' || condition.fieldType=='Double' || condition.fieldType=='Int64' || condition.fieldType=='Int32'">
        <el-input-number v-model="singleValue" placeholder="请输入" class="value" clearable @change="changeSingleValue" />
      </template>
      <template v-if="condition.fieldType=='DateTime' || condition.fieldType=='Date'">
        <el-date-picker
          v-model="singleValue"
          type="date"
          class="value"
          placeholder="选择日期"
          :picker-options="pickerOptions"
          @change="changeSingleValue"
        />
      </template></template>
  </div>
</template>

<script>
import { pickerOptions } from '@/utils/tools'
export default {
  name: 'Value',
  model: {
    prop: 'value',
    event: 'update'
  },
  props: {
    value: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      condition: { fieldType: 'String', value: null },
      singleValue: null,
      multiValue: null,
      min: null,
      max: null,
      datetimeRange: [],
      pickerOptions
    }
  },
  watch: {
    value: {
      handler() {
        this.valueChange()
      },
      deep: true
    }
  },
  mounted() {
    this.valueChange()
  },
  methods: {
    setValue() {
      if (this.condition && this.condition.value) {
        if (this.condition.operator === 'Any' || this.condition.operator === 'NotAny') {
          this.multiValue = this.condition.value.join('\r\n')
        } else if (this.condition.operator === 'Range') {
          this.min = this.condition.value[0]
          this.max = this.condition.value[1]
        } else if (this.condition.operator === 'DateRange') {
          this.datetimeRange = [new Date(this.condition.value[0]), new Date(this.condition.value[1])]
        } else {
          this.singleValue = this.condition.value
        }
      }
    },

    valueChange() {
      this.condition = this.value
      this.setValue()
      this.$forceUpdate()
    },
    changeSingleValue() {
      this.condition.value = this.singleValue
      this.change()
    },
    multiValueChange() {
      this.condition.value = this.multiValue.split(/[(\r\n)\r\n,]+/g)
      this.change()
    },
    minMaxChange() {
      if (this.min && this.max && this.max > this.min) {
        this.condition.value = [this.min, this.max]
        this.change()
      }
    },
    change() {
      this.$emit('update', this.condition)
    }
  }
}
</script>
<style scoped lang="scss">
  .value{width:300px;}
  .valueMin,.valueMax{width:143px;}
</style>
