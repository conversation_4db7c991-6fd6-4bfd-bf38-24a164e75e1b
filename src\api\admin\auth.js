import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API}/auth/`

// 授权

/**
 * 登录
 */
export const login = (params, config = {}) => {  return request.post(apiPrefix + 'login', params, { ...{ api: { auth: false }}, ...config })}
export const refresh = (params, config = {}) => {  return request.get(apiPrefix + 'refresh', { params: params, ...config })}
export const getVerifyCode = (params, config = {}) => {  return request.get(apiPrefix + 'getverifycode', { params: params, ...{ api: { auth: false }}, ...config })}
export const getPassWordEncryptKey = (params, config = {}) => {  return request.get(apiPrefix + 'getPassWordEncryptKey', { params: params, ...{ api: { auth: false }}, ...config })}
export const getLoginInfo = (params, config = {}) => {  return request.get(apiPrefix + 'getUserInfo', { params: params, ...config })}


export const ddlogin = (params, config = {}) => {return request.post(apiPrefix + 'DDLogin', params, { ...{ api: { auth: false }}, ...config })}
export const ddurl = (params, config = {}) => { return request.get(apiPrefix + 'GetDDRedirectUrl', { params: params, ...config })}

//获取token中key-value
export const getTokenKeyValue = (params, config = {}) => { return request.get(apiPrefix + 'GetTokenKeyValue', { params: params, ...config })}

//获取公司
export const getCompany = (params, config = {}) => { return request.get(apiPrefix + 'GetCompany', { params: params, ...config })}

//获取临时令牌
export const getAccessToken = (params, config = {}) => { return request.get(apiPrefix + 'getAccessToken', { params: params, ...config })}