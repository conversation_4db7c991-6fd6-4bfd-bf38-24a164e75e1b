import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/SemiAndFinish/`

//获取小组列表
export const getGroupsList= (params, config = {}) => {return request.get(apiPrefix + 'GetGroups',{params:params, ...config})}
//获取区域列表
export const getAreasList= (params, config = {}) => {return request.get(apiPrefix + 'GetAreas',{params:params, ...config})}
//获取职位列表
export const getPostsList= (params, config = {}) => {return request.get(apiPrefix + 'GetPosts',{params:params, ...config})}
//获取系列编码
export const getSeriesCodes= (params, config = {}) => {return request.get(apiPrefix + 'GetSeriesCodes',{params:params, ...config})}
//获取成品编码
export const getGoodCodes= (params, config = {}) => {return request.get(apiPrefix + 'GetGoodCodes',{params:params, ...config})}


//新增成品转半成品
export const addSemiPurchase= (params, config = {}) => {return request.post(apiPrefix + 'AddSemiPurchase',params,config)}
//新增半成品转成品
export const addProductPurchase= (params, config = {}) => {return request.post(apiPrefix + 'AddProductPurchase',params,config)}
//查询成品转半成品
export const getSemiPurchase= (params, config = {}) => {return request.post(apiPrefix + 'GetSemiPurchase',params,config)}
//查询半成品转成品
export const getProductPurchase= (params, config = {}) => {return request.post(apiPrefix + 'GetProductPurchase',params,config)}
//删除半成品转成品
export const deleteProductPurchase= (params, config = {}) => {return request.get(apiPrefix + 'DeleteProductPurchase',{params:params, ...config})}
//删除成品转半成品
export const deleteSemiPurchase= (params, config = {}) => {return request.get(apiPrefix + 'DeleteSemiPurchase',{params:params, ...config})}
//编辑半成品转成品
export const editProductPurchase= (params, config = {}) => {return request.post(apiPrefix + 'UpdateProductPurchase',params,config)}
//编辑成品转半成品
export const editSemiPurchase= (params, config = {}) => {return request.post(apiPrefix + 'UpdateSemiPurchase',params,config)}