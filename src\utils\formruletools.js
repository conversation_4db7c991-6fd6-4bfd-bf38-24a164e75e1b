 import { getDirector<PERSON>ist,getDirector<PERSON><PERSON><PERSON><PERSON>,getDirectorUser<PERSON>ist,getDirector<PERSON>roup<PERSON>ser<PERSON>ist,getDirectorAndGroupList, getList,getProductBianMaCategoryPageList } from '@/api/operatemanage/base/shop'
 import { getList as getcategoryList } from '@/api/operatemanage/base/category'
 import {convertToChild,warehouselist,platformlist } from '@/utils/tools'
 import {getExpressComanyAll,queryCompanyList} from '@/api/express/express'
 import {getAllBuModule} from '@/api/admin/business'
 import {getAllProBrand} from '@/api/inventory/warehouse'
 import { getProductProjectList } from "@/api/operatemanage/productmanager"

const ruleShop=async(platformid)=>  {
    let rule= {validate: [{type: 'number', required: true, message:'请选择'}],options: [{value:null, label:'请选择'}]}
    const res =await  getList({platform:platformid,pageSize:9999,currentPage:1})
    if (!res?.success) { return }
    if(res.data.list&&res.data.list.length>0)
      res.data.list.forEach(f=>{rule.options.push({value:f.id,label:f.shopName})})
    return rule;
  }
const ruleShopCode=async(platformid)=>  {
    let rule= {validate: [{type: 'string', required: true, message:'请选择'}],options: [{value:null, label:'请选择'}]}
    if(!platformid) return rule;
    const res =await getList({platform:platformid,pageSize:50,currentPage:1})
    if (!res?.success) { return }
    if(res.data.list&&res.data.list.length>0)
      res.data.list.forEach(f=>{rule.options.push({value:f.shopCode,label:f.shopName})})
    return rule;
  }

  const ruleProjectList=async (parm)=>{
    let rule= {validate: [{type: 'string', required: false, message:'请选择'}],options: [{value:'', label:'请选择'}]}
    const res = await getProductProjectList(parm)
    if (!res?.success) {  return }
    if(res.data&&res.data.length>0)
       res.data.forEach(f=>{rule.options.push({value:f.projName,label:f.projName})})
    return rule;
  }

  const projList=async (parm)=>{
    let rule= {validate: [{type: 'string', required: false, message:'请选择'}],options: [{value:'', label:'请选择'}]}
    const res = await getProductProjectList(parm)
    if (!res?.success) {  return }
    if(res.data&&res.data.length>0)
      res.data.forEach(f=>{rule.options.push({value:f.projName,label:f.projName})})
      rule.options = rule.options.filter(item => item.label == '大马美甲');
      return rule;
  }

 const ruleDirector=async (parm)=>{
    let rule= {validate: [{type: 'string', required: false, message:'请选择'}],options: [{value:'', label:'请选择'}]}
    const res = await getDirectorList(parm)
    if (!res?.success) {  return }
    if(res.data&&res.data.length>0)
       res.data.forEach(f=>{rule.options.push({value:f.key,label:f.value})})
    return rule;
  }
  const ruleDirectorUserType=async (parm)=>{
     let rule= {validate: [{type: 'string', required: false, message:'请选择'}],options: [{value:'', label:'请选择'}]}
     const res = await getDirectorList(parm)
     if (!res?.success) {  return }
     if(res.data&&res.data.length>0)
        res.data.forEach(f=>{
          if(f.userType)
            rule.options.push({value:f.key,label:f.value})
      })
     return rule;
    }
  const ruleDirectorBackup=async (parm)=>{
    let rule= {validate: [{type: 'string', required: false, message:'请选择'}],options: [{value:'', label:'请选择'}]}
    const res = await getDirectorList(parm)
    if (!res?.success) {  return }
    if(res.data&&res.data.length>0)
       res.data.forEach(f=>{rule.options.push({value:f.key,label:f.value})})
    return rule;
  }
  const ruleDirectorBackupUserType=async (parm)=>{
    let rule= {validate: [{type: 'string', required: false, message:'请选择'}],options: [{value:'', label:'请选择'}]}
    const res = await getDirectorList(parm)
    if (!res?.success) {  return }
    if(res.data&&res.data.length>0)
       res.data.forEach(f=>{
          if(f.userType)
            rule.options.push({value:f.key,label:f.value})
        })
    return rule;
  }
  const ruleDirectorName=async (parm)=>{
    let rule= {validate: [{type: 'string', required: false, message:'请选择'}],options: [{value:'', label:'请选择'}]}
    const res = await getDirectorList(parm)
    if (!res?.success) {  return }
    if(res.data&&res.data.length>0)
       res.data.forEach(f=>{rule.options.push({value:f.value,label:f.value})})
    return rule;
  }
  const ruleDirectorGroup=async ()=>{
    let rule= {validate: [{type: 'string', required: true, message:'请选择'}],options: [{value:'', label:'请选择'}]}
    const res = await getDirectorGroupList()
    if (!res?.success) {return }
    if(res.data&&res.data.length>0)
      res.data.forEach(f=>{rule.options.push({value:f.key,label:f.value})})
    return rule;
  }
  const ruleMultityDirectorGroup=async ()=>{
    let rule= {validate: [{type: 'string', required: true, message:'请选择'}],options: []}
    const res = await getDirectorGroupList()
    if (!res?.success) {return }
    if(res.data&&res.data.length>0)
      res.data.forEach(f=>{rule.options.push({value:f.key,label:f.value})})
    return rule;
  }
  const ruleMultityProBrand= async()=>{
    let rule= { options: []}
    const res = await getAllProBrand()
    if (!res?.success) {  return }
    if(res.data&&res.data.length>0)
      res.data.forEach(f=>{rule.options.push({value:f.key,label:f.value})})
    return rule;
  }
  const ruleDirectorUser=async (parm)=>{
    let rule= {validate: [{type: 'string', required: true, message:'请选择'}],options: [{value:'', label:'请选择'}]}
    const res = await getDirectorUserList(parm)
    if (!res?.success) {  return }
    if(res.data&&res.data.length>0)
       res.data.forEach(f=>{rule.options.push({value:f.key,label:f.value})})
    return rule;
  }
  const ruleDirectorGroupUser=async ()=>{
    let rule= {validate: [{type: 'string', required: true, message:'请选择'}],options: [{value:'', label:'请选择'}]}
    const res = await getDirectorGroupUserList()
    if (!res?.success) {return }
    if(res.data&&res.data.length>0)
      res.data.forEach(f=>{rule.options.push({value:f.key,label:f.value})})
    return rule;
  }
  const ruleDirectorBrand=async ()=>{
    let rule= {validate: [{type: 'string', required: true, message:'请选择'}],options: [{value:'', label:'请选择'}]}
    const res = await getAllProBrand()
    if (!res?.success) {return }
    if(res.data&&res.data.length>0)
      res.data.forEach(f=>{rule.options.push({value:f.key,label:f.value})})
    return rule;
  }
  // type 0:不转uesrid,1转userid
 const ruleDirectorAndGroup=async (type)=>{
    let rule= {//validate: [{required: true, message:'请选择'}],
                 props:{clearable:true,changeOnSelect:true, options: []}}
    const res = await getDirectorAndGroupList({type:type})
    if (!res?.success) {return }
    const list=[];
    //{value:,label:,parentvalue:}
    res.data.forEach(f=>{
       list.push({value: f.id.toString(),label:f.userName,parentvalue:f.parentId.toString()})
    })
    rule.props.options= convertToChild(list,'0')
    return rule;
  }
 const ruleBusinessModul=async ()=>{
  // {type:"cascader",field:"_reason",title:"原因",value:[],props:{options:[
  //   {value: '采购原因',label: '采购原因',children: [{value: '进货（时效、数量）判断失误',label: '进货（时效、数量）判断失误'},{value: '厂家（改价、发货、物流）异常',label: '厂家（改价、发货、物流）异常'},{value: '特殊（定制产品、专利产品）',label: '特殊（定制产品、专利产品）'},{value: '其它原因',label: '其它原因'}]},
  //   {value: '运营原因',label: '运营原因',children: [{value: '产品冲量、活动（未告知采购）',label: '新品进货（前两次采购单）'},{value: '运营给量（判断错误）',label: '运营给量（判断错误）'},{value: '下架产品出单',label: '下架产品出单'},{value: '更换厂家衔接问题',label: '更换厂家衔接问题'}]},
  //   {value: '仓库原因',label: '仓库原因',children: [{value: '审单原因',label: '审单原因'},{value: '到货提货不及时',label: '到货提货不及时'},{value: '到货入库不及时',label: '到货入库不及时'},{value: '质检入库数量型号错误',label: '质检入库数量型号错误'},{value: '加工时效超时',label: '加工时效超时'},{value: '质量问题返厂更换',label: '质量问题返厂更换'},{value: '仓库盘点、其他出库',label: '仓库盘点、其他出库'}]},
  //   {value: '市场原因',label: '市场原因',children: [{value: '疫情原因',label: '疫情原因'},{value: '天气灾害',label: '天气灾害'},{value: '其他市场波动',label: '其他市场波动'}]},
  //   ]}
    let rule= {props:{allowClear:true, multiple: true ,options: []}}
    var res=await getAllBuModule();
    if (!res?.success) {return }
    const list=[];
    res.data.forEach(f=>{
      var children=[];
      if(f.items) f.items.forEach(c=>children.push({value:c.code,label:c.name}))
      list.push({value:f.code,label:f.name,children:children})
    })
    rule.props.options= list
    return rule;
  }
 const ruleYesornoBool=async ()=>{
    let rule= {validate: [{type: 'boolean', required: true, message:'请选择'}],
            options: [{value:null, label:'请选择'},{value:false, label:'否'},{value:true, label:'是'}]}
    return rule;
  }
  const ruleProductState=async ()=>{
    let rule= {validate: [{type: 'number', required: true, message:'请选择'}],
            options: [{value:null, label:'请选择'},{value:1, label:'正常'},{value:2, label:'缺货'},{value:3, label:'禁用'}]}
    return rule;
  }
  const rulePlatform=async ()=>{
    let rule= {validate: [{type: 'number', required: true, message:'请选择'}],options:platformlist}
    //options:platformlist}// [{value:null, label:'请选择'},{value:1, label:'淘系'},{value:2, label:'拼多多'},{value:3, label:'跨境'},{value:4, label:'阿里巴巴'},{value:8, label:'工厂店'},{value:5, label:'其他'}]}
    return rule;
  }
  const ruleIllegalType=async ()=>{
    let rule= {validate: [{type: 'number', required: true, message:'请选择'}],
    options: [{value:null, label:'请选择扣款原因'},{value:0, label:'未知'},{value:1, label:'缺货'},{value:2, label:'虚假轨迹'},{value:9, label:'虚假发货'},
              {value:3, label:'延迟发货'},{value:4, label:'商家责任退货'},{value:5, label:'售后补偿'},{value:6, label:'投诉赔偿'},
              {value:8, label:'质量问题'},{value:10, label:'商品质量罚款'},{value:7, label:'其他'}]}
    return rule;
  }
  const ruleProductMask=async ()=>{
    let rule= {validate: [{required: true, message:'请选择'}],
            options: [{value:1, label:' 红色标'},{value:2, label:'绿色标'},{value:3, label:'黄色标'},{value:4, label:'蓝色标'},
                      {value:5, label:'粉红色标'},{value:6, label:'灰色标'},{value:7, label:'黑色标'},{value:8, label:'深蓝色标'}]}
    return rule;
  }
const ruleWarehouse=async ()=>{
    let rule= {validate: [{required: true, message:'请选择'}],
    options:warehouselist// [{value:0, label:' 义乌'},{value:1, label:'昌东'},{value:3, label:'安徽'},{value:4, label:'上海'}]
    }
    return rule;
  }
const ruleSendWarehouse=async ()=>{
      let rule= {
        validate: [{required: true, message:'请选择'}],
        options: [ {value:1, label:' （本仓）'},{value:2, label:'义乌四楼'},{value:3, label:'南昌昌东'},{value:5, label:' 昀晗跨境仓'},{value:6, label:' 南昌高科云仓'},{value:7, label:' 安徽'}]
      }
      return rule;
}
const ruleExpressComany=async ()=>{
    let rule= {validate: [{required: true, message:'请选择'}],options: [{value:'', label:'请选择'}]}
    const res = await getExpressComanyAll()
    if (!res?.success) {return }
    if(res.data&&res.data.length>0)
      res.data.forEach(f=>{rule.options.push({value:f.id,label:f.name})})
    return rule;
}
/*快递编码 */
const ruleExpressComanycode=async ()=>{
  let rule= {validate: [{required: true, message:'请选择'}],options: [{value:'', label:'请选择'}]}
  const res = await queryCompanyList()
  if (!res?.success) {return }
  if(res.data&&res.data.length>0)
    res.data.forEach(f=>{rule.options.push({value:f.comanyCode,label:f.companyName})})
  return rule;
}
const ruleProductCategory=async (platformid)=>{
    let rule= {validate: [{required: true, message:'请选择'}],props:{options: []}}
    const res = await getcategoryList({platform:platformid,CurrentPage:1,PageSize:100})
    if (!res?.success) {return }
    const list=[];
    //{value:,label:,parentvalue:}
    res.data.forEach(f=>{
       list.push({value:f.id,label:f.categoryName,parentvalue:f.parentId})
    })
    rule.props.options= convertToChild(list,'0')
    console.log('ruleProductCategory',rule)
    return rule;
  }
const ruleProductBianMaCategory=async ()=>{
    let rule= {validate: [{type: 'string', required: true, message:'请选择'}],options: [{value:'', label:'请选择'}]}
    const res = await getProductBianMaCategoryPageList()
    if (!res?.success) {return }
    if(res.data&&res.data.list.length>0)
      res.data.list.forEach(f=>{rule.options.push({value:f.id,label:f.categoryName})})
    return rule;
  }
export{
  ruleShop,
  ruleShopCode,
  ruleDirector,
  ruleDirectorUserType,
  ruleDirectorBackup,
  ruleDirectorBackupUserType,
  ruleDirectorName,
  ruleDirectorGroup,
  ruleMultityDirectorGroup,
  ruleMultityProBrand,
  ruleDirectorUser,
  ruleDirectorGroupUser,
  ruleDirectorAndGroup,
  ruleDirectorBrand,
  ruleBusinessModul,
  ruleYesornoBool,
  ruleProductState,
  ruleProductMask,
  rulePlatform,
  ruleIllegalType,
  ruleWarehouse,
  ruleSendWarehouse,
  ruleExpressComany,
  ruleExpressComanycode,
  ruleProductCategory,
  ruleProductBianMaCategory,
  ruleProjectList,
  projList,
  }
