import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Express}/expressOrderInfo/`

//获取快递重量列表
export const queryOrderInfoListAsync = (params, config = {}) => { return request.post(apiPrefix + 'QueryOrderInfoListAsync', params, config) }

//获取字典设置值
export const getExpressDictionValue = (params, config = {}) => { return request.post(apiPrefix + 'GetExpressDictionValue', params, config) }

//设置字典值
export const setExpressDictionValue = (params, config = {}) => { return request.post(apiPrefix + 'SetExpressDictionValue', params, config) }

//通知复检
export const verifyGoodsByInnerOrderNo= (params, config = {}) => { return request.post(apiPrefix + 'VerifyGoodsByInnerOrderNo', params, config) }