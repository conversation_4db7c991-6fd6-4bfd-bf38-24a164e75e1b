import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/ImpulseDeclaration/`
 
export const addoreditImpulse = (params, config = {}) => {return request.post(apiPrefix + 'AddOrEditImpulse', params, config)}  
export const examImpulse = (params, config = {}) => {return request.post(apiPrefix + 'ExamImpulse', params, config)}
export const cancelImpulse = (params, config = {}) => {return request.post(apiPrefix + 'CancelImpulse', params, config)}  
export const deleteimpulsebyid = (id, config = {}) => {return request.delete(apiPrefix + 'DeleteImpulseById', { params: id, ...config })}
export const getImpulseDeclaration = (params, config = {}) => { return request.get(apiPrefix + 'GetImpulseDeclarationAsync', { params, ...config })}
export const pageImpulsedeclaration = (params, config = {}) => { return request.get(apiPrefix + 'PageImpulseDeclarationAsync', { params, ...config })}

