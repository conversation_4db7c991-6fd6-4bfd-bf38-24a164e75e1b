<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <!-- elment样式 -->
    <link rel="stylesheet" href="/approvalform/html/api/elment.css">
    <!-- vue -->
    <script src="/approvalform/html/api/vue.min.js"></script>
    <!-- elment脚本 -->
    <script src="/approvalform/html/api/elment.js"></script>
    <!-- jquery -->
    <script src="/approvalform/html/api/jquery.min.js"></script>
    <title></title>
</head>

<body>
    <div id="app" style="margin:0 auto;">
        <el-container v-loading="thisLonding" direction="vertical"
            style="padding: 0px 10px 0px 10px ; border: 1px #ccc solid;">
            <template>
                <el-table :data="list" height="500px" style="width: 99%;" tooltip-effect="dark">
                    <el-table-column show-overflow-tooltip prop="platform" label="平台" width="120">
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="sname" label="姓名" width="120">
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="groupType" label="分类" width="120">
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="groupname" label="分组" width="200">
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="groupManager" label="组长" width="120">
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="shopname" label="店铺" width="300">
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="snick" label="昵称" width="300">
                    </el-table-column>
                </el-table>
            </template>
        </el-container>
    </div>
    <script>
        var vm = new Vue({
            el: '#app',
            data: function () {
                return {
                    thisLonding: false,
                    list: [],
                }
            },
            created() {
            },
            async mounted() {
                this.onSearch();
            },
            methods: {
                onSearch() {
                    var me = this;
                    let searchURL = window.location.search;
                    searchURL = searchURL.substring(1, searchURL.length);
                    let id = searchURL.split("=")[1];
                    me.thisLonding = true;
                    $.ajax({
                        type: 'GET',
                        async: false,
                        url: `/api/customerservice/Group/GetGroupXiaoYun`,
                        data: {
                            id: id
                        },
                        success: function (result) {
                            me.thisLonding = false;
                            me.list = result.data
                        }
                    })
                },
            }
        });
    </script>
</body>

</html>