import * as echarts from 'echarts'
// import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
import { getTableColumnCache, setTableColumnCache,GetVxeTableColumnCacheAsync,SetVxeTableColumnCacheAsync } from '@/api/admin/business'
import { template } from 'lodash';
import { tonumfuc } from '@/utils/tonumqian.js'
import {matchImg} from '@/utils/getCols'
const tablejs = {
 // components: {OrderActionsByInnerNos},
 props: {
  cstmExportFunc: {
   type: Function, default: () => {
    return function () { };
   }
  },
  indexWidth: { type: Number, default: () => { return 30; } },
  dftLvl: { type: Number, default: () => { return 1; } },
  isSelectLvl: { type: Boolean, default: () => { return false } },
  editconfig: { type: Object, default: () => { return {} } },
  treeProp: { type: Object, default: () => { return { rowField: 'id', parentField: 'parentId' } } },
  // 树状默认值传为
  hasSeq: { type: Boolean, default: () => { return true } },
  // 表格数据
  tableData: { type: Array, default: () => [] },
  // 表格型号：mini,medium,small
  size: { type: String, default: 'small' },
  type: { type: String, default: 'primary' },
  isBorder: { type: Boolean, default: true },
  // 表格列配置
  tableCols: { type: Array, default: () => [] },
  isRemoteSort: { type: Boolean, default: () => { return true } }, //是否远程排序
  id: { type: String, default: () => { return new Date().valueOf().toString() } },
  that: { type: Object, default: () => { return this } },
  border: { type: Boolean | Object, default: () => { return 'default' } },
  tableHandles: { type: Array, default: () => [] },
  showsummary: { type: Boolean, default: false },
  align: { type: String, default: 'center' }, //对齐方式
  summaryarry: { type: Object, default: () => { } },
  tablekey: { type: String, default: '' },//表格key
  isstorage: { type: Boolean, default: true }, //true为本地缓存，false为后端缓存
  enabled: { type: Boolean, default: true },
  height: { type: String, default: '100%' },
  ygt: { type: Number, default: 100 },
  xgt: { type: Number, default: 100 },
  somerow: { type: String, default: '' },
  cellClassName: { type: Function, default: null },
  toolbarshow: { type: Boolean, default: () => { return true } },
  loading: { type: Boolean, default: () => { return false } },
  showheaderoverflow: { type: String, default: '' },//ellipsis（只显示省略号）,title（并且显示为原生 title）,tooltip（并且显示为 tooltip 提示）
  showoverflow: { type: String, default: 'title' },//ellipsis（只显示省略号）,title（并且显示为原生 title）,tooltip（并且显示为 tooltip 提示）
  isDisableCheckBox: { type: Boolean, default: () => { return false } },
  isIndexFixed: { type: Boolean, default: () => { return true } },
  enableCheckRange: { type: Boolean, default: () => { return true } },
  noToFixed: { type: Boolean, default: () => { return false } },
  resizable: { type: Boolean, default: () => { return true } },
  //工具栏初始定位
  position: {
   type: Object,
   default: function () {
    return {
     top: "5.25rem",
     right: "0"
    }
   }
  },
  showFooterOverflow: { type: String, default: 'tooltip' },
  scrollX: { type: Object, default: function () { return { gt: 50, enabled: true } } },
  scrollY: { type: Object, default: function () { return { gt: 50, enabled: true } } },
  isvirtual: { type: Boolean, default: false }, //是否开启虚拟滚动，注意：虚拟滚动开启后不支持页面ctrl+f查询字段

 },
 data() {
  return {
   tonumfuc,
   currentLvl: 1,
   lastSortArgs: {
    field: "",
    order: "",
   },
   orderNo: null,
   dialogHisVisible: false,
   orderNoInner: null,
   isTx: false,
   datacstmExportFunc: null,
   summarycolumns: [],
   checkBoxGroup: [],
   echartsLoading: false,
   echarts: echarts,
   exportColumnsDialogProp: {
    visible: false,
    data: [],
    colIds: [],
    colFields: [],
    colNames: [],
    mapFields: {},
    isLoadMapFields: false
   },
  }
 },

    methods: {
        formatImg(img){
            return matchImg(img)
        },
    async restoreStore({id, type, storeData}){
        
        let resp=  await GetVxeTableColumnCacheAsync({tableId:id});
        let store =null;
        if(resp && resp.success && resp.data){
            store = JSON.parse(resp.data);
        }
        if(store.fixedData){
            this.tableCols.map((item)=>{
                item.fixed = store.fixedData[item.prop]
            })
        }

        return store??storeData;

    },
    async updateStore({id, type, storeData}){
        let newobj = {};
        let mergearr = [];

        this.tableCols.map((item)=>{
            if(item.merge){
                mergearr.push({
                    name:  item.prop,
                    value: item.cols[0]['prop']
                });
            }
        })

        this.$refs.xTable.getColumns().map((item)=>{
            
            if(item.type){
                return;
            }
            mergearr.map((itemm)=>{
                if(item.field === itemm.value){
                    // item.fixed = itemm.value
                    newobj[itemm.name] = item.fixed
                }
            })
            newobj[item.field]  = item.fixed;
        })
        storeData.fixedData = newobj;

        await SetVxeTableColumnCacheAsync({tableId:id,ColumnConfig:JSON.stringify(storeData)});
    },
  dialogHisVisiblefuc(obj){
    this.isTx = obj.isTx;
    this.orderNoInner = obj.orderNoInner;
    this.orderNo = obj.orderNo;
    this.dialogHisVisible = true;
  },
  showLogDetail(val, orderType, row) {
   this.orderNoInner = orderType == 'orderNoInner' ? val : null
   this.orderNo = orderType == 'orderNo' ? val : null
   //将row对象的key转换为小写,有platForm的情况
   let res = Object.keys(row).reduce((newObj, key) => {
    newObj[key.toLowerCase()] = row[key];
    return newObj;
   }, {});
   if (this.orderNo && res.platform && (res.platform == 1 || res.platform == 4 || res.platform == 8 || res.platform == 9)) {
    this.isTx = true
   } else {
    this.isTx = false
   }
   this.$emit('dialogHisVisible', {
    isTx: this.isTx,
    orderNoInner: this.orderNoInner,
    orderNo: this.orderNo,
   })
   // this.dialogHisVisible = true;
  },
  rowStyleTable(row) {
   let styleitem = null;
   this.$emit('rowStyle', row, val => {
    styleitem = val
   });
   return styleitem;
  },
  downloadFile(files) {
   if (files) {
    let jsonF = JSON.parse(files);
    if (jsonF && jsonF.length > 0)
     window.open(jsonF[0].url);
   }
  },
  downloadFiles(files) {
   if (files) {
    let jsonF = JSON.parse(files);

    this.$showDialogform({
     path: `@/views/base/DownloadFilesForm.vue`,
     title: '文件列表',
     autoTitle: false,
     args: { files: jsonF, mode: 3 },
     height: 300,
     width: '600px',
     callOk: null
    })
   }

  },
  setExportCols() {
   if (!this.exportColumnsDialogProp.visible) {
    // this.exportColumnsDialogProp.visible = true;
    this.$emit('exportColumnsDialogProp');

    let allcolumns = this.$refs.xTable.getTableColumn();
    let tempCols = [...allcolumns.collectColumn];
    if (tempCols.length > 0 && tempCols[0].title == null) {
     tempCols[0].title = "#";
    }
    let tempRoot = {
     id: '_root',
     parentId: "_root",
     children: tempCols,
     title: '全部'
    };


    if (this.exportColumnsDialogProp.data.length == 0) {
     const visibleColumn = this.$refs.xTable.getColumns();

     visibleColumn.forEach(item => {
      this.exportColumnsDialogProp.colIds.push(item.id);
     });
    }

    if (this.exportColumnsDialogProp.isLoadMapFields == false) {
     //如果还没加载字段与导出的映射关系，先加载
     this.tableCols.forEach(col => {
      if (col.prop && col.exportField) {
       this.exportColumnsDialogProp.mapFields[col.prop] = col.exportField;
      }
      if (col.cols && col.cols.length > 0) {
       col.cols.forEach(col1 => {
        if (col1.prop && col1.exportField) {
         this.exportColumnsDialogProp.mapFields[col1.prop] = col1.exportField;
        }
        if (col1.cols && col1.cols.length > 0) {
         col1.cols.forEach(col2 => {
          if (col2.prop && col2.exportField) {
           this.exportColumnsDialogProp.mapFields[col2.prop] = col2.exportField;
          }
         });
        }
       });

      }
     });

     this.exportColumnsDialogProp.isLoadMapFields = true;
    }

    this.exportColumnsDialogProp.data = [tempRoot];


    return;
   }

   let selNodes = this.$refs.exportColTree.getCheckedNodes(true);
   this.exportColumnsDialogProp.colIds = selNodes.map(x => x.id);

   this.exportColumnsDialogProp.colFields = selNodes.map(x => {
    return x.field && this.exportColumnsDialogProp.mapFields[x.field] ? this.exportColumnsDialogProp.mapFields[x.field] : x.field;
   });

   if (this.datacstmExportFunc && typeof (this.datacstmExportFunc) == 'function') {
    console.log('开始调用导出');
    this.datacstmExportFunc({ "YH_EXT_ExportColumns": [...this.exportColumnsDialogProp.colFields] });
    console.log('导出数据结束');
   }

   this.exportColumnsDialogProp.visible = false;
  },
  // 图片点击放大
  showImg(e) {
   if (e.target.tagName == 'IMG') {
    this.$emit('showImg', e)
   }
  },
  toggleTreeMethod({ expanded, column, columnIndex, row, rowIndex }) {
   if (!expanded) return;
   return new Promise(resolve => {
    this.$emit('toggleTreeMethod', row)
    return true;
   })
  },
  copytext(e) {
   let textarea = document.createElement("textarea")
   textarea.value = e
   textarea.readOnly = "readOnly"
   document.body.appendChild(textarea)
   textarea.select()
   let result = document.execCommand("copy")
   if (result) {
    this.$message({
     message: '复制成功',
     type: 'success'
    })
   }
   textarea.remove()
  },
  //层级切换
  lvlChang(v1) {
   if (v1 == 9) {
    this.$refs.xTable.setAllTreeExpand(true);
   }
   else if (v1 == 1) {
    this.$refs.xTable.setAllTreeExpand(false)
   }
   else {
    let rows = this.$refs.xTable.getTableData().fullData;
    this.lvlShow(rows, 1, v1);
   }
  },
  lvlShow(rows, curI, maxLvl) {
   if (!rows || rows == undefined || rows == null)
    return;

   if (curI >= maxLvl) {
    rows.forEach(r => {
     this.$refs.xTable.setTreeExpand(r, false)
    })
    return;
   } else {
    rows.forEach(r => {
     this.$refs.xTable.setTreeExpand(r, true);
     this.lvlShow(r.children, curI + 1, maxLvl);
    });
   }

  },
  // 通用行合并函数（将相同多列数据合并为一行）
  mergeRowMethod({ row, _rowIndex, column, visibleData }) {
   const fields = this.somerow.split(',')
   const cellValue = row[column.property]
   if (cellValue && fields.includes(column.property)) {
    const prevRow = visibleData[_rowIndex - 1]
    let nextRow = visibleData[_rowIndex + 1]
    if (prevRow && prevRow[column.property] === cellValue) {
     return { rowspan: 0, colspan: 0 }
    } else {
     let countRowspan = 1
     while (nextRow && nextRow[column.property] === cellValue) {
      nextRow = visibleData[++countRowspan + _rowIndex]
     }
     if (countRowspan > 1) {
      return { rowspan: countRowspan, colspan: 1 }
     }
    }
   }
  },
  changecolumn(val) {
   setTimeout(() => {
    this.columns.forEach(column => {
     if (val != null) {
      if (val.includes(column.property)) {
       column.visible = false
      }
     }
    })
    if (this.$refs.xTable) {
     this.$refs.xTable.refreshColumn()
    }
   }, 800)
  },
  changecolumn_setTrue(val) {
   setTimeout(() => {
    this.columns.forEach(column => {
     if (val != null) {
      if (val.includes(column.property)) {
       column.visible = true
      }
     }
    })
    if (this.$refs.xTable) {
     this.$refs.xTable.refreshColumn()
    }
   }, 800)
  },
  async tooclick(params) {
   console.log(params)
   //行数据
   const visibleColumn = this.$refs.xTable.getColumns()
   const Columnevn = this.$refs.xTable;

   switch (params.type) {
    case 'confirm': {
     var checked = [];
     var nochecked = [];
     var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
     visibleColumn.forEach(item => {
      if (item?.property)
       checked.push(item.property)
     })

     var arrList = Columnevn.tableFullColumn;
     var newList = visibleColumn;
     arrList = arrList.filter((item) => {
      return newList.every((item2) => {
       return item.property != item2.property;
      });
     });
     arrList.forEach(item => {
      nochecked.push(item.property)
     })
     if (!this.isstorage)
      await setTableColumnCache({ key: key, displays: checked, hides: nochecked });

     //获取label未选中的值
     // setTimeout(async() => {
     //     var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
     //     let res = await getTableColumnCache({ key: key })
     //     console.log("返回的数据",res)
     // }, 200);
     break
    }
    case 'reset': {
     var checked = [];
     var nochecked = [];
     var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
     visibleColumn.forEach(item => {
      if (item?.property)
       checked.push(item.property)
     })
     if (!this.isstorage)
      await setTableColumnCache({ key: key, displays: checked, hides: [] });

     break
    }
    case 'close': {
     var checked = [];
     var nochecked = [];
     var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
     visibleColumn.forEach(item => {
      if (item?.property)
       checked.push(item.property)
     })

     var arrList = Columnevn.tableFullColumn;
     var newList = visibleColumn;
     arrList = arrList.filter((item) => {
      return newList.every((item2) => {
       return item.property != item2.property;
      });
     });
     arrList.forEach(item => {
      nochecked.push(item.property)
     })
     if (!this.isstorage)
      await setTableColumnCache({ key: key, displays: checked, hides: nochecked });

     // VXETable.modal.message({ content: `关闭了面板，显示为 ${visibleColumn.length} 列`, status: 'info' })
     break
    }
    case 'open': {
     break
    }
   }

   if (params.type && params.type != 'open')
    this.loadRowEcharts();
  },
  // 清空全选
  clearSelection() {
   this.$refs.xTable.clearCheckboxRow()
  },
  async checkboxall() {
   const records = this.$refs.xTable.getCheckboxRecords()
   this.$emit('checkbox-range-end', records);
   this.$emit('select', records);
  },
  async toggleRowSelection(val) {
   await this.$refs.xTable.clearCheckboxRow()
   await this.$refs.xTable.setCheckboxRow(val, true)
   console.log("组件内数据", val)
  },
  selectAllEvent({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {
   const records = this.$refs.xTable.getCheckboxRecords()
   //   this.$emit('checkbox-range-end', records);
   this.$emit("cellClick", { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event });
  },
  cellClickEvent({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {
   this.$emit("cellClick", { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event });

  },
  selectChangeEvent({ checked }) {
   const records = this.$refs.xTable.getCheckboxRecords()
   this.$emit('checkbox-range-end', records);
   this.$emit('select', records);
  },
  cellStyleFun({ row, rowIndex, column }) {
   let rltStyle = {};
   let col = column;
   var colArg = this.tableCols.find(x => x.prop == col.property);

   if (colArg) {
    if (colArg.type && (colArg.type == "images" || colArg.type == "image"))
     rltStyle = {
      ...rltStyle,
      ...{
       textAlign: "center"
      }
     };

    if (colArg.align)
     rltStyle = {
      ...rltStyle,
      ...{
       textAlign: colArg.align
      }
     };

   }
   return rltStyle;
  },
  customSortMethod({ data, sortList }) {
   if (this.isRemoteSort) {
    if (sortList && sortList.length > 0) {
     if (sortList[0].field != this.lastSortArgs.field || sortList[0].order != this.lastSortArgs.order) {
      this.lastSortArgs = { ...sortList[0] };
      this.$emit('sortchange', {
       order: (this.lastSortArgs.order.indexOf('desc') > -1 ? 'descending' : 'asc'),
       prop: this.lastSortArgs.field
      });
     }
    }
   } else {
    this.$refs.xTable.sort(sortList[0].field, sortList[0].order)
   }
  },
  headerCellClassName({ column, columnIndex }) {
   let className = '';
   var col = this.tableCols.find(x => x.prop == column.property);

   if (col && col.align) {
    className = ' ' + `vxetableheadercell-${column.align}-20221216`;
   } else if (col && col.type && (col.type == "images" || col.type == "image")) {
    className = ' ' + `vxetableheadercell-center-20221216`;
   }

   return className;
  },
             // 格式化函数，处理千位分隔符和小数位
             formatNumber (number){
              const absNumber = Math.abs(number);
              const options = {
                  minimumFractionDigits: absNumber >= 100 ? 0 : 2,
                  maximumFractionDigits: absNumber >= 100 ? 0 : 2,
              };
              return new Intl.NumberFormat('zh-CN', options).format(number);
          },  
  footerMethod({ columns, data }) {
   const sums = [];
   if (!this.summaryarry)
    return sums
   var arr = Object.keys(this.summaryarry);
   if (arr.length == 0)
    return sums
   //const { columns, data } = param;
   var hashj = false;
   columns.forEach((column, index) => {

    if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
     var sum = this.summaryarry[column.property + '_sum'];
     if (sum == null) return;
     else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
    //  else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2);
     else if (Math.abs(parseInt(sum)) < 100){
        if(   Number(Math.abs(parseInt(sum))) === Math.floor(Number(Math.abs(parseInt(sum)))) ){
            sums[index] = sum;
        }
        sums[index] = sum.toFixed(2);
    }
     else if (Math.abs(parseInt(sum)) && this.noToFixed) sums[index] = sum
    //  else sums[index] = sum.toFixed(0);
    else sums[index] = this.formatNumber(sum);
    }
    else if (column.property == 'buchar') sums[index] = '汇总趋势图'
    else if (index == '0' || column.type == 'seq' || index == 0) { sums[0] = '合计'; }


    else sums[index] = ''
   });
   if (this.summarycolumns.length == 0) {
    this.summarycolumns = columns;
    //this.initsummaryEvent();
   }
   return [sums]
  },
  initsummaryEvent() {
   let self = this;
   let table;
   this.$nextTick(() => {
    if (this.tablekey) table = document.querySelector('[name=' + this.tablekey + '] .vxe-table--footer-wrapper>table');
    else table = document.querySelectorAll('.vxe-table--footer-wrapper>table');
    if (table?.length > 0) table = table[0]
    this.$nextTick(() => {
     self.summarycolumns.forEach((column, index) => {
      if (column.property) {
       var col = findcol(self.tableCols, column.property);
       if (col && col.summaryEvent) {
        table.rows[0].cells[index].style.color = "red";
        table.rows[0].cells[index].style.cursor = "pointer";
       }
      }
     })
    })

   })

   function findcol(cols, property) {
    let column;
    for (var i = 0; i < cols.length; i++) {
     var c = cols[i];
     if (column) break
     else if (c.prop && c.prop.toLowerCase() == property.toLowerCase()) {
      column = c;
      break
     }
     else if (c.cols && c.cols.length > 0) column = findcol(c.cols, property)
    }
    return column
   }
  },
  footercellclick({ items, $rowIndex, column, columnIndex, $columnIndex, $event }) {
   let self = this;
   var col = findcol(self.tableCols, column.property);
   if (col && col.summaryEvent)
    self.$emit('summaryClick', column.property)

   function findcol(cols, property) {
    let column;
    for (var i = 0; i < cols.length; i++) {
     var c = cols[i];
     if (column) break
     else if (c.prop && c.prop.toLowerCase() == property.toLowerCase()) {
      column = c;
      break
     }
     else if (c.cols && c.cols.length > 0) column = findcol(c.cols, property)
    }
    return column
   }
  },
  checCheckboxkMethod2({ row }) {
   if (this.isDisableCheckBox) {
    let result;
    this.$emit('checCheckboxkMethod', { row }, val => { result = val })
    return result;
   } else {
    return true;
   }
  },
  /* 加载行图表 */
  loadRowEcharts() {
   let self = this;
   let echarts = self.echarts;
   self.echartsLoading = true;
   setTimeout(_ => {

    let idPrex = 'rptIdecharts' + self.id;
    let ids = [];
    for (let p in self.$refs) {
     if (p.startsWith(idPrex))
      ids.push(p);
    }

    ids.forEach(e => {
     var idNode = self.$refs[e];

     if (!idNode || idNode.length == 0 || !idNode[0].id || !(idNode[0]?.attributes?.chartdata?.value)) {
      return;
     }


     //检测是否已经存在echarts实例，如果不存在，则不再去初始化
     // var myEchart = echarts.init(document.getElementById('main'));
     let el = document.getElementById(idNode[0].id);

     let myChart = echarts.getInstanceByDom(el);
     if (!myChart) {
      myChart = echarts.init(el);
     }
     myChart.clear();

     let dataStr = idNode[0].attributes.chartdata.value;
     if (dataStr == undefined || dataStr == null || dataStr == "") {
      return;
     }


     let rowChartData = JSON.parse(dataStr);
     let series = [];
     if (rowChartData == undefined || rowChartData == null) {
      return;
     }

     rowChartData.series.forEach(s => {
      series.push({ type: 'line', smooth: true, showSymbol: false, ...s })
     });

     var xAxis = { ...rowChartData.xAxis };
     xAxis.type = "category";
     //xAxis.boundaryGap=false;
     xAxis.show = false;
     xAxis.boundaryGap = false;

     myChart.setOption({
      legend: {
       show: false,
      },
      grid: {
       left: "0",
       top: "1",
       right: "6",
       bottom: "1",
       containLabel: false,
      },
      xAxis: xAxis,
      yAxis: {
       type: 'value',
       show: false,
      },
      series: series
     });

     let width = el.clientWidth;
     let height = el.clientHeight;

     myChart.resize({ width, height });

    });

    self.echartsLoading = false
   }, 1000)
  },
  resizableChange({ $rowIndex, column, columnIndex, $columnIndex, $event }) {
   this.loadRowEcharts();
  },
  formatSelText(value, options) {
   return options.find((ele) => ele.value === value)?.label;
  },
  retname(val) {
   var newa = [];
   val.map((item) => {
    newa.push(item.toString());
   });
   return newa;
  },
  formatSelTextMore(value, options) {
   var txt = "";
   for (let num in value) {
    if (num == 0)
     txt += options.find((ele) => ele.value === value[num])?.label;
    else
     txt += "," + options.find((ele) => ele.value === value[num])?.label;
   }

   return txt;
  },
 }
}
export default tablejs;
