<template>

    <el-cascader :options="options" :props="props"  v-model="selectedOptions" @change="handleChange" :placeholder="placeholder" :clearable="clearable"  style="width: 100%">
    </el-cascader>

</template>

<script>
    import { getRegion } from '@/api/admin/business'

    export default {
        name: 'YhCityselector',        
        props: {
            orgProps:{
                type: Object,
                default() {
                    return {};
                }
            },
            Address: {
                type: Array,
                default() {
                    return ['', '', '', ''];
                }
            },
            MaxLvl: {
                type: Number,
                default() {
                    return 3;
                }
            },
            placeStr:{
                type: String,
                default() {
                    return "请选择省市";
                }
            },
            clearable:{
                type: Boolean,
                default() {
                    return false;
                }
            },
        },
        watch: {
            Address(val) {     
                let showlabel=this.placeStr;
                if(val && val.length>0){
                    for(var i=0;i<val.length;i++){
                        if(i==0){
                            if(val[0])
                                showlabel=val[0];
                        }else if(val[i]){
                            showlabel+= '/'+val[i];
                        }
                    }
                }
                this.placeholder=showlabel;
                this.selectedOptions = val
            }
        },
        data() {
            return {
                that: this,   
                placeholder:"",             
                getData: {},
                options: [],
                optData: {},
                props: {
                    lazy: true,
                    lazyLoad: (async (node, resolve) => {                        
                        let pcode = 0;
                        if (node.level > 0)
                            pcode = node.data.extObj.code;

                        let res = await getRegion({ parentcode: pcode });
                        if (res && res.success) {
                            let nodes = res.data.map(item => ({
                                value: item.name=="市辖区"?node.value:item.name,
                                label: item.name=="市辖区"?node.value:item.name,
                                leaf: node.level + 1 >= this.MaxLvl,
                                extObj: item,
                            }));

                            resolve(nodes);
                        }
                    }),
                    ...this.orgProps,

                },
                selectedOptions: this.Address
            }
        },
        methods: {
            handleChange(val) {
                let rlt = ["", "", "", ""];
                if (val && val.length > 0) {
                    rlt[0] = val[0];
                    if (val.length > 1)
                        rlt[1] = val[1];
                    if (val.length > 2)
                        rlt[2] = val[2];
                    if (val.length > 3)
                        rlt[3] = val[3];
                    if (val.length > 4)
                        rlt[4] = val[4];
                }

                this.$emit('change', rlt)
            }
        },
      
    }
</script>
