import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/dayreport/`
export const getProductADOneCharAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetProductADOneCharAsync', { params: params, ...config }) }

export const getFinacialCustomerPage = (params, config = {}) => { return request.get(apiPrefix + 'GetFinacialCustomerPageList', { params, ...config }) }
export const getFinacialDeductionPage = (params, config = {}) => { return request.get(apiPrefix + 'GetFinacialDeductionPageList', { params, ...config }) }
export const getDeductionTypeDic = (params, config = {}) => { return request.get(apiPrefix + 'GetDeductionTypeDic', { params, ...config }) }
export const pageProductDayReport = (params, config = {}) => { return request.post(apiPrefix + 'PageProductDayReport', params, config) }
export const queryDayReportAnalysis = (params, config = {}) => { return request.post(apiPrefix + 'QueryDayReportAnalysisAsync', params, config) }
export const queryDayReportGradeAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'QueryDayReportGradeAnalysisAsync', { params, ...config }) }
export const exportProductDayReport = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportProductDayReport', params, config) }
export const GetBusinessStaffPlatFormData = (params, config = {}) => { return request.get(apiPrefix + 'GetBusinessStaffPlatFormData', { params, ...config }) }
export const pageAgentSendCost = (params, config = {}) => { return request.get(apiPrefix + 'PageAgentSendCostAsync', { params, ...config }) }
export const getParm = (params, config = {}) => { return request.get(apiPrefix + 'GetParm', { params, ...config }) }
export const setParm = (params, config = {}) => { return request.post(apiPrefix + 'SetParm', params, config) }
export const deleteAgentSendCost = (params, config = {}) => { return request.delete(apiPrefix + 'DeleteAgentSendCostAsync?batchNumber=' + params) }
export const getFinancialStaticticsByUser = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialStaticticsByUser', { params, ...config }) }
export const getFinancialPddStaticticsByUser = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialPddStaticticsByUser', { params, ...config }) }
export const exportFinancialStaticticsByUser = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportFinancialStaticticsByUser', params, config) }
export const exportFinancialPddStaticticsByUser = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportFinancialPddStaticticsByUser', params, config) }
export const SetPerformanceTarget = (params, config = {}) => { return request.get(apiPrefix + 'SetPerformanceTarget', { params, ...config }) }
export const getPerformanceTarget = (params, config = {}) => { return request.get(apiPrefix + 'getPerformanceTarget', { params, ...config }) }
export const getPerformanceStaticticsByUser = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformanceStaticticsByUser', { params, ...config }) }
export const getPerformanceStaticticsByGroup = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformanceStaticticsByGroup', { params, ...config }) }
export const getPerformanceStaticticsByShop = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformanceStaticticsByShop', { params, ...config }) }
export const getDayReportStaticticsByIDUser = (params, config = {}) => { return request.get(apiPrefix + 'GetDayReportStaticticsByIDUser', { params, ...config }) }
export const getproductinfo = (params, config = {}) => { return request.get(apiPrefix + 'PrDirection', { params, ...config }) }
//生意参谋平台趋势图
export const getBusinessStaffPlatForm = (params, config = {}) => { return request.get(apiPrefix + 'BusinessStaffPlatForm', { params, ...config }) }
//系列编码生意参谋趋势图
export const getProCodeBusinessStaffPlatForm = (params, config = {}) => { return request.get(apiPrefix + 'ProCodeBusinessStaffPlatForm', { params, ...config }) }
//引力魔方趋势图
export const getYinLiMoFangChart = (params, config = {}) => { return request.get(apiPrefix + 'YinLiMoFangChartAsync', { params, ...config }) }
export const getFinancialTaoGongChangDayReport = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialTaoGongChangDayReport', { params, ...config }) }
export const getADdata = (params, config = {}) => { return request.get(apiPrefix + 'GetADdata', { params, ...config }) }
//首页日报数据展示
export const getDayReportSumSaleAmont = (params, config = {}) => { return request.get(apiPrefix + 'GetDayReportSumSaleAmontAsync', { params, ...config }) }

export const queryDayReportAnalysisHome = (params, config = {}) => { return request.get(apiPrefix + 'QueryDayReportAnalysisHomeAsync', { params, ...config }) }

export const getHomeGuestUnitPriceAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'GetHomeGuestUnitPriceAnalysisAsync', { params, ...config }) }
//项目组趋势图
export const getPerformanceStaticticsByProjMap = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformanceStaticticsByProjMap', { params, ...config }) }
//运营按组统计趋势图
export const getPerformanceStaticticsByGroupMap = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformanceStaticticsByGroupMap', { params, ...config }) }
//运营按组统计趋势图
export const getPerformanceStaticticsByonSuperviseMap = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformanceStaticticsByonSuperviseMap', { params, ...config }) }

//店铺统计趋势图
export const getPerformanceStaticticsByShopMap = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformanceStaticticsByShopMap', { params, ...config }) }

//运营专员/助理统计趋势图
export const getPerformanceStaticticsByUserMap = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformanceStaticticsByUserMap', { params, ...config }) }


//运营小组弹窗明细
export const getPerformanceStaticticsByGroupDialog = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformanceStaticticsByGroupDialog', { params, ...config }) }


//获取行政模块人效数据
export const getDayReportKpiAdminAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDayReportKpiAdminAsync', params, config)
}

//获取行政模块人效同步时间
export const GetLastCalcSumDayReportKpiAdminTxt = (params, config = {}) => { return request.get(apiPrefix + 'GetLastCalcSumDayReportKpiAdminTxt', { params, ...config }) }
//同步行政模块人效数据，方便批量查询
export const CalcSumDayReportKpiAdminAsync = (params, config = {}) => { return request.get(apiPrefix + 'CalcSumDayReportKpiAdminAsync', { params, ...config }) }


export const getCategoryPerformanceStatistics = (params, config = {}) => { return request.get(apiPrefix + 'GetCategoryPerformanceStatisticsAsync', { params, ...config }) }
export const getCategoryPSChart = (params, config = {}) => { return request.get(apiPrefix + 'GetCategoryPSChartAsync', { params, ...config }) }
//淘系售后退款
export const getSaleAfterTxList = (params, config = {}) => { return request.get(apiPrefix + 'GetSaleAfterTxList', { params, ...config }) }
//拼多多售后退款
export const getSaleAfterPddList = (params, config = {}) => { return request.get(apiPrefix + 'GetSaleAfterPddList', { params, ...config }) }
//拼多多售后退款(汇总)
export const getSaleAfterPddSumList = (params, config = {}) => { return request.get(apiPrefix + 'GetSaleAfterPddSumList', { params, ...config }) }
export const calDayRepoty = (params, config = {}) => { return request.get(apiPrefix + 'CalDayRepotyAsync', { params, ...config }) }
export const calDayRepotyDtl = (params, config = {}) => { return request.get(apiPrefix + 'CalDayRepotyDtlAsync', { params, ...config }) }

//日报分析
export const getDailyAnalysisList = (params, config = {}) => { return request.get(apiPrefix + 'GetDailyAnalysisList', { params, ...config }) }

//日报分析（获取系列编码库存数、系列编码库存资金）
export const getDailyAnalysisDetailList = (params, config = {}) => { return request.get(apiPrefix + 'GetDailyAnalysisDetailList', { params, ...config }) }

//编辑日报分析处理方案
export const editDailyAnalysis = (params, config) => { return request.post(apiPrefix + 'EditDailyAnalysisAsync', params, config) }


//拼多多售后退款导出
export const exportSaleAfterPddList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportSaleAfterPddListAsync', params, config) }


//淘系售后退款导出
export const exportSaleAfterTxList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportSaleAfterTxListAsync', params, config) }

//淘系售后退款(汇总)
export const getSaleAfterTxSumList = (params, config = {}) => { return request.get(apiPrefix + 'GetSaleAfterTxSumList', { params, ...config }) }

export const getAccountNumberDYList = (params, config = {}) => { return request.get(apiPrefix + 'GetAccountNumberDYList', { params, ...config }) }


//添加抖音账号
export const editAccountNumberDY = (params, config) => { return request.post(apiPrefix + 'EditAccountNumberDYAsync', params, config) }


export const getProductNewAccountNumberDYList = (params, config = {}) => { return request.get(apiPrefix + 'GetProductNewAccountNumberDYList', { params, ...config }) }
export const queryProductNewLikeNumberDYChart = (params, config = {}) => { return request.get(apiPrefix + 'QueryProductNewLikeNumberDYChartAnalysis', { params, ...config }) }

//更改监控状态
export const changeAccountNumberDYStatus = (params, config = {}) => { return request.get(apiPrefix + 'ChangeAccountNumberDYStatusAsync', { params: params, ...config }) }

//更改监控状态
export const changeProductNewAccountNumberDYStatus = (params, config = {}) => { return request.get(apiPrefix + 'ChangeProductNewAccountNumberDYStatusAsync', { params: params, ...config }) }

//淘系业绩导出
export const exportPerformanceStaticticsByUser = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPerformanceStaticticsByUser', params, config) }
export const exportPerformanceStaticticsByGroup = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPerformanceStaticticsByGroup', params, config) }
export const exportPerformanceStaticticsByShop = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPerformanceStaticticsByShop', params, config) }

//抖音业绩导出
export const exportPerformanceStaticticsByUserForDouYin = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportPerformanceStaticticsByUserForDouYin', { params: params, ...config }) }
export const exportPerformanceStaticticsByGroupForDouYin = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPerformanceStaticticsByGroupForDouYin', params, config) }
export const exportPerformanceStaticticsByShopForDouYin = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPerformanceStaticticsByShopForDouYin', params, config) }



//拼多多小额打款导出
export const exportPettyPaymentPDDList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPettyPaymentPDDList', params, config) }

//拼多多小额打款
export const getPettyPaymentPDDList = (params, config = {}) => { return request.get(apiPrefix + 'GetPettyPaymentPDDList', { params, ...config }) }

//分页查询日报确认数据
export const getPageDailyReportConfirmList = (params, config = {}) => { return request.get(apiPrefix + 'GetPageDailyReportConfirmList', { params, ...config }) }

//新增确认日报数据
export const insertDailyReportConfirmList = (params, config = {}) => { return request.post(apiPrefix + 'InsertDailyReportConfirmList', params, config) }

//新版拼多多日报账单费用查询
export const getNewPddBillingCharge = (params, config = {}) => { return request.get(apiPrefix + 'GetNewPddBillingCharge', { params, ...config }) }

//新版拼多多日报售后主题分析查询
export const getAfterSalesSubjectAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'GetAfterSalesSubjectAnalysis', { params, ...config }) }

//新版拼多多账单费用导出
export const exportNewPddBillingCharge = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportNewPddBillingChargeList', params, config) }



//拼多多日报-款式编码统计弹窗
export const DayReportStyleCodeAnalysisPdd = (params, config = {}) => { return request.post(apiPrefix + 'DayReportStyleCodeAnalysisPdd', params, config) }

//拼多多日报-拼多多商品ID弹窗
export const DayReportProCodeAnalysisPdd = (params, config = {}) => { return request.post(apiPrefix + 'DayReportProCodeAnalysisPdd', params, config) }

//小组销售查询
export const getDayReportGroupSaleAmontAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetDayReportGroupSaleAmontAsync', { params, ...config }) }
//首页退款
export const getHomeReturnAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'GetHomeReturnAnalysisAsync', { params: params, ...config }) }



//全平台订单明细维度日报计算
export const CalDayRepotyDtlAsync = (params, config = {}) => { return request.get(apiPrefix + 'CalDayRepotyDtlAsync', { params, ...config }) }

//公共品表格（当前）
export const getPublicGoodsCurrent = (params, config = {}) => { return request.get(apiPrefix + 'GetPublicGoodsCurrent', { params, ...config }) }

//公共品表格（历史）
export const getPublicGoodsHistory = (params, config = {}) => { return request.get(apiPrefix + 'GetPublicGoodsHistory', { params, ...config }) }

//公共品表格趋势图(当前/历史)
export const getPublicGoodsCurrentHistoryChart = (params, config = {}) => { return request.get(apiPrefix + 'GetPublicGoodsCurrentHistoryChart', { params, ...config }) }



//日报设置，id白名单列表
export const GetRptIgnoreProCodeList = (params, config = {}) => { return request.post(apiPrefix + 'GetRptIgnoreProCodeList', params, config) }

//日报设置，id白名单保存
export const AddRptIgnoreProCodeList = (params, config = {}) => { return request.post(apiPrefix + 'AddRptIgnoreProCodeList', params, config) }


// buschart 保存获取选中项
export const GetDayReportSumDialogColumnCacheAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetDayReportSumDialogColumnCacheAsync', { params: params, ...config }) }
export const SetDayReportSumDialogColumnCacheAsync = (params, config = {}) => { return request.post(apiPrefix + 'SetDayReportSumDialogColumnCacheAsync', params, config) }
//希音,拼多多全托,拼多多半托-计算日报
export const calDayRepoty_KJAsync = (params, config = {}) => { return request.get(apiPrefix + 'CalDayRepoty_KJAsync', { params, ...config }) }

//项目组日报-查询
export const pageProjProductDayReport = (params, config = {}) => { return request.post(apiPrefix + 'PageProjProductDayReport', params, config) }

//项目组日报-导出
export const exportProjProductDayReport = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportProjProductDayReport', params, config) }

//项目组日报-趋势图
//export const queryProjDayReportAnalysisAsync = (params, config = {}) => { return request.get(apiPrefix + 'QueryProjDayReportAnalysisAsync', { params, ...config }) }
export const queryProjDayReportAnalysisAsync = (params, config = {}) => { return request.post(apiPrefix + 'QueryProjDayReportAnalysisAsync', params, config) }

//抖音毛六业绩目标设置
export const setTargetDyProfit6 = (params, config = {}) => { return request.post(apiPrefix + 'SetTargetDyProfit6', params, config) }

//毛六亏损下架白名单
export const getProfitLossRemovalWhiteList = (params, config = {}) => { return request.post(apiPrefix + 'GetProfitLossRemovalWhiteList', params, config) }

export const addOrEditWhiteList = (params, config = {}) => { return request.post(apiPrefix + 'AddOrEditWhiteList', params, config) }

export const deleteWhiteList = (params, config = {}) => { return request.post(apiPrefix + 'DeleteWhiteList', params, config) }

//毛六亏损下架白名单-到处
export const exportProfitLossRemovalWhiteList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + `ExportProfitLossRemovalWhiteList`, params, config) }

// 项目组页签配置的项目
export const getDailyReportProjects = (params, config = {}) => { return request.get(apiPrefix + 'GetDailyReportProjects', { params, ...config }) }

export const addDailyReportProject = (params, config = {}) => { return request.post(apiPrefix + 'AddDailyReportProject', params, config) }

export const removeDailyReportProject = (params, config = {}) => { return request.post(apiPrefix + 'RemoveDailyReportProject', params, config) }