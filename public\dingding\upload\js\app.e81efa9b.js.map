{"version": 3, "file": "js/app.e81efa9b.js", "mappings": "mEAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAACF,EAAG,MAAM,CAACG,YAAY,cAAc,CAACH,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,GAAG,OAAS,GAAG,YAAY,OAAO,SAAW,GAAG,eAAc,EAAM,kBAAiB,EAAM,YAAYJ,EAAIM,aAAa,CAACJ,EAAG,IAAI,CAACG,YAAY,mBAAmBH,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAACL,EAAIO,GAAG,aAAaL,EAAG,KAAK,CAACF,EAAIO,GAAG,eAAe,GAAGL,EAAG,MAAM,CAACG,YAAY,eAAe,CAACH,EAAG,OAAO,CAACF,EAAIO,GAAG,QAAQP,EAAIQ,IAAI,EAAER,EAAIS,OAAO,IAAIT,EAAIU,SAASC,QAAQ,IAAI,SAAUX,EAAIY,MAAOV,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,QAAQ,CAACE,MAAM,CAAC,IAAMJ,EAAIY,MAAM,SAAW,QAAQZ,EAAIa,MAC1oB,EACIC,EAAkB,G,QCFf,SAASC,EAAUC,EAAMC,EAAO,UACnC,OAAO,IAAIC,SAAQC,IACf,IAAIC,EAAW,IAAIC,WACN,WAATJ,EACAG,EAASE,cAAcN,GACP,WAATC,GACPG,EAASG,kBAAkBP,GAE/BI,EAASI,OAAUC,IACfN,EAAQM,EAAGC,OAAOC,OAAO,CAC5B,GAET,C,0CCoCA,GACAC,KAAAA,MACAC,OACA,OACApB,MAAAA,EACAG,MAAAA,KACAkB,KAAAA,EACAC,QAAAA,EACAC,YAAAA,GACAC,iBAAAA,GACAC,WAAAA,EACAC,SAAAA,GACAC,UAAAA,GACA1B,OAAAA,EAEA,EAEA2B,UACA,0BACA,8BACAC,SAAAA,QAAAA,wDAEA,EAGAC,QAAAA,CACAC,QAAAA,GACA,kBACA,EACAC,UAAAA,GACA,kBACA,GAEAC,UAEAC,GAAAA,UAAAA,SAAAA,GACAC,QAAAA,IAAAA,GACAC,YAAAA,KACAF,GAAAA,YAAAA,CAAAG,OAAAA,GAAA,GACA,IACA,CACA,EACAC,QAAAA,CACA,oBACA,WACA,aACA/B,EAAAA,EAAAA,IACA4B,QAAAA,IAAAA,WAAAA,GACAI,EAAAA,QAAAA,EAAAA,QAAAA,EAEA,IAEAC,EACAC,EAHA,sBACAC,EAAAA,IAAAA,IAAAA,aAGAA,EAAAA,OAAAA,GACAF,EAAAA,EAAAA,MACAC,EAAAA,qBAAAA,KAAAA,EAAAA,MAAAA,GAeA,gBACA,oCACA,SACA,IACAF,EAAAA,OAAAA,EAAAA,OAAAA,OAAAA,EAAAA,YACAJ,QAAAA,IAAAA,QAAAA,EAAAA,QACA,mCACA,OACAQ,MAAAA,EAAAA,MAAAA,EAAAA,EAAAA,GACAC,SAAAA,GAAAA,KAAAA,KAAAA,KAEAC,GAAAA,EACAC,EAAAA,KAAAA,EACA,CACAX,QAAAA,IAAAA,OAAAA,GACA,gBACA,YACA,kBAKA,EACA,oBAEA,SACA,OACA,+BAEA,WAEA,mBAMA,OALAY,EAAAA,OAAAA,OAAAA,EAAAA,OACAA,EAAAA,OAAAA,cAAAA,EAAAA,YAAAA,EAAAA,YAAAA,IACAA,EAAAA,OAAAA,WAAAA,EAAAA,UACAA,EAAAA,OAAAA,QAAAA,EAAAA,GACAA,EAAAA,OAAAA,QAAAA,EAAAA,YACA,SACAC,gDAAAA,EAAAA,CACAC,QAAAA,CAAA,wCAEAC,MAAAA,IAYA,GAXAhC,EAAAA,EAAAA,KACAiB,QAAAA,IAAAA,SAAAA,GACA,SACAI,EAAAA,YAAAA,EAAAA,MAEA,YACAA,EAAAA,OAAAA,EACAA,EAAAA,SAAAA,OAAAA,EAAAA,IAIA,qBAEA,6BAEAA,EAAAA,UAAAA,KAAAA,GACA,4BAIAJ,QAAAA,IAAAA,4BAAAA,EAAAA,UAAAA,QACAA,QAAAA,IAAAA,mBAAAA,EAAAA,SACA,gCACAA,QAAAA,IAAAA,SAAAA,GACAD,GAAAA,YAAAA,CAAAG,OAAAA,IAiCA,IACA,EAEAc,EAAAA,KAAAA,EAAAA,IAEAhB,QAAAA,IAAAA,OAAAA,EAAAA,UAMA,QACA,YACA,8BACAiB,OAAAA,CACAZ,KAAAA,KAAAA,QAGAtB,EAAAA,EAAAA,KACA,YACA,kBACA,EAEA,YAEA,aACA,YAEAmC,WAGA,OACAC,IACAC,KAAAA,EAEAA,GACA,EAOAC,YACA,YAKA,OAHA,mBACA,aAKA,YACA,aACA,IChR+R,I,UCQ3RC,GAAY,OACd,EACAnE,EACAe,GACA,EACA,KACA,WACA,MAIF,EAAeoD,EAAiB,Q,mBCXhCC,EAAAA,WAAAA,IAAQC,KAERD,EAAAA,WAAAA,OAAAA,eAA2B,EAC3B,IAAIA,EAAAA,WAAI,CACNpE,OAAQsE,GAAKA,EAAEC,KACdC,OAAO,O,GCZNC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaE,QAGrB,IAAIC,EAASN,EAAyBE,GAAY,CAGjDG,QAAS,CAAC,GAOX,OAHAE,EAAoBL,GAAUI,EAAQA,EAAOD,QAASJ,GAG/CK,EAAOD,OACf,CAGAJ,EAAoBO,EAAID,E,WCzBxB,IAAIE,EAAW,GACfR,EAAoBS,EAAI,SAASvD,EAAQwD,EAAUC,EAAIC,GACtD,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASxB,EAAI,EAAGA,EAAIkB,EAASO,OAAQzB,IAAK,CACrCoB,EAAWF,EAASlB,GAAG,GACvBqB,EAAKH,EAASlB,GAAG,GACjBsB,EAAWJ,EAASlB,GAAG,GAE3B,IAJA,IAGI0B,GAAY,EACPC,EAAI,EAAGA,EAAIP,EAASK,OAAQE,MACpB,EAAXL,GAAsBC,GAAgBD,IAAaM,OAAOC,KAAKnB,EAAoBS,GAAGW,OAAM,SAASC,GAAO,OAAOrB,EAAoBS,EAAEY,GAAKX,EAASO,GAAK,IAChKP,EAASY,OAAOL,IAAK,IAErBD,GAAY,EACTJ,EAAWC,IAAcA,EAAeD,IAG7C,GAAGI,EAAW,CACbR,EAASc,OAAOhC,IAAK,GACrB,IAAIiC,EAAIZ,SACER,IAANoB,IAAiBrE,EAASqE,EAC/B,CACD,CACA,OAAOrE,CArBP,CAJC0D,EAAWA,GAAY,EACvB,IAAI,IAAItB,EAAIkB,EAASO,OAAQzB,EAAI,GAAKkB,EAASlB,EAAI,GAAG,GAAKsB,EAAUtB,IAAKkB,EAASlB,GAAKkB,EAASlB,EAAI,GACrGkB,EAASlB,GAAK,CAACoB,EAAUC,EAAIC,EAwB/B,C,eC5BAZ,EAAoBwB,EAAI,SAASnB,GAChC,IAAIoB,EAASpB,GAAUA,EAAOqB,WAC7B,WAAa,OAAOrB,EAAO,UAAY,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAL,EAAoB2B,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CACR,C,eCNAzB,EAAoB2B,EAAI,SAASvB,EAASyB,GACzC,IAAI,IAAIR,KAAOQ,EACX7B,EAAoB8B,EAAED,EAAYR,KAASrB,EAAoB8B,EAAE1B,EAASiB,IAC5EH,OAAOa,eAAe3B,EAASiB,EAAK,CAAEW,YAAY,EAAMC,IAAKJ,EAAWR,IAG3E,C,eCPArB,EAAoBkC,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAO3G,MAAQ,IAAI4G,SAAS,cAAb,EAGhB,CAFE,MAAOC,GACR,GAAsB,kBAAXC,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,eCAxBtC,EAAoB8B,EAAI,SAASS,EAAKC,GAAQ,OAAOtB,OAAOuB,UAAUC,eAAeC,KAAKJ,EAAKC,EAAO,C,eCCtGxC,EAAoBuB,EAAI,SAASnB,GACX,qBAAXwC,QAA0BA,OAAOC,aAC1C3B,OAAOa,eAAe3B,EAASwC,OAAOC,YAAa,CAAEC,MAAO,WAE7D5B,OAAOa,eAAe3B,EAAS,aAAc,CAAE0C,OAAO,GACvD,C,eCDA,IAAIC,EAAkB,CACrB,IAAK,GAaN/C,EAAoBS,EAAEQ,EAAI,SAAS+B,GAAW,OAAoC,IAA7BD,EAAgBC,EAAgB,EAGrF,IAAIC,EAAuB,SAASC,EAA4B9F,GAC/D,IAKI6C,EAAU+C,EALVtC,EAAWtD,EAAK,GAChB+F,EAAc/F,EAAK,GACnBgG,EAAUhG,EAAK,GAGIkC,EAAI,EAC3B,GAAGoB,EAAS2C,MAAK,SAASC,GAAM,OAA+B,IAAxBP,EAAgBO,EAAW,IAAI,CACrE,IAAIrD,KAAYkD,EACZnD,EAAoB8B,EAAEqB,EAAalD,KACrCD,EAAoBO,EAAEN,GAAYkD,EAAYlD,IAGhD,GAAGmD,EAAS,IAAIlG,EAASkG,EAAQpD,EAClC,CAEA,IADGkD,GAA4BA,EAA2B9F,GACrDkC,EAAIoB,EAASK,OAAQzB,IACzB0D,EAAUtC,EAASpB,GAChBU,EAAoB8B,EAAEiB,EAAiBC,IAAYD,EAAgBC,IACrED,EAAgBC,GAAS,KAE1BD,EAAgBC,GAAW,EAE5B,OAAOhD,EAAoBS,EAAEvD,EAC9B,EAEIqG,EAAqBC,KAAK,yBAA2BA,KAAK,0BAA4B,GAC1FD,EAAmBE,QAAQR,EAAqBS,KAAK,KAAM,IAC3DH,EAAmBI,KAAOV,EAAqBS,KAAK,KAAMH,EAAmBI,KAAKD,KAAKH,G,IC/CvF,IAAIK,EAAsB5D,EAAoBS,OAAEN,EAAW,CAAC,MAAM,WAAa,OAAOH,EAAoB,KAAO,IACjH4D,EAAsB5D,EAAoBS,EAAEmD,E", "sources": ["webpack://20200726/./src/App3.vue", "webpack://20200726/./src/assets/utils.js", "webpack://20200726/src/App3.vue", "webpack://20200726/./src/App3.vue?58c9", "webpack://20200726/./src/App3.vue?5b6d", "webpack://20200726/./src/main.js", "webpack://20200726/webpack/bootstrap", "webpack://20200726/webpack/runtime/chunk loaded", "webpack://20200726/webpack/runtime/compat get default export", "webpack://20200726/webpack/runtime/define property getters", "webpack://20200726/webpack/runtime/global", "webpack://20200726/webpack/runtime/hasOwnProperty shorthand", "webpack://20200726/webpack/runtime/make namespace object", "webpack://20200726/webpack/runtime/jsonp chunk loading", "webpack://20200726/webpack/startup"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{attrs:{\"id\":\"app\"}},[_c('div',{staticClass:\"bodyupload\"},[_c('el-upload',{attrs:{\"drag\":\"\",\"action\":\"\",\"list-type\":\"text\",\"multiple\":\"\",\"auto-upload\":false,\"show-file-list\":false,\"on-change\":_vm.changeFile}},[_c('i',{staticClass:\"el-icon-upload\"}),_c('div',{staticClass:\"el-upload__text\"},[_vm._v(\" 点击按钮上传， \"),_c('em',[_vm._v(\"点击上传\")])])])],1),_c('div',{staticClass:\"bodyuploadd\"},[_c('span',[_vm._v(\"上传进度：\"+_vm._s((0|_vm.total*(100/_vm.newnum)).toFixed(0))+\"%\")])]),(_vm.video)?_c('div',{staticClass:\"uploadImg\"},[_c('video',{attrs:{\"src\":_vm.video,\"controls\":\"\"}})]):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "export function fileParse(file, type = \"base64\") {\n    return new Promise(resolve => {\n        let fileRead = new FileReader();\n        if (type === \"base64\") {\n            fileRead.readAsDataURL(file);\n        } else if (type === \"buffer\") {\n            fileRead.readAsArrayBuffer(file);\n        }\n        fileRead.onload = (ev) => {\n            resolve(ev.target.result);\n        };\n    });\n};", "<template>\n  <div id=\"app\">\n\t\t<!-- <el-upload drag action list-type=\"text\" :auto-upload=\"false\" :show-file-list=\"false\" :on-change=\"changeFile\">\r\n\t\t  <i class=\"el-icon-upload\"></i>\r\n\t\t  <div class=\"el-upload__text\">\r\n\t\t    点击按钮上传，\r\n\t\t    <em>点击上传</em>\r\n\t\t  </div>\r\n\t\t</el-upload> -->\r\n\t<!-- <el-upload\r\n\t  action\r\n\t  list-type=\"picture-card\"\r\n\t  :on-preview=\"handlePictureCardPreview\"\r\n\t  :on-remove=\"handleRemove\">\r\n\t  <i class=\"el-icon-plus\"></i>\r\n\t</el-upload> -->\r\n\t<div class=\"bodyupload\">\r\n\t\t<el-upload drag action list-type=\"text\" multiple :auto-upload=\"false\" :show-file-list=\"false\" :on-change=\"changeFile\">\r\n\t\t  <i class=\"el-icon-upload\"></i>\r\n\t\t  <div class=\"el-upload__text\">\r\n\t\t    点击按钮上传，\r\n\t\t    <em>点击上传</em>\r\n\t\t  </div>\r\n\t\t</el-upload>\r\n\t</div>\r\n\t\n    <!-- PROGRESS -->\n    <div class=\"bodyuploadd\">\n      <!-- <span>上传进度：{{(0|total*(100/chunkCount)).toFixed(0)}}%</span> -->\r\n\t  <span>上传进度：{{(0|total*(100/newnum)).toFixed(0)}}%</span>\n    </div>\n\n    <!-- VIDEO  -->\n    <div class=\"uploadImg\" v-if=\"video\"><video :src=\"video\" controls /></div>\n  </div>\n</template>\n\r\n<script type=\"text/javascript\" src=\"https://unpkg.com/@dcloudio/uni-webview-js@0.0.3/index.js\"></script>\r\n<!-- <script src=\"https://g.alicdn.com/dingding/dingtalk-jsapi/2.13.42/dingtalk.open.js\"></script> -->\r\n<!-- <script type=\"text/javascript\" src=\"https://g.alicdn.com/dingding/dingtalk-jsapi/2.10.3/dingtalk.open.js\"></script> -->\r\n<script type=\"text/javascript\" src=\"https://appx/web-view.min.js\"></script>\r\n<script>\r\n// import * as dd from './assets/dingtalkopen.js';\r\n// import * as dd from 'dingtalk-jsapi';\nimport { fileParse } from \"./assets/utils\";\nimport axios from \"axios\";\nimport SparkMD5 from \"spark-md5\";\n\nexport default {\n  name: \"App\",\n  data() {\n    return {\n      total: 0,\n      video: null,\n      btn: false,\r\n\t  filenum: 0,\r\n\t  batchnumber: '',\r\n\t  atfterUplaodData: '',\r\n\t  chunkCount: 0,\r\n\t  jsondata: '',\r\n\t  arraylist: [],\r\n\t  newnum: 0\n    };\n  },\r\n  //////////\r\n created() {\r\n     var userAgent = navigator.userAgent\r\n     if (userAgent.indexOf('AlipayClient') > -1) {\r\n       document.writeln('<script src=\"https://appx/web-view.min.js\"' + '>' + '<' + '/' + 'script>')\r\n     }\r\n   },\r\n  ////////////\r\n  \n  filters: {\n    btnText(btn) {\n      return btn ? \"继续\" : \"暂停\";\n    },\n    totalText(total) {\n      return total > 100 ? 100 : total;\n    },\n  },\r\n  mounted() {\r\n\r\n    dd.onMessage = function(e) {\r\n      console.log(e);\r\n\t  setTimeout(()=>{\r\n\t\t  dd.postMessage({upfile:e});\r\n\t  },1000)\r\n    }\r\n  },\n  methods: {\n    async changeFile(file) {\r\n\t\tvar _this = this;\n      if (!file) return;\r\n      file = file.raw;\r\n\t\tconsole.log(\"打印文件file\",file);\r\n\t\t_this.filenum = _this.filenum+1\n      // ...\n      let buffer = await fileParse(file, \"buffer\"),\n        spark = new SparkMD5.ArrayBuffer(),\n        hash,\n        suffix;\n      spark.append(buffer);\n      hash = spark.end();\n      suffix = /\\.([0-9a-zA-Z]+)$/i.exec(file.name)[1];\n\n//方法一\n      // let partList = [],\n      //   partsize = file.size / 100,\n      //   cur = 0;\n      // for (let i = 0; i < 300; i++) {\n      //   let item = {\n      //     chunk: file.slice(cur, cur + partsize),\n      //     filename: `${hash}_${i}.${suffix}`,\n      //   };\n      //   cur += partsize;\n      //   partList.push(item);\n      // }\r\n// 方法二\r\n\t\tconst chunkSize = 2 * 1024 * 1024 // 1MB一片\r\n\t\tthis.chunkCount = Math.ceil(file.size / chunkSize) // 总片数\r\n\t\tlet partList = []\r\n\t\tlet cur = 0;\r\n\t\t_this.newnum = _this.newnum + Number(_this.chunkCount);\r\n\t\tconsole.log(\"打印总端数\",_this.newnum);\r\n\t\tfor (var i = 0; i < this.chunkCount; i++) {\r\n\t\t  let item = {\r\n\t\t      chunk: file.slice(cur, cur + chunkSize),\r\n\t\t      filename: `${hash}_${i}.${suffix}`,\r\n\t\t    };\r\n\t\t    cur += chunkSize;\r\n\t\t    partList.push(item);\r\n\t\t}\n\t\tconsole.log(\"全部切片\",partList);\n      this.partList = partList;\n      this.hash = hash;\r\n\t  this.sendRequest()\n   //   await  this.sendRequest().then(function(){\n   //   // that.finalymsg()\n   // });\r\n\t  \n    },\n    async sendRequest() {\r\n      // 集合\n      let requestList = [];\r\n\t  let _this = this;\n      this.partList.forEach((item, index) => {\r\n        //请求 \r\n        let fn = () => {\r\n\t\t\t// let _this = this;\n          let formData = new FormData();\n          formData.append(\"data\", item.chunk);\r\n\t\t  formData.append(\"batchnumber\", _this.batchnumber?_this.batchnumber:'');\n          formData.append(\"filename\", item.filename);\r\n\t\t  formData.append(\"index\", index+1);\r\n\t\t  formData.append(\"total\", _this.chunkCount);\n          return axios\n            .post(\"/api/uploadddh5/file/XMTVideoUploadBlockAsync\", formData, {\n              headers: { \"Content-Type\": \"multipart/form-data\" },\n            })\n            .then((result) => {\n              result = result.data;\r\n\t\t\t  console.log(\"data数值\",result);\r\n\t\t\t  if(result.data){\r\n\t\t\t\t  _this.batchnumber = result.data;\r\n\t\t\t  }\n              if (result.code == 1) {\n                _this.total += 1;\n                _this.partList.splice(index, 1);\n              }\r\n\t\t\t  \r\n\t\t\t  \r\n\t\t\t  if(result.data.relativePath){\r\n\t\t\t\t  // _this.oneup = _this.oneup+1;\r\n\t\t\t\t  let atfterUplaodData =  JSON.stringify(result.data);\r\n\t\t\t\t  // _this.jsondata = atfterUplaodData;\r\n\t\t\t\t  _this.arraylist.push(atfterUplaodData);\r\n\t\t\t\t  let finallydata = _this.arraylist.join(\"-\");\r\n\t\t\t\t  // this.filenum = this.filenum++\r\n\t\t\t\t  // let ceshi = finallydata.split(\"-\");\r\n\t\t\t\t  \r\n\t\t\t\t  console.log(\"_this.arraylist.length888\",_this.arraylist.length);\r\n\t\t\t\t  console.log(\"_this.filenum999\",_this.filenum);\r\n\t\t\t\t  if(_this.arraylist.length==_this.filenum){\r\n\t\t\t\t\t  console.log(\"最后一次循环\",finallydata);\r\n\t\t\t\t\t  dd.postMessage({upfile:finallydata});\r\n\t\t\t\t  }\r\n\t\t\t\t  \r\n\t\t\t\t  ///保存数据\r\n\t\t\t\t  // let fn = () => {\r\n\t\t\t\t  //   let formData = new FormData();\r\n\t\t\t\t  //   formData.append(\"upfile\", item.chunk);\r\n\t\t\t\t  //   formData.append(\"videotaskid\", this.batchnumber?this.batchnumber:'');\r\n\t\t\t\t  //   formData.append(\"cuteid\", item.filename);\r\n\t\t\t\t  //   return axios\r\n\t\t\t\t  //     .post(\"/api/uploadnew/file/XMTVideoUploadBlockAsync\", formData, {\r\n\t\t\t\t  //       headers: { \"Content-Type\": \"multipart/form-data\" },\r\n\t\t\t\t  //     })\r\n\t\t\t\t  //     .then((result) => {\r\n\t\t\t\t  //       result = result.data;\r\n\t\t\t\t  // \t  console.log(\"data数值\",result);\r\n\t\t\t\t  // \t  if(result.data){\r\n\t\t\t\t  // \t\t  this.batchnumber = result.data;\r\n\t\t\t\t  // \t  }\r\n\t\t\t\t  //       if (result.code == 1) {\r\n\t\t\t\t  //         this.total += 1;\r\n\t\t\t\t  //         this.partList.splice(index, 1);\r\n\t\t\t\t  //       }\r\n\t\t\t\t  // \t  if(this.total==100){\r\n\t\t\t\t  // \t\t  let atfterUplaodData =  JSON.stringify(result.data);\r\n\t\t\t\t  \t\t  \r\n\t\t\t\t  // \t\t  //保存数据\r\n\t\t\t\t  \t\t  \r\n\t\t\t\t  // \t\t  dd.postMessage({upfile:atfterUplaodData});\r\n\t\t\t\t  // \t  }\r\n\t\t\t\t  //     });\r\n\t\t\t\t  // };\r\n\t\t\t\t  \r\n\t\t\t  }\n            });\n        };\n        requestList.push(fn);\n      });\r\n\t  console.log(\"结束打印\",_this.jsondata)\r\n\t  // dd.postMessage({upfile:this.jsondata});\n\t\t// return;\r\n\t\t\r\n\t\t\n      // 传递\n      let i = 0;\n      let complete = async () => {\n        let result = await axios.get(\"/merge\", {\n          params: {\n            hash: this.hash,\n          },\n        });\n        result = result.data;\n        if (result.code == 0) {\n          this.video = result.path;\n        }\n      };\n      let send = async () => {\n        // 不再上传\n        if (this.abort) return;\n        if (i >= requestList.length) {\n          // 都传完了\n          complete();\n          return;\n        }\n        await requestList[i]();\n        i++;\n        send();\n      };\n      send();\n    },\r\n\t// finalymsg(){\r\n\t// \tconsole.log(\"发送消息\")\r\n\t// \tif(this.oneup==2&&index==_this.chunkCount-1){\r\n\t// \t\tdd.postMessage({upfile:this.jsondata});\r\n\t// \t}\r\n\t// },\n    handleBtn() {\n      if (this.btn) {\n        //断点\n        this.abort = false;\n        this.btn = false;\n        // this.sendRequest();\n        return;\n      }\n      //暂停\n      this.btn = true;\n      this.abort = true;\n    },\n  },\n};\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.bodyupload{\r\n\t\tmargin: 0 80rpx;\r\n\t\theight: 150px;\r\n\t\t// background-color: aqua;\r\n\t\tborder: 1rpx solid #d5d5d5;\r\n\t\tborder-radius: 12px;\r\n\t\t\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\t.bodyuploadd{\r\n\t\tmargin: 20 80rpx;\r\n\t\theight: 50px;\r\n\t\t// background-color: aqua;\r\n\t\tborder: 1rpx solid #d5d5d5;\r\n\t\tborder-radius: 12px;\r\n\t\t\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n</style>", "import mod from \"-!../node_modules/_thread-loader@3.0.4@thread-loader/dist/cjs.js!../node_modules/_babel-loader@8.2.5@babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../node_modules/_vue-loader@15.10.0@vue-loader/lib/index.js??vue-loader-options!./App3.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/_thread-loader@3.0.4@thread-loader/dist/cjs.js!../node_modules/_babel-loader@8.2.5@babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../node_modules/_vue-loader@15.10.0@vue-loader/lib/index.js??vue-loader-options!./App3.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./App3.vue?vue&type=template&id=27726b32&scoped=true&\"\nimport script from \"./App3.vue?vue&type=script&lang=js&\"\nexport * from \"./App3.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App3.vue?vue&type=style&index=0&id=27726b32&prod&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/_vue-loader@15.10.0@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"27726b32\",\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue';\nimport App from './App3.vue';\n\n/* 导入公共的样式 && ELEMENT */\nimport './assets/reset.min.css';\nimport './assets/common.less';\nimport ElementUI from 'element-ui';\nimport 'element-ui/lib/theme-chalk/index.css';\nVue.use(ElementUI);\n\nVue.config.productionTip = false;\nnew Vue({\n  render: h => h(App),\n}).$mount('#app');", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t143: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunk_20200726\"] = self[\"webpackChunk_20200726\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [998], function() { return __webpack_require__(2571); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "staticClass", "changeFile", "_v", "_s", "total", "newnum", "toFixed", "video", "_e", "staticRenderFns", "fileParse", "file", "type", "Promise", "resolve", "fileRead", "FileReader", "readAsDataURL", "readAsA<PERSON>y<PERSON><PERSON>er", "onload", "ev", "target", "result", "name", "data", "btn", "filenum", "batchnumber", "atfterUplaodData", "chunkCount", "jsondata", "arraylist", "created", "document", "filters", "btnText", "totalText", "mounted", "dd", "console", "setTimeout", "upfile", "methods", "_this", "hash", "suffix", "spark", "chunk", "filename", "cur", "partList", "formData", "post", "headers", "then", "requestList", "params", "complete", "i", "send", "handleBtn", "component", "<PERSON><PERSON>", "ElementUI", "h", "App", "$mount", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "exports", "module", "__webpack_modules__", "m", "deferred", "O", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "length", "fulfilled", "j", "Object", "keys", "every", "key", "splice", "r", "n", "getter", "__esModule", "d", "a", "definition", "o", "defineProperty", "enumerable", "get", "g", "globalThis", "Function", "e", "window", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "installedChunks", "chunkId", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "some", "id", "chunkLoadingGlobal", "self", "for<PERSON>ach", "bind", "push", "__webpack_exports__"], "sourceRoot": ""}