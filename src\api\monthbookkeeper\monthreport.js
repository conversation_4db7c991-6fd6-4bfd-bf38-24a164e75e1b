import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_MonthBookKeeper}/MonthReport/`

export const pageAmountDetail = (params, config = {}) => {return request.get(apiPrefix + 'PageAmountDetail', { params: params, ...config })}
export const exportAmountDetail = (params, config = {responseType: 'blob'}) => {return request.get(apiPrefix + `ExportAmountDetail`,  {params, ...config}) }  

