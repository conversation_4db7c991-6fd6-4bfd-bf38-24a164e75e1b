import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/DistributionGoodsReport/`


export const getDistributionGoodsOnlineReportAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetDistributionGoodsOnlineReportAsync', params, config)}

export const distributionGoodsReportSummaryAnalysis= (params, config = {}) => { return request.post(apiPrefix + 'DistributionGoodsReportSummaryAnalysis', params, config)} 

export const distributionGoodsSummaryOneTypeReportAnalysis= (params, config = {}) => { return request.post(apiPrefix + 'DistributionGoodsSummaryOneTypeReportAnalysis', params, config)} 