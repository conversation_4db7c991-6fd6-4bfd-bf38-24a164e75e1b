<!DOCTYPE html>
<html lang="en">
<!-- 产品管理-一键通知日志查看 -->

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <!-- elment样式 -->
    <link rel="stylesheet" href="/approvalform/html/api/elment.css">
    <!-- vue -->
    <script src="/approvalform/html/api/vue.min.js"></script>
    <!-- elment脚本 -->
    <script src="/approvalform/html/api/elment.js"></script>
    <!-- jquery -->
    <script src="/approvalform/html/api/jquery.min.js"></script>
    <title></title>
</head>

<body>
    <div id="app" style="margin:0 auto;width: 1000px;">
        <el-container v-loading="thisLonding" direction="vertical"
            style="padding: 15px 10px 0px 10px ; border: 1px #ccc solid;">
            <template>
                <el-form :model="oneNoticeFormData" label-width="80px" ref="oneNoticeForm" label-position="right">
                    <el-row>
                        <el-form-item label="系列编码" prop="styleCode">
                            <el-input v-model="oneNoticeFormData.styleCode" auto-complete="off" disabled />
                        </el-form-item>
                    </el-row>
                    <el-row>
                        <el-form-item label="宝贝ID" prop="proCode">
                            <el-input v-model="oneNoticeFormData.proCode" auto-complete="off" disabled>
                                <template slot="append" v-if="oneNoticeFormData.proCodeCount">共{{oneNoticeFormData.proCodeCount}}条
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-row>
                    <el-row>
                        <el-form-item label="通知时间" prop="noticeTime">
                            <el-input v-model="oneNoticeFormData.noticeTime" auto-complete="off" disabled />
                        </el-form-item>
                    </el-row>
                    <el-row>
                        <el-form-item label="标题" prop="noticeTitle">
                            <el-input v-model="oneNoticeFormData.noticeTitle" auto-complete="off" disabled />
                        </el-form-item>
                    </el-row>
                    <el-row>
                        <el-form-item label="内容" prop="noticeTitle">
                            <el-input v-model="oneNoticeFormData.noticeContext" auto-complete="off" type="textarea"
                                :rows="4" disabled />
                        </el-form-item>
                    </el-row>

                    <el-row>
                        <el-form-item label="图片" prop="noticeImgs">
                            <div v-for="item in imgList" :key="item.name" style="float: left; margin-right: 5px;">
                                <el-image style="width: 60px; height: 60px" lazy fit="scale-down" :src="item.url"
                                    :preview-src-list="[item.url]" />
                            </div>
                        </el-form-item>
                    </el-row>
                </el-form>
            </template>
        </el-container>
    </div>
    <script>
        var vm = new Vue({
            el: '#app',
            data: function () {
                return {
                    oneNoticeFormData: {},
                    thisLonding: false,
                    imgList: [],
                }
            },
            created() {
            },
            async mounted() {
                this.getStyleSheetInfo();
            },
            methods: {
                async getStyleSheetInfo() {
                    var me = this;
                    me.imgList = [];
                    let searchURL = window.location.search;
                    searchURL = searchURL.substring(1, searchURL.length);
                    let targetPageId = searchURL.split("&")[0].split("=")[1];
                    me.thisLonding = true;
                    $.ajax({
                        type: 'GET',
                        async: false,
                        url: `/api/operatemanage/productmanager/GetProductNoticeLogById`,
                        data: {
                            id: targetPageId
                        },
                        success: function (result) {
                            console.log(result);
                            me.thisLonding = false;
                            if (result?.success) {
                                me.oneNoticeFormData = result.data.list[0];
                                if (me.oneNoticeFormData.noticeImgs) {
                                    let images = JSON.parse(me.oneNoticeFormData.noticeImgs);
                                    if (images.length > 0) {
                                        me.imgList = images;
                                    }
                                }
                            }
                        }
                    })
                },
            }
        });
    </script>
</body>

</html>