import request from "@/utils/request";
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/purchaseReturnExWarehouse/`;

//分页获取采购退货汇总 GetPurchaseReturnExWarehouseSummaryPage
export const getPurchaseReturnExWarehouseSummaryPage = (
  params,
  config = {}
) => {
  return request.post(
    apiPrefix + "GetPurchaseReturnExWarehouseSummaryPage",
    params,
    config
  );
};

//导出获取采购退货汇总 ExportPurchaseReturnExWarehouseSummary
export const exportPurchaseReturnExWarehouseSummary = (
  params,
  config = { responseType: "blob" }
) => {
  return request.post(
    apiPrefix + "ExportPurchaseReturnExWarehouseSummary",
    params,
    config
  );
};

//分页获取采购退货出库明细 GetPurchaseReturnExWarehouseDtlPage
export const getPurchaseReturnExWarehouseDtlPage = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetPurchaseReturnExWarehouseDtlPage",
    params,
    config
  );
};

//导出采购退货出库明细 ExportPurchaseReturnExWarehouseDtl
export const exportPurchaseReturnExWarehouseDtl = (
  params,
  config = { responseType: "blob" }
) => {
  return request.post(
    apiPrefix + "ExportPurchaseReturnExWarehouseDtl",
    params,
    config
  );
};

//删除采购退货出库 DelPurchaseReturnExWarehouseByBatch
export const delPurchaseReturnExWarehouseByBatch = (params, config = {}) => {
  return request.post(
    apiPrefix + "DelPurchaseReturnExWarehouseByBatch",
    params,
    config
  );
};

//取消奖金
export const cancleBonusPurchaseReturnExWarehouseApply = (params, config = {}) => {
  return request.post(
    apiPrefix + "CancleBonusPurchaseReturnExWarehouseApply",
    params,
    config
  );
};


//根据批次号保存图片 SaveReturnExWarehouseImgByBatchNumber
export const saveReturnExWarehouseImgByBatchNumber = (params, config = {}) => {
  return request.post(
    apiPrefix + "SaveReturnExWarehouseImgByBatchNumber",
    params,
    config
  );
};

//根据Id保存图片 SaveReturnExWarehouseImgById
export const saveReturnExWarehouseImgById = (params, config = {}) => {
  return request.post(
    apiPrefix + "SaveReturnExWarehouseImgById",
    params,
    config
  );
};

// 获取采购退货出库审批数据 GetPurchaseReturnExWarehouseApplyInfo
export const getPurchaseReturnExWarehouseApplyInfo = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetPurchaseReturnExWarehouseApplyInfo",
    params,
    config
  );
};

//发起采购退货出库审批 SendPurchaseReturnExWarehouseApply
export const sendPurchaseReturnExWarehouseApply = (params, config = {}) => {
  return request.post(
    apiPrefix + "SendPurchaseReturnExWarehouseApply",
    params,
    config
  );
};

//保存采购退货出库审批 SavePurchaseReturnExWarehouseApplyData
export const savePurchaseReturnExWarehouseApplyData = (params, config = {}) => {
  return request.post(
    apiPrefix + "SavePurchaseReturnExWarehouseApplyData",
    params,
    config
  );
};

//查看审批数据 GetPurchaseReturnExWarehouseApplyData
export const getPurchaseReturnExWarehouseApplyData = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetPurchaseReturnExWarehouseApplyData",
    params,
    config
  );
};

//获取奖金设置数据
export const getBonusSetData = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetBonusSetData",
    params,
    config
  );
};

//保存奖金设施数据
export const saveBonusSetData = (params, config = {}) => {
  return request.post(
    apiPrefix + "SaveBonusSetData",
    params,
    config
  );
};

//变更退货事由
export const updateReturnReason = (params, config = {}) => { return request.post(apiPrefix + "UpdateReturnReason",params,config); };

//获取采购退货出库-日志分页
export const getPurchaseRturnExWarehouseLogPage = (params, config = {}) => { return request.post(apiPrefix + "GetPurchaseRturnExWarehouseLogPage",params,config); };

//获取采购退货出库商品数据
export const getPurchaseRturnExWarehouseGoods = (params, config = {}) => { return request.post(apiPrefix + "GetPurchaseRturnExWarehouseGoods",params,config); };

//获取采购退货出库商品数据
export const editPurchaseRturnExWarehouseActualQty = (params, config = {}) => { return request.post(apiPrefix + "EditPurchaseRturnExWarehouseActualQty",params,config); };