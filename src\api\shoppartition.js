import request from '@/utils/request'
const ShopPartitionPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/ShopPartition/`

// 导入店铺分区管理列表
export const importShopPartitionList = (params, config = {}) => { return request.post(ShopPartitionPrefix + 'ImportPddShopPartitionAsync', params, config) }

// 获取店铺分区管理列表
export const getShopPartitionList = (params, config = {}) => { return request.post(ShopPartitionPrefix + 'GetPageList', params, config) }

// 获取店铺分区管理详情
export const getShopPartition = (params, config = {}) => { return request.get(ShopPartitionPrefix + 'GetById', { params: params, ...config }) }

// 根据ID获取分区详情
export const getPartitionById = (id) => { return request.get(ShopPartitionPrefix + 'GetById?id=' + id, ) }

// 创建店铺分区
export const createShopPartition = (params, config = {}) => { return request.post(ShopPartitionPrefix + 'AddOrUpdate', params, config) }

// 更新店铺分区
export const updateShopPartition = (params, config = {}) => { return request.post(ShopPartitionPrefix + 'AddOrUpdate', params, config) }

// 删除店铺分区
export const deleteShopPartition = (params, config = {}) => { return request.delete(ShopPartitionPrefix + 'Delete?id='+params.id, params,config) }

// 根据店铺名称获取店铺列表
export const getShopsByName = (params, config = {}) => { return request.post(ShopPartitionPrefix + 'GetShopsByName', params, config) }
