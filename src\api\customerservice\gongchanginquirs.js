import request from '@/utils/request'
const GroupPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/GongChangInquirs/`



//获取淘工厂店铺
export const GetGongChangShops = (params, config = {}) => { return request.get(GroupPrefix + 'GetGongChangShops', { params: params, ...config }) }
//获取组distinct
export const GetGongChangGroup = (params, config = {}) => { return request.get(GroupPrefix + 'GetGongChangGroup', { params: params, ...config }) }
//分页获取分组
export const GetGongChangGroupPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetGongChangGroupPageList', params, config) }
//新增组
export const AddGongChangGroup = (params, config = {}) => { return request.post(GroupPrefix + 'AddGongChangGroup', params, config) }
//删除组
export const DeleteGongChangGroup = (params, config = {}) => { return request.get(GroupPrefix + 'DeleteGongChangGroup', { params: params, ...config }) }
//修改组
export const UpdateGongChangGroup = (params, config = {}) => { return request.post(GroupPrefix + 'UpdateGongChangGroup', params, config) }
//导入组
export const ImportGongChangGroup = (params, config = {}) => { return request.post(GroupPrefix + 'ImportGongChangGroup', params, config) }
//获取分组修改日志
export const GetGroupLogList = (params, config = {}) => { return request.post(GroupPrefix + 'GetGroupLogList', params, config) }
//通过批次号删除组
export const DeleteBatchStrGongChangGroup = (params, config = {}) => { return request.post(GroupPrefix + 'DeleteBatchStrGongChangGroup', params, config) }
//批量修改离组日期
export const BatchUpdateLeaveDate = (params, config = {}) => { return request.post(GroupPrefix + 'BatchUpdateLeaveDate', params, config) }



//导入咨询数据
export const ImportGongChangInquirs = (params, config = {}) => { return request.post(GroupPrefix + 'ImportGongChangInquirs', params, config) }
//删除咨询数据
export const DeleteGongChangInquirs = (params, config = {}) => { return request.get(GroupPrefix + 'DeleteGongChangInquirs', { params: params, ...config }) }
//分页获取咨询数据
export const GetGongChangInquirsPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetGongChangInquirsPageList', params, config) }
//咨询数据没有匹配到分组的
export const GetGongChangInquirsNotExistsList = (params, config = {}) => { return request.post(GroupPrefix + 'GetGongChangInquirsNotExistsList', params, config) }



//个人效率统计分页查询
export const GetGongChangUserInquirsPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetGongChangUserInquirsPageList', params, config) }
//个人效率统计分页查询
export const ExportGongChangUserInquirsList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportGongChangUserInquirsList', params, config)
}
//个人效率统计趋势图
export const GetGongChangUserInquirsChart = (params, config = {}) => { return request.post(GroupPrefix + 'GetGongChangUserInquirsChart', params, config) }
//个人效率统计按店统计分页查询
export const GetGongChangUserInquirsShopPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetGongChangUserInquirsShopPageList', params, config) }




//组效率统计按店统计分页查询
export const GetGongChangGroupInquirsPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetGongChangGroupInquirsPageList', params, config) }
//组效率统计趋势图
export const GetGongChangGroupInquirsChart = (params, config = {}) => { return request.post(GroupPrefix + 'GetGongChangGroupInquirsChart', params, config) }
//组效率统计分页查询
export const ExportGongChangGroupInquirsList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportGongChangGroupInquirsList', params, config)
}




//店效率统计按店统计分页查询
export const GetGongChangShopInquirsPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetGongChangShopInquirsPageList', params, config) }
//店效率统计趋势图
export const GetGongChangShopInquirsChart = (params, config = {}) => { return request.post(GroupPrefix + 'GetGongChangShopInquirsChart', params, config) }
//店效率统计分页查询
export const ExportGongChangShopInquirsList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportGongChangShopInquirsList', params, config)
}
