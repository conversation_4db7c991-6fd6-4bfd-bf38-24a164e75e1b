import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/dayreportV2/`


//新版抖音日报账单费用查询
export const getNewDYBillingCharge = (params, config = {}) => { return request.get(apiPrefix + 'GetNewDYBillingCharge', { params, ...config }) }

//新版抖音账单费用导出
export const exportNewDYBillingChargeList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportNewDYBillingChargeList', params, config) }


//新版抖音账单费用导入
export const importBillingCharge = (params, config = {}) => { return request.post(apiPrefix + 'ImportBillingChargeAsync', params, config) }

// 新版销售主题导入
export const importCodeSalesThemeAnalysis = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportCodeSalesThemeAnalysisAsync', params, config)
}

//新版售后主题分析导入
export const importAfterSalesSubjectAnalysis = (params, config = {}) => { return request.post(apiPrefix + 'ImportAfterSalesSubjectAnalysisAsync', params, config) }
export const importPDDBillingCharge = (params, config = {}) => { return request.post(apiPrefix + 'ImportPDDBillingChargeAsync', params, config) }

//抖音日报精选联盟费用导入
export const importDYSelectAlliance = (params, config = {}) => { return request.post(apiPrefix + 'ImportDYSelectAllianceAsync', params, config) }
//抖音日报精选联盟费用查询
export const getDYSelectAlliance = (params, config = {}) => { return request.get(apiPrefix + 'GetDYSelectAlliance', { params, ...config }) }

//新版淘系日报账单费用查询
export const getNewTXBillingCharge = (params, config = {}) => { return request.get(apiPrefix + 'GetNewTXBillingCharge', { params, ...config }) }

//新版淘系账单费用导出
export const exportNewTXBillingCharge = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportNewTXBillingChargeList', params, config) }

//新版淘系日报账单费用查询
export const getNewTXBillingChargeWX = (params, config = {}) => { return request.get(apiPrefix + 'GetNewTXBillingChargeWX', { params, ...config }) }

//新版淘系账单费用导出
export const exportNewTXBillingChargeWX = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportNewTXBillingChargeListWX', params, config) }


//新版阿里巴巴日报账单费用查询
export const getNewAlibabaBillingCharge = (params, config = {}) => { return request.get(apiPrefix + 'GetNewAlibabaBillingCharge', { params, ...config }) }

//新版阿里巴巴账单费用导出
export const exportNewAlibabaBillingCharge = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportNewAlibabaBillingChargeList', params, config) }

//新版京东日报账单费用查询
export const getNewJDBillingCharge = (params, config = {}) => { return request.get(apiPrefix + 'GetNewJDBillingCharge', { params, ...config }) }

//新版京东账单费用导出
export const exportNewJDBillingCharge = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportNewJDBillingChargeList', params, config) }

//新版淘工厂日报账单费用查询
export const getNewTGCBillingCharge = (params, config = {}) => { return request.get(apiPrefix + 'GetNewTGCBillingCharge', { params, ...config }) }

//新版淘工厂账单费用导出
export const exportNewTGCBillingCharge = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportNewTGCBillingChargeList', params, config) }

//新版淘工厂日报账单费用查询
export const getNewTGCBillingChargeWX = (params, config = {}) => { return request.get(apiPrefix + 'GetNewTGCBillingChargeWX', { params, ...config }) }

//新版淘工厂账单费用导出
export const exportNewTGCBillingChargeWX = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportNewTGCBillingChargeListWX', params, config) }


//新版账单费用汇总查询
export const billingSummary = (params, config) => { return request.post(apiPrefix + 'BillingSummary', params, config) }

//账单费用汇总界面下拉框
export const getBillingSummaryBillingItem = (params, config = {}) => { return request.get(apiPrefix + 'GetBillingSummaryBillingItem', { params, ...config }) }

//账单费用汇总界面下拉框
export const DeletePackingChargeNewAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeletePackingChargeNewAsync', { params, ...config }) }


//新增账单汇总类目信息
export const addBillingSummaryCategory = (params, config) => { return request.post(apiPrefix + 'AddBillingSummaryCategoryAsync', params, config) }

//设置账单汇总类目信息
export const settingsBillingSummaryCategory = (params, config) => { return request.post(apiPrefix + 'SettingsBillingSummaryCategoryAsync', params, config) }

//账单费用汇总新增类型查询
export const getBillingSummaryNewAddType = (params, config = {}) => { return request.get(apiPrefix + 'GetBillingSummaryNewAddType', { params, ...config }) }

//账单费用汇总界面类目下拉框
export const getBillingSummaryCategorySelect = (params, config = {}) => { return request.post(apiPrefix + 'GetBillingSummaryCategorySelect', params, config) }

//账单费用汇总界面获取弹窗复选框数据
export const BillingSummaryCategoryCheckbox = (params, config = {}) => { return request.post(apiPrefix + 'BillingSummaryCategoryCheckbox', { params, ...config }) }



//加工费导入
export const importProcessingCost = (params, config = {}) => { return request.post(apiPrefix + 'ImportProcessingCostAsync', params, config) }
//加工费查询
export const getProcessingCost = (params, config = {}) => { return request.get(apiPrefix + 'GetProcessingCost', { params, ...config }) }

//加工费编辑
export const editProcessingCost = (params, config) => { return request.post(apiPrefix + 'EditProcessingCostAsync', params, config) }

//快递费均价查询
export const getExpressFeeAveragePrice = (params, config = {}) => { return request.get(apiPrefix + 'GetExpressFeeAveragePrice', { params, ...config }) }

//快递费均价编辑
export const editExpressFeeAveragePrice = (params, config) => { return request.post(apiPrefix + 'EditExpressFeeAveragePriceAsync', params, config) }

export const importBasicServiceRate = (params, config = {}) => { return request.post(apiPrefix + 'ImportBasicServiceRateAsync', params, config) }
export const getBasicServiceRate = (params, config = {}) => { return request.get(apiPrefix + 'GetBasicServiceRate', { params, ...config }) }

export const editBasicServiceRate = (params, config) => { return request.post(apiPrefix + 'EditBasicServiceRateAsync', params, config) }

//仓储损耗（系列编码）查询
export const getStorageLossSerialCoding = (params, config = {}) => { return request.get(apiPrefix + 'GetStorageLossSerialCoding', { params, ...config }) }

//仓储损耗（系列编码）编辑
export const editStorageLossSerialCoding = (params, config) => { return request.post(apiPrefix + 'EditStorageLossSerialCodingAsync', params, config) }



//产品运费查询
export const getProductFreight = (params, config = {}) => { return request.get(apiPrefix + 'GetProductFreight', { params, ...config }) }

//产品运费编辑
export const editProductFreight = (params, config) => { return request.post(apiPrefix + 'EditProductFreightAsync', params, config) }

//损耗下架查询
export const getLossOffShelf = (params, config = {}) => { return request.get(apiPrefix + 'GetLossOffShelf', { params, ...config }) }

//损耗下架编辑
export const editLossOffShelf = (params, config) => { return request.post(apiPrefix + 'EditLossOffShelfAsync', params, config) }

//预估费用查询
export const getEstimatedCost = (params, config = {}) => { return request.get(apiPrefix + 'GetEstimatedCost', { params, ...config }) }

//预估费用编辑
export const editEstimatedCost = (params, config) => { return request.post(apiPrefix + 'EditEstimatedCostAsync', params, config) }


//客服费查询
export const getCustomerServiceFee = (params, config = {}) => { return request.get(apiPrefix + 'GetCustomerServiceFee', { params, ...config }) }

//客服费编辑
export const editCustomerServiceAsync = (params, config) => { return request.post(apiPrefix + 'EditCustomerServiceAsync', params, config) }

//包装费导入
export const importPackingCharge = (params, config = {}) => { return request.post(apiPrefix + 'ImportPackingChargeAsync', params, config) }

//新版包装费导入
export const importPackingChargeNewAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportPackingChargeNewAsync', params, config) }

//包装费查询
export const getPackingCharge = (params, config = {}) => { return request.get(apiPrefix + 'GetPackingCharge', { params, ...config }) }

//包装费新查询
export const getPackingChargeNewAsync = (params, config) => { return request.post(apiPrefix + 'GetPackingChargeNewAsync', params, config) }
export const ExportPackingChargeNewAsync = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportPackingChargeNewAsync', params, config)
}

//包装费编辑
export const editPackingCharge = (params, config) => { return request.post(apiPrefix + 'EditPackingChargeAsync', params, config) }

//分摊费率查询
export const getContributoryRate = (params, config = {}) => { return request.get(apiPrefix + 'GetContributoryRate', { params, ...config }) }

//分摊费率编辑
export const editContributoryRate = (params, config) => { return request.post(apiPrefix + 'EditContributoryRateAsync', params, config) }

//国外汇率查询
export const getForeignExchangeRate = (params, config = {}) => { return request.get(apiPrefix + 'GetForeignExchangeRate', { params, ...config }) }

//国外汇率编辑
export const addOrEditForeignExchangeRate = (params, config) => { return request.post(apiPrefix + 'AddOrEditForeignExchangeRate', params, config) }

//普通辅料包装费查询
export const getNormalAccessory = (params, config = {}) => { return request.get(apiPrefix + 'GetNormalAccessory', { params, ...config }) }

//普通辅料包装费编辑
export const editNormalAccessory = (params, config) => { return request.post(apiPrefix + 'EditNormalAccessoryAsync', params, config) }


//出仓成本查询
export const getExitCost = (params, config = {}) => { return request.get(apiPrefix + 'GetExitCost', { params, ...config }) }

//出仓成本编辑
export const editExitCost = (params, config) => { return request.post(apiPrefix + 'EditExitCostAsync', params, config) }


//仓库损耗查询
export const getLossOffFee = (params, config = {}) => { return request.get(apiPrefix + 'GetLossOffFee', { params, ...config }) }

//编辑仓库损耗信息
export const editLossOffFee = (params, config) => { return request.post(apiPrefix + 'EditLossOffFeeAsync', params, config) }




//订单日报查询
export const productOrderDayReport = (params, config = {}) => { return request.post(apiPrefix + 'PageProductOrderDayReport', params, config) }

//订单日报导出
export const exportProductOrderDayReport = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportProductOrderDayReport', params, config) }


//编码利润分页查询
export const getProductReportGoodsProfitPageList = (params, config) => { return request.post(apiPrefix + 'GetProductReportGoodsProfitPageList', params, config) }
//编码利润-编码趋势图
export const getProductReportGoodsProfitChart = (params, config) => { return request.post(apiPrefix + 'GetProductReportGoodsProfitChart', params, config) }
//编码利润-明细趋势图
export const getProductReportDtlProfitChart = (params, config) => { return request.post(apiPrefix + 'GetProductReportDtlProfitChart', params, config) }

//获取订单日报汇总趋势图数据
export const queryProductOrderDayReportSumChart = (params, config = {}) => { return request.get(apiPrefix + 'QueryProductOrderDayReportSumChart', { params, ...config }) }

//账单类目查询接口
export const getBillingSummaryCategory = (params, config = {}) => { return request.post(apiPrefix + 'GetBillingSummaryCategory', params, config) }

//删除账单汇总类目信息
export const deleteBillingSummaryCategory = (id, config = {}) => { return request.delete(apiPrefix + `DeleteBillingSummaryCategory?id=${id}`) }


//新增账单汇总类目信息
export const addBillingSummaryCategoryAsync = (params, config = {}) => { return request.post(apiPrefix + 'AddBillingSummaryCategoryAsync', params, config) }

//导出
export const exportBillingSummary = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportBillingSummary', params, config) }

//订单亏损平台前20个店铺维度
export const getOrderDayReportOutStoreLossShop = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderDayReportOutStoreLossShop', params, config) }

//订单日报出仓亏损-平台总维度
export const getOrderDayReportOutStoreLossAllPlatform = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderDayReportOutStoreLossAllPlatform', params, config) }

//订单日报出仓亏损趋势图-平台天维度
export const getOrderDayReportOutStoreLossPlatformChart = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderDayReportOutStoreLossPlatformChart', params, config) }

//订单日报出仓亏损-平台所有组维度
export const getOrderDayReportOutStoreLossGroup = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderDayReportOutStoreLossGroup', params, config) }

//订单日报出仓亏损趋势图-平台单个店铺维度
export const getOrderDayReportOutStoreLossShopChart = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderDayReportOutStoreLossShopChart', params, config) }

//订单日报出仓亏损趋势图-平台单个组维度
export const getOrderDayReportOutStoreLossGroupChart = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderDayReportOutStoreLossGroupChart', params, config) }

//出仓负利润ID订单明细获取"订单+"数据
export const exitNegativeProfitIDOrderDetailsDialog = (params, config = {}) => { return request.get(apiPrefix + 'ExitNegativeProfitIDOrderDetailsDialog', { params, ...config }) }

//出仓负利润ID订单明细订单+导出
export const exportExitNegativeProfitIDOrderDetailsDialog = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportExitNegativeProfitIDOrderDetailsDialog', params, config) }

//京东钱包导入
export const importJDPurseBill = (params, config = {}) => { return request.post(apiPrefix + 'ImportJDPurseBillAsync', params, config) }
//京东钱包查询
export const getJDPurseBill = (params, config = {}) => { return request.get(apiPrefix + 'GetJDPurseBill', { params, ...config }) }

//希音账单费用查询
export const getNewSheInBillingCharge = (params, config = {}) => { return request.get(apiPrefix + 'GetNewSheInBillingCharge', { params, ...config }) }

//希音账单费用类型-界面下拉框
export const getBillingTypeSheIn = (params, config = {}) => { return request.get(apiPrefix + 'GetBillingTypeSheIn', { params, ...config }) }

//新版希音日报库存损耗明细查询
export const getNewSheInInventoryDepletion = (params, config = {}) => { return request.get(apiPrefix + 'GetNewSheInInventoryDepletion', { params, ...config }) }

//日报-运营销售分析列表查询
export const pageProCodeStyleCodeGoodsCountList = (params, config = {}) => { return request.post(apiPrefix + 'PageProCodeStyleCodeGoodsCountList', params, config) }

//日报-运营销售分析列表导出
export const exportProCodeStyleCodeGoodsCountList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportProCodeStyleCodeGoodsCountList', params, config) }

//希音平台库存管理-希音台账变动明细列表查询
export const getLedgerChangeDetails_SheIn = (params, config = {}) => { return request.post(apiPrefix + 'GetLedgerChangeDetails_SheIn', params, config) }

//希音平台库存管理-公司发货与平台入库差异列表查询
export const getCompanySendGoodsSheIn = (params, config = {}) => { return request.post(apiPrefix + 'GetCompanySendGoodsSheIn', params, config) }

//希音平台库存管理-希音平台库存台账列表查询
export const getPlatformInventoryLedger = (params, config = {}) => { return request.post(apiPrefix + 'GetPlatformInventoryLedger', params, config) }

//希音平台库存管理-希音销售主题分析导入
export const importCodeSalesThemeAnalysis_SheInV2Async = (params, config = {}) => { return request.post(apiPrefix + 'ImportCodeSalesThemeAnalysis_SheInV2Async', params, config) }

//希音平台库存管理-希音销售主题分析导入
export const importOpeningDetail_SheInAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportOpeningDetail_SheInAsync', params, config) }


//希音平台库存管理-希音销售主题分析
export const pageCodeSalesThemeAnalysis_SheInAsync = (params, config = {}) => { return request.get(apiPrefix + 'PageCodeSalesThemeAnalysis_SheInAsync', { params, ...config }) }

//淘工厂超链接服务费导入
export const importHyperlinkServiceRate = (params, config = {}) => { return request.post(apiPrefix + 'ImportHyperlinkServiceRateAsync', params, config) }

//淘工厂超链接服务费查询
export const getHyperlinkServiceRate = (params, config = {}) => { return request.get(apiPrefix + 'GetHyperlinkServiceRate', { params, ...config }) }

//淘工厂超链接ID导入
export const importHyperlinkIDServiceRate = (params, config = {}) => { return request.post(apiPrefix + 'ImportHyperlinkIDServiceRateAsync', params, config) }

//淘工厂超链接ID服务费查询
export const getHyperlinkIDServiceRate = (params, config = {}) => { return request.get(apiPrefix + 'GetHyperlinkIDServiceRate', { params, ...config }) }

//查询希音期初明细
export const getOpeningDetail_SheIn = (params, config = {}) => { return request.post(apiPrefix + 'GetOpeningDetail_SheIn', params, config) }

//希音平台库存管理-公司发货与平台入库差异计算日报
export const calCompanySendGoodsSheIn = (params, config = {}) => { return request.post(apiPrefix + 'CalCompanySendGoodsSheIn?RequestYearMonthDay=' + params.RequestYearMonthDay) }

//希音平台库存管理-平台库存台账计算日报
export const calPlatformInventoryLedger = (params, config = {}) => { return request.post(apiPrefix + 'CalPlatformInventoryLedger?RequestYearMonthDay=' + params.RequestYearMonthDay) }

//希音平台库存管理-希音台账变动明细-变动类型选择器数据
export const getLedgerChangeDetails_SheInSelect = (params, config = {}) => { return request.get(apiPrefix + 'GetLedgerChangeDetails_SheInSelect', { params, ...config }) }

//希音期初明细导出
export const exportOpeningDetail_SheIn = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportOpeningDetail_SheIn', params, config) }

//希音-公司发货与平台入库差异-导出
export const exportCompanySendGoodsSheIn = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportCompanySendGoodsSheIn', params, config) }

//希音-希音平台库存台账导出
export const exportPlatformInventoryLedger = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPlatformInventoryLedger', params, config) }

//希音销售主题分析导出
export const exportCodeSalesThemeAnalysis_SheIn = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportCodeSalesThemeAnalysis_SheIn', params, config) }

//希音台账明细导出
export const exportLedgerChangeDetails_SheIn = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportLedgerChangeDetails_SheIn', params, config) }

//拼多多跨境日报推广费用交易收入导入
export const importTransactionIncomeAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportTransactionIncomeAsync', params, config) }

//拼多多跨境日报推广费用交易收入查询
export const getTransactionIncome_PddTemu = (params, config = {}) => { return request.post(apiPrefix + 'GetTransactionIncome_PddTemu', params, config) }

//拼多多跨境日报售后金额导入
export const importReserveAmountAfterSaleAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportReserveAmountAfterSaleAsync', params, config) }

//拼多多跨境日报售后金额查询
export const getReserveAmountAfterSale_PddTemu = (params, config = {}) => { return request.post(apiPrefix + 'GetReserveAmountAfterSale_PddTemu', params, config) }

//拼多多跨境账单费用导入
export const importPddTemuBillingChargeAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportPddTemuBillingChargeAsync', params, config) }

//拼多多跨境日报-账单弃货查询
export const getBillingCharge_derelict_PddTemu = (params, config = {}) => { return request.post(apiPrefix + 'GetBillingCharge_derelict_PddTemu', params, config) }

//拼多多跨境日报-账单环保费查询
export const getBillingCharge_HbKf_PddTemu = (params, config = {}) => { return request.post(apiPrefix + 'GetBillingCharge_HbKf_PddTemu', params, config) }

//拼多多跨境日报-售后赔付查询
export const getBillingCharge_Shpf_PddTemu = (params, config = {}) => { return request.post(apiPrefix + 'GetBillingCharge_Shpf_PddTemu', params, config) }

//拼多多跨境日报-退回服务费查询
export const getBillingCharge_ThFw_PddTemu = (params, config = {}) => { return request.post(apiPrefix + 'GetBillingCharge_ThFw_PddTemu', params, config) }

//拼多多跨境日报-商品补寄赔付金查询
export const getBillingCharge_Spbj_PddTemu = (params, config = {}) => { return request.post(apiPrefix + 'GetBillingCharge_Spbj_PddTemu', params, config) }

//拼多多跨境日报-账单批次删除
export const batchDeleteBillingCharge_PddTemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'BatchDeleteBillingCharge_PddTemuAsync', params, config) }

//跨境首页-列表数据查询
export const getCrossBorderHomePage_Kj = (params, config = {}) => { return request.post(apiPrefix + 'GetCrossBorderHomePage_Kj', params, config) }

//跨境首页-导入
export const importTemuListingAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportTemuListingAsync', params, config) }

//跨境首页-查询Temu 快递/包装费单条数据
export const getTemuExpressPackage = (params, config = {}) => { return request.get(apiPrefix + 'GetTemuExpressPackage', { params, ...config }) }

//跨境首页-新增或编辑Temu 快递/包装费
export const addOrEditTemuExpressPackage = (params, config = {}) => { return request.post(apiPrefix + 'AddOrEditTemuExpressPackage', params, config) }

//跨境首页-跨境首页趋势图
export const getCrossBorderHomePage_KjAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'GetCrossBorderHomePage_KjAnalysis', { params, ...config }) }

//跨境首页-获取岗位选择器数据
export const getKjDistrict = (params, config = {}) => { return request.get(apiPrefix + 'GetKjDistrict', { params, ...config }) }

//日报数据维护-特殊ID/店铺-导入
export const importSpecialIdShopAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportSpecialIdShopAsync', params, config) }

//日报数据维护-特殊ID/店铺-列表查询
export const getSpecialIdShop = (params, config = {}) => { return request.post(apiPrefix + 'GetSpecialIdShop', params, config) }

//日报数据维护-特殊ID/店铺-一键启用
export const editSpecialIdShopAsync = (params, config = {}) => { return request.post(apiPrefix + 'EditSpecialIdShopAsync', params, config) }

//日报数据维护-特殊ID/店铺-导出
export const exportSpecialIdShop = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportSpecialIdShop', params, config) }

//日报数据维护-特殊ID/店铺-删除
export const deleteSpecialIdShopAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteSpecialIdShopAsync', params, config) }

//新版账单费用-快手账单费用查询
export const getNewKWaiBillingCharge = (params, config = {}) => { return request.get(apiPrefix + 'GetNewKWaiBillingCharge', { params, ...config }) }

//新版账单费用-快手账单费用导出
export const exportNewKWaiBillingChargeList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportNewKWaiBillingChargeList', params, config) }
//新版账单费用-快手资金账单费用导出
export const exportKWaiShopFundStatementList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportKWaiShopFundStatementList', params, config) }

//新版账单费用-快手资金账单费用导出
export const exportJDPurseBillList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportJDPurseBillList', params, config) }


//新版账单费用-快手小额打款查询
export const getPettyPayment_KWai = (params, config = {}) => { return request.post(apiPrefix + 'GetPettyPayment_KWai', params, config) }

//新版账单费用-快手退货补运费查询
export const getReturnFreight_KWai = (params, config = {}) => { return request.post(apiPrefix + 'GetReturnFreight_KWai', params, config) }

//新版账单费用-快手账单费用-侨宝特殊单平台补贴查询
export const getKsDepositGuaranteeDetail = (params, config = {}) => { return request.get(apiPrefix + 'GetKsDepositGuaranteeDetail', { params, ...config }) }

//新版账单费用-快手账单费用-快手保证金导入
export const importKsDepositGuaranteeAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportKsDepositGuaranteeAsync', params, config) }

//定制成本-查询
export const pageCodeSalesThemeAnalysis_CustomCostAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageCodeSalesThemeAnalysis_CustomCostAsync', params, config) }

//定制成本-导出
export const exportCodeSalesThemeAnalysis_CustomCostAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportCodeSalesThemeAnalysis_CustomCostAsync', params, config) }

//计算昨日拼多多日报系列编码维度退款率前五十个系列编码的排名
export const getPDDStyleCodeRefundRate = (params, config = {}) => { return request.post(apiPrefix + 'GetPDDStyleCodeRefundRate', params, config) }

//TEMU半托销售明细导入
export const importHalfSalesDetails_TemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportHalfSalesDetails_TemuAsync', params, config) }

//TEMU半托销售明细查询
export const pageHalfSalesDetails_TemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHalfSalesDetails_TemuAsync', params, config) }

//TEMU半托结算数据导入
export const importSettleAccount_TemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportSettleAccount_TemuAsync', params, config) }

//TEMU半托结算数据查询
export const pageSettleAccount_TemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageSettleAccount_TemuAsync', params, config) }

//TEMU半托账单费用导入
export const importBillingCharge_TemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportBillingCharge_TemuAsync', params, config) }

//TEMU半托账单费用查询
export const pageBillingCharge_TemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageBillingCharge_TemuAsync', params, config) }

//TEMU半托运输费用导入
export const importTransportationCost_TemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportTransportationCost_TemuAsync', params, config) }

//TEMU半托运输费用查询
export const pageTransportationCost_TemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageTransportationCost_TemuAsync', params, config) }

//TEMU半托售后退款查询
export const pageHalfSalesDetails_RefundAfterSale_TemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHalfSalesDetails_RefundAfterSale_TemuAsync', params, config) }

//TEMU半托运费收入查询
export const pageHalfSalesDetails_FreightRevenue_TemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHalfSalesDetails_FreightRevenue_TemuAsync', params, config) }

//TEMU半托运费退款查询
export const pageHalfSalesDetails_FreightRefund_TemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHalfSalesDetails_FreightRefund_TemuAsync', params, config) }


//TEMU半托售后明细导入
export const importAfterSaleDetails_Ban_TemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportAfterSaleDetails_Ban_TemuAsync', params, config) }

//TEMU半托售后明细查询
export const pageAfterSaleDetails_Ban_TemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageAfterSaleDetails_Ban_TemuAsync', params, config) }

//日报数据维护-产品运费（新）-查询
export const getProductFreightNew = (params, config = {}) => { return request.get(apiPrefix + 'GetProductFreightNew', { params, ...config }) }

//日报数据维护-产品运费（新）-导入
export const importProductFreightNewAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportProductFreightNewAsync', params, config) }

export const getGuoHuoWuJie_Tx = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGuoHuoWuJie_Tx', params, config)
}

export const exportGuoHuoWuJie_Tx = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportGuoHuoWuJie_Tx', params, config)
}

//新版账单费用-日报账单费用汇总查询
export const pageUnCalcCharBillItem = (params, config = {}) => { return request.post(apiPrefix + 'PageUnCalcCharBillItem', params, config) }

//新版账单费用-日报账单费用汇总查询
export const pageUnClacChargeDealRule = (params, config = {}) => { return request.post(apiPrefix + 'PageUnClacChargeDealRule', params, config) }

export const pageUnClacChargeDealRuleEditLogEntity = (params, config = {}) => { return request.post(apiPrefix + 'PageUnClacChargeDealRuleEditLogEntity', params, config) }

export const editClacChargeDealRule = (params, config = {}) => { return request.post(apiPrefix + 'EditClacChargeDealRule', params, config) }

//新版账单费用-日报账单费用汇总导出
export const exportUnCalcCharBillItem = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportUnCalcCharBillItem ', params, config) }

//新版账单费用-日报账单费用金额明细
export const pageUnCalcCharBillItemDetail = (params, config = {}) => { return request.post(apiPrefix + 'PageUnCalcCharBillItemDetail', params, config) }

//支付宝账单账单查询 PageShopZfbBill
export const PageShopZfbBill = (params, config = {}) => { return request.post(apiPrefix + 'PageShopZfbBill', params, config) }

//抖音账单查询 PageShopBill_Dy
export const PageShopBill_Dy = (params, config = {}) => { return request.post(apiPrefix + 'PageShopBill_Dy', params, config) }

//京东账单查询 PageShopBill_Jd
export const PageShopBill_Jd = (params, config = {}) => { return request.post(apiPrefix + 'PageShopBill_Jd', params, config) }

//京东钱包查询 PageWalletShopBill_Jd
export const PageWalletShopBill_Jd = (params, config = {}) => { return request.post(apiPrefix + 'PageWalletShopBill_Jd', params, config) }

//快手账单查询 PageShopBill_KWai
export const PageShopBill_KWai = (params, config = {}) => { return request.post(apiPrefix + 'PageShopBill_KWai', params, config) }

//支付宝账单导入 ImportShopZfbBillAsync
export const ImportStoreFundStatementAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportStoreFundStatementAsync', params, config) }

//仓储费查询 GetStorageCharges_BanTuo_TemuAsync
export const GetStorageCharges_BanTuo_TemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetStorageCharges_BanTuo_TemuAsync', params, config) }

//头程查询 GetFirstLeg_BanTuo_TemuAsync
export const GetFirstLeg_BanTuo_TemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetFirstLeg_BanTuo_TemuAsync', params, config) }

//代发成本查询 GetIssuingCost_BanTuo_TemuAsync
export const GetIssuingCost_BanTuo_TemuAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetIssuingCost_BanTuo_TemuAsync', params, config) }

//跨境日报数据维护-导入
export const ImportIncidentalExpensesAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportIncidentalExpensesAsync', params, config) }

export const CheckShopFundStatement = (params, config = {}) => { return request.post(apiPrefix + 'CheckShopFundStatement', params, config) }

//特别区域快递费-导入
export const importSpecAreaExpressFee = (params, config = {}) => { return request.post(apiPrefix + 'ImportSpecAreaExpressFee', params, config) }

//特别区域快递费-查询
export const getSpecAreaExpressFee = (params, config = {}) => { return request.get(apiPrefix + 'GetSpecAreaExpressFee', { params, ...config }) }

//特别区域快递费-新增/编辑
export const editSpecAreaExpressFee = (params, config = {}) => { return request.post(apiPrefix + 'EditSpecAreaExpressFee', params, config) }

//新版账单费用-日报账单费用汇总-账单明细导出
export const exportUnCalcCharBillItemDetail = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportUnCalcCharBillItemDetail', params, config) }

//跨境日报数据维护-产品运费-查询
export const getProductFreight_KJ = (params, config = {}) => { return request.get(apiPrefix + 'GetProductFreight_KJ', { params, ...config }) }

//出仓成本均价查询
export const getExitCostAveragePrice = (params, config = {}) => { return request.get(apiPrefix + 'GetExitCostAveragePrice', { params, ...config }) }

//出仓成本均价编辑
export const editExitCostAveragePriceAsync = (params, config) => { return request.post(apiPrefix + 'EditExitCostAveragePriceAsync', params, config) }

//快手资金账单明细导入
export const importKsFundsStatementDetailAsync = (params, config) => { return request.post(apiPrefix + 'ImportKsFundsStatementDetailAsync', params, config) }

//快手资金账单明细查询
export const getKsFundsStatementDetail = (params, config = {}) => { return request.get(apiPrefix + 'GetKsFundsStatementDetail', { params, ...config }) }

//京喜NNSKU-导入
export const importJXNNSKUAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportJXNNSKUAsync', params, config) }

//京喜NNSKU-查询
export const getJXNNSKUList = (params, config = {}) => { return request.get(apiPrefix + 'GetJXNNSKUList', { params, ...config }) }

//京喜NNSKU-删除
export const deleteJXNNSKU = (params, config = {}) => { return request.get(apiPrefix + 'DeleteJXNNSKU', { params, ...config }) }

//不计算快递费店铺-查询
export const getShopNotCalcExpressFeeList = (params, config = {}) => { return request.get(apiPrefix + 'GetShopNotCalcExpressFeeList', { params, ...config }) }

//不计算快递费店铺-新增
export const addShopNotCalcExpressFee = (params, config = {}) => { return request.post(apiPrefix + 'AddShopNotCalcExpressFee', params, config) }

//不计算快递费店铺-删除
export const deleteShopNotCalcExpressFee = (params, config = {}) => { return request.post(apiPrefix + 'DeleteShopNotCalcExpressFeeAsync', params, config) }

//不计算包装出库-查询
export const getExcludingPackingAndWarehouseCost = (params, config = {}) => { return request.get(apiPrefix + 'GetExcludingPackingAndWarehouseCost', { params, ...config }) }

//不计算包装出库-新增
export const addExcludingPackingAndWarehouseCost = (params, config = {}) => { return request.post(apiPrefix + 'AddExcludingPackingAndWarehouseCost', params, config) }

//不计算包装出库-删除
export const deleteExcludingPackingAndWarehouseCost = (params, config = {}) => { return request.post(apiPrefix + 'DeleteExcludingPackingAndWarehouseCost', params, config) }

//不计算快递费店铺-查询
export const getDmShopList = (params, config = {}) => { return request.get(apiPrefix + 'GetDmShopList', { params, ...config }) }

//不计算快递费店铺-新增
export const addDmShop = (params, config = {}) => { return request.post(apiPrefix + 'AddDmShop', params, config) }

//不计算快递费店铺-删除
export const deleteDmShop = (params, config = {}) => { return request.post(apiPrefix + 'DeleteDmShopAsync', params, config) }


//项目组日报-订单/编码/ID/店铺/店铺SKU/明细日报查询
export const pageProjProductOrderDayReport = (params, config = {}) => { return request.post(apiPrefix + 'PageProjProductOrderDayReport', params, config) }

//项目组日报-订单/编码/ID/店铺/店铺SKU/明细日报导出
export const exportProjProductOrderDayReport = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportProjProductOrderDayReport', params, config) }

//项目组日报-订单/编码/ID/店铺/店铺SKU/明细日报趋势图
export const queryProjProductOrderDayReportSumChart = (params, config = {}) => { return request.post(apiPrefix + 'QueryProjProductOrderDayReportSumChart', params, config) }

//日报数据维护-系列编码包装费-导入
export const importSeriesCodePackFeesync = (params, config = {}) => { return request.post(apiPrefix + 'ImportSeriesCodePackFeesync', params, config) }

//日报数据维护-系列编码包装费-查询
export const getSeriesCodePackFeeList = (params, config = {}) => { return request.get(apiPrefix + 'GetSeriesCodePackFeeList', { params, ...config }) }

//日报数据维护-系列编码包装费-新增/编辑
export const addorEditSeriesCodePackFee = (params, config = {}) => { return request.post(apiPrefix + 'AddorEditSeriesCodePackFee', params, config) }

//日报数据维护-系列编码包装费-删除
export const deleteSeriesCodePackFeeBatchAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteSeriesCodePackFeeBatchAsync', params, config) }

//京东运营费用-京东入仓自营-导入
export const importJdSku = (params, config = {}) => { return request.post(apiPrefix + 'ImportJdSku', params, config) }

//京东运营费用-京东入仓自营-查询
export const getJdSelfSKUList = (params, config = {}) => { return request.get(apiPrefix + 'GetJdSelfSKUList', { params, ...config }) }

//京东运营费用-京东入仓自营-新增/编辑
export const addorEditJdSelfSKU = (params, config = {}) => { return request.post(apiPrefix + 'AddorEditJdSelfSKU', params, config) }

//京东运营费用-京东入仓自营-删除
export const deleteJdSelfSKUBatchAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteJdSelfSKUBatchAsync', params, config) }
export const exportJdSelfSKU = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportJdSelfSKU', { params, ...config }) }
export const exportJDSelfSkuPriceDayRpt = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportJDSelfSkuPriceDayRpt', { params, ...config }) }

//京东自营-京东联盟扣款文件-导入
export const importAffiliateDeduction_JingDongasync = (params, config = {}) => { return request.post(apiPrefix + 'ImportAffiliateDeduction_JingDongasync', params, config) }

//京东自营-京东自营商品编码sku文件-导入
export const importSKU_JingDongSelfSupportsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportSKU_JingDongSelfSupportsync', params, config) }

//京东自营-京东自营产品维护界面文件-导入
export const importSKUShopGoodsCode_JingDongSelfSupportsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportSKUShopGoodsCode_JingDongSelfSupportsync', params, config) }

//京东自营-京东自营商品明细查询-导入
export const importCodeSalesThemeAnalysisJDSelfSupportAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportCodeSalesThemeAnalysisJDSelfSupportAsync', params, config) }

//京东自营-京东自营商品明细查询-查询
export const getCodeSalesThemeAnalysis_JDSelfSupport = (params, config = {}) => { return request.get(apiPrefix + 'GetCodeSalesThemeAnalysis_JDSelfSupport', { params, ...config }) }

//京东自营-京东联盟扣款-正常扣费查询
export const getAffiliateDeduction_JingDongSelfSupport = (params, config = {}) => { return request.get(apiPrefix + 'GetAffiliateDeduction_JingDongSelfSupport', { params, ...config }) }

//京东自营-京东联盟扣款-退货返款查询
export const getAffiliateDeductionTuiHuo_JingDongSelfSupport = (params, config = {}) => { return request.get(apiPrefix + 'GetAffiliateDeductionTuiHuo_JingDongSelfSupport', { params, ...config }) }


//京东自营-京东联盟扣款文件-导入
export const importJdSelfWarehousOut = (params, config = {}) => { return request.post(apiPrefix + 'ImportJdSelfWarehousOut', params, config) }
//京东自营-京东联盟扣款-退货返款查询
export const getJdSelfWarehousOutList = (params, config = {}) => { return request.get(apiPrefix + 'GetJdSelfWarehousOutList', { params, ...config }) }
//京东自营-京东联盟扣款-导出
export const exportJdSelfWarehousOutList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportJdSelfWarehousOutList', params, config) }

//京东自营-京东联盟扣款文件-导入
export const importJdSelfWarehousOutReturn = (params, config = {}) => { return request.post(apiPrefix + 'ImportJdSelfWarehousOutReturn', params, config) }
//京东自营-京东联盟扣款-退货返款查询
export const getJdSelfWarehousOutReturnList = (params, config = {}) => { return request.get(apiPrefix + 'GetJdSelfWarehousOutReturnList', { params, ...config }) }


//京东自营-成本
export const getJDSelfSkuPriceDayRpt = (params, config = {}) => { return request.get(apiPrefix + 'GetJDSelfSkuPriceDayRpt', { params, ...config }) }

//京东自营-账单费用-查询
export const getBillingCharge_JingDongSelfSupport = (params, config = {}) => { return request.get(apiPrefix + 'GetBillingCharge_JingDongSelfSupport', { params, ...config }) }

//京东自营-商品编码维护-查询
export const getSKU_JingDongSelfSupport = (params, config = {}) => { return request.get(apiPrefix + 'GetSKU_JingDongSelfSupport', { params, ...config }) }

//京东自营-SKU产品维护-查询
export const getSKUShopGoodsCode_JingDongSelfSupport = (params, config = {}) => { return request.get(apiPrefix + 'GetSKUShopGoodsCode_JingDongSelfSupport', { params, ...config }) }

//京东自营-编辑或新增商品编码维护
export const addorEditShopGoodsCode_JingDongSelfSupport = (params, config = {}) => { return request.post(apiPrefix + 'AddorEditShopGoodsCode_JingDongSelfSupport', params, config) }

//京东自营-编辑或新增SKU产品维护
export const addorEditSKUShopGoodsCode_JingDongSelfSupport = (params, config = {}) => { return request.post(apiPrefix + 'AddorEditSKUShopGoodsCode_JingDongSelfSupport', params, config) }
//京东自营-批量修改SKU产品维护
export const bulkEditSKUShopGoodsCode_JingDongSelfSupport = (params, config = {}) => { return request.post(apiPrefix + 'BulkEditSKUShopGoodsCode_JingDongSelfSupport', params, config) }

//项目组日报-SKU产品维护导出
export const exportSKUShopGoodsCode_JingDongSelfSupport = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportSKUShopGoodsCode_JingDongSelfSupport', params, config) }

//京东自营-SKU产品维护删除
export const deleteSKUShopGoodsCode_JingDongSelfSupportAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteSKUShopGoodsCode_JingDongSelfSupportAsync', params, config) }

//小红书日报-小额打款导入
export const importPettyPayment_RedBookAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportPettyPayment_RedBookAsync', params, config) }

//小红书日报-小额打款-查询
export const getPettyPayment_RedBook = (params, config = {}) => { return request.get(apiPrefix + 'GetPettyPayment_RedBook', { params, ...config }) }

//小红书日报-店铺余额导入
export const importShopBalance_RedBookAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportShopBalance_RedBookAsync', params, config) }

//小红书日报-店铺余额-查询
export const getShopBalance_RedBook = (params, config = {}) => { return request.get(apiPrefix + 'GetShopBalance_RedBook', { params, ...config }) }

//小红书日报-支付宝账户和微信-导入
export const importPayWeChat_RedBookAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportPayWeChat_RedBookAsync', params, config) }

//小红书日报-支付宝账户和微信-查询
export const getPayWeChat_RedBook = (params, config = {}) => { return request.get(apiPrefix + 'GetPayWeChat_RedBook', { params, ...config }) }

//小红书日报-违规赔付导入
export const importCompensationViolations_RedBookAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportCompensationViolations_RedBookAsync', params, config) }

//小红书日报-违规赔付-查询
export const getCompensationViolations_RedBook = (params, config = {}) => { return request.get(apiPrefix + 'GetCompensationViolations_RedBook', { params, ...config }) }

//小红书日报-笔记营销导入
export const importNoteMarketing_RedBookAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportNoteMarketing_RedBookAsync', params, config) }

//小红书日报-笔记营销-查询
export const getNoteMarketing_RedBook = (params, config = {}) => { return request.get(apiPrefix + 'GetNoteMarketing_RedBook', { params, ...config }) }

//小红书日报-订单货款-查询
export const getBillingCharge_RedBook = (params, config = {}) => { return request.get(apiPrefix + 'GetBillingCharge_RedBook', { params, ...config }) }

//小红书日报-后台商品资料-导入
export const importBackEndProductInformation_RedBookAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportBackEndProductInformation_RedBookAsync', params, config) }

//小红书日报-后台商品资料-编辑
export const addorEditBackEndProductInformation_RedBook = (params, config = {}) => { return request.post(apiPrefix + 'AddorEditBackEndProductInformation_RedBook', params, config) }

//小红书日报-后台商品资料-删除
export const deleteBackEndProductInformation_RedBookAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteBackEndProductInformation_RedBookAsync', params, config) }

//小红书日报-后台商品资料-查询
export const getBackEndProductInformation_RedBookList = (params, config = {}) => { return request.get(apiPrefix + 'GetBackEndProductInformation_RedBookList', { params, ...config }) }

//视频号-账单费用-查询
export const getNewBillingCharge_WeChat = (params, config = {}) => { return request.get(apiPrefix + 'GetNewBillingCharge_WeChat', { params, ...config }) }

export const getDrNewBillingCharge_WeChat = (params, config = {}) => { return request.get(apiPrefix + 'GetDrNewBillingCharge_WeChat', { params, ...config }) }

//视频号-账单费用-查询-导出
export const exportDrNewBillingCharge_WeChat = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportDrNewBillingCharge_WeChat', params, config) }

//视频号日报-违规文件导入
export const importIllegalDeduction_WeChatAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportIllegalDeduction_WeChatAsync', params, config) }

//视频号日报-违规文件-查询
export const getIllegalDeduction_WeChat = (params, config = {}) => { return request.get(apiPrefix + 'GetIllegalDeduction_WeChat', { params, ...config }) }

//视频号日报-商家欠款导入
export const importMerchantsArrearsAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportMerchantsArrearsAsync', params, config) }

//视频号日报-商家欠款-查询
export const getMerchantsArrears_WeChat = (params, config = {}) => { return request.get(apiPrefix + 'GetMerchantsArrears_WeChat', { params, ...config }) }

//快手达人佣金预估文件导入
export const importMasterCommissionEstimate_Ks = (params, config = {}) => { return request.post(apiPrefix + 'ImportMasterCommissionEstimate_Ks', params, config) }

//快手达人佣金预估查询
export const getMasterCommissionEstimate_Ks = (params, config = {}) => { return request.get(apiPrefix + 'GetMasterCommissionEstimate_Ks', { params, ...config }) }

//快手达人佣金预估删除批次
export const deleteMasterCommissionEstimate_KsBatch = (params, config = {}) => { return request.get(apiPrefix + 'DeleteMasterCommissionEstimate_KsBatch', { params, ...config }) }

//淘系运营费用明细-查询
export const getProductAdv_TXList = (params, config = {}) => { return request.get(apiPrefix + 'GetProductAdv_TXList', { params, ...config }) }

//淘系运营费用明细-导出
export const exportProductAdv_TX = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportProductAdv_TX', params, config) }

//淘系运营费用趋势图
export const getProductAdv_TXChartList = (params, config = {}) => { return request.get(apiPrefix + 'GetProductAdv_TXChartList', { params, ...config }) }

//淘系运营费用汇总-导出
export const exportProductAdvMultipleDimensions_TX = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportProductAdvMultipleDimensions_TX', params, config) }

//淘系运营费用汇总-查询
export const getProductAdvMultipleDimensions_TXList = (params, config = {}) => { return request.get(apiPrefix + 'GetProductAdvMultipleDimensions_TXList', { params, ...config }) }

//淘系运营费用明细-新增通知配置
export const addNotificationConfigurationAdv_TX = (params, config = {}) => { return request.post(apiPrefix + 'AddNotificationConfigurationAdv_TX', params, config) }

//淘系运营费用汇总-获取通知配置
export const getNotificationConfigurationAdv_TX = (params, config = {}) => { return request.get(apiPrefix + 'GetNotificationConfigurationAdv_TX', { params, ...config }) }

//淘系运营费用汇总-H5页面
export const getDataProductAdv_TXList = (params, config = {}) => { return request.get(apiPrefix + 'GetDataProductAdv_TXList', { params, ...config }) }

//淘系运营费用汇总-一键通知
export const notificationConfigurationAdv_TX = (params, config = {}) => { return request.post(apiPrefix + 'NotificationConfigurationAdv_TX', params, config) }

//阿里巴巴运营费用-严选订单处理
export const importYanxuanOrdersAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportYanxuanOrdersAsync', params, config) }
export const getYanxuanOrdersList = (params, config = {}) => { return request.get(apiPrefix + 'GetYanxuanOrdersList', { params: params, ...config }) }
export const deleteYanxuanOrdersAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteYanxuanOrdersAsync', { params: params, ...config }) }
export const exportanxuanOrders = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportanxuanOrders', params, config) }

export const importShangJiaChuZisync = (params, config = {}) => { return request.post(apiPrefix + 'ImportShangJiaChuZisync', params, config) }
export const getShangJiaChuZiList = (params, config = {}) => { return request.get(apiPrefix + 'GetShangJiaChuZiList', { params: params, ...config }) }
export const deleteShangJiaChuZiBatchAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteShangJiaChuZiBatchAsync', { params: params, ...config }) }
export const exportShangJiaChuZiList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + `ExportShangJiaChuZiList`, { params, ...config }) }


export const importPlatformIncrementsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportPlatformIncrementsync', params, config) }
export const getPlatformIncrementList = (params, config = {}) => { return request.get(apiPrefix + 'GetPlatformIncrementList', { params: params, ...config }) }
export const deletePlatformIncrementBatch = (params, config = {}) => { return request.get(apiPrefix + 'DeletePlatformIncrementBatchAsync', { params: params, ...config }) }

export const importOrder_TGC = (params, config = {}) => { return request.post(apiPrefix + 'ImportOrder_TGCAsync', params, config) }
export const getOrder_TGCList = (params, config = {}) => { return request.get(apiPrefix + 'GetOrder_TGCList', { params: params, ...config }) }
export const exportOrder_TGCAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportOrder_TGCAsync', params, config) }

export const importhPinPaiNewEnjoyAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImporthPinPaiNewEnjoyAsync', params, config) }
export const getPinPaiNewEnjoyList = (params, config = {}) => { return request.get(apiPrefix + 'GetPinPaiNewEnjoyList', { params: params, ...config }) }
export const deletePinPaiNewEnjoyAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeletePinPaiNewEnjoyAsync', { params: params, ...config }) }

export const importWeChatOrderManageAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportWeChatOrderManageAsync', params, config) }
export const getWeChatOrderManageList = (params, config = {}) => { return request.post(apiPrefix + 'GetWeChatOrderManageList', params, config) }

//淘系运营费用淘宝品牌新享预估文件导入
export const importhPinPaiNewEnjoy_TaoBaoAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImporthPinPaiNewEnjoy_TaoBaoAsync', params, config) }

//日报数据维护-快手服务费率-导入
export const importksServiceRateAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportksServiceRateAsync', params, config) }

//日报数据维护-快手服务费率-查询
export const getksServiceRateList = (params, config = {}) => { return request.get(apiPrefix + 'GetksServiceRateList', { params, ...config }) }

//日报数据维护-快手服务费率-新增/编辑
export const addorEditksServiceRate = (params, config = {}) => { return request.post(apiPrefix + 'AddorEditksServiceRate', params, config) }

//日报数据维护-快手服务费率-删除
export const deleteksServiceRateBatchAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteksServiceRateBatchAsync', params, config) }

//日报数据维护-大马美甲商品加工费-导入
export const importDmProductAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportDmProductAsync', params, config) }

//日报数据维护-大马美甲商品加工费-查询
export const getDmProductList = (params, config = {}) => { return request.get(apiPrefix + 'GetDmProductList', { params, ...config }) }

//日报数据维护-大马美甲商品加工费-新增/编辑
export const addorEditksDmProduct = (params, config = {}) => { return request.post(apiPrefix + 'AddorEditksDmProduct', params, config) }

//日报数据维护-大马美甲商品加工费-删除
export const deleteDmProductBatchAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteDmProductBatchAsync', params, config) }

//查询所有平台商务日报
export const getBusinessOrderDayReport_AllList = (params, config = {}) => { return request.post(apiPrefix + 'GetBusinessOrderDayReport_AllList', params, config) }

//快手商务订单数据文件导入
export const importBusinessOrderInfoAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportBusinessOrderInfoAsync', params, config) }

//快手/视频号达人管理导入
export const importMasterManage_KsWeChartAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportMasterManage_KsWeChartAsync', params, config) }

//编辑或新增快手/视频号达人管理
export const addorEditMasterManage_KsWeChart = (params, config = {}) => { return request.post(apiPrefix + 'AddorEditMasterManage_KsWeChart', params, config) }

//快手/视频号达人管理删除
export const deleteMasterManage_KsWeChartAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteMasterManage_KsWeChartAsync', params, config) }

//快手/视频号达人管理查询
export const getMasterManage_KsWeChartList = (params, config = {}) => { return request.get(apiPrefix + 'GetMasterManage_KsWeChartList', { params, ...config }) }

//导入达人商务关系
export const importProductDayReportWiseManBzRef = (params, config = {}) => { return request.post(apiPrefix + 'ImportProductDayReportWiseManBzRef', params, config) }

//一键更改商务人员
export const oneKeyUpdateBusinessMan = (params, config = {}) => { return request.post(apiPrefix + 'OneKeyUpdateBusinessMan', params, config) }

//保存抖音达人商务关系
export const saveProductWiseManBzRef = (params, config = {}) => { return request.post(apiPrefix + 'SaveProductWiseManBzRef', params, config) }

//查询达人商务关系
export const getBusinessWiseManList = (params, config = {}) => { return request.post(apiPrefix + 'GetBusinessWiseManList', params, config) }

//导出商务达人关系
export const exportWiseManExcelAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportWiseManExcelAsync', params, config) }

//查询快手商务日报
export const getBusinessOrderDayReport_KWaiShopList = (params, config = {}) => { return request.post(apiPrefix + 'GetBusinessOrderDayReport_KWaiShopList', params, config) }

//商务BD业绩统计-商务业绩统计趋势图
export const getBusinessOrderDayReport_AllChartList = (params, config = {}) => { return request.get(apiPrefix + 'GetBusinessOrderDayReport_AllChartList', { params, ...config }) }

//日报数据维护-品牌管理-品牌店铺-查询
export const getBrandShopList = (params, config = {}) => { return request.get(apiPrefix + 'GetBrandShopList', { params, ...config }) }

//日报数据维护-品牌管理-品牌店铺-导入
export const importBrandShopAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportBrandShopAsync', params, config) }

//日报数据维护-品牌管理-品牌店铺-删除
export const deleteBrandShopAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteBrandShopAsync', params, config) }

//日报数据维护-品牌管理-品牌店铺-新增/编辑
export const addorEditBrandShop = (params, config = {}) => { return request.post(apiPrefix + 'AddorEditBrandShop', params, config) }

//日报数据维护-品牌管理-品牌店铺-导出
export const exportBrandShopList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportBrandShopList', params, config) }

//日报数据维护-品牌管理-品牌管理费用-查询
export const getBrandFeeList = (params, config = {}) => { return request.get(apiPrefix + 'GetBrandFeeList', { params, ...config }) }

//日报数据维护-品牌管理-品牌管理费用-导入
export const importBrandFeeAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportBrandFeeAsync', params, config) }

//日报数据维护-品牌管理-品牌管理费用-删除
export const deleteBrandFeeAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteBrandFeeAsync', params, config) }

//日报数据维护-品牌管理-品牌管理费用-新增/编辑
export const addorEditBrandFee = (params, config = {}) => { return request.post(apiPrefix + 'AddorEditBrandFee', params, config) }

//日报数据维护-品牌管理-品牌管理费用-导出
export const exportBrandFeeList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportBrandFeeList', params, config) }

//日报数据维护-品牌管理-品牌管理费用-查询
export const getBrandSpecFrightFeeAvgList = (params, config = {}) => { return request.get(apiPrefix + 'GetBrandSpecFrightFeeAvgList', { params, ...config }) }

//日报数据维护-品牌管理-品牌管理费用-删除
export const deleteBrandSpecFrightFeeAvgAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteBrandSpecFrightFeeAvgAsync', params, config) }

//日报数据维护-品牌管理-品牌管理费用-新增/编辑
export const addorEditBrandSpecFrightFeeAvg = (params, config = {}) => { return request.post(apiPrefix + 'AddorEditBrandSpecFrightFeeAvg', params, config) }

//品牌组合装编码-查询
export const getBrandCombGoodsList = (params, config = {}) => { return request.post(apiPrefix + 'GetBrandCombGoodsList', params, config) }

//品牌组合装编码-导入
export const importBrandCombGoodsAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportBrandCombGoodsAsync', params, config) }

//品牌组合装编码-删除
export const deleteBrandCombGoodsAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteBrandCombGoodsAsync', params, config) }

//品牌组合装编码-新增/编辑
export const addorEditBrandCombGoods = (params, config = {}) => { return request.post(apiPrefix + 'AddorEditBrandCombGoods', params, config) }
export const exportBrandCombGoods = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportBrandCombGoods', params, config) }

//抖音爆款保护-查询
export const getExplosiveProtectionDyAsyncList = (params, config = {}) => { return request.get(apiPrefix + 'GetExplosiveProtectionDyAsyncList', { params, ...config }) }

//抖音爆款保护-导入
export const importExplosiveProtectionDyAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportExplosiveProtectionDyAsync', params, config) }

//抖音爆款保护-删除
export const deleteExplosiveProtectionDyAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteExplosiveProtectionDyAsync', params, config) }

//抖音爆款保护-更新
export const updateExplosiveProtectionDyAsync = (params, config = {}) => { return request.post(apiPrefix + 'UpdateExplosiveProtectionDyAsync', params, config) }

//抖音爆款保护-导出
export const exportExplosiveProtectionDy = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportExplosiveProtectionDy', params, config) }

//抖音爆款保护-编辑品牌
export const editExplosiveProtectionDyBand = (params, config = {}) => { return request.post(apiPrefix + 'EditExplosiveProtectionDyBand', params, config) }

//抖音运营费用-电商返佣-查询
export const getDianShangFanYong_DyList = (params, config = {}) => { return request.get(apiPrefix + 'GetDianShangFanYong_DyList', { params, ...config }) }

//抖音运营费用-电商返佣-导入
export const importDianShangFanYong_Dysync = (params, config = {}) => { return request.post(apiPrefix + 'ImportDianShangFanYong_Dysync', params, config) }

//抖音运营费用-电商返佣-删除
export const deleteDianShangFanYong_DyBatchAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteDianShangFanYong_DyBatchAsync', { params, ...config }) }

//抖音运营费用-电商返佣-导出
export const exportDianShangFanYong_DyList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportDianShangFanYong_DyList', { params, ...config }) }

//淘系运营费用-万象台优惠券-查询
export const getWanXiangTaiYouHui_TxList = (params, config = {}) => { return request.get(apiPrefix + 'GetWanXiangTaiYouHui_TxList', { params, ...config }) }

//淘系运营费用-万象台优惠券-导入
export const importWanXiangTaiYouHui_Txsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportWanXiangTaiYouHui_Txsync', params, config) }

//淘系运营费用-万象台优惠券-删除
export const deleteWanXiangTaiYouHui_TxBatchAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteWanXiangTaiYouHui_TxBatchAsync', { params, ...config }) }

//淘系运营费用-万象台优惠券-导出
export const exportWanXiangTaiYouHui_TxList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportWanXiangTaiYouHui_TxList', { params, ...config }) }

//视频号运营费用-商品推广-查询
export const getshangPinTuiGuang_WeChatList = (params, config = {}) => { return request.get(apiPrefix + 'GetshangPinTuiGuang_WeChatList', { params, ...config }) }

//视频号运营费用-商品推广-导入
export const importshangPinTuiGuang_WeChatsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportshangPinTuiGuang_WeChatsync', params, config) }

//视频号运营费用-商品推广-删除
export const deleteshangPinTuiGuang_WeChatBatchAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteshangPinTuiGuang_WeChatBatchAsync', { params, ...config }) }

//视频号运营费用-商品推广-导出
export const exportshangPinTuiGuang_WeChatList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportshangPinTuiGuang_WeChatList', { params, ...config }) }

//视频号运营费用-直播推广-查询
export const getzhiBoTuiGuang_WeChatList = (params, config = {}) => { return request.get(apiPrefix + 'GetzhiBoTuiGuang_WeChatList', { params, ...config }) }

//视频号运营费用-直播推广-导入
export const importzhiBoTuiGuang_WeChatsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportzhiBoTuiGuang_WeChatsync', params, config) }

//视频号运营费用-直播推广-删除
export const deletezhiBoTuiGuang_WeChatBatchAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeletezhiBoTuiGuang_WeChatBatchAsync', { params, ...config }) }

//视频号运营费用-直播推广-导出
export const exportzhiBoTuiGuang_WeChatList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportzhiBoTuiGuang_WeChatList', { params, ...config }) }

//视频号运营费用-推广流水-导入
export const importTuiGuangLiuShui_WeChatsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportTuiGuangLiuShui_WeChatsync', params, config) }

//视频号运营费用-推广流水-删除
export const deleteTuiGuangLiuShui_WeChatBatchAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteTuiGuangLiuShui_WeChatBatchAsync', { params, ...config }) }

//视频号运营费用-推广流水-查询
export const getTuiGuangLiuShui_WeChatList = (params, config = {}) => { return request.get(apiPrefix + 'GetTuiGuangLiuShui_WeChatList', { params, ...config }) }

//视频号运营费用-推广流水-导出
export const exportTuiGuangLiuShui_WeChatList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportTuiGuangLiuShui_WeChatList', { params, ...config }) }

//阿里巴巴运营费用-严选订单处理
export const importFxSupplyOrdersAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportFxSupplyOrdersAsync', params, config) }
export const getFxSupplyOrdersList = (params, config = {}) => { return request.get(apiPrefix + 'GetFxSupplyOrdersList', { params: params, ...config }) }
export const deleteFxSupplyOrdersAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteFxSupplyOrdersAsync', { params: params, ...config }) }
export const exportFxSupplyOrders = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportFxSupplyOrders', params, config) }
export const updateFxSalesFxSupplyOrders = (params, config = {}) => { return request.get(apiPrefix + 'UpdateFxSalesFxSupplyOrders', { params: params, ...config }) }
export const pageCodeSalesThemeAnalysisEmptyCodeAsync = (params, config = {}) => { return request.get(apiPrefix + 'PageCodeSalesThemeAnalysisEmptyCodeAsync', { params: params, ...config }) }

//加工费编辑
export const editCodeSalesThemeAnalysisEmptyCode = (params, config) => { return request.post(apiPrefix + 'EditCodeSalesThemeAnalysisEmptyCode', params, config) }

export const importCodeSalesThemeAnalysisEmptyCodeAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportCodeSalesThemeAnalysisEmptyCodeAsync', params, config) }


export const exportCodeSalesThemeAnalysisEmptyCodeAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportCodeSalesThemeAnalysisEmptyCodeAsync', params, config) }
export const updateCodeSalesThemeAnalysisEmptyCodeAsync = (params, config = {}) => { return request.get(apiPrefix + 'UpdateCodeSalesThemeAnalysisEmptyCodeAsync', { params: params, ...config }) }

//特别ID快递费-导入
export const importSpecProCodeExpressFee = (params, config = {}) => { return request.post(apiPrefix + 'ImportSpecProCodeExpressFee', params, config) }

//特别ID快递费-查询
export const getSpecProCodeExpressFee = (params, config = {}) => { return request.post(apiPrefix + 'GetSpecProCodeExpressFee', params, config) }

//特别ID快递费-新增/编辑
export const editSpecProCodeExpressFee = (params, config = {}) => { return request.post(apiPrefix + 'EditSpecProCodeExpressFee', params, config) }
//特别ID快递费-删除
export const delSpecProCodeExpressFee = (params, config = {}) => { return request.get(apiPrefix + 'DelSpecProCodeExpressFee', { params: params, ...config }) }
export const exportSpecProCode = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportSpecProCode', params, config) }

//淘工厂广告费验算-导入
export const importSignUpRecordAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportSignUpRecordAsync', params, config) }

//淘工厂广告费验算-查询
export const getSignUpRecordList = (params, config = {}) => { return request.get(apiPrefix + 'GetSignUpRecordList', { params: params, ...config }) }

//淘工厂日报-编辑比例
export const editSignUpRate = (params, config = {}) => { return request.get(apiPrefix + 'EditSignUpRate', { params: params, ...config }) }

//淘工厂日报-编辑比例回显
export const getSignUpRate = (params, config = {}) => { return request.get(apiPrefix + 'GetSignUpRate', { params: params, ...config }) }

//编码利润
export const getProductReportGoodsProfitTreePageList = (params, config) => { return request.post(apiPrefix + 'GetProductReportGoodsProfitTreePageList', params, config) }

export const importGoodsChuZi_GoodsPaysync = (params, config = {}) => { return request.post(apiPrefix + 'ImportGoodsChuZi_GoodsPaysync', params, config) }
export const getGoodsChuZi_GoodsPayList = (params, config = {}) => { return request.get(apiPrefix + 'GetGoodsChuZi_GoodsPayList', { params: params, ...config }) }
export const deleteGoodsChuZi_GoodsPayBatchAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteGoodsChuZi_GoodsPayBatchAsync', { params: params, ...config }) }
export const exportGoodsChuZi_GoodsPayList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + `ExportGoodsChuZi_GoodsPayList`, { params, ...config }) }



//运营提成-查询
export const getyyTiChengList = (params, config = {}) => { return request.get(apiPrefix + 'GetyyTiChengList', { params, ...config }) }

//运营提成-导入
export const importyyTiChengsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportyyTiChengsync', params, config) }

//运营提成-删除
export const deleteyyTiChengAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteyyTiChengAsync', { params, ...config }) }

//运营提成-导出
export const exportyyTiChengList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportyyTiChengList', { params, ...config }) }

//下架记录-查询
export const getProfit6TakeOffLoss = (params, config = {}) => { return request.post(apiPrefix + 'GetProfit6TakeOffLoss', params, config) }
//平台天数设置
export const getInitPlatformLossDays = (params, config = {}) => { return request.post(apiPrefix + 'GetInitPlatformLossDays', params, config) }
//平台天数设置
export const initPlatformLossDays = (params, config = {}) => { return request.post(apiPrefix + 'InitPlatformLossDays', params, config) }
//报备
export const profit6TakeOffLossBaoBei = (params, config = {}) => { return request.post(apiPrefix + 'Profit6TakeOffLossBaoBei', params, config) }
//批量报备
export const batchProfit6TakeOffLossBaoBei = (params, config = {}) => { return request.post(apiPrefix + 'BatchProfit6TakeOffLossBaoBei', params, config) }

//导出
export const exportProfit6TakeOffLoss = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + `ExportProfit6TakeOffLoss`, params, config) }



//组长对应主管和平台负责人-查询
export const getyyPlatFormGroupList = (params, config = {}) => { return request.get(apiPrefix + 'GetyyPlatFormGroupList', { params, ...config }) }

//组长对应主管和平台负责人文件导入
export const importyyPlatFormGroupsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportyyPlatFormGroupsync', params, config) }

//组长对应主管和平台负责人删除
export const deleteyyPlatFormGroupAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteyyPlatFormGroupAsync', { params, ...config }) }

//组长对应主管和平台负责人-导出
export const exportyyPlatFormGroupList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportyyPlatFormGroupList', { params, ...config }) }

//京东运营人员业绩-获取京东店铺列表
export const getShop_JdList = (params, config = {}) => { return request.get(apiPrefix + 'GetShop_JdList', { params, ...config }) }

//京东运营人员业绩-店铺配置编辑或新增京东店铺信息
export const addorEditShop_JdData = (params, config = {}) => { return request.post(apiPrefix + 'AddorEditShop_JdData', params, config) }

export const orderDayRpt_OrderTypes = [
    { label: "普通订单", value: 1 },
    { label: "补发订单", value: 2 },
    { label: "分销Plus", value: 4 },
    { label: "换货订单", value: 8 },
    { label: "分销", value: 16 }
];

export const orderDayRpt_OrderTypes_fmtFunc = (t) => {
    let tempStr = '';
    orderDayRpt_OrderTypes.forEach(item => {
        if ((item.value & t) == item.value) {
            if (tempStr)
                tempStr += ',' + item.label;
            else
                tempStr = item.label;
        }
    });
    return tempStr;
}


export const orderDayRpt_OrderStatuss = [
    { label: "已发货", value: "已发货" },
    { label: "被拆分", value: "被拆分" },
    { label: "已取消", value: "已取消" },
    { label: "被合并", value: "被合并" },
    { label: "异常", value: "异常" },
    { label: "发货中(打单拣货)", value: "发货中(打单拣货)" },
    { label: "已付款待审核", value: "已付款待审核" },
    { label: "等供销商发货", value: "等供销商发货" },

];

//查询ID详细信息-AI用
export const getProductDetailAI = (params, config = {}) => { return request.get(apiPrefix + 'GetProductDetailAI', { params: params, ...config }) }





//日报数据维护-品牌管理-品牌仓库维护
export const GetBrandWareList = (params, config = {}) => { return request.post(apiPrefix + 'GetBrandWareList', params, config) }
export const AddBrandWare = (params, config = {}) => { return request.post(apiPrefix + 'AddBrandWare', params, config) }
export const DeleteBrandWare = (params, config = {}) => { return request.get(apiPrefix + 'DeleteBrandWare', { params: params, ...config }) }

//特别重量快递费-查询
export const getSpecWeightExpressFee = (params, config = {}) => { return request.post(apiPrefix + 'GetSpecWeightExpressFee', params, config) }

//特别重量快递费-新增/编辑
export const editSpecWeightExpressFee = (params, config = {}) => { return request.post(apiPrefix + 'EditSpecWeightExpressFee', params, config) }

export const delSpeWeightExpressFee = (params, config = {}) => { return request.get(apiPrefix + 'DelSpeWeightExpressFee', { params: params, ...config }) }


//临时仓库-查询
export const getTempWareHouseSalary = (params, config = {}) => { return request.post(apiPrefix + 'GetTempWareHouseSalary', params, config) }

//临时仓库-新增/编辑
export const editTempWareHouseSalary = (params, config = {}) => { return request.post(apiPrefix + 'EditTempWareHouseSalary', params, config) }
//临时仓库薪资
export const delTempWareHouseSalary = (params, config = {}) => { return request.get(apiPrefix + 'DelTempWareHouseSalary', { params: params, ...config }) }
//时仓库薪资导出
export const ExportTempWareHouseSalary = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportTempWareHouseSalary', params, config) }


//保费扣除导入
const apiPrefixImport = `${process.env.VUE_APP_BASE_API_BookKeeper}/Import/`
export const importPremiumDeduction = (params, config = {}) => { return request.post(apiPrefixImport + 'ImportPremiumDeduction', params, config) }
//保费扣除查询
export const getPremiumDeductionPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetPremiumDeductionPageList', { params: params, ...config }) }
//保费扣除导出
export const exportPremiumDeduction = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPremiumDeduction', params, config) }


//日报数据维护-运营工资-品牌店铺-查询
export const getyyBrandShopList = (params, config = {}) => { return request.get(apiPrefix + 'GetyyBrandShopList', { params, ...config }) }

//日报数据维护-运营工资-品牌店铺-导入
export const importyyBrandShopAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportyyBrandShopAsync', params, config) }

//日报数据维护-运营工资-品牌店铺-删除
export const deleteyyBrandShopAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteyyBrandShopAsync', params, config) }

//日报数据维护-运营工资-品牌店铺-新增/编辑
export const addorEdityyBrandShop = (params, config = {}) => { return request.post(apiPrefix + 'AddorEdityyBrandShop', params, config) }

//日报数据维护-运营工资-品牌工资-查询
export const getyyBrandSalaryList = (params, config = {}) => { return request.get(apiPrefix + 'GetyyBrandSalaryList', { params, ...config }) }

//日报数据维护-运营工资-品牌工资-导入
export const importyyBrandSalaryAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportyyBrandSalaryAsync', params, config) }

//日报数据维护-运营工资-品牌工资-删除
export const deleteyyBrandSalaryAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteyyBrandSalaryAsync', params, config) }

//日报数据维护-运营工资-品牌工资-新增/编辑
export const addorEdityyBrandSalary = (params, config = {}) => { return request.post(apiPrefix + 'AddorEdityyBrandSalary', params, config) }

//日报数据维护-包装费-纸管木条包装-导入
export const importPaperTubeAndWoodenStripPackaging = (params, config = {}) => { return request.post(apiPrefix + 'ImportPaperTubeAndWoodenStripPackaging', params, config) }

//日报数据维护-包装费-纸管木条包装-查询
export const getPaperTubeAndWoodenStripPackagingList = (params, config = {}) => { return request.get(apiPrefix + 'GetPaperTubeAndWoodenStripPackagingList', { params, ...config }) }

//日报数据维护-包装-费纸管木条包装-新增/编辑
export const addorEditPaperTubeAndWoodenStripPackaging = (params, config = {}) => { return request.post(apiPrefix + 'AddorEditPaperTubeAndWoodenStripPackaging', params, config) }

//日报数据维护-包装费-纸管木条包装-删除
export const deletePaperTubeAndWoodenStripPackaging = (params, config = {}) => { return request.post(apiPrefix + 'DeletePaperTubeAndWoodenStripPackaging', params, config) }
//日报数据维护-包装费-纸管木条包装-导出
export const exportPaperTubeAndWoodenStripPackaging = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPaperTubeAndWoodenStripPackaging', params, config) }


//日报数据维护-包装费-云仓包装出仓-导入
export const importCloudWarehousePackagingForOutboundShipment = (params, config = {}) => { return request.post(apiPrefix + 'ImportCloudWarehousePackagingForOutboundShipment', params, config) }

//日报数据维护-包装费-云仓包装出仓-查询
export const getCloudWarehousePackagingForOutboundShipmentList = (params, config = {}) => { return request.get(apiPrefix + 'GetCloudWarehousePackagingForOutboundShipmentList', { params, ...config }) }

//日报数据维护-包装-云仓包装出仓-新增/编辑
export const addorEditCloudWarehousePackagingForOutboundShipment = (params, config = {}) => { return request.post(apiPrefix + 'AddorEditCloudWarehousePackagingForOutboundShipment', params, config) }

//日报数据维护-包装费-云仓包装出仓-删除
export const deleteCloudWarehousePackagingForOutboundShipment = (params, config = {}) => { return request.post(apiPrefix + 'DeleteCloudWarehousePackagingForOutboundShipment', params, config) }
//日报数据维护-包装费-云仓包装出仓-导出
export const exportCloudWarehousePackagingForOutboundShipment = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportCloudWarehousePackagingForOutboundShipment', params, config) }

//新版账单费用-抖音账单费用-订单管理界面导入
export const importOrderManagement = (params, config = {}) => { return request.post(apiPrefixImport + 'ImportOrderManagement', params, config) }
//新版账单费用-抖音账单费用-订单管理界面查询
export const getOrderManagementPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderManagementPageList', { params, ...config }) }
//新版账单费用-抖音账单费用-订单管理界面导出
export const exportOrderManagement = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportOrderManagement', params, config) }

// 新版账单费用-抖音账单费用-侨宝特殊单平台补贴配置查询
export const getQiaobaoSpecialSinglePlatformSubsidyShop = (params, config = {}) => { return request.post(apiPrefix + 'GetQiaobaoSpecialSinglePlatformSubsidyShop', params, config) }
// 新版账单费用-抖音账单费用-侨宝特殊单平台补贴配置新增编辑
export const addQiaobaoSpecialSinglePlatformSubsidyShop = (params, config = {}) => { return request.post(apiPrefix + 'AddQiaobaoSpecialSinglePlatformSubsidyShop', params, config) }
// 新版账单费用-抖音账单费用-侨宝特殊单平台补贴配置删除
export const deleteQiaobaoSpecialSinglePlatformSubsidyShop = (params, config = {}) => { return request.post(apiPrefix + 'DeleteQiaobaoSpecialSinglePlatformSubsidyShop', params, config) }
//新版账单费用-抖音账单费用-侨宝特殊单平台补贴配置导入
export const importQiaobaoSpecialSinglePlatformSubsidyShop = (params, config = {}) => { return request.post(apiPrefixImport + 'ImportQiaobaoSpecialSinglePlatformSubsidyShop', params, config) }
//新版账单费用-抖音账单费用-侨宝特殊单平台补贴查询
export const getQiaobaoSpecialSinglePlatformSubsidy = (params, config = {}) => { return request.post(apiPrefix + 'GetQiaobaoSpecialSinglePlatformSubsidy', params, config) }
//新版账单费用-抖音账单费用-侨宝特殊单平台补贴导出
export const exportQiaobaoSpecialSinglePlatformSubsidy = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportQiaobaoSpecialSinglePlatformSubsidy', params, config) }
//新版账单费用-抖音账单费用-侨宝特殊单平台补贴导入
export const importQiaobaoSpecialSinglePlatformSubsidy = (params, config = {}) => { return request.post(apiPrefixImport + 'ImportQiaobaoSpecialSinglePlatformSubsidy', params, config) }
//侨宝特殊单平台补贴-店铺配置的店铺
export const GetDyShopList = (params, config = {}) => { return request.post(apiPrefix + 'GetDyShopList', params, config) }

//日报数据维护-包装费-仓库包装维护-导入
export const ImportWarehousePackagingMaintenance = (params, config = {}) => { return request.post(apiPrefixImport + 'ImportWarehousePackagingMaintenance', params, config) }

//日报数据维护-包装费-仓库包装维护-查询
export const GetWarehousePackagingMaintenanceList = (params, config = {}) => { return request.get(apiPrefix + 'GetWarehousePackagingMaintenanceList', { params, ...config }) }

//日报数据维护-包装-仓库包装维护-新增/编辑
export const AddEditWarehousePackagingMaintenance = (params, config = {}) => { return request.post(apiPrefix + 'AddEditWarehousePackagingMaintenance', params, config) }

//日报数据维护-包装费-仓库包装维护-删除
export const DeleteWarehousePackingMaintenance = (params, config = {}) => { return request.post(apiPrefix + 'DeleteWarehousePackingMaintenance', params, config) }
//日报数据维护-包装费-仓库包装维护-导出
export const ExportWarehousePackingMaintenance = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportWarehousePackingMaintenance', params, config) }

//视频号日报-商务达人-达人管理-导入
export const ImportVideoExpertManagement = (params, config = {}) => { return request.post(apiPrefixImport + 'ImportVideoExpertManagement', params, config) }

//视频号日报-商务达人-达人管理-查询
export const GetVideoExpertManagementList = (params, config = {}) => { return request.post(apiPrefix + 'GetVideoExpertManagementList', params, config) }

//视频号日报-商务达人-达人管理-新增/编辑
export const AddEditVideoExpertManagement = (params, config = {}) => { return request.post(apiPrefix + 'AddEditVideoExpertManagement', params, config) }

//视频号日报-商务达人-达人管理-删除
export const DeleteVideoExpertManagement = (params, config = {}) => { return request.post(apiPrefix + 'DeleteVideoExpertManagement', params, config) }

//视频号日报-商务达人-查询
export const GetVideoBusinessExpertList = (params, config = {}) => { return request.post(apiPrefix + 'GetVideoBusinessExpertList', params, config) }

//视频号日报-商务达人-新增/编辑
export const AddEditVideoBusinessExpert = (params, config = {}) => { return request.post(apiPrefix + 'AddEditVideoBusinessExpert', params, config) }

//视频号日报-商务达人-批量编辑
export const BatchEditVideoBusinessExpert = (params, config = {}) => { return request.post(apiPrefix + 'BatchEditVideoBusinessExpert', params, config) }
//视频号日报-商务达人-导出
export const ExportVideoBusinessExpert = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportVideoBusinessExpert', params, config) }
//视频号日报-商务达人-导入
export const ImportVideoBusinessExpert = (params, config = {}) => { return request.post(apiPrefixImport + 'ImportVideoBusinessExpert', params, config) }

//视频号日报-商务日报-查询
export const GetVideoBusinessDayReportList = (params, config = {}) => { return request.post(apiPrefix + 'GetVideoBusinessDayReportList', params, config) }
//视频号日报-商务日报-导入
export const ImportVideoBusinessDayReport = (params, config = {}) => { return request.post(apiPrefixImport + 'ImportVideoBusinessDayReport', params, config) }
