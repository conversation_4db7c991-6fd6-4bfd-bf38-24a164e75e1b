import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/OutSource/`


//外包客服/店铺管理——获取外包数据
export const getOutSourceStoreList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetOutSourceStoreList', params, config)
  }

//外包客服/店铺管理——修改外包数据
// UpdateOutSourceStoreInfo
export const updateOutSourceStoreInfo = (params, config = {}) => { return request.post(apiPrefix + 'UpdateOutSourceStoreInfo', params, config ) }


//外包客服/店铺管理——删除外包数据
export const deleteOutSourceStore = (params, config = {}) => { return request.get(apiPrefix + 'DeleteOutSourceStore', { params: params, ...config }) }

//外包客服/店铺管理——添加外包数据
export const addOutSourceStoreInfo = (params, config = {}) => { return request.post(apiPrefix + 'AddOutSourceStoreInfo', params, config) }

//外包客服/店铺管理——导入数据
export const importOutSourceStoreAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportOutSourceStoreAsync', params, config) }

//外包客服，店铺管理，不分页店铺名称List
export const getOSStoreNameList = (params, config = {}) => { return request.get(apiPrefix + 'GetOSStoreNameList', { params: params, ...config }) }
