
   import request from '@/utils/request'
   const apiPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Wages/`

   export const pageGropuWages = (params, config = {}) => {return request.get(apiPrefix + 'PageGropuWagesAsync', { params: params, ...config })}
   export const pageMGCommission = (params, config = {}) => {return request.get(apiPrefix + 'PageMGCommissionAsync', { params: params, ...config })}

   export const pageXMTCommission = (params, config = {}) => {return request.get(apiPrefix + 'PageXMTCommissionAsync', { params: params, ...config })}
   export const exportXMTCommission = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + `ExportXMTCommission`, params, config ) }

   export const pageWagesCornerWall = (params, config = {}) => {return request.get(apiPrefix + 'PageWagesCornerWallAsync', { params: params, ...config })}
   export const pageMachinefee = (params, config = {}) => {return request.get(apiPrefix + 'PageMachinefeeAsync', { params: params, ...config })}

   export const pageCGCommission = (params, config = {}) => {return request.get(apiPrefix + 'PageCGCommissionAsync', { params: params, ...config })}
   export const exportCGCommission = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + `ExportCGCommission`, params, config ) }

   export const pageWagesCompute = (params, config = {}) => {return request.get(apiPrefix + 'PageWagesComputeAsync', { params: params, ...config })}
   export const pageWagesMonthReport = (params, config = {}) => {return request.get(apiPrefix + 'PageWagesMonthReportAsync', { params: params, ...config })}
   export const importCommissionCGAsync = (params, config = {}) => {return request.post(apiPrefix + 'ImportCommissionCGAsync', params, config)}
   export const computWages = (params, config = {}) => {return request.post(apiPrefix + 'ComputWagesAsync',  params, config)}

   export const importGropuWages = (params, config = {}) => {return request.post(apiPrefix + 'ImportGropuWagesAsync', params, config)}
   export const importWagesCornerWall = (params, config = {}) => {return request.post(apiPrefix + 'ImportWagesCornerWallAsync', params, config)}
   export const importMGCommission = (params, config = {}) => {return request.post(apiPrefix + 'ImportMGCommissionAsync', params, config)}
   export const importXMTCommission = (params, config = {}) => {return request.post(apiPrefix + 'ImportXMTCommissionAsync', params, config)}
   export const importMachinefee = (params, config = {}) => {return request.post(apiPrefix + 'ImportMachinefeeAsync', params, config)}

   
   export const exportWagesComputeAsync = (params, config = {responseType: 'blob'}) => {return request.get(apiPrefix + `ExportWagesComputeAsync`,  {params, ...config}) }

   const MonthApiPrefix = `${process.env.VUE_APP_BASE_API_MonthBookKeeper}/FinancialReport/`
   export const exportXMTNotCalculated = (params, config = {responseType: 'blob'}) => {return request.post(MonthApiPrefix + `ExportXMTNotCalculatedAsync`, params, config ) }  

