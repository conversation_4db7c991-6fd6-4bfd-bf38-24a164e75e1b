import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Express}/Freight/`

export const pageFreightRepeat = (params, config = {}) => {return request.get(apiPrefix + 'PageFreightRepeatAsync', { params: params, ...config })}
export const batchUpdateRepeatExpress = (params, config = {}) => {return request.post(apiPrefix + 'BatchUpdateRepeatExpressAsync',  params, config)}
export const pageFreightBill = (params, config = {}) => {return request.get(apiPrefix + 'PageFreightBillAsync', { params: params, ...config })}


export const exportFreightRepeat = (params, config = {responseType: 'blob'}) => {return request.get(apiPrefix + 'ExportFreightRepeatAsync',  { params: params, ...config })}
