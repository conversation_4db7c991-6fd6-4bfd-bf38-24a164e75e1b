import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/LogisticsAnalyse/`


//获取可用过滤数据源
export const FilterDatas = (params, config = {}) => { return request.get(apiPrefix + 'FilterDatas', { params: params, ...config }) }

//分页 获取订单滞留信息汇总
export const PageCalcOrderLogisticsDelay = (params, config = {}) => { return request.post(apiPrefix + 'PageCalcOrderLogisticsDelay', params, config) }

//分页 获取订单滞留信息明细
export const PageCalcOrderLogisticsDelayDetail = (params, config = {}) => { return request.post(apiPrefix + 'PageCalcOrderLogisticsDelayDetail', params, config) }

//导出 获取订单滞留信息明细
export const exportCalcOrderLogisticsDelayDetail = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportCalcOrderLogisticsDelayDetail', params, config) }
