<template>
<div style="width:100%;height:100%;">
    <vxe-toolbar v-if="showToolbar" ref="xToolbar" custom class="vxetoolbar20221212">
          <template #buttons>
            <slot name="tbHeader" />
          </template>
    </vxe-toolbar>
    <vxe-table :row-config="{isHover: true}" :size="size"
    resizable ref="xTable" :loading="loading"
    :column-config="{maxFixedSize: 300, maxFixedSize: 20,
	resizable: true,}"
    highlight-hover-row
          highlight-current-row
    :tree-config="{transform: true, ...treeProp}"
    :data="tableData"
    :show-header-overflow="showheaderoverflow"
    :sort-config="{sortMethod: customSortMethod}"
    :scroll-y="{gt:100}"
    :scroll-x="{gt:100}"
     show-overflow
     :show-footer="showsummary"
     :footer-method="footerMethod"
    class="vxetable202212161323 mytable-scrollbar20221212"
    :cell-style="cellStyleFun"
    height="100%"
    stripe
    :border="border"
    :custom-config="{storage: true,restoreStore:restoreStore,updateStore:updateStore}"
    :id="id"
    :header-cell-class-name="headerCellClassName"
    @cell-click="cellClick"
    :edit-config="editconfig"
    >
    <!-- border show-overflow-->

        <vxe-column v-if="hasSeq" type="seq" width="60" fixed="left"></vxe-column>

        <slot name="left" />

        <template v-for="(col,colIndex) in tableCols">

                <template v-if="!col.type">
                    <vxe-column :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.prop"
                    :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                    show-overflow :tree-node="!!col.treeNode? true:false"
                    :width="col.width"
                    :min-width="col.minwidth?col.minwidth:null"
                    :sortable="!!col.sortable"
                    :fixed="col.fixed?col.fixed:''"
                    :align="col.align?col.align:'left'"
                    v-if='(!col.permission||(col.permission&&checkPermission(col.permission)))'
                    >
                        <template #default="{ row }">
                            <slot :name="col.prop" v-bind:row="row">
                                <!-- <el-image  :src="col.formatter? col.formatter(row): row[col.prop]" ></el-image> -->
                                <span v-if="canJump(row, col)">
                                  <a @click="handleLinkClick(row, col)" style="color: blue; cursor: pointer;"> {{ (() => {
                                      if (col.formatter)
                                        return tonumfuc(col.formatter(row,that), col.label);
                                      else
                                        return tonumfuc(row[col.prop], col.label); })() }} </a>
                                </span>
                                <span v-else>{{col.formatter? tonumfuc(col.formatter(row), col.label): tonumfuc(row[col.prop], col.label)}}</span>
                            </slot>
                        </template>
                    </vxe-column>
                </template>

                <vxe-column show-header-overflow type="checkbox" width="40" v-else-if="col.type=='checkbox'"  fixed="left" :key="colIndex"></vxe-column>

                <vxe-column  v-else-if="col.type=='images'"
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow  :tree-node="!!col.treeNode? true:false"
                :width="col.width"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="col.align?col.align:'center'"
                >
                    <template #default="{ row }">

                        <template v-if=" row && !!row[col.prop] && row[col.prop].length>2">

                            <template v-if="row[col.prop][0]=='[' && JSON.parse(row[col.prop]).length>1">
                                <el-badge
                                    class="badgeimage20221212"
                                    :value="JSON.parse(row[col.prop]).length" style="margin-top:0px;margin-right:40px;">
                                    <el-image  :src="(row[col.prop][0]=='['?(formatImg(row[col.prop])[0].url):(formatImg(row[col.prop]) ? formatImg(row[col.prop])[0] : '' ) )"
                                    class="images20221212"
                                    :preview-src-list="(row[col.prop][0]=='['
                            ?(()=>{
                                let tempArray=JSON.parse(row[col.prop]);
                                let tempRltArr=[];
                                tempArray.forEach(x=>tempRltArr.push(x.url));
                                return tempRltArr
                            })()
                            :(()=>{
                                return [row[col.prop]]
                            })()  )">
                                    </el-image>
                                </el-badge>
                            </template>
                            <template v-else>
                                <el-image  :src="formatImg(row[col.prop])[0].url ? formatImg(row[col.prop])[0].url : formatImg(row[col.prop])[0]"

                                class="images20221212"
                                :preview-src-list="(row[col.prop][0]=='['
                            ?(()=>{
                                let tempArray=JSON.parse(row[col.prop]);
                                let tempRltArr=[];
                                tempArray.forEach(x=>tempRltArr.push(x.url));
                                return tempRltArr
                            })()
                            :(()=>{
                                return [row[col.prop]]
                            })()  )">
                                </el-image>
                            </template>
                        </template>

                    </template>
                </vxe-column>

                <vxe-column  v-else-if="col.type=='treeimages'"
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow  :tree-node="!!col.treeNode? true:false"
                :width="col.width"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="col.align?col.align:'center'"
                >
                    <template #default="{ row }">
                            <el-image  v-if="col.formatter"  :src="col.formatter? (formatImg(col.formatter(row)) ? formatImg(col.formatter(row))[0] : '') : formatImg(row[col.prop]) " :preview-src-list="[col.formatter? col.formatter(row): row[col.prop]]"></el-image>
                        </template>
                </vxe-column>

                <vxe-column  v-else-if="col.type=='imageClick'"
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow  :tree-node="!!col.treeNode? true:false"
                :width="col.width"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="col.align?col.align:'center'"
                >
                    <template #default="{ row }">
                        <el-badge v-if="row[col.prop] && row[col.prop][0] == '[' && JSON.parse(row[col.prop].length > 1 )" class="badgeimage20221212 imageClick" :value="row[col.prop] ? JSON.parse(row[col.prop]).length : ''" style="margin-top:0px;margin-right:40px;">
                            <el-image  :src="formatImg(row[col.prop])[0].url ? formatImg(row[col.prop])[0].url : formatImg(row[col.prop])[0]" @click=" col.handle(that,row)"></el-image>
                        </el-badge>
                        <el-image v-else-if="row[col.prop] && row[col.prop][0] == '[' && JSON.parse(row[col.prop].length == 1 )"  :src="formatImg(row[col.prop])[0].url ? formatImg(row[col.prop])[0].url : formatImg(row[col.prop])[0]" :fit="fit"></el-image>
                        <el-image v-else ></el-image>
                        </template>
                </vxe-column>

                <vxe-column  v-else-if="col.type=='files'"
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow  :tree-node="!!col.treeNode? true:false"
                :width="col.width"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="col.align?col.align:'center'"
                >
                    <template #default="{ row }">

                        <template v-if=" row && !!row[col.prop] && row[col.prop].length>2">

                            <template v-if=" JSON.parse(row[col.prop]).length>1">
                                <span style="color:blue;cursor:pointer;"  @click="downloadFiles(row[col.prop])">{{JSON.parse(row[col.prop]).length}}个文件</span>
                            </template>
                            <template v-else>
                                <span style="color:blue;cursor:pointer;"  @click="downloadFile(row[col.prop])">{{JSON.parse(row[col.prop])[0].name}}</span>
                            </template>
                        </template>

                    </template>
                </vxe-column>

                <vxe-column  v-else
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow  :tree-node="!!col.treeNode? true:false"
                :width="col.width"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="(col.type && (col.type=='color' || col.type=='split' || col.type=='images' || col.type=='image'))?'center':  (col.align?col.align:'left')"
                >
                    <template #default="scope">
                        <span v-if="col.type=='color' || col.type=='split'" >
                            |
                        </span>
                        <template  v-if="col.type==='button'">
                            <template v-for="(btn,btnIndex) in col.btnList" >
                                <el-link :key="btn.label" :style="''+ (btnIndex>0?'margin-left:5px;':'')"
                                v-if="(!btn.permission||(btn.permission&&checkPermission(btn.permission)))&&!(btn.hasOwnProperty('ishide')&&((typeof btn.ishide=='function'&& btn.ishide(that,scope.row)==true)||btn.ishide==true))"
                                :disabled="(btn.hasOwnProperty('display')&&((typeof btn.display=='function'&& btn.display(scope.row)==true)||btn.display==true))"
                                :type="((!!btn.type)? btn.type: 'primary')" :size="btn.size || size " :icon="btn.icon" @click="btn.handle(that,scope.row)">
                                    {{btn.label}}
                                </el-link>
                            </template>
                        </template>
                        <template  v-if="col.type==='treeButton'">
                            <template v-for="(btn,btnIndex) in col.btnList" >
                                <el-link :key="btn.label" :style="''+ (btnIndex>0?'margin-left:5px;':'')"
                                v-if="(!btn.permission||(btn.permission&&checkPermission(btn.permission)))&&!(btn.hasOwnProperty('ishide')&&((typeof btn.ishide=='function'&& btn.ishide(that,scope.row)==true)||btn.ishide==true))&&!(btn.hasOwnProperty('display')&&((typeof btn.display=='function'&& btn.display(scope.row)==true)||btn.display==true))"
                                :disabled="(btn.hasOwnProperty('display')&&((typeof btn.display=='function'&& btn.display(scope.row)==true)||btn.display==true))"
                                :type="((!!btn.type)? btn.type: 'primary')" :size="btn.size || size " :icon="btn.icon" @click="btn.handle(that,scope.row)">
                                    {{btn.label}}
                                </el-link>
                            </template>
                        </template>
                        <span v-if="col.type==='clickLink'" :style="col.style==null?'color:blue;cursor:pointer;':typeof(col.style)=='function'?col.style(that,scope.row,col,scope.row[col.prop]):column.style" @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">{{(col.formatter && col.formatter(scope.row)) || scope.row[col.prop]}}</span>
                        <el-button v-else-if="col.type=='click'"  type="text" @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">
                            {{(col.formatter && tonumfuc(col.formatter(scope.row), coll.label)) || tonumfuc(scope.row[col.prop], col.label)}}
                        </el-button>
                        <div v-else-if="col.type=='html'"  v-html="col.formatter? col.formatter(scope.row): scope.row[col.prop]  " @click="handleClick($event,scope.row[col.prop])"></div>
                        <el-switch v-else-if="col.type=='switch'" v-model="scope.row[col.prop]" @change='col.change && col.change(scope.row,that)'></el-switch>
                        <div v-else-if="col.type=='treeStar'" @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])" :style="(col.formatter(scope.row) != '') ? col.style : ''" >
                            <el-badge value="重" class="item" v-if="scope.row.isExitProvider==1" type="warning">
                                <span>{{ scope.row.providerName}}</span>
                            </el-badge>
                                <span v-else>{{ scope.row.providerName}}</span>
                        </div>
                        <div v-else-if="col.type=='treeStar1'" @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])" :style="col.style" >
                            <el-badge value="重" class="item" v-if="scope.row.isExitProvider==1" type="warning">
                                <span>{{ scope.row.providerName}}</span>
                            </el-badge>
                                <span v-else>{{ scope.row.providerName}}</span>
                        </div>
                        <div v-else-if="col.type=='changeColor'"  :style="{color: col.formatter(scope.row) ? 'red' : 'green'}" >
                          <span>{{ scope.row[col.prop] }}</span>
                        </div>
                        <div v-if="col.type=='xptooltip'">
                            <el-tooltip effect="dark"  placement="top-start">
                                <div slot="content">
                                    <div  v-for="(item,i) in scope.row[col.props]" :key="i">{{item.platformName}}:
                                        {{col.prop=='totalLastMonthSaleCount'?item.lastMonthSaleCount:
                                        col.prop=='totalLastMonthSaleAmount'?item.lastMonthSaleAmount:
                                        col.prop=='totalLastMonthProfitAmount'?item.lastMonthProfitAmount:
                                        col.prop=='totalLastMonthProfitRate'?item.lastMonthProfitRate:'' }}
                                        <br/></div>
                                </div>
                                <div class="textover" style="width: 80%;">{{ (col.formatter && col.formatter(scope.row)) || scope.row[col.prop] }}</div>
                            </el-tooltip>
                        </div>
                        <el-progress v-if="col.type==='progress'" :text-inside="true" :stroke-width="20" :percentage="Number(scope.row[col.prop])" :status="scope.row[col.prop]==100?'success':null"></el-progress>


                    </template>
                </vxe-column>

        </template>


        <slot name="right" />

    </vxe-table>

    <el-dialog :visible.sync="exportColumnsDialogProp.visible" width="300" draggable="true" title="导出选项"  v-dialogDrag>
        <el-row>
            <el-col :span="24" style="height:400px;overflow: auto;">
                <el-tree
                ref="exportColTree"
                :data="exportColumnsDialogProp.data"
                show-checkbox
                node-key="id"
                default-expand-all
                :default-checked-keys="exportColumnsDialogProp.colIds"
                :props="{
                    'label':'title'
                }"
                >
                </el-tree>

            </el-col>
            <el-col :span="24" style="text-align:right">
                <el-button type="primary" @click="setExportCols">确定</el-button>
                <el-button @click="exportColumnsDialogProp.visible=false;" >取消</el-button>
            </el-col>
        </el-row>

    </el-dialog>

</div>
</template>

<script>
    import { tonumfuc } from '@/utils/tonumqian.js'
    import { getTableColumnCache, setTableColumnCache,GetVxeTableColumnCacheAsync,SetVxeTableColumnCacheAsync } from '@/api/admin/business'
    import {matchImg} from '@/utils/getCols'
    import { SaveProductIdViewLog } from '@/api/operatemanage/PddChart'
    import { canJump, onJumpLink } from '@/utils/tools';
    export default {
        name:"vxetablebase",
        props: {
            cstmExportFunc:{type:Function,default:()=>{
                return null;
            }},
            treeProp: { type: Object, default: () => { return { rowField: 'id', parentField: 'parentId' } } },
            hasSeq: { type: Boolean, default: () => { return true } },
            // 表格数据
            tableData: { type: Array, default: () => [] },
            // 表格列配置
            tableCols: { type: Array, default: () => [] },
            isRemoteSort:{ type: Boolean, default: () => { return true } },
            id:{type:String,default:()=>{ return new Date().valueOf().toString()}},
            that:{type:Object,default:()=>{return null}},
            size:{type:String,default:()=>{return 'small'}},
            loading:{type:Boolean,default:()=>{return false;}},
            border:{type:Boolean | Object ,default:()=>{return 'default'}},
            showsummary: { type: Boolean, default: false },
            summaryarry: { type: Object, default: () => { } },
            showToolbar:{
                type:Boolean,
                default:()=>{ return true;}
            },
            resizable:{type:Boolean,default:()=>{return true}},
            showheaderoverflow: { type: String, default: '' },//ellipsis（只显示省略号）,title（并且显示为原生 title）,tooltip（并且显示为 tooltip 提示）
            showoverflow: { type: String, default: 'title' },//ellipsis（只显示省略号）,title（并且显示为原生 title）,tooltip（并且显示为 tooltip 提示）
            editconfig:{ type: Object, default: () => { return {  } } },
        },
        data() {
            return {
                tonumfuc,
                lastSortArgs:{
                    field:"",
                    order:"",
                },
                summarycolumns:[],
                exportColumnsDialogProp:{
                    visible:false,
                    data:[],
                    colIds:[],
                    colFields:[],
                    colNames:[],
                    mapFields:{},
                    isLoadMapFields:false
                }
            }
        },
        created(){
            this.$nextTick(() => {
              // 手动将表格和工具栏进行关联
              if(this.showToolbar)
                this.$refs.xTable.connect(this.$refs.xToolbar)
            })
        },
        async mounted(){
            this.$nextTick(() => {
              this.columns = this.$refs.xTable.getColumns()
            })
        },
        methods:{
            canJump,
            async handleLinkClick(row, column) {
              if (!this.canJump(row, column)) return;
              try {
                await onJumpLink(row[column.prop], column.prop);
              } catch (err) {
                this.$message.error('小昀工具箱不在线，请开启后使用！');
              }
            },
            handleClick(e, prop) {
                if (!prop) return
                if (!e.target.parentNode.innerHTML.includes('复') && !e.target.parentNode.innerHTML.includes('查 ')) return
                let res = JSON.parse(JSON.stringify(prop));
                if (res.length > 6) {
                    res = res.substring(0, 2) + '**' + res.substring(res.length - 2, res.length);
                }
                if (e.target.innerHTML == '复') {
                    var _this = this;
                    this.$copyText(prop).then(function (e) {
                        _this.$message({ message: "内容已复制到剪切板！", type: "success" });
                    }, function (e) {
                        _this.$message({ message: "抱歉，复制失败！", type: "warning" });
                    })
                    this.sendLog(prop, '复制宝贝ID', 'ERP')
                } else if (e.target.innerHTML == '查 ') {
                    if (e.target.parentNode.innerHTML.includes(res)) {
                        e.target.parentNode.innerHTML = e.target.parentNode.innerHTML.replace(res, prop)
                    }
                    this.sendLog(prop, '查看宝贝ID', 'ERP')
                } else {
                    if (res == e.target.innerHTML || prop == e.target.innerHTML) {
                        this.sendLog(prop, '打开链接', 'ERP')
                    }
                }
            },
            async sendLog(proCode, action, source) {
                await SaveProductIdViewLog({ proCode, action, source })
            },
            formatImg(img){
                return matchImg(img)
            },
            async restoreStore({id, type, storeData}){

                let resp=  await GetVxeTableColumnCacheAsync({tableId:id});
                let store =null;
                if(resp && resp.success && resp.data){
                    store = JSON.parse(resp.data);
                }
                if(store.fixedData){
                    this.tableCols.map((item)=>{
                        item.fixed = store.fixedData[item.prop]
                    })
                }

                return store??storeData;

            },
            async updateStore({id, type, storeData}){
                let newobj = {};
                let mergearr = [];

                this.tableCols.map((item)=>{
                    if(item.merge){
                        mergearr.push({
                            name:  item.prop,
                            value: item.cols[0]['prop']
                        });
                    }
                })

                this.$refs.xTable.getColumns().map((item)=>{

                    if(item.type){
                        return;
                    }
                    mergearr.map((itemm)=>{
                        if(item.field === itemm.value){
                            // item.fixed = itemm.value
                            newobj[itemm.name] = item.fixed
                        }
                    })
                    newobj[item.field]  = item.fixed;
                })
                storeData.fixedData = newobj;

                await SetVxeTableColumnCacheAsync({tableId:id,ColumnConfig:JSON.stringify(storeData)});
            },
            //默认列隐藏 批量控制列的显影
            changecolumn(val){
              setTimeout(() => {
              this.columns.forEach(column => {
                  if(val!=null){
                      if (val.includes(column.property)) {
                          column.visible = false
                      } else if(val.length==0){
                          column.visible = true
                    }
                  }
              })
              if (this.$refs.xTable) {
                  this.$refs.xTable.refreshColumn()
              }
              }, 800)
            },
            footerMethod ({ columns, data }) {
                const sums = [];
                if (!this.summaryarry)
                    return sums
                var arr = Object.keys(this.summaryarry);
                if (arr.length == 0)
                    return sums
                //const { columns, data } = param;
                var hashj = false;
                columns.forEach((column, index) => {

                    if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
                        var sum = this.summaryarry[column.property + '_sum'];
                        if (sum == null) return;
                        else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
                        else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2);
                        else if (Math.abs(parseInt(sum)) && this.noToFixed) sums[index] = sum
                        else sums[index] = sum.toFixed(0);
                    }
                    else if (column.property == 'buchar') sums[index] = '汇总趋势图'
                    else if (index == '0'||column.type == 'seq'||index == 0){ sums[0] = '合计';}


                    else sums[index] = ''
                });
                if (this.summarycolumns.length == 0) {
                    this.summarycolumns = columns;
                    //this.initsummaryEvent();
                }
                return [sums]
            },
            downloadFile(files) {
              // 原：
              // if(files){
              //       let jsonF=JSON.parse(files);
              //       if(jsonF && jsonF.length>0)
              //           window.open(jsonF[0].url);
              // }
              if (files) {
                let jsonF = JSON.parse(files);
                if (jsonF && jsonF.length > 0) {
                  jsonF.forEach(file => {
                    const fileName = file.name; // 获取文件名
                    const fileUrl = file.url; // 获取文件URL
                    // 使用 fetch 以确保文件能通过流下载，避免浏览器直接预览
                    fetch(fileUrl)
                      .then(response => {
                        if (!response.ok) {
                          throw new Error("文件下载失败");
                        }
                        return response.blob(); // 将响应转换为 Blob 对象
                      })
                      .then(blob => {
                        // 创建一个隐藏的 <a> 标签用于下载
                        const aLink = document.createElement("a");
                        const objectUrl = URL.createObjectURL(blob); // 创建 Blob URL
                        aLink.href = objectUrl;
                        aLink.download = fileName; // 设置下载的文件名
                        aLink.style.display = "none";
                        // 将 <a> 标签添加到页面并触发点击事件
                        document.body.appendChild(aLink);
                        aLink.click();
                        // 移除 <a> 标签并释放 Blob URL
                        document.body.removeChild(aLink);
                        URL.revokeObjectURL(objectUrl);
                      })
                      .catch(error => {
                        console.error("下载文件时出错：", error);
                        alert("下载失败，请稍后再试！");
                      });
                  });
                }
              }
            },
            downloadFiles(files){
                if(files){
                    let jsonF=JSON.parse(files);

                    this.$showDialogform({
                        path: `@/views/base/DownloadFilesForm.vue`,
                        title: '文件列表',
                        autoTitle: false,
                        args: { files:jsonF, mode: 3 },
                        height: 300,
                        width: '600px',
                        callOk:null
                    })
                }

            },
            cellStyleFun({ row, rowIndex, column }){
                let rltStyle={};
                let col=column;
                var colArg=this.tableCols.find(x=>x.prop==col.property );

                if(colArg ){
                    if(colArg.type && (colArg.type=="images" ||colArg.type=="image"))
                        rltStyle={
                            ...rltStyle,
                            ...{
                                textAlign:"center"
                            }
                        };

                    if(colArg.align)
                        rltStyle={
                            ...rltStyle,
                            ...{
                                textAlign:colArg.align
                            }
                        };

                }
                return rltStyle;
            },
            customSortMethod({ data, sortList }){
                if(this.isRemoteSort){
                    if(sortList && sortList.length>0){
                        if(sortList[0].field != this.lastSortArgs.field || sortList[0].order!=this.lastSortArgs.order){
                            this.lastSortArgs={...sortList[0]};
                            this.$emit('sortchange',{
                                order:(this.lastSortArgs.order.indexOf('desc')>-1?'descending':'asc'),
                                prop:this.lastSortArgs.field
                            });
                        }
                    }
                }else{
                    this.$refs.xTable.sort(sortList[0].field, sortList[0].order)
                }
            },
            headerCellClassName ({ column, columnIndex }) {
                let className='';
                var col=this.tableCols.find(x=>x.prop==column.property );

                if (col && col.align ) {
                    className=' '+`vxetableheadercell-${column.align}-20221216`;
                }else if(col && col.type && (col.type=="images" || col.type=="image")){
                    className=' '+`vxetableheadercell-center-20221216`;
                }

                return className;
            },
            cellClick({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }){
                this.$emit("cellClick",{ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event });
            },
            setExportCols(){
                if(!this.exportColumnsDialogProp.visible){
                    this.exportColumnsDialogProp.visible=true;

                    let allcolumns=this.$refs.xTable.getTableColumn();
                    let tempCols=[...allcolumns.collectColumn];
                    if(tempCols.length>0 && tempCols[0].title==null){
                        tempCols[0].title="#";
                    }
                    tempCols =  tempCols.filter(x=>x.title != null && x.title!='操作' && x.title != '');
                    let tempRoot={
                        id:'_root',
                        parentId:"_root",
                        children:tempCols,
                        title:'全部'
                    };


                    if(this.exportColumnsDialogProp.data.length==0){
                        const visibleColumn = this.$refs.xTable.getColumns();

                        visibleColumn.forEach(item=>{
                            this.exportColumnsDialogProp.colIds.push(item.id);
                        });
                    }

                    if(this.exportColumnsDialogProp.isLoadMapFields==false){
                        //如果还没加载字段与导出的映射关系，先加载
                        this.tableCols.forEach(col=>{
                            if(col.prop && col.exportField){
                                this.exportColumnsDialogProp.mapFields[col.prop]=col.exportField;
                            }
                            if(col.cols && col.cols.length>0){
                                col.cols.forEach(col1=>{
                                    if(col1.prop && col1.exportField){
                                        this.exportColumnsDialogProp.mapFields[col1.prop]=col1.exportField;
                                    }
                                    if(col1.cols && col1.cols.length>0){
                                        col1.cols.forEach(col2=>{
                                            if(col2.prop && col2.exportField){
                                                this.exportColumnsDialogProp.mapFields[col2.prop]=col2.exportField;
                                            }
                                        });
                                    }
                                });

                            }
                        });

                        this.exportColumnsDialogProp.isLoadMapFields=true;
                    }

                    this.exportColumnsDialogProp.data=[tempRoot];

                    return;
                }

                let selNodes=this.$refs.exportColTree.getCheckedNodes(true);
                this.exportColumnsDialogProp.colIds=selNodes.map(x=>x.id);

                this.exportColumnsDialogProp.colFields=selNodes.map(x=>{
                    return x.field  &&  this.exportColumnsDialogProp.mapFields[x.field] ? this.exportColumnsDialogProp.mapFields[x.field] :x.field;
                });

                if(this.cstmExportFunc &&  typeof(this.cstmExportFunc)=='function' ){
                    console.log('开始调用导出');
                    this.cstmExportFunc({"YH_EXT_ExportColumns":[...this.exportColumnsDialogProp.colFields]});
                    console.log('导出数据结束');
                }

                this.exportColumnsDialogProp.visible=false;
            },
        }
    }
</script>


<style lang="scss" scoped>
   ::v-deep  .vxe-body--column{
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;
        user-select: text !important;
    }
</style>

<style lang="scss" >

        /*滚动条整体部分*/
        .mytable-scrollbar20221212 ::-webkit-scrollbar {
          width: 10px;
          height: 10px;
        }
        /*滚动条的轨道*/
        .mytable-scrollbar20221212 ::-webkit-scrollbar-track {
          background-color: #FFFFFF;
        }
        /*滚动条里面的小方块，能向上向下移动*/
        .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
          background-color: #bfbfbf;
          border-radius: 5px;
          border: 1px solid #F1F1F1;
          box-shadow: inset 0 0 6px rgba(0,0,0,.3);
        }
       .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
          background-color: #A8A8A8;
        }
        .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
          background-color: #787878;
        }
        /*边角，即两个滚动条的交汇处*/
       .mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
          background-color: #FFFFFF;
        }

        // 图片大小
        .mytable-scrollbar20221212  .images20221212{
          max-width: 150px;max-height: 150px;
          width:40px !important;
          height:40px  !important;
        }

        // 图片张数标记
        .mytable-scrollbar20221212 .badgeimage20221212 .el-badge__content.is-fixed{
            top:10px;
        }
        .mytable-scrollbar20221212 .badgeimage20221212 .imageClick .el-badge__content.is-fixed{
            top:10px;
            right: 10px;
        }


        /*  工具箱位置  */
        .vxetoolbar20221212{
            position:absolute ;
            top: 30px;
            right: 5px;
            padding-top:0;
            padding-bottom:0;
            z-index: 999;
            background-color: rgb(255 255 255 / 0%);
        }

        .vxetableheadercell-left-20221216
        {
            text-align: left;
        }

        .vxetableheadercell-center-20221216
        {
            text-align: center;
        }

        .vxetableheadercell-right-20221216
        {
            text-align: right;
        }

</style>
