<template>
    <div>
        <el-date-picker class="timeCss" v-model="timeRange" :type="type" :align="align" unlink-panels
            range-separator="至" :value-format="valueFormat" :start-placeholder="startPlaceholder"
            :end-placeholder="endPlaceholder" :picker-options="getOptions" @change="update" :clearable="clearable">
        </el-date-picker>
    </div>
</template>

<script>
import { pickerOptions } from '@/utils/tools'
import { timePickerOptions } from '@/utils/getCols'
export default {
    props: {
        endPlaceholder: {
            type: String,
            default: '结束日期'
        },
        startPlaceholder: {
            type: String,
            default: '开始日期'
        },
        align: {
            type: String,
            default: 'left'
        },
        valueFormat: {
            type: String,
            default: 'yyyy-MM-dd'
        },
        type: {
            type: String,
            default: 'daterange'
        },
        startDate: {
            type: String,
            default: ''
        },
        endDate: {
            type: String,
            default: ''
        },
        clearable: {
            type: <PERSON>olean,
            default: true
        },
        isOtherPicker: {
            type: Boolean,
            default: false
        },
        OtherPickerOptions: {
            type: Object,
            default: () => { }
        },
    },
    data() {
        return {
            timeRange: [],
            startTime: '',
            endTime: '',
            pickerOptions,
            timePickerOptions,
        }
    },
    watch: {
        value: {
            handler() {
                this.valueChange()
            },
            deep: true
        }
    },
    mounted() {
        this.valueChange()
    },
    computed: {
        getOptions() {
            if (this.isOtherPicker) {
                return this.OtherPickerOptions
            } else {
                return this.type == 'daterange' ? this.pickerOptions : this.timePickerOptions
            }
        }
    },
    methods: {
        valueChange() {
            if (this.startDate) this.startTime = this.startDate
            if (this.endDate) this.endTime = this.endDate
            this.timeRange = [this.startTime, this.endTime]
        },
        update(e) {
            this.startTime = e ? e[0] : ''
            this.endTime = e ? e[1] : ''
            this.$emit('update:startDate', this.startTime)
            this.$emit('update:endDate', this.endTime)
            this.$emit('change', e)
        },
    }
}
</script>

<style scoped lang="scss">
.timeCss {
    width: 100%;
}
</style>