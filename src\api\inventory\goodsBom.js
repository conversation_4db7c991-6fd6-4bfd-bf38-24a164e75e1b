import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/goodsBom/`
const apiPrefixExt = `${process.env.VUE_APP_BASE_API_ImportInventory}/purchase/`

//分页查询Bom列表
export const getGoodsBomPageAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGoodsBomPage', params, config)
}

//查询Bom明细
export const getGoodsBomDetailAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGoodsBomDetail', params, config)
}

//新增编码拉取聚水潭数据
export const addGoodsBomCodeAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'AddGoodsBomCode', params, config)
}

//导入Bom编码数据
export const importGoodsBomDataAsync = (params, config = {}) => {
    return request.post(apiPrefixExt + 'ImportGoodsBomData', params, config)
}