import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API}/approvalNode/`

  export const gtApprovalNodeListAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GtApprovalNodeListAsync', params, config )
  }
  export const deleteApprovalNodeAsync = (params, config = {}) => {
    return request.delete(apiPrefix + 'DeleteApprovalNodeAsync', { params: params, ...config })
  }
  export const updateApprovalNodeAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'updateApprovalNodeAsync', params, config)
  }
  export const addApprovalNodeAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'AddApprovalNodeAsync', params, config)
  }
  export const getUserList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetUserList', params, config)
  }
  export const getApprovalNode = (params, config = {}) => {
    return request.get(apiPrefix + 'GetApprovalNode',  { params: params, ...config })
  }