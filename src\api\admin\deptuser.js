import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API}/deptmentUserInfo/`


//获取所有钉钉部门
export const getALLDDDeptTree = (params,config ={}) =>{
    return request.get(apiPrefix+'ALLDDDeptTree', {params: params, ...config})
}
//获取所有钉钉部门，仅南昌武汉
export const AllDDDeptTreeNcWh = (params,config ={}) =>{
    return request.get(apiPrefix+'AllDDDeptTreeNcWh', {params: params, ...config})
}
//获取所有列表组织
export const getALLDDDeptList = (params,config ={}) =>{
    return request.get(apiPrefix+'AllDDDeptList', {params: params, ...config})
}
//获取部门成员
export const getDeptUsers = (params,config ={}) =>{
    return request.get(apiPrefix+'GetDeptUsers', {params: params, ...config})
}
//获取所有钉角色
export const getDingRoles = (params,config ={}) =>{
    return request.get(apiPrefix+'GetDingRoles', {params: params, ...config})
}
//获取所有钉角色 树
export const getDingRolesTree = (params,config ={}) =>{
    return request.get(apiPrefix+'GetDingRolesTree', {params: params, ...config})
}

//获取所有钉用户，含离职
export const QueryAllDDUserTop100 = (params,config ={}) =>{
  return request.get(apiPrefix+'QueryAllDDUserTop100', {params: params, ...config})
}

//获取所有部门视图
export const AllDeptViewList = (params,config ={}) =>{
    return request.get(apiPrefix+'AllDeptViewList', {params: params, ...config})
}

//获取所有部门视图,含虚拟总集团
export const AllDeptViewContainRootList = (params,config ={}) =>{
    return request.get(apiPrefix+'AllDeptViewContainRootList', {params: params, ...config})
}

//分页查询钉钉用户明细
export const PageDDUserInfo = (params,config ={}) =>{
    return request.post(apiPrefix+'PageDDUserInfo', params, config)
}


//保存用户默认部门
export const SaveUserDefaultDeptId = (params,config ={}) =>{
    return request.get(apiPrefix+'SaveUserDefaultDeptId', {params: params, ...config})
}


//获取用户其他信息
export const GetUserExtInfo = (params,config ={}) =>{
    return request.get(apiPrefix+'GetUserExtInfo', {params: params, ...config})
}

//保存用户其他信息
export const SaveUserExtInfo = (params,config ={}) =>{
    return request.post(apiPrefix+'SaveUserExtInfo', params, config)
}





//分页查询组织留存
export const PageEmployeeStatusReport = (params,config ={}) =>{
    return request.post(apiPrefix+'PageEmployeeStatusReport', params, config)
}



//分页查询组织留存明细
export const PageEmployeeStatusReportDtl = (params,config ={}) =>{
    return request.post(apiPrefix+'PageEmployeeStatusReportDtl', params, config)
}



//组织留存图表
export const EmployeeStatusReportChart = (params,config ={}) =>{
    return request.post(apiPrefix+'EmployeeStatusReportChart', params, config)
}

//获取核价专员
export const getPriceuserList = (params,config ={}) =>{
  return request.get(apiPrefix+'GetPriceuserList', {params: params, ...config})
}


//获取客服数据
export const queryAllCustomerServiceDDUserTop100 = (params, config = {}) => {
    return request.get(apiPrefix + 'QueryAllCustomerServiceDDUserTop100', {params: params, ...config})
  }

//查询每日同步简易岗位
export const GetSimplePostContrast = (params,config ={}) =>{
    return request.post(apiPrefix+'GetSimplePostContrast',  params, config)
}

//GetSimplePostContrastLog 获取岗位对照日志
export const GetSimplePostContrastLog = (params,config ={}) =>{
    return request.post(apiPrefix+'GetSimplePostContrastLog', params,config)
}

//EditSimplePostContrastLabel 修改岗位对照标签
export const EditSimplePostContrastLabel = (params,config ={}) =>{
    return request.post(apiPrefix+'EditSimplePostContrastLabel', params,config)
}

//CopySimplePostContrastLabel 复制岗位对照标签
export const CopySimplePostContrastLabel = (params,config ={}) =>{
    return request.post(apiPrefix+'CopySimplePostContrastLabel', params,config)
}

//DeleteSimplePostContrast 删除岗位对照
export const DeleteSimplePostContrast = (params,config ={}) =>{
    return request.post(apiPrefix+'DeleteSimplePostContrast', params,config)
}

//GetAllDept 获取所有部门
export const GetAllDept = (params,config ={}) =>{
    return request.post(apiPrefix+'GetAllDept', params,config)
}

//GetAllPost 获取所有岗位
export const GetAllPost = (params,config ={}) =>{
    return request.post(apiPrefix+'GetAllPost', params,config)
}

//GetFirstDept 获取一,二,三级部门
export const GetFirstDept = (params,config ={}) =>{
    return request.post(apiPrefix+'GetFirstDept', params,config)
}
