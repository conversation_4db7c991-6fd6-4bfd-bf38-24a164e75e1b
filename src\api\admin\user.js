import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API}/user/`
// axios.defaults.headers.post['Content-Type']='application/json;charset=UTF-8';

// 用户管理
export const getUser = (params, config = {}) => {
  return request.get(apiPrefix + 'get', { params: params, ...config })
}
export const resetPass = (params, config = {}) => {
  return request.get(apiPrefix + 'ResetPassword', { params: params, ...config })
}


export const getUserListPage = (params, config = {}) => {
  return request.post(apiPrefix + 'getpage', params, config)
}
export const removeUser = (params, config = {}) => {
  return request.delete(apiPrefix + 'softdelete', { params: params, ...config })
}
export const editUser = (params, config = {}) => {
  return request.put(apiPrefix + 'update', params, config)
}
export const updateBasicUser = (params, config = {}) => {
  return request.put(apiPrefix + 'updateBasic', params, config)
}
export const addUser = (params, config = {}) => {
  return request.post(apiPrefix + 'add', params, config)
}
export const batchRemoveUser = (params, config = {}) => {
  return request.put(apiPrefix + 'BatchsoftDelete', params, config)
}
export const changePassword = (params, config = {}) => {
  return request.put(apiPrefix + 'ChangePassword', params, config)
}
export const getBasic = (params, config = {}) => {
  return request.get(apiPrefix + 'getbasic', { params: params, ...config })
}
export const queryIsAdmin = (params, config = {}) => { return request.get(apiPrefix + 'IsAdmin', { params: params, ...config }) }

export const getWookTeamUser = (params, config = {}) => { return request.get(apiPrefix + 'GetWookTeamUser', { params: params, ...config }) }

//获取当前用户
export const getCurUser = (params, config = {}) => {
  return request.post(apiPrefix + 'GetCurUser', params, config)
}

//获取当前登录信息
export const getCurrentUserAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'GetCurrentUserAsync', params, config)
}


//获取聊天的用户列表
export const getHotSaleGoodChatUserListsAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'GetHotSaleGoodChatUserListsAsync', { params: params, ...config })
}

//获取聊天用户的双方信息
export const getHotSaleGoodChatUserInfosAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'GetHotSaleGoodChatUserInfosAsync', { params: params, ...config })
}

//获取用户单独聊天的消息记录
export const getHotSaleGoodChatByUserIdAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'GetHotSaleGoodChatByUserIdAsync', { params: params, ...config })
}

//发送消息
export const hotSaleGoodChatDDUserAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'HotSaleGoodChatDDUserAsync', params, config)
}

//更新消息已读状态
export const updateHotSaleGoodChatSendMesgAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'UpdateHotSaleGoodChatSendMesgAsync', { params: params, ...config })
}

//删除对话
export const deleteHotSaleGoodChat = (params, config = {}) => {
  return request.get(apiPrefix + 'DeleteHotSaleGoodChatAsync', { params: params, ...config })
}

//入职周年通知查询已保存的信息
export const getAnniversaryNotice = (params,config ={}) =>{
  return request.get(apiPrefix+'GetAnniversaryNotice', {params: params, ...config})
}

//入职周年通知新增并保存通知信息
export const saveAnniversaryNoticeRelation = (params,config ={}) =>{
  return request.post(apiPrefix+'SaveAnniversaryNoticeRelation',  params, config)
}

//入职周年通知获取不接收通知人员
export const getAllCorpDeptPersonList = (params,config ={}) =>{
  return request.get(apiPrefix+'GetAllCorpDeptPersonList', {params: params, ...config})
}

//入职周年通知获取公司到二级部门（接收通知）
export const getAllCorpDeptList = (params,config ={}) =>{
  return request.get(apiPrefix+'GetAllCorpDeptList', {params: params, ...config})
}

//入职周年通知启用禁用
export const enabledAnniversaryNotice = (params,config ={}) =>{
  return request.get(apiPrefix+'EnabledAnniversaryNotice', {params: params, ...config})
}

//获取所有类型
export const getNoticeSubTypes = (params,config ={}) =>{
  return request.get(apiPrefix+'GetNoticeSubTypes', {params: params, ...config})
}

//获取登录用户消息订阅
export const getUserNoticeSub = (params,config ={}) =>{
  return request.get(apiPrefix+'GetUserNoticeSub', {params: params, ...config})
}

//更新用户消息订阅类型
export const updateNoticeSub = (params,config ={}) =>{
  return request.post(apiPrefix+'UpdateNoticeSub',  params, config)
}

//分页获取用户站内信
export const pageGetUserNotices = (params,config ={}) =>{
  return request.post(apiPrefix+'PageGetUserNotices',  params, config)
}

//用户站内信设置已读
export const readNotice = (params,config ={}) =>{
  return request.get(apiPrefix+'ReadNotice', {params: params, ...config})
}

//用户站内信设置已读
export const ReadNoticeBatch = (params,config ={}) =>{
  return request.post(apiPrefix+'ReadNoticeBatch',  params, config)
}


//用户站内信设置已读
export const ExecNotice = (params,config ={}) =>{
  return request.post(apiPrefix+'ExecNotice',  params, config)
}

//获取所有订阅的唯独条数 全部相加为总数
export const getUnreadCount = (params,config ={}) =>{
  return request.post(apiPrefix+'GetUnreadCount',  params, config)
}

//获取erp用户数据权限
export const allErpDataPermissions = (params,config ={}) =>{
  return request.post(apiPrefix+'AllErpDataPermissions',  params, config)
}

//获取erp用户数据权限
export const PageAllErpDataPermissions = (params,config ={}) =>{
  return request.post(apiPrefix+'PageAllErpDataPermissions',  params, config)
}

//添加erp用户数据权限
export const addErpDataPermissions = (params,config ={}) =>{
  return request.post(apiPrefix+'AddErpDataPermissions',  params, config)
}

//删除erp用户数据权限
export const delErpDataPermissions = (params,config ={}) =>{
  return request.get(apiPrefix+'DelErpDataPermissions?ids='+params,  params, config)
}

//获取用户dduserID GetUserDingCode
export const getUserDingCode = (params,config ={}) =>{
  return request.get(apiPrefix+'GetUserDingCode', {params: params, ...config})
}
