<template>
    <div class="relativecss" :style="[{ height: (tableHandles != null && tableHandles.length>0 ? '95%' : '100%') }, { width: '100%' }, { 'margin': ' 0' }]">
        <el-button-group v-if="tableHandles.length>0">
            <template v-for='item in tableHandles'>
                <el-button v-if="(!item.permission || (item.permission && checkPermission(item.permission)))"
                    :key='item.label' :size="item.size || size" :type="item.type || type" :icon='item.icon || ""'
                    v-throttle="item.throttle || 1000" @click="item.handle(that)">{{ item.label }}</el-button>
            </template>
            <slot name="extentbtn" ></slot>
        </el-button-group>


        <div ref="pageDiv" @mousemove="demo_move" @mouseup="demo_up"
                :class="{'zlevelTop':mouseDownState}" v-if="toolbarshow"
                style="position: absolute;top: 0;height: 100%;width: 100%">
            <div v-if="menuOpen"  @click.stop="closeOpenModal"
            style="position: fixed;top: 0;right: 0;width: 100%;height: 100%;z-index: 1"></div>

            <div :class="{'six-more-modal-btn':menuOpen,'moreModal':!menuOpen,'more-tran-animate':!mouseDownState}"
                ref="actionMgr" :style="position"  @mousedown="demo_down">
                <div v-if="!menuOpen"  class="imgMore">
                <vxe-toolbar ref="xToolbar" custom class="vxetoolbar20221212" v-if="toolbarshow">
                    <template #buttons>
                    <slot name="tbHeader" ></slot>
                </template>
                </vxe-toolbar>
                </div>
                <div v-else></div>
            </div>
        </div>




        <vxe-table v-if="isvirtual" :row-config="{isHover: true}" :size="size"
        :row-style="rowStyleTable"
        @custom="tooclick"
        highlight-hover-row
        highlight-current-row
        :show-header-overflow="showheaderoverflow"
        :align="align"
        :column-config="{resizable: resizable}"
         ref="xTable" :loading="loading"
        :data="tableData"
        :sort-config="{trigger: 'cell', sortMethod: isRemoteSort?customSortMethod: null}"
        :scroll-y="scrollY"
        :scroll-x="scrollX"
        :show-overflow="showoverflow"
        class="vxetable202212161323 mytable-scrollbar20221212"
        :cell-style="cellStyleFun"
        :height="height"
        stripe
        :border="border"
        :custom-config="{storage: isstorage}"
        :id="id"
        :cell-class-name="cellClassName"
        :header-cell-class-name="headerCellClassName"
        :show-footer="showsummary"
        :footer-method="footerMethod"
        v-bind:name="tablekey"
        :checkbox-config="{labelField: '', highlight: true, range: enableCheckRange, checkMethod: checCheckboxkMethod2}"
        @checkbox-range-end="selectAllEvent"
        @cell-click="selectAllEvent"
        @checkbox-change="selectChangeEvent"
        @checkbox-all="checkboxall"
        :edit-config="editconfig"
        @footer-cell-click="footercellclick"
        @resizable-change="resizableChange"
        :span-method="mergeRowMethod"
        :expand-config="{accordion: true, toggleMethod: toggleTreeMethod  }"

        >
            <vxe-column show-header-overflow v-if="hasSeq" type="seq" :width="indexWidth" :fixed="isIndexFixed?'left':''" :align="'left'">
                <template v-if="isSelectLvl" #header>
                    <el-select v-model="currentLvl" placeholder="超小尺寸" size="mini" @change="lvlChang" style="width:50px;">
                        <el-option v-for="num in 9" :key="num" :value="num" :label="`+${num}`"></el-option>
                    </el-select>
                </template>
                <template #footer="{ items, _columnIndex }">
                    <span ></span>
                </template>
            </vxe-column>
            <slot name="left" ></slot>

            <template v-for="(col,colIndex) in tableCols">
                <yhVTwoColumn @dialogHisVisible="dialogHisVisiblefuc" @exportColumnsDialogProp="()=>{exportColumnsDialogProp.visible = true}"
                 :that="that" :col="col" :colIndex="colIndex"  :size="size" :key=" colIndex"></yhVTwoColumn>
            </template>
            <slot name="right" ></slot>

        </vxe-table>
        <vxe-table v-else :row-config="{isHover: true}" :size="size"
        :row-style="rowStyleTable"
        @custom="tooclick"
        highlight-hover-row
        highlight-current-row
        :show-header-overflow="showheaderoverflow"
        :align="align"
        :column-config="{resizable: resizable,maxFixedSize: 300, maxFixedSize: 20,
	resizable: true,}"
         ref="xTable" :loading="loading"
        :data="tableData"
        :sort-config="{trigger: 'cell', sortMethod: isRemoteSort?customSortMethod: null}"
        :scroll-y="scrollY"
        :scroll-x="scrollX"
        :show-overflow="showoverflow"
        class="vxetable202212161323 mytable-scrollbar20221212"
        :cell-style="cellStyleFun"
        :height="height"
        stripe
        :border="border"
        :custom-config="{storage: isstorage,restoreStore:restoreStore,updateStore:updateStore}"
        :id="id"
        :cell-class-name="cellClassName"
        :header-cell-class-name="headerCellClassName"
        :show-footer="showsummary"
        :footer-method="footerMethod"
        v-bind:name="tablekey"
        :checkbox-config="{labelField: '', highlight: true, range: enableCheckRange, checkMethod: checCheckboxkMethod2}"
        @checkbox-range-end="selectAllEvent"
        @cell-click="selectAllEvent"
        @checkbox-change="selectChangeEvent"
        @checkbox-all="checkboxall"
        :edit-config="editconfig"
        @footer-cell-click="footercellclick"
        @resizable-change="resizableChange"
        :tree-config="treeProp"

        :span-method="mergeRowMethod"
        :expand-config="{accordion: true, toggleMethod: toggleTreeMethod  }"
        >

            <vxe-column show-header-overflow v-if="hasSeq" type="seq" :width="indexWidth" :fixed="isIndexFixed?'left':''" :align="'left'">
                <template v-if="isSelectLvl" #header>
                    <el-select v-model="currentLvl" placeholder="超小尺寸" size="mini" @change="lvlChang" style="width:50px;">
                        <el-option v-for="num in 9" :key="num" :value="num" :label="`+${num}`"></el-option>
                    </el-select>
                </template>
                <template #footer="{ items, _columnIndex }">
                    <span ></span>
                </template>
            </vxe-column>



            <slot name="left" ></slot>

            <template v-for="(col,colIndex) in tableCols">
                <yhVTwoColumn
                 @dialogHisVisible="dialogHisVisiblefuc" @exportColumnsDialogProp="()=>{exportColumnsDialogProp.visible = true}"
                 :that="that" :col="col" :colIndex="colIndex"  :size="size" :key=" colIndex"></yhVTwoColumn>
            </template>
            <slot name="right" ></slot>

        </vxe-table>

        <el-dialog :visible.sync="exportColumnsDialogProp.visible" width="300" draggable="true" title="导出选项"  v-dialogDrag>
            <el-row>
                <el-col :span="24" style="height:400px;overflow: auto;">
                    <el-tree
                    ref="exportColTree"
                    :data="exportColumnsDialogProp.data"
                    show-checkbox
                    node-key="id"
                    default-expand-all
                    :default-checked-keys="exportColumnsDialogProp.colIds"
                    :props="{
                        'label':'title'
                    }"
                    >
                    </el-tree>

                </el-col>
                <el-col :span="24" style="text-align:right">
                    <el-button type="primary" @click="setExportCols">确定</el-button>
                    <el-button @click="exportColumnsDialogProp.visible=false;" >取消</el-button>
                </el-col>
            </el-row>

        </el-dialog>

        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            v-dialogDrag>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNoInner="orderNoInner" :orderNo="orderNo" :isTx="isTx"
                style="z-index:10000;height:600px" />
        </el-dialog>
    </div>
    </template>

    <script>
import * as echarts from "echarts";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
import yhVTwoColumn from "@/components/VxeTable/yh_vxetable_v_two_column.vue";
import { getTableColumnCache, setTableColumnCache } from "@/api/admin/business";
import drag from "@/utils/drag.js";
import tablejs from "@/utils/tablejs.js";
export default {
  name: "vxetablebase",
  components: {
    OrderActionsByInnerNos,
    yhVTwoColumn,
  },

  mixins: [drag, tablejs],
  created() {
    this.$nextTick(() => {
      // 手动将表格和工具栏进行关联
      this.$refs.xTable.connect(this.$refs.xToolbar);
    });
  },
  async mounted() {
    let _this = this;
    this.currentLvl = this.dftLvl;
    this.$nextTick(() => {
      this.columns = this.$refs.xTable.getColumns();
    });
    var arrlist = [];
    this.tableCols.map((item) => {
      if (item.fixed) {
        arrlist.push(item.prop);
      }
    });
    this.arrlist = arrlist;

    // 获取数据
    if (!this.isstorage) {
      let key =
        window.location.origin +
        window.location.pathname +
        this.tablekey +
        "v1";
      let res = await getTableColumnCache({ key: key });
      await this.changecolumn(res.data.hideContent);
    }

    this.datacstmExportFunc = this.cstmExportFunc;
    //window.addEventListener('resize', this.loadRowEcharts);
  },
};
</script>

    <style lang="scss" scoped>
      ::v-deep  .vxe-body--column{
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;
        user-select: text !important;
    }
    
.relativecss {
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0;
}
.zlevelTop {
  z-index: 2000;
}
.more-tran-animate {
  transition: 0.5s;
}
.moreModal {
  /* 如果碰到滑动问题，1.3 请检查 z-index。z-index需比web大一级*/
  z-index: 2000;
  position: fixed;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  // background-color: #337AB7;
  // line-height: 40px;
  text-align: center;
  // color: #fff;
  // opacity: 0.6;
}
.moreModal:hover {
  opacity: 1;
}
.six-more-modal-btn {
  position: fixed;
  z-index: 2000;
  width: 14rem;
  height: 14rem;
  border-radius: 5px;
  // background: #1A1A1A;
  // color: #fff;
}
.imgMore {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

/////////////

/*滚动条整体部分*/
.mytable-scrollbar20221212 ::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
/*滚动条的轨道*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-track {
  background-color: #ffffff;
}
/*滚动条里面的小方块，能向上向下移动*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
  background-color: #bfbfbf;
  border-radius: 5px;
  border: 1px solid #f1f1f1;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
  background-color: #787878;
}
/*边角，即两个滚动条的交汇处*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
  background-color: #ffffff;
}

// 图片大小
.mytable-scrollbar20221212 .images20221212 {
  max-width: 150px;
  max-height: 150px;
  width: 40px !important;
  height: 40px !important;
}

// 图片张数标记
.mytable-scrollbar20221212 .badgeimage20221212 .el-badge__content.is-fixed {
  top: 10px;
}

/*  工具箱位置  */
.vxetoolbar20221212 {
  position: absolute;
  top: 30px;
  right: 0px;
  padding-top: 0;
  padding-bottom: 0;
  z-index: 999;
  background-color: rgb(255 255 255 / 0%);
}

.vxetoolbar20221212 ::v-deep .vxe-custom--wrapper {
  margin-left: 0px !important;
}

.vxetableheadercell-left-20221216 {
  text-align: left;
}

.vxetableheadercell-center-20221216 {
  text-align: center;
}

.vxetableheadercell-right-20221216 {
  text-align: right;
}

// ::v-deep .vxe-table .vxe-cell--sort {
//   width: 10px !important;
// }
::v-deep .vxe-table--render-default .vxe-cell {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

.copyhover {
  display: none;
}
.relativebox {
  width: 80%;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}
.relativebox:hover {
  width: 80%;
}
.textover {
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}
.relativebox:hover .textover {
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.relativebox:hover .copyhover {
  display: block;
  position: absolute;
  top: 50%;
  left: 75%;
  margin: 0 10px;
  z-index: 99;
  transform: translate(-50%, -50%);
  color: #409eff;
  font-weight: 600;
}

.vxe-icon-ellipsis-h:hover {
  color: #409eff;
  margin-left: 2px;
  background-color: #f1f1f1;
}

.vxe-icon-ellipsis-h {
  color: #999;
  font-size: 15px;
}
.custom-button.text-like-button {
  background-color: transparent;
  border: none;
  padding: 0;
  cursor: pointer;
  user-select: text;
  color: #409eff;
}

.beforeBox {
  position: relative;
  height: 40px;
  .beforeBox_item1 {
    position: absolute;
    bottom: 0;
    left: 5px;
  }
  .beforeBox_item2 {
    position: absolute;
    top: 0;
    right: 5px;
  }
}
.alicenter {
  display: flex;
  align-items: center;
  justify-content: center;
}
.imgstyle {
  max-height: 50px !important;
}
</style>
