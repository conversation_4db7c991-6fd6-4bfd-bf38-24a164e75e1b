import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/sampleGoods/`

// 新增或编辑样品仓库
export const addOrEditSampleWarehouse = (params, config = {}) => {
  return request.post(apiPrefix + 'AddOrEditSampleWarehouse', params, config)
}

// 新增或编辑样品仓库区域
export const addOrEditSampleWarehouseArea = (params, config = {}) => {
  return request.post(apiPrefix + 'AddOrEditSampleWarehouseArea', params, config)
}

// 仓库下拉
export const getSampleWarehouse = (params, config = {}) => {
  return request.get(apiPrefix + 'GetSampleWarehouse', { params: params, ...config })
}

// 区域下拉
export const getSampleWarehouseArea = (params, config = {}) => {
  return request.get(apiPrefix + 'GetSampleWarehouseArea', { params: params, ...config })
}

// 新增或编辑库位管理
export const addOrEditWarehouseLocationManagement = (params, config = {}) => {
  return request.post(apiPrefix + 'AddOrEditWarehouseLocationManagement', params, config)
}

// 库位管理查询
export const getWarehouseLocationManagement = (params, config = {}) => {
  return request.get(apiPrefix + 'GetWarehouseLocationManagement', { params: params, ...config })
}

// 仓库+仓区查询
export const getSampleWarehouseAreaWarehouse = (params, config = {}) => {
  return request.get(apiPrefix + 'GetSampleWarehouseAreaWarehouse', { params: params, ...config })
}

// 批量新增库位
export const batchAddWarehouseLocationManagement = (params, config = {}) => {
  return request.post(apiPrefix + 'BatchAddWarehouseLocationManagement', params, config)
}

// 批量删除库位
export const deleteWarehouseLocationManagement = (params, config = {}) => {
  return request.post(apiPrefix + 'DeleteWarehouseLocationManagement', params, config)
}

// 导入样品登记
export const importSampleRegistrationAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'ImportSampleRegistrationAsync', params, config)
}

// 样品登记查询
export const getSampleRegistration = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSampleRegistration', params, config)
}

// 样品登记导出
export const exportSampleRegistrationAsync = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'ExportSampleRegistrationAsync', params, config)
}

// 新增或编辑样品登记
export const addOrEditSampleRegistration = (params, config = {}) => {
  return request.post(apiPrefix + 'AddOrEditSampleRegistration', params, config)
}

// 新增样品领用申请
export const addSampleReceiveApplication = (params, config = {}) => {
  return request.post(apiPrefix + 'AddSampleReceiveApplication', params, config)
}

// 样品领用确认领用
export const editSampleReceiveApplication = (params, config = {}) => {
  return request.post(apiPrefix + 'EditSampleReceiveApplication', params, config)
}

// 样品领用申请查询
export const getSampleReceiveApplication = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSampleReceiveApplication', params, config)
}

// 库位管理--批量打印
export const getWarehouseLocationManagementBatch = (params, config = {}) => {
  return request.get(apiPrefix + 'GetWarehouseLocationManagementBatch', { params: params, ...config })
}

// 批量打印条码
export const getBathSuiJiGoodsCode = (params, config = {}) => {
  return request.get(apiPrefix + 'getBathSuiJiGoodsCode', { params: params, ...config })
}

// 批量新增或更换样品包材
export const addOrEditSampleRegistrationPackage = (params, config = {}) => {
  return request.post(apiPrefix + 'AddOrEditSampleRegistrationPackage', params, config)
}

// 查询更换样品包材日志
export const getSampleRegistrationPackageLog = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSampleRegistrationPackageLog', params, config)
}

// 复核样品包材
export const editSampleRegistrationTongGuo = (params, config = {}) => {
  return request.post(apiPrefix + 'EditSampleRegistrationTongGuo', params, config)
}

// 设置样品是否同品归还
export const setSampleRegistrationCongruence = (params, config = {}) => {
  return request.post(apiPrefix + 'SetSampleRegistrationCongruence', params, config)
}

// 获取样品间区域配置(品牌、标签)
export const getSampleRegistrationRegionConfig = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSampleRegistrationRegionConfig', params, config)
}

// 保存样品间区域品牌配置
export const saveSampleRegistrationRegionConfig = (params, config = {}) => {
  return request.post(apiPrefix + 'SaveSampleRegistrationRegionConfig', params, config)
}

// 分页获取区域样品数据
export const getRegionSampleRegistrationPage = (params, config = {}) => {
  return request.post(apiPrefix + 'GetRegionSampleRegistrationPage', params, config)
}

// 获取在职采购
export const getBeOnTheJobBrand = (params, config = {}) => {
  return request.post(apiPrefix + 'GetBeOnTheJobBrand', params, config)
}

// 获取区域仓库(非外仓)
export const getRegionWms = (params, config = {}) => {
  return request.post(apiPrefix + 'GetRegionWms', params, config)
}

// 生成样品间区域待拍数据
export const addRegionSampleRegistrationForRegionToBeFilmed = (params, config = {}) => {
  return request.post(apiPrefix + 'AddRegionSampleRegistrationForRegionToBeFilmed', params, config)
}

// 导出
export const exportRegionSampleRegistration = (params, config = {}) => {
  return request.post(apiPrefix + 'ExportRegionSampleRegistration', params, config)
}

// 分页获取相似品待查列表
export const getSimilaritySampleRegistrationPage = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSimilaritySampleRegistrationPage', params, config)
}

// 设置相似品数据是否一致
export const setSampleRegistrationSimilarIsEqual = (params, config = {}) => {
  return request.post(apiPrefix + 'SetSampleRegistrationSimilarIsEqual', params, config)
}

// 样品间质检
export const sampleRegistrationVerify = (params, config = {}) => {
  return request.post(apiPrefix + 'SampleRegistrationVerify', params, config)
}

// 获取样品间质检不一致分页数据
export const getSampleRegistrationNotLastEqualPage = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSampleRegistrationNotLastEqualPage', params, config)
}

// 获取质检数据
export const getSampleRegistrationVerifyData = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSampleRegistrationVerifyData', params, config)
}
