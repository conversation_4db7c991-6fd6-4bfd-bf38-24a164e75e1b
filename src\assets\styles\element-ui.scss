.el-menu-item [class^=fa],
.el-submenu [class^=fa],
.el-menu-item [class^=el-icon-],
.el-submenu [class^=el-icon-] {
  vertical-align: middle;
  margin-right: 5px;
  width: 20px;
  text-align: center;
  font-size: 14px;
}

.el-menu--collapse .el-menu-item>span,
.el-menu--collapse .el-submenu .el-submenu__title>span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}
.el-menu--collapse .el-menu-item .el-submenu__icon-arrow,
.el-menu--collapse .el-submenu .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.el-drawer .el-drawer__header{
  margin-bottom: 0px;
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;
}
.el-drawer__body{
  overflow: auto;
}
.el-drawer__container{
  outline: none;
}

.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li{
  border-radius: 50% !important;
}
.el-pagination.is-background .btn-next:hover,
.el-pagination.is-background .btn-prev:hover{
  color:#409EFF;
}

.el-table .cell{
  text-overflow: unset;
}
.el-table th>.cell{
  padding-left: 14px;
  padding-right: 14px;
}
.el-table--medium td, .el-table--medium th,
.el-table--mini td, .el-table--mini th{
  padding:4px 0;
}

.el-table--mini, .el-table--small, .el-table__expand-icon{
  font-size: 13px;
}

.el-drawer__wrapper{
  overflow:hidden;
}

.el-pagination__total{
  margin-right: 0px;
  float:left;
}
.el-pagination__jump{
  margin-left: 0px;
}
// .el-pagination__sizes{
//     float:left;
// }

.el-checkbox-group .el-checkbox {
  margin-right: 15px;
}
.el-checkbox__label{
padding-left: 5px;
}

.el-dropdown-menu--mini .el-dropdown-menu__item {
  line-height: 30px;
}

.el-menu-item, .el-submenu__title{
  height: 42px;
  line-height: 42px;
}
.el-submenu .el-menu-item {
  height: 38px;
  line-height: 38px;
  padding:0 20px;
}

.el-drawer{
  outline: none;
}
.el-drawer__header>:first-child{
  outline: none;
}
.el-drawer__close-btn{
  border: none;
  outline: none;
}
.el-drawer__close-btn:focus .el-dialog__close, .el-drawer__close-btn:hover .el-dialog__close {
  color: #409eff;
}

.el-tabs__nav{
  padding-left: 15px;
}
.el-tabs--border-card, .el-tabs--card {
  .el-tabs__nav{
      padding-left: 0px;
  }
}
.el-tabs--card>.el-tabs__header .el-tabs__nav{
  border-left-width: 0px;
  border-top-width: 0px;
}
.el-tabs--card>.el-tabs__header .el-tabs__nav.is-bottom{
  border-radius: 0px 0px 4px 4px;
}
.el-tabs__active-bar{
  margin-left: 15px;
}
.el-tabs__header{
  margin-bottom: 0px;
}

.el-tabs__nav-next, .el-tabs__nav-prev{
  font-size: 16px;
  line-height: 40px;
  &:hover{
      color: #409eff;
  }
}
.el-tabs--bottom .el-tabs__active-bar.is-bottom,
.el-tabs--bottom .el-tabs__nav-wrap.is-bottom:after {
  top: 0;
}
.el-tabs--bottom .el-tabs__header.is-bottom {
  margin-top: 0px;
}

.el-tabs--border-card{
  border-width: 0px 0px 0px 0px;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.el-tabs--border-card > .el-tabs__content {
  padding: 0px;
}
.el-tabs--border-card {
  border-width: 0px;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.el-tabs--border-card > .el-tabs__content {
  padding: 0px;
}

.el-tabs--card>.el-tabs__header .el-tabs__item.is-bottom{
  border-top: 1px solid transparent;
}
.el-tabs--card>.el-tabs__header .el-tabs__item.is-bottom.is-active{
  border-top-color: #fff;
}
.el-tabs--card>.el-tabs__header .is-scrollable .el-tabs__item:first-child {
  border-left: 1px solid #E4E7ED;
}
.el-tabs--bottom.el-tabs--card .el-tabs__nav-wrap.is-bottom{
  margin-top:-1px;
}
.el-tabs--bottom.el-tabs--card .el-tabs__header.is-bottom {
  border-bottom: 0;
  border-top: 1px solid #DCDFE6;
}

/*el-date-picker*/
.el-date-picker__header
{
  margin: 0px 12px;
}
.el-picker-panel__content{
  margin-top: 0px;
  margin-bottom: 0px;
}
.el-date-table td{
  padding-top: 0px;
  padding-bottom: 0px;
}
.el-date-table th{
  padding: 3px;
}
.el-date-range-picker__content{
  padding:5px;
}
.el-picker-panel [slot=sidebar], .el-picker-panel__sidebar{
  width: 80px;
}
.el-picker-panel [slot=sidebar]+.el-picker-panel__body, .el-picker-panel__sidebar+.el-picker-panel__body{
  margin-left: 80px;
}
.el-date-range-picker{
  width:580px;
}
.el-date-range-picker.has-sidebar{
  width:660px;
}

.el-form-item__content .el-input-group{
  vertical-align: 0px;
}

.el-main .v-modal{
  position:absolute;
}

.el-select-content { 
  width: calc(100% - 10px);
  margin: 0;
}

.el-tabs__content {
  overflow: hidden;
  position: relative;
  height: 100%;
}

/**修改全局的滚动条*/
/**滚动条的宽度*/
::-webkit-scrollbar {
   width: 12px; 
}
//滚动条的滑块
::-webkit-scrollbar-thumb {
  //background-color: #eaecf1;
  background-color: #aeb1b4;
  border-radius: 3px;
  cursor:hand ;
}

//修改表格的滚动条
.el-table__body-wrapper::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}
//滚动条的滑块
.el-table__body-wrapper::-webkit-scrollbar-thumb {
  // background-color: #a1a3a9;
  background-color: #aeb1b4;
  border-radius: 3px;
}
.el-table__fixed-footer-wrapper{
  max-height: 36px;
}

.el-popover{
  max-height:80%;
  overflow: auto;
}

.el-table__body-wrapper{
  z-index: 2
}

.el-table {
  // border-color: black;
  // border-width: 2px;
  // .el-table__fixed { 
  //   //pointer-events: none;
  //   height: auto !important;
  //  // bottom: 43px; // 改为自动高度后，设置与父容器的底部距离，高度会动态改变，值可以设置比滚动条的高度稍微大一些
  //  bottom:17px !important;  
  //   //margin-bottom: 12px;
  //   .el-table__fixed-footer-wrapper{
  //      display:none ; 
  //   }
  // }
  // .el-table__fixed-right { // 右固定列
  //   height: auto !important;
  //   bottom: 25px; // 改为自动高度后，设置与父容器的底部距离，高度会动态改变，值可以设置比滚动条的高度稍微大一些  
  //   .el-table__fixed-footer-wrapper{
  //      display:none ; 
  //   }
  // }
}

// .el-menu--horizontal>.el-submenu .el-submenu__title {
//   height: 60px;
//   line-height: 60px;
//   border-bottom: 2px solid transparent;
//   color: #fff;
//   font-size: 20px;
// }



// ::v-deep.el-menu {
//   background-color: transparent;
//   .el-menu-item {
//     color: #ffffff;
//     //    padding-left: 20px !important;
//     height: 40px;
//     margin: 8px;
//     border-radius: 4px;
//     line-height: 40px;
//     i {
//       color: #ffffff;
//     }
//   }
//   .el-menu-item:hover,
//   .el-menu-item:focus {
//     background-color: transparent;
//     //background-image: url("../../../assets/img/menu_bg.png");
//     background-position: center;
//     // linear-gradient(
//     //   to bottom right,
//     //   #6691ff,
//     //   #6269fc,
//     //   #6269fc
//     // );
//   }
//   .el-menu-item.is-active {
//     color: #ffffff;
//   }
//   .el-submenu__title i {
//     color: #ffffff;
//   }
//   .el-menu-item-group {
//     .el-menu-item-group__title {
//       color: #ffffff;
//       //   padding-left: 30px !important;
//     }
//   }
//   .el-submenu {
//     .el-submenu__title {
//       padding-left: 30px !important;
//       &:hover {
//         background-color: transparent;
//         //background-image: url("../../../assets/img/menu_bg.png");
//         background-position: center;
//       }
//     }
//     .el-menu-item {
//       min-width: 180px;
//       //    padding-left: 20px !important;
//     }
//   }
// }