<template>
    <div style="height:100%;padding-left:10px;overflow: auto;min-height: 100px;" v-loading="loading">
        
        <div :id="'hotchar'+randrom" :style="thisStyle" ></div>

    </div>
</template>
<script>
    import * as echarts from 'echarts';
    export default {
        name: 'hotchar',
        components: {},
        props: {
            loading:{type:Boolean,default:function(){
                return false;
            }},
            analysisData: { type: Object, default: null },
            analysisParam: { type: Object, default: null },
            thisStyle: {
                type: Object,
                default: function () {
                    return {
                        width: '100%', height: '550px', 'box-sizing': 'border-box', 'line-height': '360px'
                    }
                }
            },
        },
        data () {
            return {
                that: this,
                randrom: "",
                chart:null,
            }
        },
        created () {
            let e = 10;
            let t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
                a = t.length,
                n = "";
            for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
            this.randrom = n;
        },
        watch: {
            analysisData: {
                deep: true,
                handler(val) {   
                    this.setOptionInfo(val)
                }
            }
        },
        mounted () {
            this.initcharts();
        },
        methods: {
            initcharts () {

                let chartDom = document.getElementById('hotchar' + this.randrom);
                this.chart =  echarts.init(chartDom);
            },
            getOptionInfo(pdata){
               let option ={
                    title: {
                        top: 30,
                        left: 'center',
                        text: ''
                    },
                    tooltip: {
                        position: 'top',
                        formatter: function (p) {
                            const format = echarts.time.format(p.data[0], '{yyyy}-{MM}-{dd}', false);
                            return format + ': ' + p.data[1];
                        }
                    },
                    visualMap: {
                        min: this.analysisParam.visualMapMin,
                        max:this.analysisParam.visualMapMax,
                        type: 'piecewise',
                        orient: 'horizontal',
                        left: 'center',
                        top: 65,
                        outOfRange:{ color: '#8B0000'}
                    },
                    calendar: {
                        top: 120,
                        left: 30,
                        right: 30,
                        cellSize:  [20, 20],
                        range: this.analysisParam.calendarrange,
                        itemStyle: {
                            borderWidth: 0.5
                        },
                        yearLabel: { show: false }
                    },
                    series: {
                        type: 'heatmap',
                        coordinateSystem: 'calendar',
                        data:pdata,
                    }
                };
                return option;
            },
            setOptionInfo(){
                let op=this.getOptionInfo(this.analysisData);
                console.log(op);
                
                this.chart.setOption(op);
            },
        }
    }
</script>

<style lang="scss" scoped>
.suspend{
       border-radius: 50%;
    position: relative;
    text-align: center;
    width: 30px;
    font-size: 20px;
    border: 1px solid gray;
    height: 30px;
    
}
.fixBox{
    position: absolute;
    display: flex;
    align-items: center;
    cursor: pointer;
    z-index: 1;
    &:hover{
        color: #73b8ff;
        .suspend{

            border-color: #73b8ff !important;
        }
    }
}

::v-deep .fontOverflow{
    .el-checkbox{
        width: fit-content;
        max-width: 100%;
        display: flex;
        align-items: center;
        .el-checkbox__label{
            width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    white-space: nowrap;
    text-overflow:ellipsis;
        }
    }
}
.legendGroup{
    & + &{
        margin-top: 20px;
    }
    
}
::v-deep .legendGroup > :nth-child(1) .el-checkbox__label{
    font-weight: 900;
    text-decoration: underline;
}

</style>

