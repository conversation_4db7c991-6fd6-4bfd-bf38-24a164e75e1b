import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Media}/shootingvideo/`

//新增或更改
export const addOrUpdateShootingVideoTaskAsync = (params, config = {}) =>
{ return request.post(apiPrefix + 'AddOrUpdateShootingVideoTaskAsync', params, config ) }
//列表查询
export const pageShootingViewTaskAsync = (params, config = {}) =>
{ return request.post(apiPrefix + 'PageShootingViewTaskAsync', params, config ) }

//获取任务附件
export const getShootingTaskFliesAsync = (params, config = {}) =>
{ return request.get(apiPrefix + 'GetShootingTaskFliesAsync', { params: params, ...config }) }


//
export const unPickShootingTaskAsync = (params, config = {}) =>
{ return request.get(apiPrefix + 'unPickShootingTaskAsync', { params: params, ...config }) }


//
export const pickShootingTaskAsync = (params, config = {}) =>
{ return request.get(apiPrefix + 'pickShootingTaskAsync', { params: params, ...config }) }

//
export const unConfrimShootingTaskAsync = (params, config = {}) =>
{ return request.get(apiPrefix + 'UnConfrimShootingTaskAsync', { params: params, ...config }) }
//提交反驳
export const saveShootingBhInfo = (params, config = {}) =>
{ return request.get(apiPrefix + 'SaveShootingBhInfo', { params: params, ...config }) }

//提交评分
export const saveShootingScoreInfo = (params, config = {}) =>
{ return request.get(apiPrefix + 'SaveShootingScoreInfo', { params: params, ...config }) }

export const shootUrgencyCilckAsync = (params, config = {}) =>
{ return request.get(apiPrefix + 'ShootUrgencyTaskAsync', { params: params, ...config }) }

//
export const confrimShootingTaskAsync = (params, config = {}) =>
{ return request.get(apiPrefix + 'ConfrimShootingTaskAsync', { params: params, ...config }) }


//上传成果文件
export const uploadSuccessAttachment = (params, config = {}) =>
{ return request.post(apiPrefix + 'UploadSuccessAttachment', params, config ) }


//获取成果文件
export const getUploadSuccessAttachment = (params, config = {}) =>
{ return request.get(apiPrefix + 'getUploadSuccessAttachment', { params: params, ...config }) }



// 获取成果文件评分
export const getUploadSuccessAttachmentScore = (params, config = {}) =>
{ return request.get(apiPrefix + 'GetUploadSuccessAttachmentScore', { params: params, ...config }) }


//提交成果文件评分
export const subSuccessAttachmentScore = (params, config = {}) =>
{ return request.get(apiPrefix + 'SubSuccessAttachmentScore', { params: params, ...config }) }

//提交打包请求
export const packagingCompressionTask= (params, config = {}) =>
{ return request.get(apiPrefix + 'PackagingCompressionTask', { params: params, ...config }) }

//获取打包状态
export const getPackagingCompressionTaskStatus= (params, config = {}) =>
{ return request.get(apiPrefix + 'GetPackagingCompressionTaskStatus', { params: params, ...config }) }


//获取打包进度状态
export const pageShootingPackageViewTaskAsync= (params, config = {}) =>
{ return request.get(apiPrefix + 'PageShootingPackageViewTaskAsync', { params: params, ...config }) }


//任务批量完成
export const taskOverActionsAsync= (params, config = {}) =>
{ return request.post(apiPrefix + 'TaskOverActionsAsync', params, config) }


//任务批量终止
export const taskShopActionAsync= (params, config = {}) =>
{ return request.post(apiPrefix + 'TaskShopActionAsync', params, config) }

//任务批量重新启动
export const taskRestartActionAsync= (params, config = {}) =>
{ return request.post(apiPrefix + 'TaskRestartActionAsync', params, config) }

//删除到回收站操作
export const deleteShootingTaskActionAsync= (params, config = {}) =>
{ return request.post(apiPrefix + 'DeleteShootingTaskActionAsync', params, config)  }

//回收站删除
export const deleteTaskActionAsync= (params, config = {}) =>
{   return request.post(apiPrefix + 'DeleteTaskActionAsync', params, config)  }

//回收站删除
export const endShootingTaskActionAsync= (params, config = {}) =>
{   return request.post(apiPrefix + 'EndShootingTaskActionAsync', params, config)  }


//
export const signShootingTaskActionAsync= (params, config = {}) =>
{   return request.post(apiPrefix + 'SignShootingTaskActionAsync', params, config)  }
//
export const unSignShootingTaskActionAsync= (params, config = {}) =>
{   return request.post(apiPrefix + 'UnSignShootingTaskActionAsync', params, config)  }

//
export const caclShootingTaskActionAsync= (params, config = {}) =>
{   return request.post(apiPrefix + 'CaclShootingTaskActionAsync', params, config)  }

//
export const unCaclShootingTaskActionAsync= (params, config = {}) =>
{   return request.post(apiPrefix + 'UnCaclShootingTaskActionAsync', params, config)  }



//保存详情备注
export const saveShootingTaskMarkAsync= (params, config = {}) =>
{   return request.post(apiPrefix + 'SaveShootingTaskMarkAsync',params, config)  }
//删除详情备注
export const delShootingTaskMarkAsync= (params, config = {}) =>
{   return request.get(apiPrefix + 'DelShootingTaskMarkAsync',{ params: params, ...config }) }

//获取备注信息
export const getShootingTaskMarkAsync= (params, config = {}) =>
{   return request.get(apiPrefix + 'GetShootingTaskMarkAsync', { params: params, ...config }) }


//删除附件
export const delShootingTploadFileTaskAsync= (params, config = {}) =>
{   return request.get(apiPrefix + 'DelShootingTploadFileTaskAsync', { params: params, ...config }) }



//详情页
export const getPageDetailImgInfo= (params, config = {}) =>
{ return request.get(apiPrefix + 'GetPageDetailImgInfo', { params: params, ...config }) }

//总之重启
export const endRestartActionAsync= (params, config = {}) =>
{   return request.post(apiPrefix + 'EndRestartActionAsync',params, config)}


// 获取当前用户角色
export const getUserRoleList= (params, config = {}) =>
{   return request.post(apiPrefix + 'GetUserRoleList',params, config)}

//获取人员下拉
export const getShootingViewPersonAsync= (params, config = {}) =>
{ return request.get(apiPrefix + 'GetShootingViewPersonAsync', { params: params, ...config }) }

//校验任务是否完成
export const checkShootingTaskAction= (params, config = {}) =>
{ return request.get(apiPrefix + 'CheckShootingTaskAction', { params: params, ...config }) }

//导出
export const exportShootingTaskReport=(params, config = { responseType: 'blob' }) =>
{ return request.post(apiPrefix + 'ExportShootingTaskReport', params, config ) }
/*  (params, config = { responseType: 'blob' }) => { return  request.get(apiPrefix + 'ExportShootingTaskReport', { params: params, ...config }) } */


//下单发货
export const getShootingTaskOrderListById = (params, config = {}) => { return request.get(apiPrefix + 'GetShootingTaskOrderListById', { params: params, ...config }) }
export const shootingTaskAddOrderSaveCheckTaskIds = (params, config = {}) => { return request.post(apiPrefix + 'ShootingTaskAddOrderSaveCheckTaskIds', params, config) }
export const getCityAllData = (params, config = {}) => { return request.get(apiPrefix + 'GetCityAllData', { params: params, ...config }) }
export const shootingTaskAddOrderSave = (params, config = {}) => { return request.post(apiPrefix + 'ShootingTaskAddOrderSave', params, config) }


//统计1
export const getOperationGroupStatistics= (params, config = {}) =>
{   return request.post(apiPrefix + 'GetOperationGroupStatistics', params, config)  }
//获取统计2
export const shootingTaskStatistics= (params, config = {}) =>
{   return request.post(apiPrefix + 'ShootingTaskStatistics', params, config)  }

//获取统计3
export const modelingTaskDetailStatistics= (params, config = {}) =>
{   return request.post(apiPrefix + 'ModelingTaskDetailStatistics', params, config)  }

//获取统计2
export const modelingTaskStatistics= (params, config = {}) =>
{   return request.post(apiPrefix + 'ModelingTaskStatistics', params, config)  }

//获取统计2
export const getFpStrpStatistics= (params, config = {}) =>
{   return request.post(apiPrefix + 'GetFpStrpStatistics', params, config)  }

//获取统计2
export const getOperationGroupStatisticsForPerson= (params, config = {}) =>
{   return request.post(apiPrefix + 'GetOperationGroupStatisticsForPerson', params, config)  }


//获取统计2
export const getFpStrpStatisticsForPerson= (params, config = {}) =>
{   return request.post(apiPrefix + 'GetFpStrpStatisticsForPerson', params, config)  }
//获取统计2
export const modelingTaskTotalStatistics= (params, config = {}) =>
{   return request.post(apiPrefix + 'ModelingTaskTotalStatistics', params, config)  }
  //获取统计2
export const getTaskTotalStatistics= (params, config = {}) =>
{   return request.post(apiPrefix + 'GetTaskTotalStatistics', params, config)  }
//获取统计2
export const getShootingTaskStatistics= (params, config = {}) =>
{   return request.post(apiPrefix + 'GetShootingTaskStatistics', params, config)  }
//获取统计2
export const getTaskNcOrYwStatistics= (params, config = {}) =>
{   return request.post(apiPrefix + 'GetTaskNcOrYwStatistics', params, config)  }
//获取统计2
export const getPlatmStrpStatistics= (params, config = {}) =>
{   return request.post(apiPrefix + 'GetPlatmStrpStatistics', params, config)  }

//获取统计2
export const getTaskforPersonTableStatistics= (params, config = {}) =>
{   return request.post(apiPrefix + 'GetTaskforPersonTableStatistics', params, config)  }

//获取统计2
export const getTaskInfoStatistics= (params, config = {}) =>
{   return request.post(apiPrefix + 'GetTaskInfoStatistics', params, config)  }

//获取统计2
export const getTaskScoreGroupStatistics= (params, config = {}) =>
{   return request.post(apiPrefix + 'GetTaskScoreGroupStatistics', params, config)  }

//获取统计2
export const getTaskScoreStatistics= (params, config = {}) =>
{   return request.post(apiPrefix + 'GetTaskScoreStatistics', params, config)  }

//获取参考对比
//   export const getShootRefenceInfo= (params, config = {}) =>
// { return request.get(apiPrefix + 'GetShootRefenceInfo', { params: params, ...config }) }

export const getShootRefenceInfo = (id, config = {}) => {
  return request.post(apiPrefix + `GetShootRefenceInfo?taskId=${id}`, {}, config)
}
//保存对比上传信息
export const saveShootRefenceInfo= (params, config = {}) =>
{   return request.post(apiPrefix + 'SaveShootRefenceInfo', params, config)  }



//获取成果文件
export const getUploadSuccessAttachmentNew = (params, config = {}) =>
{ return request.get(apiPrefix + 'GetUploadSuccessAttachmentNew', { params: params, ...config }) }


//获取附件SKU
export const getTaskReferenceInfo = (params, config = {}) =>
{ return request.get(apiPrefix + 'GetTaskReferenceInfo', { params: params, ...config }) }


//保存产品Id
export const saveProductId = (params, config = {}) =>
{ return request.get(apiPrefix + 'SaveProductId', { params: params, ...config }) }

//美工下单
export const getHotSaleGoodInfo= (params, config = {}) =>
{ return request.get(apiPrefix + 'GetHotSaleGoodInfo', { params: params, ...config }) }


export const setShootingReStartAsync= (params, config = {}) =>
{ return request.get(apiPrefix + 'SetShootingReStartAsync', { params: params, ...config }) }

//首页新品拍摄统计
export const getTaskInfoStatisticsHome= (params, config = {}) =>
{ return request.get(apiPrefix + 'GetTaskInfoStatisticsHome', { params: params, ...config }) }

//每日完成统计
export const getTaskDailyCompletionStatistics= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetTaskDailyCompletionStatistics', params, config) }

//任务统计-总数
export const getMainTaskStatistics= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainTaskStatistics', params, config) }

//任务统计-任务汇总统计
export const getMainTaskTotalStatistics= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainTaskTotalStatistics', params, config) }

//任务统计-任务明细
export const getMainShootingTaskStatistics= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainShootingTaskStatistics', params, config) }

//任务数弹窗-数据统计-小任务统计
export const getMainStatTaskPopLittleTaskList= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatTaskPopLittleTaskList', params, config) }

//平台汇总
export const getMainStatPlatStatistics= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatPlatStatistics', params, config) }

//平台汇总-趋势图
export const getMainStatPlatStatisticsChat= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatPlatStatisticsChat', params, config) }

//平台汇总-运营小组趋势图
export const getMainStatPlatGroupStatisticsChat= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatPlatGroupStatisticsChat', params, config) }

//平台汇总-弹窗
export const getMainStatPlatGroupPopStatisticsChat= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatPlatGroupPopStatisticsChat', params, config) }

//类型统计
export const getMainStatModelingTaskStatistics= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatModelingTaskStatistics', params, config) }

//类型统计-运营小组
export const getMainStatModelingTaskGroupStatistics= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatModelingTaskGroupStatistics', params, config) }

//任务数弹窗-数据统计
export const getMainStatTaskPopList= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatTaskPopList', params, config) }

//修改任务是否考核
export const updateFineStyle= (params, config = {}) =>
{ return request.post(apiPrefix + 'UpdateFineStyle', params, config) }

//修改精品
export const fineStyleSet= (params, config = {}) =>
{ return request.post(apiPrefix + 'FineStyleSet', params, config) }

//获取组名
export const QueryGroups= (params, config = {}) =>
  { return request.get(apiPrefix + 'QueryGroups', params, config) }

//根据组获取每日上新
export const GetOperationStatistics= (params, config = {}) =>
  { return request.post(apiPrefix + 'GetOperationStatistics', params, config) }
