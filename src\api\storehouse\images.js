import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/images/`

export const addImage =(params,config) =>{return request.post(apiPrefix + 'AddImage',params,config)}
export const updateImage =(params,config) =>{return request.post(apiPrefix + 'UpdateImage',params,config)}
export const deleteImage =(id,config) =>{return request.delete(apiPrefix + 'DeleteImage?id='+id,config)}
export const queryImage = (id, config = {}) => {return request.get(apiPrefix + 'QueryImage?id='+id,config)}
export const pageImages = (params, config = {}) => {return request.get(apiPrefix + 'PageImagesAsync', { params: params, ...config })}
export const searchImages = (params, config = {}) => {return request.get(apiPrefix + 'SearchImagesAsync', { params: params, ...config })}
export const exportImages =(params,config={responseType: 'blob'}) =>{return request.post(apiPrefix + 'ExportImagesAsync',params,config)}

export const addImageNotFund =(params,config) =>{return request.post(apiPrefix + 'AddImageNotFund',params,config)}
export const pageImagesNotFund =(params,config) =>{return request.get(apiPrefix + 'PageImagesNotFundAsync',{ params: params, ...config })}


