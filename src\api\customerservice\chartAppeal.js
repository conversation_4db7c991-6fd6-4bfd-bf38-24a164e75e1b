import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/UnPayOrderAppeal/`

//客服聊天核查申诉  分页列表/售前
export const getUnPayOrderAppealPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetUnPayOrderAppealPageList', params, config)
  }
//客服聊天核查申诉  分页列表/售后
export const getUnPayOrderAppealAfterSalesPageList  = (params, config = {}) => {
    return request.post(apiPrefix + 'GetUnPayOrderAppealAfterSalesPageList', params, config)
  }
//客服聊天核查申诉  售前导出
export const unPayOrderAppealExport = (params, config = {responseType: 'blob'}) => {
    return request.post(apiPrefix + 'UnPayOrderAppealExport', params, config)}
//客服聊天核查申诉  售后导出
export const unPayOrderAppealAfterSalesExport = (params, config = {responseType: 'blob'}) => {
    return request.post(apiPrefix + 'UnPayOrderAppealAfterSalesExport', params, config)}

//客服聊天核查  售前申诉
export const appealApplication = (params, config = {}) => {
  return request.post(apiPrefix + 'AppealApplication', params, config)
}    

//客服聊天核查  售后申诉
export const appealApplicationAfterSales = (params, config = {}) => {
  return request.post(apiPrefix + 'AppealApplicationAfterSales', params, config)
}    
//客服聊天核查申诉  分页列表/售前
export const getAppealReviewPageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetAppealReviewPageList', params, config)
}
//客服聊天核查申诉  分页列表/售后
export const getAppealReviewAfterSalesPageList  = (params, config = {}) => {
  return request.post(apiPrefix + 'GetAppealReviewAfterSalesPageList', params, config)
}
//客服聊天核查申诉  分组下拉
export const getGroupNameList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGroupNameList', params, config)
}
//客服聊天核查申诉  组长下拉
export const getGroupManagerList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGroupManagerList', params, config)
}
//客服聊天核查申诉  售前申述审核
export const appealReview = (params, config = {}) => {
  return request.post(apiPrefix + 'AppealReview', params, config)
}
//客服聊天核查申诉  售后申述审核
export const appealReviewAfterSales = (params, config = {}) => {
  return request.post(apiPrefix + 'AppealReviewAfterSales', params, config)
}
//申诉  售前导出
export const appealReviewExport = (params, config = {responseType: 'blob'}) => {
  return request.post(apiPrefix + 'AppealReviewExport', params, config)}
//申诉  售后导出
export const appealReviewAfterSalesExport = (params, config = {responseType: 'blob'}) => {
  return request.post(apiPrefix + 'AppealReviewAfterSalesExport', params, config)}
