<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="referrer" content="no-referrer" />
  <!-- elment样式 -->
  <link rel="stylesheet" href="/approvalform/html/api/elment.css" />
  <!-- vue -->
  <script src="/approvalform/html/api/vue.min.js"></script>
  <!-- elment脚本 -->
  <script src="/approvalform/html/api/elment.js"></script>
  <!-- jquery -->
  <script src="/approvalform/html/api/jquery.min.js"></script>

  <script src="/approvalform/html/api/html2canvas.js"></script>

  <title>税费开票信息</title>
  <style type="text/css">
    .linebreak {
      overflow: hidden;
      /*超出部分隐藏*/
      text-overflow: ellipsis;
      /* 超出部分显示省略号 */
      white-space: normal;
      /*规定段落中的文本不进行换行 */
      width: 100%;
    }

    html,
    body {
      height: 100%;
      overflow-y: hidden;
    }
  </style>
</head>

<body>
  <div id="app" style="margin:0 auto;height: 100%;">
    <div ref="oneboxx" style="height: 98%;">
      <el-table ref="table" :data="list" row-key="id" border style="width: 100%" height="100%" show-overflow-tooltip>
        <el-table-column type="index" label="#" align="center" width="50"></el-table-column>
        <el-table-column label="采购单流程号" prop="businessId" width="170" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="开票抬头/付款账号" prop="billHeader" width="150"
          :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="税费" prop="taxes" width="100" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="税率" prop="taxesRate" width="100" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="货款金额" prop="goodsAmount" width="100" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="支付日期" prop="payTime" width="150" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="税费付款方式" prop="taxPayType" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="系统供应商名称" prop="vendor" width="150" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="账户名称" prop="accountName" width="150" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="银行账号" prop="account" width="180" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="开户行" prop="accountBank" width="150" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="税费私转账户" prop="taxAccountName" width="125"
          :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="税费私转开户行" prop="taxAccountBank" width="125"
          :show-overflow-tooltip="true"></el-table-column>
      </el-table>
    </div>
  </div>
  <script>
    var vm = new Vue({
      el: "#app",
      data: function () {
        return {
          thisLonding: true,
          list: [],
        };
      },
      created() { },
      async mounted() {
        this.getStyleSheetInfo();
      },
      methods: {
        async getStyleSheetInfo() {
          var me = this;
          let searchURL = window.location.search;
          searchURL = searchURL.substring(1, searchURL.length);
          let bulkId = searchURL.split("=")[1];
          me.thisLonding = true;
          $.ajax({
            url: `/api/CwManage/TaxData/QueryTaxData`,
            type: "POST",
            dataType: "json",
            contentType: "application/json",
            data: JSON.stringify({
              bulkId,
              currentPage: 1,
              pageSize: 999999,
            }),
            success: function (response) {
              // 在这里打印获取到的数据进行调试
              me.list = response.data.list;
            },
            error: function (xhr, textStatus, errorThrown) {
              console.log("Error: ", errorThrown);
            },
          });
        }
      },
    });
  </script>
</body>

</html>
