import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/continuLosses/`


//获取持续亏损主列表接口
export const getStyleCodeContinuLossesList = (params, config) => { return request.post(apiPrefix + 'GetStyleCodeContinuLossesList', params, config) }

//申报保存接口
export const submitOperationApply = (params, config) => { return request.post(apiPrefix + 'SubmitOperationApply', params, config) }

//撤销运营申报
export const revokeOperationApply = (params, config = {}) => { return request.get(apiPrefix + 'RevokeOperationApply', { params: params, ...config }) }
//获取系列编码
export const getContinuLossesGoodsCodeDetailList = (params, config) => { return request.post(apiPrefix + 'GetContinuLossesGoodsCodeDetailList', params, config) }

//获取系列编码
export const getReportDataForProId = (params, config) => { return request.post(apiPrefix + 'GetReportDataForProId', params, config) }

//获取汇总、个人申报主列表趋势图
export const getContinuLossesListAnalysis = (params, config) => { return request.post(apiPrefix + 'GetContinuLossesListAnalysis', params, config) }

//获取汇总、个人申报明细趋势图
export const getContinuLossesDtlAnalysis = (params, config) => { return request.post(apiPrefix + 'GetContinuLossesDtlAnalysis', params, config) }

//获取申报表数据
export const getContinuLossesGoodsCodeDetailByStyleCodeAndGroup = (params, config) => { return request.post(apiPrefix + 'GetContinuLossesGoodsCodeDetailByStyleCodeAndGroup', params, config) }

//持续亏损分析
//获取趋势图
export const getPlatformsLossesLinkAnalysis = (params, config) => { return request.post(apiPrefix + 'GetPlatformsLossesLinkAnalysis', params, config) }

//获取分析表
export const getContinuLossesDayReportList = (params, config) => { return request.post(apiPrefix + 'GetContinuLossesDayReportList', params, config) }

//添加亏损原因
export const editLossReason = (params, config) => { return request.post(apiPrefix + 'EditLossReason', params, config) }

//获取日志
export const getContinuLossesDayReportOperateLogList = (params, config) => { return request.post(apiPrefix + 'GetContinuLossesDayReportOperateLogList', params, config) }

//获取亏损原因
export const getLossReason = (params, config) => { return request.post(apiPrefix + 'GetLossReason', params, config) }

//设置系列编码加入白名单 SetStyleCodeInWhitelist
export const setStyleCodeInWhitelist = (params, config) => { return request.post(apiPrefix + 'SetStyleCodeInWhitelist', params, config) }

//获取白名单 GetWhitelist
export const getWhitelist = (params, config) => { return request.post(apiPrefix + 'GetWhitelist', params, config) }

//获取持续亏损进货审批列表 GetContinuLossesInStockApplyList
export const getContinuLossesInStockApplyList = (params, config) => { return request.post(apiPrefix + 'GetContinuLossesInStockApplyList', params, config) }

//移除白名单 RemoveWhitelistStyleCode
export const removeWhitelistStyleCode = (params, config) => { return request.post(apiPrefix + 'RemoveWhitelistStyleCode', params, config) }

//运营申报-根据商品Id获取商品编码数据 GetGoodsCodeDataByProCode
export const getGoodsCodeDataByProCode = (params, config) => { return request.post(apiPrefix + 'GetGoodsCodeDataByProCode', params, config) }

//获取当前登录用户 运营角色 及 运营组 GetDirectorAndDirectorGroup
export const getDirectorAndDirectorGroup = (params, config) => { return request.post(apiPrefix + 'GetDirectorAndDirectorGroup', params, config) }

//获取系列亏损报表数据明细 PageContinuLossesStyleCodeReport
export const pageContinuLossesStyleCodeReport = (params, config) => { return request.post(apiPrefix + 'PageContinuLossesStyleCodeReport', params, config) }

//获取系列亏损报表数据明细系列编码远程搜索 GetContinuLossesStyleCode
export const getContinuLossesStyleCode = (params, config) => { return request.get(apiPrefix + 'GetContinuLossesStyleCode?styleCode=' + params.styleCode, params, config) }

//根据系列编码获取商品编码 GetGoodsCodeByStyleCode
export const getGoodsCodeByStyleCode = (params, config) => { return request.get(apiPrefix + 'GetGoodsCodeByStyleCode?styleCode=' + params.styleCode, params, config) }

//获取系列亏损报表弹窗数据 PageContinuLossesGoodsCodeReport
export const pageContinuLossesGoodsCodeReport = (params, config) => { return request.post(apiPrefix + 'PageContinuLossesGoodsCodeReport', params, config) }

//获取趋势图 GetContinuLossesStyleCodeReportAnalysis
export const getContinuLossesStyleCodeReportAnalysis = (params, config) => { return request.post(apiPrefix + 'GetContinuLossesStyleCodeReportAnalysis', params, config) }

//获取趋势图 GetSalesSalesAmountAnalysis
export const getSalesSalesAmountAnalysis = (params, config) => { return request.post(apiPrefix + 'GetSalesSalesAmountAnalysis', params, config) }

//导出 ExportContinuLossesStyleCodeReport
export const exportContinuLossesStyleCodeReport = (params,config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportContinuLossesStyleCodeReport', params, config) }

//获取上月毛三、毛三率趋势图
export const getLastMonthProfitAnalysis = (params, config) => { return request.post(apiPrefix + 'GetLastMonthProfitAnalysis', params, config) }

//添加黑名单
export const setStyleCodeInBlackList = (params, config) => { return request.post(apiPrefix + 'SetStyleCodeInBlackList', params, config) }

//获取黑名单
export const getBlackList = (params, config) => { return request.post(apiPrefix + 'GetBlackList', params, config) }

//移除黑名单
export const removeBlacklistStyleCode = (params, config) => { return request.post(apiPrefix + 'RemoveBlacklistStyleCode', params, config) }

//获取持续亏损编码运营组
export const getLossesDayReportGroupList = (params, config) => { return request.post(apiPrefix + 'GetLossesDayReportGroupList', params, config) }

//批量添加白名单(运营组)
export const batchSaveWhitelist = (params, config) => { return request.post(apiPrefix + 'BatchSaveWhitelist', params, config) }
