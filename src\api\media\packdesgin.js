import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Media}/packdesgin/`

//新增或更改
export const addOrUpdateShootingVideoTaskAsync = (params, config = {}) => 
{ return request.post(apiPrefix + 'AddOrUpdateShootingVideoTaskAsync', params, config ) }
//列表查询
export const pageShootingViewTaskAsync = (params, config = {}) => 
{ return request.post(apiPrefix + 'PageShootingViewTaskAsync', params, config ) }

 
//获取任务附件
export const getShootingTaskFliesAsync = (params, config = {}) => 
{ return request.get(apiPrefix + 'GetShootingTaskFliesAsync', { params: params, ...config }) }


// 
export const unPickShootingTaskAsync = (params, config = {}) => 
{ return request.get(apiPrefix + 'unPickShootingTaskAsync', { params: params, ...config }) }


// 
export const pickShootingTaskAsync = (params, config = {}) => 
{ return request.get(apiPrefix + 'pickShootingTaskAsync', { params: params, ...config }) }

// 
export const unConfrimShootingTaskAsync = (params, config = {}) => 
{ return request.get(apiPrefix + 'UnConfrimShootingTaskAsync', { params: params, ...config }) }

export const shootUrgencyCilckAsync = (params, config = {}) => 
{ return request.get(apiPrefix + 'ShootUrgencyTaskAsync', { params: params, ...config }) }

// 
export const confrimShootingTaskAsync = (params, config = {}) => 
{ return request.get(apiPrefix + 'ConfrimShootingTaskAsync', { params: params, ...config }) }


//上传成果文件
export const uploadSuccessAttachment = (params, config = {}) => 
{ return request.post(apiPrefix + 'UploadSuccessAttachment', params, config ) }


//获取成果文件
export const getUploadSuccessAttachment = (params, config = {}) => 
{ return request.get(apiPrefix + 'getUploadSuccessAttachment', { params: params, ...config }) }

//获取成果文件
export const getUploadSuccessAttachmentNew = (params, config = {}) => 
{ return request.get(apiPrefix + 'GetUploadSuccessAttachmentNew', { params: params, ...config }) }



// 获取成果文件评分
export const getUploadSuccessAttachmentScore = (params, config = {}) => 
{ return request.get(apiPrefix + 'GetUploadSuccessAttachmentScore', { params: params, ...config }) }


//提交成果文件评分
export const subSuccessAttachmentScore = (params, config = {}) => 
{ return request.get(apiPrefix + 'SubSuccessAttachmentScore', { params: params, ...config }) }

//提交打包请求
export const packagingCompressionTask= (params, config = {}) => 
{ return request.get(apiPrefix + 'PackagingCompressionTask', { params: params, ...config }) }

//获取打包状态
export const getPackagingCompressionTaskStatus= (params, config = {}) => 
{ return request.get(apiPrefix + 'GetPackagingCompressionTaskStatus', { params: params, ...config }) }


//获取打包进度状态
export const pageShootingPackageViewTaskAsync= (params, config = {}) => 
{ return request.get(apiPrefix + 'PageShootingPackageViewTaskAsync', { params: params, ...config }) }

// 批量操作start------------------------------------------------------------------------------------------------------------------------------------
//任务终止
export const endTaskActionAsync= (params, config = {}) => 
{   return request.post(apiPrefix + 'EndTaskActionAsync', params, config)  }
 
export const unEndTaskActionAsync= (params, config = {}) => 
{   return request.post(apiPrefix + 'UnEndTaskActionAsync', params, config)  }
 

export const signTaskActionAsync= (params, config = {}) => 
{   return request.post(apiPrefix + 'SignTaskActionAsync', params, config)  }
 

export const unSignTaskActionAsync= (params, config = {}) => 
{   return request.post(apiPrefix + 'UnSignTaskActionAsync', params, config)  }
 

export const useTaskActionAsync= (params, config = {}) => 
{   return request.post(apiPrefix + 'UseTaskActionAsync', params, config)  }
 

export const caclTaskActionAsync= (params, config = {}) => 
{   return request.post(apiPrefix + 'CaclTaskActionAsync', params, config)  }
 

export const unCaclTaskActionAsync= (params, config = {}) => 
{   return request.post(apiPrefix + 'UnCaclTaskActionAsync', params, config)  }
 

export const shopInfoTaskActionAsync= (params, config = {}) => 
{   return request.post(apiPrefix + 'ShopInfoTaskActionAsync', params, config)  }
 

export const unShopInfoTaskActionAsync= (params, config = {}) => 
{   return request.post(apiPrefix + 'UnShopInfoTaskActionAsync', params, config)  }
 

export const shopTaskActionAsync= (params, config = {}) => 
{   return request.post(apiPrefix + 'ShopTaskActionAsync', params, config)  }
 

export const unShopTaskActionAsync= (params, config = {}) => 
{   return request.post(apiPrefix + 'UnShopTaskActionAsync', params, config)  }
 

export const deleteTaskActionAsync= (params, config = {}) => 
{   return request.post(apiPrefix + 'DeleteTaskActionAsync', params, config)  }
 
export const delTaskReActionAsync= (params, config = {}) => 
{   return request.post(apiPrefix + 'DelTaskReActionAsync', params, config)  }
export const dropTaskActionAsync= (params, config = {}) => 
{   return request.post(apiPrefix + 'DropTaskActionAsync', params, config)  } 


// 批量操作end------------------------------------------------------------------------------------------------------------------------------------

//独立详情备注start-----------------------------------------------------------------------------------------------------------------------------------------
//保存独立详情备注
export const saveShootingTaskMarkAsync= (params, config = {}) => 
{   return request.post(apiPrefix + 'SaveShootingTaskMarkAsync',params, config)  }
//删除详情备注
export const delShootingTaskMarkAsync= (params, config = {}) => 
{   return request.get(apiPrefix + 'DelShootingTaskMarkAsync',{ params: params, ...config }) }

//获取备注信息
export const getShootingTaskMarkAsync= (params, config = {}) => 
{   return request.get(apiPrefix + 'GetShootingTaskMarkAsync', { params: params, ...config }) }


//删除附件
export const delShootingTploadFileTaskAsync= (params, config = {}) => 
{   return request.get(apiPrefix + 'DelShootingTploadFileTaskAsync', { params: params, ...config }) }

//详情页
export const getPageDetailImgInfo= (params, config = {}) => 
{ return request.get(apiPrefix + 'GetPageDetailImgInfo', { params: params, ...config }) }

// 获取当前用户角色
export const getUserRoleList= (params, config = {}) => 
{   return request.post(apiPrefix + 'GetUserRoleList',params, config)}

//获取人员下拉
export const getShootingViewPersonAsync= (params, config = {}) => 
{ return request.get(apiPrefix + 'GetShootingViewPersonAsync', { params: params, ...config }) }

//校验任务是否完成
export const checkShootingTaskAction= (params, config = {}) => 
{ return request.get(apiPrefix + 'CheckShootingTaskAction', { params: params, ...config }) }

//导出
export const exportShootingTaskReport=
 (params, config = { responseType: 'blob' }) => { return  request.get(apiPrefix + 'ExportChangeImgeTaskReport', { params: params, ...config }) }

//下单发货
export const getShootingTaskOrderListById = (params, config = {}) => { return request.get(apiPrefix + 'GetShootingTaskOrderListById', { params: params, ...config }) }
export const shootingTaskAddOrderSaveCheckTaskIds = (params, config = {}) => { return request.post(apiPrefix + 'ShootingTaskAddOrderSaveCheckTaskIds', params, config) }
export const getCityAllData = (params, config = {}) => { return request.get(apiPrefix + 'GetCityAllData', { params: params, ...config }) }
export const shootingTaskAddOrderSave = (params, config = {}) => { return request.post(apiPrefix + 'ShootingTaskAddOrderSave', params, config) }


 

//saveShootingBhInfo
//获取备注信息
export const saveShootingBhInfo= (params, config = {}) => 
{   return request.get(apiPrefix + 'SaveShootingBhInfo', { params: params, ...config }) }


//统计模块---------------------------------------------------------

export const getPackDesginTaskJdStatistics = (params, config = {}) => { return request.post(apiPrefix + 'GetPackDesginTaskJdStatistics', params, config) }
export const getPackDesginYsFsStatistics = (params, config = {}) => { return request.post(apiPrefix + 'GetPackDesginYsFsStatistics', params, config) }
export const getPackDesginPackClassStatistics = (params, config = {}) => { return request.post(apiPrefix + 'GetPackDesginPackClassStatistics', params, config) }
export const getPackDesginPingPaiStatistics = (params, config = {}) => { return request.post(apiPrefix + 'GetPackDesginPingPaiStatistics', params, config) }
export const getPackDesginTaskFpStatistics = (params, config = {}) => { return request.post(apiPrefix + 'GetPackDesginTaskFpStatistics', params, config) } 
export const getPackDesginTaskJdDayStatistics = (params, config = {}) => { return request.post(apiPrefix + 'GetPackDesginTaskJdDayStatistics', params, config) } 

export const getPackDesginPackClassDayStatistics = (params, config = {}) => { return request.post(apiPrefix + 'GetPackDesginPackClassDayStatistics', params, config) } 
export const getPackDesginTaskTotalStatistics = (params, config = {}) => { return request.post(apiPrefix + 'GetPackDesginTaskTotalStatistics', params, config) } 
//统计模块---------------------------------------------------------