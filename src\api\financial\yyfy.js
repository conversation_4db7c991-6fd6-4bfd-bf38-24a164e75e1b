import request from '@/utils/request'

const <PERSON><PERSON><PERSON>uPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Shizhitou/`

const TuijianPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Tuijian/`

const TaobaokePrefix = `${process.env.VUE_APP_BASE_API_Financial}/Taobaoke/`

const TaotetuiguangPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Taotetuiguang/`

const ZhitongchePrefix = `${process.env.VUE_APP_BASE_API_Financial}/Zhitongche/`

const TaoteshangpingPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Taoteshangping/`

const DahuixiongPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Dahuixiong/`

const TaolijingPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Taolijing/`

const YingxiaoPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Yingxiao/`

const CuishouPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Cuishou/`

const GoodsBianMaPrefix = `${process.env.VUE_APP_BASE_API_Financial}/DayReport/`

const AccountResultPrefix = `${process.env.VUE_APP_BASE_API_Financial}/AccountResult/`

const CostDiffProductPrefix = `${process.env.VUE_APP_BASE_API_Financial}/CostDiffProduct/`

const CostDifforderPrefix = `${process.env.VUE_APP_BASE_API_Financial}/CostDifforder/`

const PingduoduoPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Pingduoduo/`

const UnusualPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Unusual/`

const CostDiffDividePrefix = `${process.env.VUE_APP_BASE_API_Financial}/CostDiffDivide/`
const JdOperatingCostPrefix = `${process.env.VUE_APP_BASE_API_Financial}/JdOperatingCost/`
const DYOperatingCostPrefix = `${process.env.VUE_APP_BASE_API_Financial}/DYOperatingCost/`

//const FinancialReportPrefix = `${process.env.VUE_APP_BASE_API_Financial}/FinancialReport/`
const FinancialReportPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/FinancialReport/`

const MonthBookKeeperPrefix = `${process.env.VUE_APP_BASE_API_MonthBookKeeper}/FinancialReport/`

export const importShizhitouAsync = (params, config = {}) => { return request.post(ShizhitouPrefix + 'ImportShizhitouAsync', params, config) }
export const getShizhitouList = (params, config = {}) => { return request.get(ShizhitouPrefix + 'GetShizhitouList', { params: params, ...config }) }
export const deleteShizhitouBatch = (params, config = {}) => { return request.get(ShizhitouPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const importTuijianAsync = (params, config = {}) => { return request.post(TuijianPrefix + 'ImportTuijianAsync', params, config) }
export const getTuijianList = (params, config = {}) => { return request.get(TuijianPrefix + 'GetTuijianList', { params: params, ...config }) }
export const deleteTuijianBatch = (params, config = {}) => { return request.get(TuijianPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const importTaobaokeAsync = (params, config = {}) => { return request.post(TaobaokePrefix + 'ImportTaobaokeAsync', params, config) }
export const getTaobaokeList = (params, config = {}) => { return request.get(TaobaokePrefix + 'GetTaobaokeList', { params: params, ...config }) }
export const deleteTaobaokeBatch = (params, config = {}) => { return request.get(TaobaokePrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const importTaoXiTuiGuangAsync = (params, config = {}) => { return request.post(TaobaokePrefix + 'ImportTaoXiTuiGuangAsync', params, config) }
export const getTaoXiTuiGuangFeeList = (params, config = {}) => { return request.get(TaobaokePrefix + 'GetTaoXiTuiGuangFeeList', { params: params, ...config }) }
export const computeTxTuiGuangFei = (params, config = {}) => { return request.post(TaobaokePrefix + 'ComputeTxTuiGuangFei', params, config) }



export const importTaotetuiguangAsync = (params, config = {}) => { return request.post(TaotetuiguangPrefix + 'ImportTaotetuiguangAsync', params, config) }
export const getTaotetuiguangList = (params, config = {}) => { return request.get(TaotetuiguangPrefix + 'GetTaotetuiguangList', { params: params, ...config }) }
export const deleteTaotetuiguangBatch = (params, config = {}) => { return request.get(TaotetuiguangPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const importZhitongcheAsync = (params, config = {}) => { return request.post(ZhitongchePrefix + 'ImportZhitongcheAsync', params, config) }
export const getZhitongcheList = (params, config = {}) => { return request.get(ZhitongchePrefix + 'GetZhitongcheList', { params: params, ...config }) }
export const deleteZhitongcheBatch = (params, config = {}) => { return request.get(ZhitongchePrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const importTaoteshangpingAsync = (params, config = {}) => { return request.post(TaoteshangpingPrefix + 'ImportTaoteshangpingAsync', params, config) }
export const getTaoteshangpingList = (params, config = {}) => { return request.get(TaoteshangpingPrefix + 'GetTaoteshangpingList', { params: params, ...config }) }
export const deleteTaoteshangpingBatch = (params, config = {}) => { return request.get(TaoteshangpingPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const importDahuixiongAsync = (params, config = {}) => { return request.post(DahuixiongPrefix + 'ImportDahuixiongAsync', params, config) }
export const getDahuixiongList = (params, config = {}) => { return request.get(DahuixiongPrefix + 'GetDahuixiongList', { params: params, ...config }) }
export const deleteDahuixiongBatch = (params, config = {}) => { return request.get(DahuixiongPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const importTaolijingAsync = (params, config = {}) => { return request.post(TaolijingPrefix + 'ImportTaolijingAsync', params, config) }
export const getTaolijingList = (params, config = {}) => { return request.get(TaolijingPrefix + 'GetTaolijingList', { params: params, ...config }) }
export const deleteTaolijingBatch = (params, config = {}) => { return request.get(TaolijingPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const importYingxiaoAsync = (params, config = {}) => { return request.post(YingxiaoPrefix + 'ImportYingxiaoAsync', params, config) }
export const getYingxiaoList = (params, config = {}) => { return request.get(YingxiaoPrefix + 'GetYingxiaoList', { params: params, ...config }) }
export const deleteYingxiaoBatch = (params, config = {}) => { return request.get(YingxiaoPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const importCuishouAsync = (params, config = {}) => { return request.post(CuishouPrefix + 'ImportCuishouAsync', params, config) }
export const getCuishouList = (params, config = {}) => { return request.get(CuishouPrefix + 'GetCuishouList', { params: params, ...config }) }
export const deleteCuishouBatch = (params, config = {}) => { return request.get(CuishouPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const accountResultAsync = (params, config = {}) => { return request.get(AccountResultPrefix + 'AccountDataAsync', { params: params, ...config }) }
export const getAccountResultList = (params, config = {}) => { return request.get(AccountResultPrefix + 'GetAccountResultList', { params: params, ...config }) }
export const deleteAccountResultBatch = (params, config = {}) => { return request.get(AccountResultPrefix + 'DeleteBatchAsync', { params: params, ...config }) }



export const importPingduoduoAsync = (params, config = {}) => { return request.post(PingduoduoPrefix + 'ImportPingduoduoAsync', params, config) }
export const importPingduoduo23Async = (params, config = {}) => { return request.post(PingduoduoPrefix + 'ImportPingduoduo23Async', params, config) }
export const computePddTuiGuangFei = (params, config = {}) => { return request.post(PingduoduoPrefix + 'ComputePddTuiGuangFei', params, config) }
export const getPingduoduoList = (params, config = {}) => { return request.get(PingduoduoPrefix + 'GetPingduoduoList', { params: params, ...config }) }
export const deletePingduoduoBatch = (params, config = {}) => { return request.get(PingduoduoPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const chooseUnusualGroupCompute = (params, config = {}) => { return request.get(UnusualPrefix + 'ChooseGroupCompute', { params: params, ...config }) }


export const getUnusualList = (params, config = {}) => { return request.get(UnusualPrefix + 'GetUnusualList', { params: params, ...config }) }
export const deleteUnusualBatch = (params, config = {}) => { return request.get(UnusualPrefix + 'DeleteBatchAsync', { params: params, ...config }) }


const TotalByfeeTypePrefix = `${process.env.VUE_APP_BASE_API_Financial}/TotalByfeeType/`
export const getTotalByfeeTypeList = (params, config = {}) => { return request.get(TotalByfeeTypePrefix + 'GetTotalByfeeTypeList', { params: params, ...config }) }


export const importCostDiffProductAsync = (params, config = {}) => { return request.post(CostDiffProductPrefix + 'ImportCostDiffProductAsync', params, config) }
export const getCostDiffProductList = (params, config = {}) => { return request.get(CostDiffProductPrefix + 'GetCostDiffProductList', { params: params, ...config }) }
export const deleteCostDiffProductBatch = (params, config = {}) => { return request.get(CostDiffProductPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const importCostDifforderAsync = (params, config = {}) => { return request.post(CostDifforderPrefix + 'ImportCostDifforderAsync', params, config) }
export const getCostDifforderList = (params, config = {}) => { return request.get(CostDifforderPrefix + 'GetCostDifforderList', { params: params, ...config }) }
export const deleteCostDifforderBatch = (params, config = {}) => { return request.get(CostDifforderPrefix + 'DeleteBatchAsync', { params: params, ...config }) }


export const accountCostDiffAsync = (params, config = {}) => { return request.get(CostDiffDividePrefix + 'AccountCostDiffAsync', { params: params, ...config }) }
export const getCostDiffDivideList = (params, config = {}) => { return request.get(CostDiffDividePrefix + 'GetCostDiffDivideList', { params: params, ...config }) }
export const deleteCostDiffDivideBatch = (params, config = {}) => { return request.get(CostDiffDividePrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const getFinancialReportList = (params, config = {}) => { return request.get(MonthBookKeeperPrefix + 'GetFinancialReportList', { params: params, ...config }) }

export const importDayReportAsync = (params, config = {}) => { return request.post(GoodsBianMaPrefix + 'ImportDayReportAsync', params, config) }
export const getPageDayReportList = (params, config = {}) => { return request.get(GoodsBianMaPrefix + 'GetPageDayReportListAsync', { params: params, ...config }) }
export const getGoodsPercentagePointsByCode = (params, config = {}) => { return request.get(GoodsBianMaPrefix + 'GetGoodsPercentagePointsByCode', { params: params, ...config }) }
export const getDayReportEDetailAsync = (params, config = {}) => { return request.get(GoodsBianMaPrefix + 'GetDayReportEDetailAsync', { params: params, ...config }) }
export const batchGetGoodsPercentagePointsByCode = (params, config = {}) => { return request.get(GoodsBianMaPrefix + 'BatchGetGoodsPercentagePointsByCode', { params: params, ...config }) }
export const addOrUpdateGoodsPercentagePoints = (params, config = {}) => { return request.post(GoodsBianMaPrefix + 'AddOrUpdateGoodsPercentagePoints', params, config) }
export const batchAddOrUpdateProduct = (params, config = {}) => { return request.post(GoodsBianMaPrefix + 'BatchAddOrUpdateProduct', params, config) }



export const importJdExpress = (params, config = {}) => { return request.post(JdOperatingCostPrefix + 'ImportJdExpressAsync', params, config) }
export const importJdWholeSiteMarketing = (params, config = {}) => { return request.post(JdOperatingCostPrefix + 'ImportJdWholeSiteMarketingAsync', params, config) }


export const exportJdWholeSiteMarketingList = (params, config = { responseType: 'blob' }) => { return request.get(JdOperatingCostPrefix + `ExportJdWholeSiteMarketingList`, { params, ...config }) }
export const exportJdExpressList = (params, config = { responseType: 'blob' }) => { return request.get(JdOperatingCostPrefix + `ExportJdExpressList`, { params, ...config }) }

export const getjSpeedDriveList = (params, config = {}) => { return request.get(JdOperatingCostPrefix + 'GetjSpeedDriveList', { params: params, ...config }) }

export const getJdExpressList = (params, config = {}) => { return request.get(JdOperatingCostPrefix + 'GetJdExpressList', { params: params, ...config }) }
export const getJdWholeSiteMarketingList = (params, config = {}) => { return request.get(JdOperatingCostPrefix + 'GetJdWholeSiteMarketingList', { params: params, ...config }) }
export const deleteJdWholeSiteMarketingBatch = (params, config = {}) => { return request.get(JdOperatingCostPrefix + 'DeleteJdWholeSiteMarketingBatchAsync', { params: params, ...config }) }
export const importjSpeedDrive = (params, config = {}) => { return request.post(JdOperatingCostPrefix + 'ImportjSpeedDriveAsync', params, config) }

export const deletejSpeedDriveBatch = (params, config = {}) => { return request.get(JdOperatingCostPrefix + 'DeletejSpeedDriveBatchAsync', { params: params, ...config }) }
export const deleteJdExpressBatch = (params, config = {}) => { return request.get(JdOperatingCostPrefix + 'DeleteJdExpressBatchAsync', { params: params, ...config }) }
export const importJLThousandRivers = (params, config = {}) => { return request.post(DYOperatingCostPrefix + 'ImportJLThousandRiversAsync', params, config) }
export const getJLThousandRiversList = (params, config = {}) => { return request.get(DYOperatingCostPrefix + 'GetJLThousandRiversList', { params: params, ...config }) }
export const deleteJLThousandRiversBatch = (params, config = {}) => { return request.delete(DYOperatingCostPrefix + 'DeleteJLThousandRiversBatchAsync', { params: params, ...config }) }
export const importYingXiaoBao = (params, config = {}) => { return request.post(DYOperatingCostPrefix + 'ImportYingXiaoBaoAsync', params, config) }
export const getYingXiaoBaoList = (params, config = {}) => { return request.get(DYOperatingCostPrefix + 'GetYingXiaoBaoList', { params: params, ...config }) }
export const deleteYingXiaoBaoBatch = (params, config = {}) => { return request.delete(DYOperatingCostPrefix + 'DeleteYingXiaoBaoBatchAsync', { params: params, ...config }) }
export const importZhanXiaoBao = (params, config = {}) => { return request.post(DYOperatingCostPrefix + 'ImportZhanXiaoBaoAsync', params, config) }
export const getZhanXiaoBaoList = (params, config = {}) => { return request.get(DYOperatingCostPrefix + 'GetZhanXiaoBaoList', { params: params, ...config }) }
export const deleteZhanXiaoBaoBatch = (params, config = {}) => { return request.delete(DYOperatingCostPrefix + 'DeleteZhanXiaoBaoBatchAsync', { params: params, ...config }) }
export const importFirstDisplay = (params, config = {}) => { return request.post(DYOperatingCostPrefix + 'ImportFirstDisplayAsync', params, config) }
export const getFirstDisplayList = (params, config = {}) => { return request.get(DYOperatingCostPrefix + 'GetFirstDisplayList', { params: params, ...config }) }
export const deleteFirstDisplayBatch = (params, config = {}) => { return request.delete(DYOperatingCostPrefix + 'DeleteFirstDisplayBatchAsync', { params: params, ...config }) }
export const importPushBroadcastRoom = (params, config = {}) => { return request.post(DYOperatingCostPrefix + 'ImportPushBroadcastRoomAsync', params, config) }
export const getPushBroadcastRoomList = (params, config = {}) => { return request.get(DYOperatingCostPrefix + 'GetPushBroadcastRoomList', { params: params, ...config }) }
export const deletePushBroadcastRoomBatch = (params, config = {}) => { return request.delete(DYOperatingCostPrefix + 'DeletePushBroadcastRoomBatchAsync', { params: params, ...config }) }
export const importOperationalSharingScheme = (params, config = {}) => { return request.post(DYOperatingCostPrefix + 'ImportOperationalSharingSchemeAsync', params, config) }
export const getOperationalSharingSchemeList = (params, config = {}) => { return request.get(DYOperatingCostPrefix + 'GetOperationalSharingSchemeList', { params: params, ...config }) }
export const importGrossThirdAndProfit = (params, config = {}) => { return request.post(DYOperatingCostPrefix + 'ImportGrossThirdAndProfitAsync', params, config) }
export const getGrossThirdAndProfitList = (params, config = {}) => { return request.get(DYOperatingCostPrefix + 'GetGrossThirdAndProfitList', { params: params, ...config }) }
export const calcUnderlyingDataTemplate = (params, config = {}) => { return request.post(DYOperatingCostPrefix + 'CalcUnderlyingDataTemplateAsync', params, config) }
export const getUnderlyingDataTemplateList = (params, config = {}) => { return request.get(DYOperatingCostPrefix + 'GetUnderlyingDataTemplateList', { params: params, ...config }) }
export const exportOperatingCommission = (params, config = { responseType: 'blob' }) => { return request.post(DYOperatingCostPrefix + 'ExportOperatingCommission', params, config) }
export const importintelligentmarketingprogram = (params, config = {}) => { return request.post(DYOperatingCostPrefix + 'ImportIntelligentMarketingProgramAsync', params, config) }
export const getintelligentmarketingprogramlist = (params, config = {}) => { return request.get(DYOperatingCostPrefix + 'GetIntelligentMarketingProgramList', { params: params, ...config }) }
export const deleteIntelligentMarketingProgramBatch = (params, config = {}) => { return request.delete(DYOperatingCostPrefix + 'DeleteIntelligentMarketingProgramBatchAsync', { params: params, ...config }) }
export const importAllStationDelivery = (params, config = {}) => { return request.post(DYOperatingCostPrefix + 'ImportAllStationDeliveryAsync', params, config) }
export const getAllStationDeliverylist = (params, config = {}) => { return request.get(DYOperatingCostPrefix + 'GetAllStationDeliveryList', { params: params, ...config }) }
export const deleteAllStationDeliveryBatch = (params, config = {}) => { return request.delete(DYOperatingCostPrefix + 'DeleteAllStationDeliveryBatchAsync', { params: params, ...config }) }

