import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_PddPlatform}/productoperationlog/`

export const importProCodeActionAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportProductOperationLogAsync', params, config)
}

export const getProductOperationLogListAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetProductOperationLogListAsync', params, config)
}


export const getProductOperationLogMap = (params, config = {}) => {
    return request.post(apiPrefix + 'GetProductOperationLogMap', params, config)
}

// 导出产品操作日志列表
export const exportProductOperationLogListAsync = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportProductOperationLogListAsync', params, config)
}


// 获取产品推广
export const getProductPromoAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetProductPromoAsync', params, config)
}

// 导出产品操作日志列表
export const exportProductPromoAsync = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportProductPromoAsync', params, config)
}
// //产品操作日志列表
// export const getProductOperationLogListAsync = (params, config = {}) => {
//     return request.post(apiPrefix + 'GetProductOperationLogListAsync', params, config)
// }