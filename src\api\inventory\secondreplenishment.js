import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/SecondReplenishment/`

//导入盘点数据
export const importSecondReplenishmentStoreInventory= (params, config = {}) => {return request.post(apiPrefix + 'ImportSecondReplenishmentStoreInventory', params, config)}

//导入按箱上架数据
export const importSecondReplenishmentOnlineStoreInventory= (params, config = {}) => {return request.post(apiPrefix + 'ImportSecondReplenishmentOnlineStoreInventory', params, config)}

//获取列表数据
export const getSecondReplenishmentData= (params, config = {}) => {return request.post(apiPrefix + 'GetSecondReplenishmentData', params, config)}

//盘点操作日志导出
export const exportSecondReplenishmentData = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportSecondReplenishmentData', params, config) }

//人员统计
export const getSecondReplenishmentDataGroupUser= (params, config = {}) => {return request.post(apiPrefix + 'GetSecondReplenishmentDataGroupUser', params, config)}

//次数统计
export const getSecondReplenishmentDataGroupCount= (params, config = {}) => {return request.post(apiPrefix + 'GetSecondReplenishmentDataGroupCount', params, config)}

//仓库次数统计
export const getSecondReplenishmentDataGroupStore= (params, config = {}) => {return request.post(apiPrefix + 'GetSecondReplenishmentDataGroupStore', params, config)}
