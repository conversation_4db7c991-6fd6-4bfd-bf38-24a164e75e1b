<template>
  <el-input v-model.trim="innerValue" :placeholder="placeholder" :clearable="clearable" :maxlength="maxlength"
    :disabled="disabled" @input="handleAmountInput" @blur="handleAmountBlur" @clear="handleAmountClear"
    :style="cststyle" />
</template>

<script>
export default {
  name: 'inputNumberYh',
  props: {
    value: {
      type: [String, Number, null], // 支持数字、字符串和 null
      default: null, // 父组件未初始化时默认为 null
    },
    placeholder: {
      type: String,
      default: '请输入数值',
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    cststyle: {
      type: Object,
      default: () => ({}),
    },
    min: {
      type: Number,
      default: 0,
    },
    max: {
      type: Number,
      default: 9999999,
    },
    maxlength: {
      type: Number,
      default: 50,
    },
    fixed: {
      type: Number,
      default: 0, // 默认保留整数
      validator(value) {
        return value >= 0; // fixed 必须为非负整数
      },
    },
  },
  data() {
    return {
      innerValue: this.formatFixedValue(this.value),
      isUserTyping: false, // 标记用户是否正在输入（统一使用这个变量名）
    };
  },
  watch: {
    value(newValue) {
      // 只有在用户不是正在输入时才格式化显示
      if (!this.isUserTyping) {
        this.innerValue = this.formatFixedValue(newValue);
      }
    },
  },
  mounted() {
    if (this.isValidNumber(this.value)) {
      this.$emit('input', parseFloat(this.value)); // 确保父组件接收有效初始值
    } else {
      this.$emit('input', null);
    }
  },
  methods: {
    formatFixedValue(val) {
      if (this.isValidNumber(val)) {
        const num = parseFloat(val);
        // 智能格式化：只有当数字有小数部分时才显示小数位
        if (this.fixed > 0 && num % 1 !== 0) {
          return num.toFixed(this.fixed);
        } else {
          return String(Math.round(num));
        }
      }
      return null;
    },
    handleAmountClear() {
      this.isUserTyping = false; // 清空时重置输入状态
      this.$emit('input', null); // 传递 null 表示无效值
      this.$emit('blur', null); // 触发失去焦点事件
    },
    isValidNumber(val) {
      if (typeof val === 'number' && !isNaN(val)) {
        return true; // 如果已经是数字且有效，直接返回 true
      }
      if (typeof val === 'string') {
        const parsed = parseFloat(val); // 尝试将字符串解析为数字
        return !isNaN(parsed); // 检查解析结果是否为有效数字
      }
      return false; // 非数字或无法解析为数字的情况返回 false
    },
    handleAmountInput(value) {
      this.isUserTyping = true; // 标记用户正在输入

      // 如果 fixed 为 0，表示不允许小数，去除小数部分
      let regex;
      let filteredValue;
      if (this.min < 0) {
        regex = this.fixed > 0 ? new RegExp(`^-?\\d*(\\.\\d{0,${this.fixed}})?`) // 动态生成正则，支持负号，限制小数位
          : new RegExp('^-?\\d*'); // 只允许整数，支持负号
        filteredValue = value.replace(/[^0-9.-]/g, ''); // 仅保留数字、负号、小数点
      } else if (this.min >= 0) {
        regex = this.fixed > 0 ? new RegExp(`^\\d*(\\.\\d{0,${this.fixed}})?`) // 动态生成正则，根据 fixed 限制小数位
          : new RegExp('^\\d*'); // 只允许整数
        filteredValue = value.replace(/[^0-9.]/g, ''); // 仅保留数字和小数点
      }
      const validValue = filteredValue.match(regex)?.[0] || ''; // 根据正则匹配有效值
      const numericValue = parseFloat(validValue);

      if (isNaN(numericValue) || numericValue < this.min || numericValue > this.max) {
        // this.$emit('input', null); // 清空父组件值
        this.innerValue = validValue; // 显示过滤后的输入
      } else {
        this.$emit('input', numericValue); // 更新父组件值为数字类型
        this.innerValue = validValue; // 保持用户输入的原始格式，不立即格式化
      }
    },
    handleAmountBlur() {
      this.isUserTyping = false; // 失去焦点时重置输入状态

      if (!this.innerValue) {
        this.$emit('input', null); // 输入为空时传递 null
        this.$emit('blur', null); // 触发失去焦点事件
        this.innerValue = '';
        return;
      }
      const numericValue = parseFloat(this.innerValue);
      if (isNaN(numericValue)) {
        this.$emit('input', null); // 传递 null 表示无效值
        this.$emit('blur', null); // 触发失去焦点事件
        this.innerValue = '';
      } else if (numericValue < this.min) {
        this.$emit('input', this.min); // 传递数字类型
        this.$emit('blur', this.min); // 触发失去焦点事件
        this.innerValue = this.formatFixedValue(this.min); // 使用统一的格式化方法
      } else if (numericValue > this.max) {
        this.$emit('input', this.max); // 传递数字类型
        this.$emit('blur', this.max); // 触发失去焦点事件
        this.innerValue = this.formatFixedValue(this.max); // 使用统一的格式化方法
      } else {
        this.$emit('input', numericValue); // 传递数字类型
        this.$emit('blur', numericValue); // 触发失去焦点事件
        this.innerValue = this.formatFixedValue(numericValue); // 失去焦点时格式化显示
      }
    },
  },
};
</script>
