import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/soldoutgoods/`

//生成下架数据
export const createSoldOutGoods = (params, config = {}) => {
  return request.post(apiPrefix + 'CreateSoldOutGoodsAsync', params, config)
}

//查询下架商品
export const pageSoldOutGoods = (params, config = {}) => {
  return request.post(apiPrefix + 'PageSoldOutGoodsAsync', params, config)
}

//获取登录用户的运营组
export const getLoginUserDirectorGroup = (params, config = {}) => {
  return request.get(apiPrefix + 'GetLoginUserDirectorGroupAsync', {params, ...config})
}
// //删除
// export const deleteSoldOutGoods = (params, config = {}) => {
//   return request.get(apiPrefix + 'DeleteSoldOutGoodsAsync', {params,...config})
// }
//删除
export const deleteSoldOutGoods = (params, config = {}) => { return request.post(apiPrefix + 'DeleteSoldOutGoodsAsync', params, config)}

//查询下架商品明细
export const pageSoldOutGoodsDetail = (params, config = {}) => {
  return request.post(apiPrefix + 'PageSoldOutGoodsDetailAsync', params, config)
}

//修改状态
export const changeSoldOutGoodsStatus = (params, config = {}) => {
  return request.post(apiPrefix + 'ChangeSoldOutGoodsStatusAsync', params,config)
}

//编码转派
export const ChangeSoldOutGoodsRedeploy = (params, config = {}) => {
  return request.post(apiPrefix + 'ChangeSoldOutGoodsRedeployAsync', params,config)
}

export const exportSoldOutGoods = (params, config = {responseType: 'blob'}) => {
  return request.get(apiPrefix + 'ExportSoldOutGoodsAsync',  { params: params, ...config })
}

export const exportSoldOutGoodsDetail = (params, config = {responseType: 'blob'}) => {
  return request.get(apiPrefix + 'ExportSoldOutGoodsDetailAsync',  { params: params, ...config })
}


