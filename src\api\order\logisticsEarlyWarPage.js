import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/LogisticsEarlyWarPage/`
export const getLogisticsEarlyWarDataAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageLogisticsEarlyWarDataAsync', params, config) }
export const importLogisticsEarlyWarAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportLogisticsEarlyWarAsync', params, config) }
export const exportLogisticsEarlyWarDataAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportLogisticsEarlyWarDataAsync', params, config) }
export const getExceptionTypeAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetExceptionTypeAsync', params, config) }
export const getOrderStatusListAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderStatusListAsync', params, config) }

//获取订单日志
export const getOrderLogListAsync = (orderNoInner, config = {}) => {
    return request.get(apiPrefix + `GetOrderLogListAsync?orderNoInner=${orderNoInner}`, {}, config)
}
//获取汇总页面取数
export const getLogisticsEarlyWarStatDataAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageLogisticsEarlyWarStatDataAsync', params, config) }
//获取汇总页面取数
export const getLogisticsEarlyWarStatDataByTypeAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageLogisticsEarlyWarStatDataByTypeAsync', params, config) }
//获取汇总页面趋势图取数
export const getLogisticsEarlyWarStatAnalysisAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetLogisticsEarlyWarStatAnalysisAsync', params, config) }

//获取物流预警日志信息
export const getLogisticsEarlyWarLogListAsync = (orderNoInner, config = {}) => {
    return request.get(apiPrefix + `GetLogisticsEarlyWarLogListAsync?orderNoInner=${orderNoInner}`, {}, config)
}

export const exportLogisticsEarlyWarStatDataAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportLogisticsEarlyWarStatDataAsync', params, config) }


export const getOrderInvalidShipmentDataAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageOrderInvalidShipmentDataAsync', params, config) }

const apiClickPrefix = `${process.env.VUE_APP_JAVA_API_BLADEGATEWAY}/yunhan-gis-click/`
export const logisticsWarningPage = (params, config = {}) => { return request.post(apiClickPrefix + 'llogisticsWarning/page', params, config) }
export const logisticsWarningExport = (params, config = {}) => { return request.post(apiClickPrefix + 'llogisticsWarning/export', params, config) }
//下载管理详情
export const exportLogDetail = (params, config = {}) => {return request.post(apiClickPrefix + 'log_export/getTaskProgressBar',  params,  config )}
//获取物流预警日志信息
export const logisticsTraceRecord = (trackingNum, platformType, config = {}) => {
    return request.get(apiClickPrefix + `llogisticsTrace/record?trackingNum=${trackingNum}&platformType=${platformType}`, {}, config)
}