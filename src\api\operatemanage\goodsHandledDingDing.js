import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/GoodsHandledDingDing/`

//获取编码处理页面列表
export const getGoodsHandledDingDingPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetGoodsHandledDingDingPageList', params, config)}

//编码处理钉钉流程发送消息记录分页查询
export const getGoodsHandledDingDingMsgLogPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetGoodsHandledDingDingMsgLogPageList', params, config)}

//编码采购退货出库钉钉流程分页查询
export const getGoodsBackDingDingPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetGoodsBackDingDingPageList', params, config)}

//编码采购退货出库钉钉流程发送消息记录分页查询
export const getGoodsBackDingDingMsgLogPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetGoodsBackDingDingMsgLogPageList', params, config)}