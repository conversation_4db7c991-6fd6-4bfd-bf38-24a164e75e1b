<template>
  <div v-if="condition">
    <el-select v-model="condition.operator" placeholder="请选择" class="operator" clearable @change="change">
      <el-option label="=" value="Equal" />
      <el-option label="!=" value="NotEqual" />
      <template v-if="condition.fieldType=='String' || condition.fieldType=='Char'">
        <el-option label="包含" value="Contains" />
        <el-option label="不包含" value="NotContains" />
        <el-option label="前包含" value="StartsWith" />
        <el-option label="后包含" value="EndsWith" />
        <el-option label="前不包含" value="NotStartsWith" />
        <el-option label="后不包含" value="NotEndsWith" />
      </template>
      <template v-else>
        <el-option label=">" value="GreaterThan" />
        <el-option label=">=" value="GreaterThanOrEqual" />
        <el-option label="<" value="LessThan" />
        <el-option label="<=" value="LessThanOrEqual" />
      </template>
      <el-option v-if="condition.fieldType=='Single' || condition.fieldType=='Decimal' || condition.fieldType=='Double' || condition.fieldType=='Int64' || condition.fieldType=='Int32'" label=">= …… <" value="Range" />
      <el-option v-if="condition.fieldType=='DateTime' || condition.fieldType=='Date'" label=">= …… <" value="DateRange" />
      <el-option label="存在于" value="Any" />
      <el-option label="不存在于" value="NotAny" />
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'Operator',
  model: {
    prop: 'value',
    event: 'update'
  },
  props: {
    value: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      condition: { fieldType: 'String' },
      operator: "Equal"
    }
  },
  watch: {
    value: {
      handler() {
        this.valueChange()
      },
      deep: true
    }
  },
  mounted() {
    this.valueChange()
  },
  methods: {
    valueChange() {
      this.condition = this.value
      if (!this.condition.fieldType) { this.condition.fieldType = 'String' }
      this.$forceUpdate()
    },
    change() {
      this.condition.value = null
      this.$emit('update', this.condition)
      this.$forceUpdate()
    }
  }
}
</script>
<style scoped lang="scss">
.operator{width:100px; text-align: center;}
</style>
