import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/import/`

export const importFirstNewIdV2 = (params, config = {}) => { return request.post(apiPrefix + 'ImportFirstNewIdV2Async', params, config) }
export const importFirstNewQianNiuDetailV2 = (params, config = {}) => { return request.post(apiPrefix + 'ImportFirstNewQianNiuDetailV2Async', params, config) }
export const importFinacialZFBDay = (params, config = {}) => { return request.post(apiPrefix + 'ImportFinacialZFBDayAsync', params, config) }
export const importAgentSendCost = (params, config = {}) => { return request.post(apiPrefix + 'ImportAgentSendCostAsync', params, config) }
export const importProductDayReport = (params, config = {}) => { return request.post(apiPrefix + 'ImportProductDayReportAsync', params, config) }
export const importPddDayReportGoodsOtherInfo = (params, config = {}) => { return request.post(apiPrefix + 'ImportPddDayReportGoodsOtherInfoAsync', params, config) }
export const ImportBusinessStaffPlatForm = (params, config = {}) => { return request.post(apiPrefix + 'ImportBusinessStaffPlatForm', params, config) }
export const importTaoKeNoMeter = (params, config = {}) => { return request.post(apiPrefix + 'ImportTaoKeNoMeter', params, config) }
export const importShopProfitAnalysis = (params, config = {}) => { return request.post(apiPrefix + 'ImportShopProfitAnalysis', params, config) }
export const importOperatingProfitAnalysis = (params, config = {}) => { return request.post(apiPrefix + 'ImportOperatingProfitAnalysis', params, config) }
export const importShopProfitsCourierFee = (params, config = {}) => { return request.post(apiPrefix + 'ImportShopProfitsCourierFee', params, config) }
export const importPDDProfitAnalysisDetail1 = (params, config = {}) => { return request.post(apiPrefix + 'ImportPDDProfitAnalysisDetail1', params, config) }
export const importPDDProfitAnalysisDetail2 = (params, config = {}) => { return request.post(apiPrefix + 'ImportPDDProfitAnalysisDetail2', params, config) }
export const importBillFee = (params, config = {}) => { return request.post(apiPrefix + 'ImportBillFeeAsync', params, config) }
export const importSaleAfterTx = (params, config = {}) => { return request.post(apiPrefix + 'ImportSaleAfterTxAsync', params, config) }
//导出拼多多售后退款
export const importSaleAfterPdd = (params, config = {}) => { return request.post(apiPrefix + 'ImportSaleAfterPddAsync', params, config) }

//抖音账号导入
export const importAccountNumberDY = (params, config = {}) => { return request.post(apiPrefix + 'ImportAccountNumberDYAsync', params, config) }

//小额打款导入
export const importPettyPaymentPDD = (params, config = {}) => { return request.post(apiPrefix + 'ImportPettyPaymentPDDAsync', params, config) }
//新版拼多多日报账单费用导入
export const importNewPddBillingCharge = (params, config = {}) => { return request.post(apiPrefix + 'ImportNewPddBillingChargeAsync', params, config) }
//新版拼多多日报售后主题分析导入
export const importAfterSalesSubjectAnalysis = (params, config = {}) => { return request.post(apiPrefix + 'ImportAfterSalesSubjectAnalysisAsync', params, config) }

//白名单导入
export const importProfitLossRemovalWhiteList = (params, config = {}) => { return request.post(apiPrefix + 'ImportProfitLossRemovalWhiteList', params, config) }