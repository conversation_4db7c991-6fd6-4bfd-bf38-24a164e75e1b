import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Media}/referencemanage/`
 
 
//列表查询
export const pageReferenceViewTaskAsync = (params, config = {}) => 
{ return request.get(apiPrefix + 'PageReferenceViewTaskAsync', { params: params, ...config }) }



// 获取对应数据
export const getReferenceMainReferencs = (params, config = {}) => 
{ return request.get(apiPrefix + 'GetReferenceMainReferencs', { params: params, ...config }) }


//图片相关保存
export const saveReferenceMainReferencsForImg = (params, config = {}) => 
{ return request.post(apiPrefix + 'SaveReferenceMainReferencsForImg', params, config ) }


//视频相关保存
export const saveReferenceMainReferencsForVedio = (params, config = {}) => 
{ return request.post(apiPrefix + 'SaveReferenceMainReferencsForVedio', params, config ) }

//sku相关保存
export const saveReferenceMainReferencsForSku = (params, config = {}) => 
{ return request.post(apiPrefix + 'SaveReferenceMainReferencsForSku', params, config ) }


export const saveReferenceMainReferencs = (params, config = {}) => 
{ return request.post(apiPrefix + 'SaveReferenceMainReferencs', params, config ) }

// //全部数据
// export const getReferenceMainReferencsAll = (params, config = {}) => 
// { return request.post(apiPrefix + 'GetReferenceMainReferencsAll',{ params, ...config } ) }

//获取单个
export const getReferenceMainReferencsAll = (id, config = {}) => {
    return request.post(apiPrefix + `GetReferenceMainReferencsAll?taskId=${id}`, {}, config)
}