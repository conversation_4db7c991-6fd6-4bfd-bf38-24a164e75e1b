<template>
  <jwChat ref="jwChat" :config="config" :showRightBox='false' :scrollType="scrollType" :taleList="MsgList"
    @enter="bindEnter" v-model="inputMsg" :toolConfig="tool" :winBarConfig="winBarConfig">
    <template slot="tools">
      <el-upload class="avatar-uploader" style="width:26px;height:21px;display:block;" action="#"
        :show-file-list="false" :http-request="imgsearchRequest" :before-upload="beforeAvatarUpload">
        <svg data-v-b4bb0df6="" aria-hidden="false" class="icon toolIcon"
          style="width:26px;height:21px;display:block;fill: currentColor;" title="图片">
          <use xlink:href="#icon-xiangce"></use>
        </svg>
      </el-upload>
    </template>
  </jwChat>
</template>

<script>
import { getHotSaleGoodChatUserListsAsync, getHotSaleGoodChatUserInfosAsync, getHotSaleGoodChatByUserIdAsync, hotSaleGoodChatDDUserAsync, updateHotSaleGoodChatSendMesgAsync,deleteHotSaleGoodChat } from "@/api/admin/user"
import store from '@/store'
import jwChat from '@/components/Chat/ChatInterface/index'
import { formatTime } from "@/utils/tools";
import Cookies from "js-cookie";
const signalR = require('@microsoft/signalr')
const avatarDefault = require('@/assets/images/avatar.png')
const logoDefault = require('@/assets/images/yhlogo.jpg')

export default {
  name: 'chatComponents',
  components: { jwChat },
  props: {
    winBarlist: {
      type: Array
    },
    hotSaleGoodsChooseEnquiryId: {
      default: -1
    }
  },
  data() {
    return {
      pageLoading: false,
      inputMsg: '',
      MsgList: [],
      tool: {
        showEmoji: true
      },
      config: {
        img: '',
        name: '',
        dept: '',
        historyConfig: {
          show: true,
          tip: '加载更多',
          callback: this.bindLoadHistory,
        }
      },
      chatVisible: false,
      pageIndex: 1,
      pageCount: 10,
      signalRconnection: '',
      chatDialogTitle: '',
      isEnd: false,
      chatInfo: {
        sendUserId: 0,
        sendDDUserId: 0,
        sendUserName: '',
        sendUserAvatar: '',
        sendUserShowName: '',
        sendUserShowImg: '',
        sendUserShowDept: '',

        //接收人信息
        receivedUserId: 0,
        receivedDDUserId: 0,
        receivedShowName: '',
        receivedShowImg: '',
        receivedShowDept: ''
      },
      winBarConfig: {
        active: '',
        width: '190px',
        listHeight: '70px',
        list: this.winBarlist,
        callback: this.bindWinBar
      },
      hotSaleGoodsId: this.hotSaleGoodsChooseEnquiryId,
      scrollType: 'noroll' 
    }
  },
  async created() {
    //this.initSignalR();
  },
  async mounted() {
    this.refreshData();
  },
  async beforeDestroy() {
    this.signalRconnection.stop()
  },
  methods: {
    initSignalR() {
      this.signalRconnection = new signalR.HubConnectionBuilder().withUrl(process.env.VUE_APP_BASE_API + '/ChatHub', {
        accessTokenFactory: () => { return store.getters.token },
        skipNegotiation: true,
        transport: signalR.HttpTransportType.WebSockets,
      }).withAutomaticReconnect([3000, 5000, 10000, 15000, 30000]).build()
      this.signalRconnection.on('ConnectSucceeded', data => { console.log(data) })
      this.signalRconnection.on('SaleGoodChatMessage', data => { this.reciveMessage(data) })
      this.signalRconnection.start()
    },
    async reciveMessage(msg) {
      if (!msg) return;
      if (msg.sendUserId == this.chatInfo.receivedUserId && msg.receivedUserId == this.chatInfo.sendUserId && msg.hotSaleGoodsChooseEnquiryId == this.hotSaleGoodsId) {
        msg.chatMsg.mine = false
        msg.chatMsg.name = this.chatInfo.receivedShowName
        msg.chatMsg.img = this.chatInfo.receivedShowImg
        this.MsgList.push(msg.chatMsg)
        //异步通知后台此消息已读
        updateHotSaleGoodChatSendMesgAsync({ messageId: msg.chatMsg.msgId, receivedUserId: msg.receivedUserId })
        if (this.winBarConfig.list != null) {
          let winBarId = msg.sendUserId + '-' + msg.hotSaleGoodsChooseEnquiryId;
          let userObj = this.winBarConfig.list.find(item => item.id == winBarId)
          userObj.dept = msg.chatMsg.date
        }
      } else {
        if (this.winBarConfig.list != null) {
          let winBarId = msg.sendUserId + '-' + msg.hotSaleGoodsChooseEnquiryId;
          let userObj = this.winBarConfig.list.find(item => item.id == winBarId)
          if (userObj == null) {
            this.winBarConfig.list.push({
              id: winBarId,
              img: this.getRecivedImage(msg.sendUserShowImg),
              name: msg.sendUserShowName,
              dept: msg.chatMsg.date,
              readNum: this.winBarConfig.list.length > 0 ? 1 : 0
            });
            await this.selectDefault();
          } else {
            userObj.readNum = isNaN(userObj.readNum) ? 1 : (parseInt(userObj.readNum) + 1)
            userObj.dept = msg.chatMsg.date
          }
        }
      }
    },
    async resetTalkUserInfo(chatToUserId, hotId) {
      let res = await getHotSaleGoodChatUserInfosAsync({ chatToUserId: chatToUserId, hotSaleGoodsChooseEnquiryId: hotId, messageId: null })
      if (!res?.success) return

      //初始化发送人信息
      let sendImg = res.data.sendUserImg;
      this.chatInfo.sendUserAvatar = this.getAvaImage(sendImg)
      this.chatInfo.sendUserId = res.data.sendUserId
      this.chatInfo.sendDDUserId = res.data.sendUserDDId
      this.chatInfo.sendUserName = res.data.sendUserName

      //初始化接收人信息 
      this.chatInfo.receivedUserId = res.data.receivedUserId
      this.chatInfo.receivedDDUserId = res.data.receivedUserDDId
      this.chatInfo.receivedShowName = res.data.receivedShowName
      this.chatInfo.receivedShowImg = this.getRecivedImage(res.data.receivedShowImg)
      this.chatInfo.receivedShowDept = res.data.receivedShowDept

      //获取聊天的用户信息，名称、部门、头像 
      this.config.name = this.chatInfo.receivedShowName;
      this.chatDialogTitle = '【' + this.chatInfo.receivedShowName + '】询价'
      this.config.img = this.chatInfo.receivedShowImg
      this.config.dept = this.chatInfo.receivedShowDept
      this.inputMsg = ''
      this.hotSaleGoodsId = hotId;
      this.config.historyConfig.tip = "加载更多";
      await this.resetChatList()
    },
    async resetChatList() {
      this.isEnd = false
      this.pageIndex = 1
      let res = await getHotSaleGoodChatByUserIdAsync({
        sendUserId: this.chatInfo.sendUserId, 
        receivedUserId: this.chatInfo.receivedUserId,
        hotSaleGoodsChooseEnquiryId: this.hotSaleGoodsId,
        pageIndex: this.pageIndex,
        pageCount: this.pageCount
      })
      if (!res?.success) return
      res.data.list.forEach(f => {
        if (f.mine) {
          f.name = this.chatInfo.sendUserName
          f.img = this.chatInfo.sendUserAvatar
        } else {
          f.name = this.chatInfo.receivedShowName
          f.img =  this.chatInfo.receivedShowImg
        }
      })
      this.MsgList = res.data.list
      this.chatVisible = true
      this.$nextTick(() => {
        this.$refs.jwChat.scrollBottom()
      });
    },
    async bindEnter(e) {
      const msg = this.inputMsg
      if (!msg || this.chatInfo.sendUserId <= 0) return;
      let form = new FormData();
      let obj = {
        message: msg,
        type: 'text',
        sendUserId: this.chatInfo.sendUserId,
        receivedUserId: this.chatInfo.receivedUserId,
        sendDDUserId: this.chatInfo.sendDDUserId,
        receivedDDUserId: this.chatInfo.receivedDDUserId,
        sendUserNickName: this.chatInfo.sendUserName,
        hotSaleGoodsChooseEnquiryId: this.hotSaleGoodsId
      }
      form.append("chatUserInput", JSON.stringify(obj));
      form.append("image", null)
      let res = await hotSaleGoodChatDDUserAsync(form)
      if (!res?.success) return
      res.data.chatMsg.name = this.chatInfo.sendUserName
      res.data.chatMsg.img = this.chatInfo.sendUserAvatar
      this.MsgList.push(res.data.chatMsg)
      this.$nextTick(() => {
        this.$refs.jwChat.scrollBottom()
      });
    },
    async bindLoadHistory() {
      if (this.isEnd) {
        this.$nextTick(() => {
          this.$refs.jwChat.finishPullDown();
        });
        return
      }
      this.config.historyConfig.tip = "加载中";
      this.pageIndex = this.pageIndex + 1
      let res = await getHotSaleGoodChatByUserIdAsync({
        sendUserId: this.chatInfo.sendUserId,
        receivedUserId: this.chatInfo.receivedUserId,
        hotSaleGoodsChooseEnquiryId: this.hotSaleGoodsId,
        pageIndex: this.pageIndex,
        pageCount: this.pageCount
      })
      if (!res?.success) return
      if (res.data.list.length == 0) {
        this.pageIndex = this.pageIndex - 1
        this.isEnd = true
        this.config.historyConfig.tip = "没有了";
        this.$nextTick(() => {
          this.$refs.jwChat.finishPullDown();
        });
      } else {
        this.config.historyConfig.tip = "加载更多";
        res.data.list.forEach(f => {
          if (f.mine) {
            f.name = this.chatInfo.sendUserName
            f.img = this.chatInfo.sendUserAvatar
          } else {
            f.name = this.chatInfo.receivedUserName
            f.img = this.chatInfo.receivedUserAvatar
          }
        })
      }
      let Msglist = res.data.list.concat(this.MsgList)
      this.MsgList = Msglist
      //加载完成后通知组件关闭加载动画并结束动画
      this.$nextTick(() => {
        setTimeout(() => {
          this.$refs.jwChat.scrollToEle("#jwMsg" + res.data.list.length);
        }, 200);
        this.$refs.jwChat.finishPullDown();
      });
    },
    imgsearchRequest: async function (parms) {
      if (this.chatInfo.sendUserId <= 0) { return }
      let form = new FormData();
      let obj = {
        message: '',
        type: 'img',
        sendUserId: this.chatInfo.sendUserId,
        receivedUserId: this.chatInfo.receivedUserId,
        sendDDUserId: this.chatInfo.sendDDUserId,
        receivedDDUserId: this.chatInfo.receivedDDUserId,
        sendUserNickName: this.chatInfo.sendUserName,
        hotSaleGoodsChooseEnquiryId: this.hotSaleGoodsId
      }
      form.append("chatUserInput", JSON.stringify(obj));
      form.append("image", parms.file)
      let res = await hotSaleGoodChatDDUserAsync(form)
      if (!res?.success) return
      res.data.chatMsg.name = this.chatInfo.sendUserName
      res.data.chatMsg.img = this.chatInfo.sendUserAvatar
      this.MsgList.push(res.data.chatMsg)
    },
    beforeAvatarUpload(file) {
      let isJPG = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif';
      let isLt2M = file.size / 1024 / 1024 < 11;
      if (!isJPG) {
        this.$message.error('发送的图片只能是 JPG/PNG/GIF 格式!');
      }
      if (!isLt2M) {
        this.$message.error('发送的图片大小不能超过 4MB!');
      }
      return isJPG && isLt2M;
    },
    async bindWinBar(play = {}) {
      let { type, data = {} } = play
      if (type === 'winBar') {
        //点击
        let { id, dept, name, img, readNum } = data
        let userObj = this.winBarConfig.list.find(item => item.id == id)
        userObj.readNum = 0
        if (this.winBarConfig.active == id) {
          return
        }
        this.winBarConfig.active = id
        this.config.name = name
        this.config.img = img

        let charIndex = id.lastIndexOf("-")
        let goodsId = id.substring(charIndex + 1, id.length)
        let chatToUserId = id.substring(charIndex, id)
        this.resetTalkUserInfo(chatToUserId, goodsId);
        Cookies.set('YH-chatId', id);
      } else if (type === 'winBtn') {
        //删除
        let { type, target = {} } = data
        let { id, dept, name, img, readNum } = target 
        this.$confirm('是否确认删除聊天信息？', '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          let charIndex = id.lastIndexOf("-")
          let goodsId = id.substring(charIndex + 1, id.length)
          let chatToUserId = id.substring(charIndex, id)
          let reqRlt = await deleteHotSaleGoodChat({ hotSaleGoodsChooseEnquiryId: goodsId , chatToUserId:chatToUserId })
          if (reqRlt.success) {
              let index = this.winBarConfig.list.findIndex(item => item.id == id)
              this.winBarConfig.list.splice(index, 1)
              if (id == this.winBarConfig.active) {
                this.winBarConfig.active = ''
                this.config.name = ''
                this.config.img = ''
                this.config.dept = ''
                this.chatInfo = {
                  sendUserId: 0,
                  sendDDUserId: 0,
                  sendUserName: '',
                  sendUserAvatar: '',
                  sendUserShowName: '',
                  sendUserShowImg: '',
                  sendUserShowDept: '',

                  //接收人信息
                  receivedUserId: 0,
                  receivedDDUserId: 0,
                  receivedShowName: '',
                  receivedShowImg: '',
                  receivedShowDept: ''
                }
                this.MsgList = []
              }
            }
        }).catch(() => {
        });
      }
    },
    async LoadWinBarConfig() {
      if (this.winBarConfig.list.length == 0) {
        let res = await getHotSaleGoodChatUserListsAsync()
        if (!res?.success) return
        res.data.forEach(f => {
          f.dept = formatTime(f.dept, 'YYYY/MM/DD HH:mm:ss')
          f.img=this.getRecivedImage(f.img)
        })
        this.winBarConfig.list = res.data
        await this.selectDefault();
      } else {
        await this.selectDefault();
      }
    },
    getAvaImage(img) {
      if (img == null || img == "") {
        return avatarDefault
      } else {
        return img
      }
    },
    getRecivedImage(img){
      if (img == null || img == "") {
        return logoDefault
      } else {
        return img
      }
    },
    async selectDefault(id) {
      let queryid = this.$route.query.msg
      if (queryid != null) {
        id = this.decode(queryid);  
        Cookies.set('YH-chatId', id)
      } else {
        let cookieId = Cookies.get('YH-chatId');
        if (cookieId != null) {
          id = cookieId
        }
      }
      if (this.winBarConfig.list != null && this.winBarConfig.list.length > 0) {
        if (id == null) id = this.winBarConfig.list[0].id
        this.winBarConfig.active = id
        this.winBarConfig.list.find(x => x.id == id).readNum = 0

        let charIndex = id.lastIndexOf("-")
        let goodsId = id.substring(charIndex + 1, id.length)
        let chatToUserId = id.substring(charIndex, id)
        this.resetTalkUserInfo(chatToUserId, goodsId);
      }
    },
    async refreshData() {
      if (this.winBarlist == null) {
        await this.resetTalkUserInfo(null, this.hotSaleGoodsId);
      } else {
        await this.LoadWinBarConfig();
      }
    },
    decode(queryid){
      if(queryid==null || queryid=="")
      return "";
      queryid = queryid.replaceAll("G", "0")
      queryid = queryid.replaceAll("C", "1");
      queryid = queryid.replaceAll("B", "2");
      queryid = queryid.replaceAll("E", "3");
      queryid = queryid.replaceAll("A", "4");
      queryid = queryid.replaceAll("K", "5");
      queryid = queryid.replaceAll("N", "6");
      queryid = queryid.replaceAll("D", "7");
      queryid = queryid.replaceAll("F", "8");
      queryid = queryid.replaceAll("Z", "9");
      return queryid;
    }
  }
}
</script> 

<style lang="css">
.toolIcon:hover {
  color: #76b1f9;
}

.mlt {
  margin-left: 30%;
  margin-top: 2%;
}
</style>