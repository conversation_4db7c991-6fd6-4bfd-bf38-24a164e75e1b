import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/Reporter/`

//GetDbNames 获取数据库名称
export const getDbNames = (params, config = {}) => { return request.get(apiPrefix + 'GetDbNames', { params, ...config }) }

//分页获取报表数据 PageGetReports
export const pageGetReports = (params, config = {}) => { return request.post(apiPrefix + 'PageGetReports', params, config) }

//MergeReporter 新增或编辑报表
export const mergeReporter = (params, config = {}) => { return request.post(apiPrefix + 'MergeReporter', params, config) }

//获取报表设置数据 GetReporterData
export const getReporterData = (params, config = {}) => { return request.post(apiPrefix + 'GetReporterData', params, config) }

//删除报表 DeleteReporter
export const deleteReporter = (params, config = {}) => { return request.post(apiPrefix + 'DeleteReporter', params, config) }

//Query 执行查询
export const query = (params, config = {}) => { return request.post(apiPrefix + 'Query', params, config) }

//汇总数据导出 Export
export const exportData = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'Export', params, config) }