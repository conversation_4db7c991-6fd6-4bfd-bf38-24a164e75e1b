import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/operate/`

//获取订单列表
export const getOrderAfterSalesPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetOrderAfterSalesPageList', params, config)
}

//获取Id的信息
export const getOrderById = (params, config = {}) => {
    return request.get(apiPrefix + 'GetOrderById', { params: params, ...config })
}

//导出订单列表
export const exportUnPayOrderAfterSalesPageList = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportUnPayOrderAfterSalesPageList', params, config)
}

 //责任人
 export const getPersonList = (params, config = {}) => { 
    return request.post(apiPrefix + 'GetPersonList', params, config)
}

//审核订单
export const chatUnPayPrderAfterSales = (params, config = {}) => {
    return request.post(apiPrefix + 'ChatUnPayPrderAfterSales', params, config)
}

//退款原因数据统计
export const getAfterSalesReasonStatics = (params, config = {}) => {
    return request.post(apiPrefix + 'GetAfterSalesReasonStatics', params, config)
}

//退款原因数据统计导出
export const reasonForRefundStatisticsExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ReasonForRefundStatisticsExport', params, config)
}

//售后退款原因数据统计趋势图
export const getReasonForRefundTrendChart = (params, config = {}) => {
    return request.post(apiPrefix + 'GetReasonForRefundTrendChart', params, config)
}

//设置分配人
export const setOrderAfterSalesAssigner = (params, config = {}) => {
    return request.post(apiPrefix + 'SetOrderAfterSalesAssigner', params, config)
}

//获取分配人
export const getOrderAfterSalesAssigner = (params, config = {}) => {
    return request.post(apiPrefix + 'GetOrderAfterSalesAssigner', params, config)
}

//删除分配人
export const deleteOrderAfterSalesAssigner = (params, config = {}) => {
    return request.post(apiPrefix + 'DeleteOrderAfterSalesAssigner', params, config)
}

//退款原因数据统计
export const getAfterSalesReasonStaticsProCode = (params, config = {}) => {
    return request.post(apiPrefix + 'GetAfterSalesReasonStaticsProCode', params, config)
}

//设置解决人
export const addAfterSalesProCodeSolver = (params, config = {}) => {
    return request.post(apiPrefix + 'AddAfterSalesProCodeSolver', params, config)
}

//设置解决方案
export const addAfterSalesProCodeSolution = (params, config = {}) => {
    return request.post(apiPrefix + 'AddAfterSalesProCodeSolution', params, config)
}

//修改解决数据
export const editAfterSalesProCodeSoluteData = (params, config = {}) => {
    return request.post(apiPrefix + 'EditAfterSalesProCodeSoluteData', params, config)
}
//导出退款原因数据统计-宝贝ID维度
export const reasonForRefundProCodeStatisticsExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ReasonForRefundProCodeStatisticsExport', params, config)
}
