import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/OrderNodes/`

// http://*************:8020/api/Order/OrderNodes/GetAttendanceOrderNodesList?StartTime=2022-01-01&EndTime=2022-08-30
export const getAttendanceOrderNodesList = (params, config = {}) => {return request.get(apiPrefix + 'GetAttendanceOrderNodesList', { params: params, ...config })}

export const exportAttendanceOrderNodesList = (params, config = {responseType: 'blob'}) => { return request.get(apiPrefix + 'ExportAttendanceOrderNodesList',  { params: params, ...config })}
