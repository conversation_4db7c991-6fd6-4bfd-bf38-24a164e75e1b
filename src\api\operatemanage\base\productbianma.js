import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/productbianmamanager/`
 
export const addOrUpdate = (params, config = {}) => {
  return request.post(apiPrefix + 'AddOrUpdate', params, config )
}
export const batchDeleteProductBianMa = (params, config = {}) => {
  return request.post(apiPrefix + 'BatchDeleteProductBianMa',  params, config )
}   
export const getProductBianMaById = (id, config = {}) => {
    return request.get(apiPrefix + `GetProductBianMaById?id=${id}`, {}, config)
  }
export const getPageList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPageList',{ params: params, ...config })
  }
export const importProductBianMaAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportProductBianMaAsync', params, config)
  }
export const importProductBianMaZhuHeAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportProductBianMaZhuHeAsync', params, config)
}
export const exportProductBianMa = (params, config = {responseType: 'blob'}) => {
  return request.get(apiPrefix + 'ExportProductBianMaAsync',  { params: params, ...config })
}

//获取编码链接getGoodsProduct
export const getGoodsProduct = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGoodsProductAsync', params, config)
}

