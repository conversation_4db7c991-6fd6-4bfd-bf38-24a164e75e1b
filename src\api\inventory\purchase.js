import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/purchase/`

export const editPurchaseOrder = (params, config) => { return request.post(apiPrefix + 'EditPurchaseOrderAsync', params, config) }

export const editPurchasePayment = (params, config) => { return request.post(apiPrefix + 'EditPurchasePaymentAsync', params, config) }
export const editReturnAmount = (params, config) => { return request.post(apiPrefix + 'EditReturnAmountAsync', params, config) }

export const editPurchasePlan = (params, config) => { return request.post(apiPrefix + 'EditPurchasePlanAsync', params, config) }
export const getLastUpdateTimeyPurchase = (params, config = {}) => { return request.get(apiPrefix + 'GetLastUpdateTimePurchaseAsync', { params: params, ...config }) }
export const getLastUpdateTimeyPurchasePlan = (params, config = {}) => { return request.get(apiPrefix + 'GetLastUpdateTimePurchasePlanAsync', { params: params, ...config }) }
export const getLastUpdateTimeyPurchasePlan2 = (params, config = {}) => { return request.get(apiPrefix + 'GetLastUpdateTimePurchasePlan2Async', { params: params, ...config }) }
export const getLastUpdateTimeWarehousingOrder = (params, config = {}) => { return request.get(apiPrefix + 'GetLastUpdateTimeWarehousingOrderAsync', { params: params, ...config }) }
export const pageGoodsCodeRecord = (params, config = {}) => { return request.get(apiPrefix + 'PageGoodsCodeRecordAsync', { params: params, ...config }) }
export const getPurchasePlan = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchasePlanAsync', { params: params, ...config }) }
export const getPurchaseOrder = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchaseOrderAsync', { params: params, ...config }) }
export const getPurchasePaymentBuyNo = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchasePaymentBuyNoAsync', { params: params, ...config }) }
export const pagePurchaseOrder = (params, config = {}) => { return request.post(apiPrefix + 'PagePurchaseOrderAsync', params, config) }
export const queryPurchaseAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'QueryPurchaseAnalysis', { params: params, ...config }) }
export const pagePurchasePlan = (params, config = {}) => { return request.get(apiPrefix + 'PagePurchasePlanAsync', { params: params, ...config }) }
export const queryPurchaseOrderDetail = (params, config = {}) => { return request.get(apiPrefix + 'QueryPurchaseOrderDetailAsync', { params: params, ...config }) }
export const queryPurchaseOrderDetailIndexNo = (params, config = {}) => { return request.get(apiPrefix + 'QueryPurchaseOrderDetailIndexNoAsync', { params: params, ...config }) }
export const queryPurchaseOrderLogByIndexNoAsync = (params, config = {}) => { return request.get(apiPrefix + 'QueryPurchaseOrderLogByIndexNoAsync', { params: params, ...config }) }
export const queryWarehousingOrderDetail = (params, config = {}) => { return request.get(apiPrefix + 'QueryWarehousingOrderDetailAsync', { params: params, ...config }) }
export const pageWarehousingOrder = (params, config = {}) => { return request.post(apiPrefix + 'PageWarehousingOrderAsync', params, config) }
export const importPurchaseOrder = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchaseOrderAsync', params, config) }
export const importPurchasePayment = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchasePaymentAsync', params, config) }
export const importPurchaseReturnGoods = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchaseReturnGoodsAsync', params, config) }
export const importWarehousingOrder = (params, config = {}) => { return request.post(apiPrefix + 'ImportWarehousingOrderAsync', params, config) }
export const importPurchasePlan = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchasePlanAsync', params, config) }
export const exportPurchasePlan = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPurchasePlanAsync', params, config) }
export const queryLogistics = (params, config = {}) => { return request.get(apiPrefix + 'QueryLogisticsAsync', { params: params, ...config }) }
export const getPurchasePayment = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchasePayment', params, config) }
export const getPurchasePlan2Parm = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchasePlan2ParmAsync', { params: params, ...config }) }
export const editPurchasePlan2Parm = (params, config = {}) => { return request.post(apiPrefix + 'EditPurchasePlan2ParmAsync', params, config) }
export const editPurchasePlan2 = (params, config = {}) => { return request.post(apiPrefix + 'EditPurchasePlan2Async', params, config) }
export const pagePurchasePlan2 = (params, config = {}) => { return request.get(apiPrefix + 'PagePurchasePlan2Async', { params: params, ...config }) }
export const getPurchasePlan2 = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchasePlan2Async', { params: params, ...config }) }
export const pagePurchasePlan2Record = (params, config = {}) => { return request.get(apiPrefix + 'PagePurchasePlan2RecordAsync', { params: params, ...config }) }
export const importPurchasePlan2 = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchasePlan2Async', params, config) }
export const exportPurchasePlan2 = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPurchasePlan2Async', params, config) }
export const exportPurchaseOrder = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPurchaseOrderAsync', params, config) }
export const deletePurchasePayment = (params, config = {}) => { return request.delete(apiPrefix + 'DeletePurchasePaymentAsync', { params: params, ...config }) }


export const deletePurchaseReturnGoods = (params, config = {}) => { return request.delete(apiPrefix + 'DeletePurchaseReturnGoodsAsync', { params: params, ...config }) }

export const deleteReturnAmount = (params, config = {}) => { return request.delete(apiPrefix + 'DeleteReturnAmountAsync', { params: params, ...config }) }


export const deletePurchasePaymentById = (params, config = {}) => { return request.delete(apiPrefix + 'DeletePurchasePaymentByIdAsync', { params: params, ...config }) }

export const getpurchaseReturnGoodsList = (params, config = {}) => { return request.get(apiPrefix + 'GetpurchaseReturnGoodsListAsync', { params: params, ...config }) }


//商品主题分析历史数据导入
export const importPurchasePlan2History = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchasePlan2HistoryAsync', params, config) }

// export const exportPurchasePaymentBuyNo = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'ExportPurchasePaymentBuyNoAsync',  params, config)}

export const exportPurchasePaymentBuyNo = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPurchasePaymentBuyNoAsync', params, config) }

export const exportReturnAmount = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportReturnAmountAsync', params, config) }


//退款导入
export const importReturnAmount = (params, config = {}) => { return request.post(apiPrefix + 'ImportReturnAmountAsync', params, config) }

export const getReturnList = (params, config = {}) => { return request.get(apiPrefix + 'GetReturnListAsync', { params: params, ...config }) }

export const getPurchaseReturnGoods = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchaseReturnGoodsAsync', { params: params, ...config }) }


export const getPurchaseReturnGoodsDetail = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchaseReturnGoodsDetailAsync', { params: params, ...config }) }


//保存手工采购单
export const savePurchaseManual = (params, config = {}) => { return request.post(apiPrefix + 'SavePurchaseManualAsync', params, config) }
//获取手工采购单详情
export const getPurchaseOrderManual = (id, config = {}) => {
    return request.get(apiPrefix + `GetPurchaseOrderManualAsync?indexNo=${id}`, { params: {}, ...config })
}

//验证供应商发货地
export const verifyPurchaseSupplierShipmentPlace = (params, config = {}) => { 
    return request.post(apiPrefix + 'VerifyPurchaseSupplierShipmentPlace', params, config) 
}
// 自动开单采购单获取计划采购建议数据
export const getAutoPurchaseOrderPlan = (params, config = {}) => {
    return request.get(apiPrefix + 'GetAutoPurchaseOrderPlan', { params: params, ...config })
}

//批量复制手工采购单
export const CopeAddPurchaseOrder = (params, config = {}) => { return request.post(apiPrefix + `TeskCopeAddPurchaseOrderAsync`, params, config) }

//批量删除手工采购单
export const deletePurchaseManual = (params, config = {}) => { return request.post(apiPrefix + 'DeletePurchaseManualAsync', params, config) }

//采购建议导入（新）
export const importNewPurchasePlan2 = (params, config = {}) => { return request.post(apiPrefix + 'ImportNewPurchasePlan2Async', params, config) }

// 新版采购建议分页
export const pagePurchaseNewPlan2 = (params, config = {}) => {
    return request.post(apiPrefix + 'PagePurchaseNewPlan2Async', params, config)
}

//修改新版采购建议，采购建议数
export const BatchUpdatePurchaseNewPlan2 = (params, config = {}) => {
    return request.post(apiPrefix + 'BatchUpdatePurchaseNewPlan2', params, config)
}
//更新采购单详情
export const batchUpdatePurchaseOrderAsync = (params, config = {}) => { return request.post(apiPrefix + 'BatchUpdatePurchaseOrderAsync', params, config) }

// 新版采购建议最晚更新时间
export const getLastUpdateTimePurchaseNewPlanAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetLastUpdateTimePurchaseNewPlanAsync', params, config)
}

// 新版采购建议最晚更新时间
export const getLastUpdateTimePurchaseNewPlanListAsync = (params, config = {}) => {
    return request.get(apiPrefix + 'GetLastUpdateTimePurchaseNewPlanListAsync', { params: params, ...config })
}

// 新建采购单，同步到聚水潭
export const taskPurchaseUpload = (params, config = {}) => {
    return request.post(apiPrefix + 'TaskPurchaseUploadAsync', params, config)
}

// 新建采购单，建立钉钉流程
export const taskPurchaseDingDing = (params, config = {}) => {
    return request.post(apiPrefix + 'TaskPurchaseDingDingAsync', params, config)
}

// 复制采购单号
export const pagePurchaseOrderByCopyBuyNo = (params, config = {}) => {
    return request.post(apiPrefix + 'PagePurchaseOrderByCopyBuyNoAsync', params, config)
}




// 采购在途明细-分页查询
export const getPurchaseInTransitDetailPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseInTransitDetailPageList', params, config) }
// 采购在途-商品在途-分页查询
export const getPurchaseInTransitGoodsPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseInTransitGoodsPageList', params, config) }
// 采购在途-商品在途-趋势图
export const getPurchaseInTransitGoodsChart = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseInTransitGoodsChart', params, config) }
// 采购在途-供应商在途-分页查询
export const getPurchaseInTransitSupplierPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseInTransitSupplierPageList', params, config) }
// 采购在途-供应商在途-趋势图
export const getPurchaseInTransitSupplierChart = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseInTransitSupplierChart', params, config) }
// 采购在途-保存发货时间
export const saveConsignment = (params, config = {}) => { return request.post(apiPrefix + 'SaveConsignment', params, config) }
// 采购在途-保存到站点时间
export const saveArrivalStation = (params, config = {}) => { return request.post(apiPrefix + 'SaveArrivalStation', params, config) }
// 采购在途-保存发货时间
export const saveQuality = (params, config = {}) => { return request.post(apiPrefix + 'SaveQuality', params, config) }
// 采购在途明细-查询前1000条采购单号
export const getPubchaseInTransitDetailBuyNosTop1000 = (params, config = {}) => { return request.post(apiPrefix + 'GetPubchaseInTransitDetailBuyNosTop1000', params, config) }
// 采购在途明细-清空发货时间
export const clearConsignment = (params, config = {}) => { return request.post(apiPrefix + 'ClearConsignment', params, config) }

// 采购明细
export const getPurchaseArrivalInfoDetailAsync = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPurchaseArrivalInfoDetailAsync', { params: params, ...config })
}
// 采购汇总
export const getPurchaseArrivalInfoAsync = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPurchaseArrivalInfoAsync', { params: params, ...config })
}

// 采购汇总
export const getNewProductCount = (params, config = {}) => {
    return request.get(apiPrefix + 'GetNewProductCountAsync', { params: params, ...config })
}


//采购单据从聚水潭同步。
export const SyncPurchaseOrder4BuyNos = (params, config = {}) => { return request.get(apiPrefix + 'SyncPurchaseOrder4BuyNos', { params: params, ...config }) }


//修复采购单状态，英文逗号分隔
export const ReCheckOrderStatus = (params, config = {}) => { return request.get(apiPrefix + 'ReCheckOrderStatus', { params: params, ...config }) }

//修复未发起成功的采购单
export const TerminateNoApplyPurchaseOrder = (params, config = {}) => { return request.get(apiPrefix + 'TerminateNoApplyPurchaseOrder', { params: params, ...config }) }

//获取供应商报价详情
export const pagePurchaseOrderByProviderNameAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'PagePurchaseOrderByProviderNameAsync', params, config)
}

export const getPurchaseFundsSumWarehouseChart = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchaseFundsSumWarehouseChart', { params: params, ...config }) }

export const getPurchaseFundsSumPlatFormChart = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchaseFundsSumPlatFormChart', { params: params, ...config }) }

export const getPurchaseFundsDetailChart = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchaseFundsDetailChart', { params: params, ...config }) }

export const getPurchaseFundsSumWarehouse = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchaseFundsSumWarehouse', { params: params, ...config }) }

export const getPurchaseFundsSumPlatForm = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchaseFundsSumPlatForm', { params: params, ...config }) }

export const getPurchaseFundsDetail = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchaseFundsDetail', { params: params, ...config }) }

//采购资金-明细-商品编码弹窗
export const getPurchaseFundsGoodsCodeDialog = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseFundsGoodsCodeDialog', params, config) }

//采购资金-明细-资金合计弹窗
export const getPurchaseFundsTotalFundsDialog = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseFundsTotalFundsDialog', params, config) }

//采购资金-明细-可售库存弹窗
export const getPurchaseFundsAvailableInventoryDialog = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseFundsAvailableInventoryDialog', params, config) }

//采购资金-明细-近七日销量
export const getPurchaseFundsNearly7DaysDialog = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseFundsNearly7DaysDialog', params, config) }

export const getLastPurchaseInfoByProviderId = (params, config = {}) => { return request.get(apiPrefix + 'GetLastPurchaseInfoByProviderId', { params: params, ...config }) }

//分页获取采购信息 GetPurchaseExtInfoPage
export const getPurchaseExtInfoPage = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseExtInfoPage', params, config) }

//获取单个采购信息 GetPurchaseExtInfoById
export const getPurchaseExtInfoById = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseExtInfoById', params, config) }

//编辑采购信息 EditPurchaseExtInfo
export const editPurchaseExtInfo = (params, config = {}) => { return request.post(apiPrefix + 'EditPurchaseExtInfo', params, config) }

//设置状态  SetPurchaseExtInfoEnabled
export const setPurchaseExtInfoEnabled = (params, config = {}) => { return request.post(apiPrefix + 'SetPurchaseExtInfoEnabled', params, config) }

//删除采购信息 DelPurchaseExtInfo
export const delPurchaseExtInfo = (params, config = {}) => { return request.post(apiPrefix + 'DelPurchaseExtInfo', params, config) }

//分页查询采购信息 GetPurchaseOrderExtPage
export const getPurchaseOrderExtPage = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseOrderExtPage', params, config) }

//导出采购信息 ExportPurchaseOrderExt
export const exportPurchaseOrderExt = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPurchaseOrderExt', params, config) }

//导出厂家信息 ExportPurchaseExtInfo
export const exportPurchaseExtInfo = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPurchaseExtInfo', params, config) }


//采购单管理-退款-查询采购单退款明细 GetPurchaseOrderRefundDetails
export const getPurchaseOrderRefundDetails = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseOrderRefundDetails', params, config) }

//采购单管理-退款-选择账号后，获取账号对应收款方式、收款人 GetAccountPayTypeUserName
export const getAccountPayTypeUserName = (params, config = {}) => { return request.post(apiPrefix + 'GetAccountPayTypeUserName', params, config) }

//采购单管理-退款-生成采购单退款明细 GeneratePurchaseOrderRefundDetails
export const generatePurchaseOrderRefundDetails = (params, config = {}) => { return request.post(apiPrefix + 'GeneratePurchaseOrderRefundDetails', params, config) }

//采购单管理-退款-添加采购单退款明细 AddPurchaseOrderRefundDetails
export const addPurchaseOrderRefundDetails = (params, config = {}) => { return request.post(apiPrefix + 'AddPurchaseOrderRefundDetails', params, config) }

//采购单管理-退款-获取采购单号 GetPurchaseOrder
export const getPurchaseRefundOrder = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseOrder', params, config) }

//采购单管理-退款-编辑 EditPurchaseOrderRefundDetailsERP
export const editPurchaseOrderRefundDetailsERP = (params, config = {}) => { return request.post(apiPrefix + 'EditPurchaseOrderRefundDetailsERP', params, config) }

//采购单管理-退款-撤销 CancelPurchaseOrderRefundDetails
export const cancelPurchaseOrderRefundDetails = (params, config = {}) => { return request.post(apiPrefix + 'CancelPurchaseOrderRefundDetails', params, config) }

//采购单管理-退款-编辑采购单退款明细 EditAddPurchaseOrderRefundDetails
export const editAddPurchaseOrderRefundDetails = (params, config = {}) => { return request.post(apiPrefix + 'EditAddPurchaseOrderRefundDetails', params, config) }

//导出采购单退款 ExportPurchaseOrderRefundDetailsERP
export const exportPurchaseOrderRefundDetailsERP = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPurchaseOrderRefundDetailsERP', params, config) }

//采购单管理-退款-新查询 GetPurchaseOrderRefundDetailsERP
export const getPurchaseOrderRefundDetailsERP = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseOrderRefundDetailsERP', params, config) }

// 省市区
export const GetProvinceCityDistrict = (params, config = {}) => { return request.get(apiPrefix + 'GetProvinceCityDistrict', { params: params, ...config }) }

export const GetProvinceCityDistrictAndNo = (params, config = {}) => { return request.get(apiPrefix + `GetProvinceCityDistrict?addNoBindArea=1`, { params: params, ...config }) }

export const ChangePackCount = (params, config = {}) => { return request.post(apiPrefix + 'ChangePackCount', params, config) }

export const getPurchaseOrderDetail2PriceHikeTicketAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchaseOrderDetail2PriceHikeTicketAsync', { params, ...config }) }

//获取供应商税率信息
export const GetSupplierTaxRateByProviderId = (params, config = {}) => { return request.get(apiPrefix + 'GetSupplierTaxRateByProviderId', { params: params, ...config }) }
