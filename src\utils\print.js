import print from "print-js";
import "print-js/dist/print.css";
/**
 *
 * @param val //val是一个对象,是打印的参数
 */
//打印
export const printJS = (val, style) => {
  print({
    printable: val.id,
    type: val.type ? val.type : "html",
    // header: val.header ? val.header : null,
    // maxWidth: val.maxWidth ? val.maxWidth : 1000,
    // honorMarginPadding: val.honorMarginPadding ? val.honorMarginPadding : false,
    // repeatTableHeader: val.repeatTableHeader ? val.repeatTableHeader : false,
    targetStyles: ["*"],
    onPrintDialogClose: function () {
      document.getElementById(val.id).style.display = "none";
    },
  });
};
