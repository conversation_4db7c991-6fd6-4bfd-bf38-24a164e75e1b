<template>
    <el-select v-model="innerValue" :clearable="clearable" filterable remote :style="cststyle" reserve-keyword placeholder="" :remote-method="remoteMethod" @change="valueChanged($event)" :loading="loading">
        <el-option v-for="item in options" :key="'suplierSelector'+item.value" :label="item.label" :value="item.value">
        </el-option>
        <el-option v-if="value && !options.find(x=>x.value==value)" :key="'suplierSelector_dft'+(value==null?0:value)" :label="text" :value="value">
        </el-option>
    </el-select>

</template>

<script>   
    import { pageSupplierAll } from '@/api/inventory/supplier'
import { number } from 'echarts'

    export default {
        name: 'YhSupplierselector',
        props: {
            rows: {
                type: Number,
                default: 50
            },
            value: {
                type: Number,
                default() {
                    return null;
                }
            },
            text: {
                type: String,
                default() {
                    return ""
                }
            },
            clearable: {
                type: <PERSON><PERSON><PERSON>,
                default() { return true; }
            },
            cststyle: {
                type: Object,
                default() {
                    return {}
                }
            }
        },
        data() {
            return {
                loading: false,
                options: [],
                innerValue: "",
                innerText: "",
                orgOptions: []
            }
        },
        computed: {
            // innerValue() {
            //     return this.value;
            // }
        },
        watch: {
            value(newVal, oldVal) {
                //如果选项值在options中没有，

                this.innerValue = newVal;

                // this.$emit("update:value", newVal);
                // this.$emit("update:text", newVal);
            }
        },
        async mounted() {            

            this.orgOptions = [...this.options];
        },
        methods: {
            valueChanged(newValue) {

                let labelValue = newValue;
                if (newValue) {

                    let findOpts = this.options.filter(item => { return item.value == labelValue; });
                    if (findOpts && findOpts.length > 0) {
                        labelValue = findOpts[0].label;
                    }
                }
                this.$emit("update:value", newValue);
                this.$emit("update:text", labelValue);
            },
            async remoteMethod(query) {
                this.loading = true;
                if (query !== '') {                  
                    let rlt= await pageSupplierAll({ currentPage: 1, pageSize: this.rows, name: query });
                    if (rlt && rlt.success) {
                        this.options = rlt.data?.list?.filter(item => { return item.supplier_id>0 }).map(item => {
                            return { label: item.name, value: item.supplier_id, id: item.supplier_id }
                        });
                    }
                } else {
                    this.options = [...this.orgOptions];
                }
                this.loading = false;
            }
        }
    }
</script>

