import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Media}/mediashare/`
//获取数据-ById
export const getOperationsGroup = (params, config = {}) => {
    return request.get(apiPrefix + 'GetOperationsGroup', { params, ...config })
}
//获取数据
export const getErpUserInfoView = (params, config = {}) => {
    return request.get(apiPrefix + 'GetErpUserInfoView', { params, ...config })
}

//获取数据
export const changeUploadImg = (params, config = {}) => {
    return request.get(apiPrefix + 'ChangeUploadImg', { params, ...config })
}

//获取数据
export const getUploadImgs = (params, config = {}) => {
    return request.get(apiPrefix + 'getUploadImgs', { params, ...config })
}
//获取数据-ById
export const getShootOperationsGroup = (params, config = {}) => {
    return request.get(apiPrefix + 'GetShootOperationsGroup', { params, ...config })
}

//获取数据
export const getErpUserInfoViewforshoot = (params, config = {}) => {
    return request.get(apiPrefix + 'GetErpUserInfoViewforshoot', { params, ...config })
}

//获取数据
export const getWorkPostListAsync = (params, config = {}) => {
    return request.get(apiPrefix + 'GetWorkPostListAsync', { params, ...config })
}

//获取数据
export const getCityAllData = (params, config = {}) => {
    return request.get(apiPrefix + 'GetCityAllData', { params, ...config })
}


//获取数据
export const getShootingTaskOrderListById = (params, config = {}) => {
    return request.get(apiPrefix + 'GetShootingTaskOrderListById', { params, ...config })
}
//获取数据
export const getErpShootDeptUserInfo = (params, config = {}) => {
    return request.get(apiPrefix + 'GetErpShootDeptUserInfo', { params, ...config })
}



//获取数据
export const shootingTaskAddOrderSave =  
(params, config = {}) => { return request.post(apiPrefix + 'ShootingTaskAddOrderSave', params, config) }

export const shootingTaskAddOrderSaveCheckTaskIds = (params, config = {}) => { return request.post(apiPrefix + 'ShootingTaskAddOrderSaveCheckTaskIds', params, config) }