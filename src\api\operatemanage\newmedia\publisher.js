import request from '@/utils/request'
const PublisherPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/Publisher/`
export const importPublisherAsync = (params, config = {}) => {return request.post(PublisherPrefix + 'ImportPublisherAsync',  params, config)}
export const getPublisherList = (params, config = {}) => {return request.get(PublisherPrefix + 'GetPublisherList', { params: params, ...config })}
export const deletePublisherBatch = (params, config = {}) => {return request.get(PublisherPrefix + 'DeleteBatchAsync', { params: params, ...config })}
export const addPublisherBatch = (params, config = {}) => {return request.get(PublisherPrefix + 'AddPublisher', { params: params, ...config })}
export const addPublisherInfo = (params, config = {}) => {return request.get(PublisherPrefix + 'AddPublisherInfo', { params: params, ...config })}
export const getBlankSnameListAsync = (params, config = {}) => {return request.get(PublisherPrefix + 'GetBlankSnameListAsync', { params: params, ...config })}

//淘系创意排行榜-获取列表数据
export const getCreativeListAsync = (params, config = {}) => {return request.post(PublisherPrefix + 'GetCreativeListAsync',  params, config)}

//淘系创意排行榜-列表数据导出
export const exportCreativeListAsync = (params, config = { responseType: 'blob' }) => { return request.post(PublisherPrefix + 'ExportCreativeListAsync', params, config) }

//淘系创意排行榜-类目选择器数据
export const getCreativeCategoryAsync = (params, config = {}) => {return request.post(PublisherPrefix + 'GetCreativeCategoryAsync',  params, config)}
