import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/basicgoods/`

//分页查询店铺商品资料
export const pageShopGoods = (params, config = {}) => {
  return request.post(apiPrefix + 'PageShopGoodsAsync', params, config)
}

//分页查询店铺商品资料 历史
export const pageShopGoodsHistory = (params, config = {}) => {
  return request.post(apiPrefix + 'PageShopGoodsHistoryAsync', params, config)
}

//分页查询组合装商品资料
export const pageCombineGoods = (params, config = {}) => {
  return request.post(apiPrefix + 'PageCombineGoodsAsync', params, config)
}
export const exportCombineGoods = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'exportCombineGoodsAsync', params, config) }
//分页查询组合装商品资料 明细
export const pageCombineGoodsDetail = (params, config = {}) => {
  return request.post(apiPrefix + 'PageCombineGoodsDetailAsync', params, config)
}

//分页查询组合装商品资料 历史
export const pageCombineGoodsHistory = (params, config = {}) => {
  return request.post(apiPrefix + 'PageCombineGoodsHistoryAsync', params, config)
}

//分页查询组合装商品资料 明细 历史
export const pageCombineGoodsDetailHistory = (params, config = {}) => {
  return request.post(apiPrefix + 'PageCombineGoodsDetailHistoryAsync', params, config)
}

//导入 店铺商品
export const importShopGoods = (params,config ={}) =>{
  return request.post(apiPrefix + 'ImportShopGoodsAsync', params, config)
}

//导入 组合商品
export const importCombineGoods = (params,config ={}) =>{
  return request.post(apiPrefix + 'ImportCombineGoodsAsync', params, config)
}


//获取商品id By 商品编码
export const getShopGoodProductIdByCode = (params, config = {}) => {
  return request.get(apiPrefix + 'GetShopGoodProductIdByCode',  { params: params, ...config })
}
//导入 组合编码数据
export const  importCombineGoodsV2= (params, config = {}) => {return request.post(apiPrefix + 'ImportCombineGoodsV2Async', params, config)}

//获取组合编码日志
export const combineGoodsRecord = (params, config = {}) => {
  return request.post(apiPrefix + 'PageCombineGoodsRecordAsync', params, config)
}

//新增或更改
export const addCombineGoods = (params, config = {}) => { return request.post(apiPrefix + 'AddCombineGoodsAsync', params, config) }
//组合编码同步到聚水潭
export const SyncToJst = (params, config = {}) => { return request.post(apiPrefix + 'SyncToJstAsync', params, config) }

//编辑组合装商品编码
export const editCombineGoodsAsync = (params, config = {}) => { return request.post(apiPrefix + 'EditCombineGoodsAsync', params, config) }

