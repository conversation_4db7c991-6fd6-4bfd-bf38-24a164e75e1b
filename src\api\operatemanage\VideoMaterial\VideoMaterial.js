import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/VideoMaterial/`

// 分页查询视频资料库
export const pageGetVideoMtl = (params, config) => { return request.post(apiPrefix + 'PageGetVideoMtl', params, config) }

//新建视频资料
export const createVideoMtl = (params, config) => { return request.post(apiPrefix + 'CreateVideoMtl', params, config) }

//逻辑删除视频资料
export const deleteVideoMaterial = (params, config) => { return request.post(apiPrefix + 'DeleteVideoMaterial', params, config) }

//分页查询视频资料库操作记录
export const pageGetVideoMtlRec = (params, config) => { return request.post(apiPrefix + 'PageGetVideoMtlRec', params, config) }

//创建视频素材记录
export const createVideoMtlRec = (params, config) => { return request.post(apiPrefix + 'CreateVideoMtlRec', params, config) }

//更新视频资料状态
export const updateStatus = (params, config) => { return request.post(apiPrefix + 'UpdateStatus', params, config) }

//修改视频资料
export const updateVideoMtl = (params, config) => { return request.post(apiPrefix + 'UpdateVideoMtl', params, config) }