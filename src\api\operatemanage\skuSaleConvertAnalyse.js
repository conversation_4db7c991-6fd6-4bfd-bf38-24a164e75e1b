import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/SkuSaleConvertAnalyse/`

// 分销SKU商品销售转换分析分页查询
export const getSkuSaleConvertAnalysePageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSkuSaleConvertAnalysePageList', params, config)
}

// 分销SKU商品销售转换分析导入
export const importFileSkuSaleConvertAnalyse = (params, config = {}) => {
  return request.post(apiPrefix + 'ImportFileSkuSaleConvertAnalyse', params, config)
}

// 分销SKU商品销售转换分析导出
export const exportSkuSaleConvertAnalysePageList = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'ExportSkuSaleConvertAnalysePageList', params, config)
}
