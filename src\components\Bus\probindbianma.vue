<!--产品绑定编码   后面做-->
<template>
  <div style="width: 100%; height: 100%;margin: 0">
    <div>
      <el-button v-if="isedit" @click="onAdd()">添加编码</el-button>
      <el-button v-if="isedit" @click="onFresh()">刷新</el-button>
    </div>
     <el-table v-loading="listLoading" :data="bianmalist" highlight-current-row  height="300"  style="width: 100%;">
      <el-table-column type="index" width="50" label="#" />
      <el-table-column prop="proBianMa" label="商品编码" width />
      <el-table-column prop="title" label="商品名称" width='180' />
      <!-- <el-table-column prop="jianChen" label="商品简称"  width='180' /> -->
      <el-table-column prop="hasChildren" label="是否组合" width> 
        <template #default="{row}">
          <el-tag :type="row.hasChildren == true ? 'success' : 'danger'" disable-transitions>
            {{ row.hasChildren == true ? '是' : row.hasChildren == false ?'否':'' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="weight" label="重量(kg)" width='100'/> 
      <el-table-column prop="price" label="销售价" width/>
      <el-table-column prop="marketPrice" label="市场价" width/> 
      <el-table-column prop="costPrice" label="成本价" width/> 
      <el-table-column prop="createdTime" label="创建时间"  width='150' />
      <el-table-column prop="status" label="状态" width>
        <template #default="{row}">
          <el-tag :type="row.status == 0 ? 'success' : 'danger'" disable-transitions>
            {{ row.status == 1 ? '正常' : row.status == 2 ?'缺货': row.status == 2 ?'停用':'' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column  v-if="isedit"  label="操作" width="180">
        <template #default="{ $index, row }">
          <el-button  v-if="isedit" @click="onDelete($index, row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
   
    <el-dialog title="新增编码" :visible.sync="dialogFormVisible11"  width='88%' height='90%' v-dialogDrag>
      <choicebianma ref="choicebianma" style="z-index:10000"/>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogFormVisible11 = false">取 消</el-button>
          <el-button type="primary" @click="onQueren()">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import choicebianma from '@/components/Bus/choicebianma'
import {getProductBianMaById} from '@/api/operatemanage/base/product'
export default {
  name: 'MyConfirmButton',
  components: {choicebianma},
  props: {
    productId: {type: String, default: ''},
    isedit: {type: Boolean, default: false},
  },
  data() {
    return {
      listLoading:false,
      dialogFormVisible11: false,
      bianmalist:[],
    }
  },
  computed: { },
  watch: {
     productId (val, oldval) {
        if (val!=oldval) {
          this.Init(val)
        }
      }
  },
  mounted: function() {
    //this.getList()
  },
  methods: {
   async onFresh() {
     this.Init(this.productId)
   },
   async Init(productId) {
     var res=  await getProductBianMaById(productId);
      if (res?.data) {
         this.bianmalist=res.data
      }
    },
   async onAdd(){
      this.dialogFormVisible11=true
      this.listLoading=false;
    },
   async onDelete(index,row){
      bianmalist.splice(index,1);
    },
   async onQueren(){
      this.dialogFormVisible11=false
      var choicelist=await this.$refs.choicebianma.getchoicelist();
      if (choicelist) {
         choicelist.forEach(f=>{
            this.bianmalist.push(f);
          })
      }
      console.log(choicelist);
    },
    async getBindlist() {
        return await this.bianmalist;
    },
    async getBindIds() {
      var ids=[]
      this.bianmalist.forEach(f=>{
        ids.push(f.id)
      })
      return await ids;
    }
  }
}
</script>
<style>
.ces-table-require::before{
  content:'*';
  color:red;
}
.table-wrapper {
    width: 100%;
    height: calc(100% - 35px);
    margin: 0;
 }
 .el-table{
   overflow:visible !important;
}
</style>
