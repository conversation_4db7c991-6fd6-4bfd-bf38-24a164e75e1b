<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <link rel="stylesheet" href="/approvalform/html/api/elment.css">
    <script src="/approvalform/html/api/vue.min.js"></script>
    <script src="/approvalform/html/api/elment.js"></script>
    <script src="/approvalform/html/api/jquery.min.js"></script>
    <script src="/approvalform/html/api/html2canvas.js"></script>
    <title>调拨单编码</title>
    <style type="text/css">
        .linebreak {
            overflow: hidden;
            /*超出部分隐藏*/
            text-overflow: ellipsis;
            /* 超出部分显示省略号 */
            white-space: normal;
            /*规定段落中的文本不进行换行 */
            width: 100%;
        }
        .el-pagination__jump {
            margin-left:0px;
        }
    </style>
</head>

<body>
    <div id="app" style="margin:0 auto;">
        <el-container direction="vertical" style=" border: 1px #ccc solid;">
            <template>
                <el-table v-if="typeId == 0" ref="tableBox" :data="list" style="width: 100%;" row-key="id" border
                    :max-height="tableHeight">
                    <el-table-column type="index" min-width="20" fixed></el-table-column>
                    <el-table-column prop="goodsCode" label="商品编码" min-width="70"></el-table-column>
                    <el-table-column prop="goodsCodeName" label="商品名称" min-width="100"></el-table-column>
                    <el-table-column prop="qty" label="调拨数量" min-width="50"></el-table-column>
                    <el-table-column prop="packedCount" label="调拨箱数" min-width="50"></el-table-column>
                    <el-table-column prop="cwarehouseName" label="调出仓/库存/周转天数" min-width="100">
                        <template slot-scope="scope">
                            <span>{{scope.row["cwarehouseName"] }}/{{scope.row["cwarehouseWarehouseStock"]
                                }}/{{scope.row["cwarehouseInventoryDay"] }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="rwarehouseName" label="调入仓/库存/周转天数" min-width="100">
                        <template slot-scope="scope">
                            <span>{{scope.row["rwarehouseName"] }}/{{scope.row["rwarehouseWarehouseStock"]
                                }}/{{scope.row["rwarehouseInventoryDay"] }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="brandName" label="采购" min-width="50"></el-table-column>
                    <el-table-column prop="createTime" label="创建时间" min-width="90"></el-table-column>
                </el-table>
                <el-table v-else ref="tableBox" :data="list" style="width: 100%;" row-key="id" border
                    :max-height="tableHeight">
                    <el-table-column type="index" min-width="20" fixed></el-table-column>
                    <el-table-column prop="sku_ids" label="商品编码" min-width="100"></el-table-column>
                    <el-table-column prop="sku_idName" label="商品名称" min-width="100"></el-table-column>
                    <el-table-column prop="qty" label="数量" min-width="50"></el-table-column>
                    <el-table-column prop="link_warehouseName" label="调入仓" min-width="100"></el-table-column>
                    <el-table-column prop="warehouseName" label="调出仓" min-width="100"></el-table-column>
                    <el-table-column prop="receiver_name" label="申请人" min-width="80"></el-table-column>
                    <el-table-column prop="expiration_date" label="申请时间" min-width="90"></el-table-column>
                </el-table>
                <div style="text-align: right;">
                    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :current-page="currentPage" :page-sizes="[20, 50, 100, 150]" :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper" :total="total">
                    </el-pagination>
                </div>
            </template>
        </el-container>
    </div>
    <script>
        var vm = new Vue({
            el: '#app',
            data: function () {
                return {
                    //正式上线时，请切换为正式地址，末尾不许带 /
                    //测试：http://**************
                    //正式：http://************* 
                    // thisInterfaceUrl: "http://**************",
                    thisLonding: true,
                    list: [],
                    tableHeight: null,
                    typeId: null,
                    currentPage: 1,
                    pageSize: 20,
                    total: 0,
                }
            },
            created () {

            },
            async mounted () {
                this.getStyleSheetInfo();
                this.beginShowing()
            },
            methods: {
                handleSizeChange (val) {
                    this.pageSize = val;
                    this.getStyleSheetInfo();
                },
                handleCurrentChange (val) {
                    this.currentPage = val;
                    this.getStyleSheetInfo();
                },
                beginShowing () {
                    this.$nextTick(function () {
                        // 文档显示区域的高度 - 
                        if (this.$refs.tableBox) {
                            this.tableHeight = window.innerHeight - 20;
                            this.$refs.tableBox.doLayout()
                        }
                    })
                },
                async getStyleSheetInfo () {
                    var me = this;
                    let searchURL = window.location.search;
                    searchURL = searchURL.substring(1, searchURL.length);
                    this.typeId = searchURL.split("&")[0].split("=")[1];
                    let targetPageId = searchURL.split("&")[1].split("=")[1];
                    me.thisLonding = true;
                    let targetUrl = '';
                    let parm = {};
                    if (this.typeId == 0) {
                        targetUrl = '/api/Inventory/Allocate/WarehouseTransferDetails';
                        parm.transferId = targetPageId;
                    } else {
                        targetUrl = '/api/Inventory/PackagesProcessing/GetApproveDetialList';
                        parm.id = targetPageId;
                    }
                    parm.pageSize = this.pageSize;
                    parm.currentPage = this.currentPage;
                    $.ajax({
                        url: targetUrl,
                        type: 'GET',
                        dataType: 'json',
                        data: parm,
                        success: function (response) {
                            me.list = response.data.list;
                            me.total = response.data.total;
                        },
                        error: function (xhr, textStatus, errorThrown) {
                            console.log('Error: ', errorThrown);
                        }
                    });
                },
            }
        });
    </script>
</body>

</html>