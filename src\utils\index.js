import dayjs from 'dayjs'

export function formatTime(date, pattern) {
  return dayjs(date).format(pattern)
}

function ListSort(arr= []) {
  var s;
  for (var i = 1; i < arr.length; i++) {
    for (var j = i; j > 0; j--) {
      if (arr[j].sort < arr[j - 1].sort) {
          s=arr[j];
          arr[j]=arr[j-1]
          arr[j-1]=s
         }
       }
   }
   return arr
  }
 
export function treeToList(tree = [], idValue = null, childrenField = 'children', idField = 'id', parentIdField = 'parentId') {
  const list = []
  if (!childrenField) childrenField = 'children'
  for (let i = 0, j = tree.length; i < j; i++) {
    const d = tree[i]
    const id = d[idField]
    if (!list.some(l => l[idField] === id)) {
      list.push(d)
    }
    if (parentIdField) d[parentIdField] = idValue
    const children = d[childrenField]
    if (children && children.length > 0) {
      const items = treeToList(children, id, childrenField, idField, parentIdField)
      const values = items.values()
      for (const v of values) {
        if (!list.some(l => l[idField] === v[idField])) {
          list.push(v)
        }
      }
    }
  }
  return list
}

export function listToTree(list = [], root = null, idField = 'id', parentIdField = 'parentId') {
  list=ListSort(list)
  const tree = []
  const hash = {}
  const childrenField = 'children'
  for (let i = 0, l = list.length; i < l; i++) {
    const d = list[i]
    hash[d[idField]] = d
  }

  for (let i = 0, l = list.length; i < l; i++) {
    const d = list[i]
    const parentID = d[parentIdField]
    if (parentID === '' || parentID === 0) {
      tree.push(d)
      continue
    }

    const parent = hash[parentID]
    if (!parent) {
      tree.push(d)
      continue
    }

    let children = parent[childrenField]
    if (!children) {
      children = []
      parent[childrenField] = children
    }
    children.push(d)
  }

  if (root) {
    root[childrenField] = tree
    return [root]
  }

  return tree
}

export function getListParents(list = [], idValue, idField = 'id', parentIdField = 'parentId', includeSelf = false) {
  const parents = []
  const self = list.find(o => o[idField] === idValue)
  if (!self) {
    return parents
  }

  if (includeSelf) {
    parents.unshift(self)
  }

  let parent = list.find(o => o[idField] === self[parentIdField])
  while (parent && parent[idField] > 0) {
    parents.unshift(parent)
    parent = list.find(o => o[idField] === parent[parentIdField])
  }
  return parents
}

export function getTreeParents(tree = [], idValue, childrenField = 'children', idField = 'id', parentIdField = 'parentId', parentIdValue = 0) {
  const list = treeToList(tree, parentIdValue, childrenField, idField, parentIdField)
  return getListParents(list, idValue, idField, parentIdField)
}

export function getTreeParentsWithSelf(tree = [], idValue, childrenField = 'children', idField = 'id', parentIdField = 'parentId', parentIdValue = 0) {
  const list = treeToList(tree, parentIdValue, childrenField, idField, parentIdField)
  return getListParents(list, idValue, idField, parentIdField, true)
}









/**
 * @param {Function} fn 防抖函数
 * @param {Number} delay 延迟时间
 */
 export function debounce(fn, delay) {
  var timer;
  return function () {
    var context = this;
    var args = arguments;
    clearTimeout(timer);
    timer = setTimeout(function () {
      fn.apply(context, args);
    }, delay);
  };
}

/**
 * @param {date} time 需要转换的时间
 * @param {String} fmt 需要转换的格式 如 yyyy-MM-dd、yyyy-MM-dd HH:mm:ss
 */
export function formatTime1(time, fmt) {
  if (!time) return '';
  else {
    const date = new Date(time);
    const o = {
      'M+': date.getMonth() + 1,
      'd+': date.getDate(),
      'H+': date.getHours(),
      'm+': date.getMinutes(),
      's+': date.getSeconds(),
      'q+': Math.floor((date.getMonth() + 3) / 3),
      S: date.getMilliseconds(),
    };
    if (/(y+)/.test(fmt))
      fmt = fmt.replace(
        RegExp.$1,
        (date.getFullYear() + '').substr(4 - RegExp.$1.length)
      );
    for (const k in o) {
      if (new RegExp('(' + k + ')').test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          RegExp.$1.length === 1
            ? o[k]
            : ('00' + o[k]).substr(('' + o[k]).length)
        );
      }
    }
    return fmt;
  }
}


export function formatdate (timestamp) {
  let date
  //判断是否为空，空值的话 会报错
  if (timestamp == null) return
  //时间戳为10位需*1000，时间戳为13位的话不需乘1000
  timestamp.length === 10 ? date = new Date(parseInt(timestamp * 1000)) : date = new Date(parseInt(timestamp));
  let Y = date.getFullYear() + '-';
  let M = (date.getMonth() + 1 < 10 ? `0${(date.getMonth() + 1)}` : date.getMonth() + 1) + '-';
  let D = date.getDate() + ' ';
  let h = date.getHours() + ':';
  let m = (date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes()) + ':';
  let s = date.getSeconds() < 10 ? `0${date.getSeconds()}` : date.getSeconds();
  return Y + M + D + h + m + s;
}


export function  getFirstDayOfMonth() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();
    const firstDay = new Date(year, month, 1);
    return formatDate(firstDay);
}
function formatDate (date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

