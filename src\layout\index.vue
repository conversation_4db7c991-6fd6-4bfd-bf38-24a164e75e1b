<template>
  <el-container class="container" style="height: 100%;">
    <el-aside class="aside" :class="collapsedClass" width>
      <div class="logo collapsedLogo" :class="isCollapse ? 'logo-collapse' : ''">
        <router-link to="/" tag="div" class="logo-text" style="font-size: 22px;"> {{ isCollapse ? projectNameShort :
          projectName }} <span style="color:red" v-if="showTest">(测试)</span> </router-link>
      </div>
      <!-- 新加的搜索框 -->
      <div v-if="showSidebarSearch" style="margin-top: 15px">
        <div class="search-display" v-if="!showSearchList" @click="toSearch">
          <i class="el-icon-search"></i>
        </div>
        <el-select v-if="showSearchList" size="medium" filterable clearable :filter-method="search" v-model="sidebar"
          @change="handleChange" class="search" placeholder="请输入关键字" ref="searchInput">
          <el-option v-for="item in groups" :key="item.key" :label="item.label" :value="item.key"></el-option>
        </el-select>
      </div>
      <el-scrollbar class="page-component__scroll" style="border-right: solid 1px #e6e6e6;">
        <el-menu :default-active="$route.path" :default-openeds="openeds" :collapse.sync="isCollapse"
          :unique-opened="false" :collapse-transition="false" class="aside-menu-vertical" style="margin-bottom:55px"
          @select="onSelectMenu">
          <my-menu-item v-for="menu in menuTree" :key="menu.id" :item="menu" :isCollapse="isCollapse" />
        </el-menu>
      </el-scrollbar>
    </el-aside>
    <el-container>
      <el-header class="header" height="auto">
        <el-row class="navbar">
          <el-col :span="10" class="left-menu">
            <div class="left-menu-item fold-btn" :title="isCollapse ? '展开' : '收起'" @click="onCollapse">
              <i :class="isCollapse ? 'fa el-icon-s-unfold' : 'fa el-icon-s-fold'" />
            </div>
            <el-breadcrumb separator-class="el-icon-arrow-right" class="breadcrumb-inner breadcrumb-container">
              <template v-for="(title, index) in menueTitles">
                <el-breadcrumb-item v-if="title" :key="index">{{ title }}</el-breadcrumb-item>
              </template>
            </el-breadcrumb>
          </el-col>
          <el-col :span="4" class="right-menu" style="{}">

          </el-col>

          <el-col :span="10" class="right-menu">
            <!-- <el-dropdown trigger="click">
              <div class="right-menu-item">
                <el-link :underline="false" @click="directWookTeam"  style="color: aliceblue;font-size: 16px;" target="_blank">团队协作</el-link>
              </div>
              <el-dropdown-menu :visible-arrow="false" style="margin-top: 2px;width:160px;"></el-dropdown-menu>
            </el-dropdown> -->
            <!-- <el-dropdown>
              <div class="right-menu-item el-dropdown-link">
                <el-link class="el-dropdown-link" :underline="false" @click="handleNewErpCommand('finance')" style="color: aliceblue;font-size: 16px;"
                  target="_blank">店铺资金</el-link>
              </div>
              <el-dropdown-menu :visible-arrow="false" style="margin-top: 2px;width:160px;"></el-dropdown-menu>
            </el-dropdown> -->
            <!-- <el-dropdown>
              <div class="right-menu-item el-dropdown-link">
                <el-link class="el-dropdown-link" :underline="false" @click="handleNewErpCommand('send')" style="color: aliceblue;font-size: 16px;"
                  target="_blank">线体汇总</el-link>
              </div>
              <el-dropdown-menu :visible-arrow="false" style="margin-top: 2px;width:160px;"></el-dropdown-menu>
            </el-dropdown> -->
            <!-- <span style="margin-right: 5px;font-size: 15px;color: white;cursor: pointer" @click="searchMethod" v-if="checkPermission('HomeKnowledgeBase')"><i class="el-icon-search"></i></span>
            <span style="font-size: 15px;color: white;cursor: pointer" @click="searchMethod" v-if="checkPermission('HomeKnowledgeBase')">搜索</span> -->
            <el-dropdown>
              <div class="right-menu-item el-dropdown-link">
                <el-link class="el-dropdown-link" :underline="false" @click="goto('salary')"
                  style="color: #409EFF;font-size: 16px;" target="_blank">薪资管理</el-link>
              </div>
              <el-dropdown-menu :visible-arrow="false" style="margin-top: 2px;width:160px;"></el-dropdown-menu>
            </el-dropdown>
            <!-- <el-dropdown>
              <div class="right-menu-item el-dropdown-link">
                <el-link class="el-dropdown-link" :underline="false" @click="handleNewErpCommand('teamwork')"
                  style="color: aliceblue;font-size: 16px;" target="_blank">蚂蚁分工</el-link>
              </div>
              <el-dropdown-menu :visible-arrow="false" style="margin-top: 2px;width:160px;"></el-dropdown-menu>
            </el-dropdown> -->
            <el-button type="primary" style="color: aliceblue;font-size: 15px;"
                @click="onShowWorkOrder">工单</el-button>
            <el-badge v-if="messagesNumber && messagesNumber > 0" :value="messagesNumber" style="z-index: 999999;"
              :max="99999" class="item">
              <el-button type="primary" style="color: aliceblue;font-size: 15px;"
                @click="loadSiteMsgList">站内消息</el-button>
            </el-badge>
            <el-button v-else type="primary" style="color: aliceblue;font-size: 15px;"
              @click="loadSiteMsgList">站内消息</el-button>
            <el-dropdown @command="handleNewErpCommand" v-if="false">
              <div class="right-menu-item el-dropdown-link" v-if="checkPermission('isbindbuynocount')">
                <el-link class="el-dropdown-link" :underline="false" @click="linknewerp"
                  style="color: aliceblue;font-size: 16px;" target="_blank">erp新板块</el-link>
              </div>
              <el-dropdown-menu slot="dropdown" style="width: 120px;">
                <el-dropdown-item command="teamwork">团队协作</el-dropdown-item>
                <el-dropdown-item>采购模块</el-dropdown-item>
                <el-dropdown-item>仓库模块</el-dropdown-item>
                <!-- <el-dropdown-item  divided>退出登录</el-dropdown-item> -->
              </el-dropdown-menu>
            </el-dropdown>
            <el-badge v-if="checkPermission('isbindbuynocount')" :value="wareCount"
              style="margin-right:10px; z-index: 99;" :max="99" class="item">
              <el-tooltip class="item" effect="dark" content="入库拍摄未绑定采购单数量" placement="bottom-start">
                <el-button type="primary" style="color: aliceblue;font-size: 15px;"
                  @click="linkwareorder">入库拍摄</el-button>
              </el-tooltip>
            </el-badge>
            <!-- <el-tooltip class="item" effect="dark" content="在线用户/在线链接，同用户可能多次登录！" placement="bottom-start">
              <el-button type="primary" style="color: aliceblue;font-size: 15px;">在线:{{ onLineUserSum.DingUserCount }}</el-button>
            </el-tooltip> -->
            <el-dropdown @command="showAppTypeUsers">
              <div class="right-menu-item el-dropdown-link">
                <span v-if="xiaoyunToolOnline==false" style="color:red;font-size: 16px;">小昀不在线</span>
                <el-link v-else class="el-dropdown-link" :underline="false"
                  style="color: aliceblue;font-size: 16px;" target="_blank">在线用户({{ onLineUserSum.DingUserCount }})</el-link>
              </div>
              <el-dropdown-menu :visible-arrow="false" style="margin-top: 2px;width:160px;" >
                <div class="right-menu-item el-dropdown-link">
                  <el-dropdown-item  >
                    <el-link type="danger" @click="showDownloadCenter=true" >下载中心</el-link>
                  </el-dropdown-item>
                  <el-dropdown-item  >总在线链接({{ onLineUserSum.OnlineCount }} )
                  </el-dropdown-item>
                  <el-dropdown-item  >在线用户({{ onLineUserSum.DingUserCount }} )
                  </el-dropdown-item>

                  <el-dropdown-item v-for="item in onLineUserSum.AppTypeCountList" :command="item.AppType" >
                    [{{item.AppType}}]在线({{ item.ConnCount }})
                  </el-dropdown-item>

              </div>
              </el-dropdown-menu>
            </el-dropdown>

            <!-- <el-dropdown trigger="click">
              <div class="right-menu-item">
                <el-link :underline="false" @click="directLuntan" style="color: aliceblue;font-size: 16px;"
                  target="_blank">昀晗论坛</el-link>
              </div>
              <el-dropdown-menu :visible-arrow="false" style="margin-top: 2px;width:160px;"></el-dropdown-menu>
            </el-dropdown> -->
            <!-- <el-dropdown trigger="click">
              <div class="right-menu-item">
                <el-link :underline="false" href="http://**************:81/zentao/user-login-L3plbnRhby8=.html "
                  style="color: aliceblue;font-size: 16px;" target="_blank">禅道</el-link>
              </div>
              <el-dropdown-menu :visible-arrow="false" style="margin-top: 2px;width:160px;"></el-dropdown-menu>
            </el-dropdown> -->
            <el-dropdown trigger="click">
              <div class="right-menu-item">
                <el-image class="user-avatar el-avatar el-avatar--circle" :src="avatar"
                  style="height:36px;width:36px;line-height: 36px;">
                  <template #error>
                    <img :src="avatarDefault">
                  </template>
                </el-image>
                <span>{{ userName }} <el-badge v-if="showcount > 0" :value="showcount" />
                </span>
              </div>
              <template #dropdown>
                <el-dropdown-menu :visible-arrow="false" style="margin-top: 2px;width:160px;">
                  <!-- <el-dropdown-item icon="el-icon-setting" @click.native="directWookTeam">蚂蚁分工(test)</el-dropdown-item> -->
                  <el-dropdown-item icon="el-icon-chat-line-round" @click.native="UserMessages">个人消息 <el-badge
                      v-if="showcount > 0" :value="showcount" />
                  </el-dropdown-item>
                  <el-dropdown-item icon="el-icon-setting" @click.native="OperateReport">运营报告
                    <!-- <el-badge v-if="showcount>0" :value="showcount" /> -->
                  </el-dropdown-item>
                  <el-dropdown-item icon="el-icon-setting" @click.native="queryTask">任务进度</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-download"
                    @click.native="downloadadministration">下载管理</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-setting" @click.native="Setting">个人设置</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-setting" @click.native="accountMaintenance">关联账户维护</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-link" v-if="checkPermission('financialManagementPermissions')" @click.native="financialManagement('salary')">新财务管理</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-link" v-if="checkPermission('SalaryCostStatisticsofCustomerServiceDepartment')" @click.native="financialManagement('department')">项目管理</el-dropdown-item>

                  <el-dropdown-item icon="el-icon-link"  @click.native="financialManagement('RPABackendManagement')">RPA后台管理</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-link"  @click.native="financialManagement('ShadowBladeInstallationPackage')">影刃安装包</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-link"  @click.native="financialManagement('DouYinTools')">抖音铺货工具箱</el-dropdown-item>

                  <el-dropdown-item icon="el-icon-link" v-if="checkPermission('linkOperatkj')" @click.native="linkOperatkj('kj')">跨境运营</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-link" @click.native="financialManagement('jstComein')">聚水潭入口</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-link" @click.native="financialManagement('qynx')">企业内训</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-link" @click.native="financialManagement('dlfxzgl')">独立站分销管理</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-link" @click.native="financialManagement('xyygl')">新运营管理</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-link" @click.native="financialManagement('1688xpzx')">1688选品中心</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-download" @click.native="DownloadUShield"
                    v-if="isUShield">下载U盾插件</el-dropdown-item>
                  <el-dropdown-item divided icon="el-icon-switch-button" @click.native="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-col>
        </el-row>
        <div v-if="tabPosition === 'top'" style="padding:0px 0px;">
          <el-tabs v-if="showTabs" ref="tabs" :value="tabName" :type="tabType" @tab-click="onTabClick"
            @tab-remove="onRemoveTab" @contextmenu.prevent.native="onOpenMenu">
            <el-tab-pane v-for="tab in tabsList" :key="tab.path" :name="tab.path" :label="tab.meta.title"
              :closable="tab.meta.closable">
              <template #label >
                <span >
                  <i :class="tab.meta.icon" /> {{ tab.meta.title }}
                  <span class="fixedSign_item" v-show="$route.path === tab.meta.path && showFixedSign"></span>
                </span>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-header>
      <div class="fixed-container">
        <!-- v-if="checkPermission('HomeKnowledgeBase')" -->
        <div class="center-container">
          <div class="relative-container">
            <i class="el-icon-full-screen AI_icon" v-show="!showAiModal && !isFirst" @click="showAiModal = !showAiModal" title="展开AI对话"></i>
            <el-input
              ref="searchInput"
              :class="['custom-inputt', {'wide-input': showSuggestions, 'narrow-input': !showSuggestions}]"
              v-model="searchContent"
              placeholder="昀晗AI"
              maxlength="50"
              clearable
              @focus.stop="onSearchEvent(true)"
              @input="handleInput"
              @clear="clearInput"
              @blur.stop="onSearchEvent(false)"
            ></el-input>
            <!-- <div v-if="showSuggestions" class="search-style-item" @click="textFieldClickEvent" v-click-outside="clickOutsideEvent">
              <searchPage ref="refsearchPage"  :searchContent="searchContent" :searchData='searchData' @searchEngineClose="searchEngineClose" />
            </div> -->
          </div>
        </div>
      </div>
      <el-main class="main" style="height:100%;">
        <keep-alive :max="15">
          <router-view :key="key" />
        </keep-alive>
      </el-main>
      <ul v-if="tabPosition === 'top'" v-show="rightMenu.visible" ref="rightMenu"
        :style="{ left: rightMenu.left + 'px', top: rightMenu.top + 'px' }" class="contextmenu">
        <li @click="refreshCurrentTab">
          <i class="el-icon-refresh-right" />刷新
        </li>
        <el-divider v-if="canClose || canCloseOthers || canCloseRight || canCloseLeft || canCloseAll" />
        <li v-if="canClose" @click="closecurrentTab">
          <span>关闭</span>
        </li>
        <li v-if="canCloseOthers" @click="closeOthersTabs">
          <i class="el-icon-more" />关闭其它
        </li>
        <li v-if="canCloseRight" @click="closeRightTabs">
          <i class="el-icon-right" />关闭到右侧
        </li>
        <li v-if="canCloseLeft" @click="closeLeftTabs">
          <i class="el-icon-back" />关闭到左侧
        </li>
        <li v-if="canCloseAll" @click="closeAllTabs">
          <span>关闭所有</span>
        </li>
        <li @click="fixedPage(true)" v-show="!showFixed">
          <span>固定标签页</span>
        </li>
        <li @click="fixedPage(false)" v-show="showFixed">
          <span>取消固定</span>
        </li>
      </ul>
      <el-footer v-if="tabPosition === 'bottom'" class="footer" height>
        <el-tabs v-if="showTabs" ref="tabs" :value="tabName" :type="tabType" :tab-position="tabPosition"
          @tab-click="onTabClick" @tab-remove="onRemoveTab" @contextmenu.prevent.native="onOpenMenu">
          <el-tab-pane v-for="tab in tabsList" :key="tab.path" :name="tab.path" :label="tab.meta.title"
            :closable="tab.meta.closable">
            <template #label>
              <span>
                <i :class="tab.meta.icon" /> {{ tab.meta.title }} </span>
            </template>
          </el-tab-pane>
        </el-tabs>
      </el-footer>
      <ul v-if="tabPosition === 'bottom'" v-show="rightMenu.visible" ref="rightMenu"
        :style="{ left: rightMenu.left + 'px', top: rightMenu.top + 'px' }" class="contextmenu">
        <li v-if="canCloseAll" @click="closeAllTabs">
          <span>关闭所有</span>
        </li>
        <li v-if="canCloseLeft" @click="closeLeftTabs">
          <i class="el-icon-back" />关闭到左侧
        </li>
        <li v-if="canCloseRight" @click="closeRightTabs">
          <i class="el-icon-right" />关闭到右侧
        </li>
        <li v-if="canCloseOthers" @click="closeOthersTabs">
          <i class="el-icon-more" />关闭其它
        </li>
        <li v-if="canClose" @click="closecurrentTab">
          <span>关闭</span>
        </li>
        <el-divider v-if="canClose || canCloseOthers || canCloseRight || canCloseLeft || canCloseAll" />
        <li @click="refreshCurrentTab">
          <i class="el-icon-refresh-right" />刷新
        </li>
      </ul>
    </el-container>
    <el-dialog title="下载管理" :visible.sync="downloadmanagementdialog" width='65%' :close-on-click-modal="false"
      height='300px' v-dialogDrag>
      <downloadmanagement ref="downloadmanagement"></downloadmanagement>
    </el-dialog>
    <el-dialog title="关联账户维护" :visible.sync="maintenancedialog" width='25%' :close-on-click-modal="false" v-dialogDrag>
      <div style="height: 100px;display: flex;justify-content: center;align-items: center;">
        <el-form ref="relevance" :model="relevance" :rules="relevancerules" label-width="100px">
          <el-form-item label="聚水谭账号: " prop="jstLoginName">
            <el-input v-model.trim="relevance.jstLoginName" placeholder="请输入聚水谭账号" maxlength="50" clearable></el-input>
          </el-form-item>
          <el-form-item label="聚水谭密码: " prop="jstPassword">
            <el-input v-model.trim="relevance.jstPassword" placeholder="请输入聚水谭密码" maxlength="50" clearable></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="maintenancedialog = false">取 消</el-button>
        <el-button type="primary" @click="onpreservation">保 存</el-button>
      </span>
    </el-dialog>

    <el-dialog title="站内信关注类型设置帮助" :visible.sync="shopSiteMsgSubTypeHelp_Visable" width='92%' height='640px'>
      <img src="https://nanc.yunhanmy.com:10010/common/20240622/1804489524860096512.png"
        style="width: 100%;height: 100%;" />
    </el-dialog>
    <el-dialog title="站内消息" :visible.sync="stationDialog" width='92%' height='640px' v-dialogDrag
      @close="getSiteMsgCount">
      <el-radio-group v-model="radio1SiteMsg" @input="loadSiteMsgList">
        <el-radio-button label="待处理"></el-radio-button>
        <el-radio-button label="已处理"></el-radio-button>
        <el-radio-button label="全部"></el-radio-button>
      </el-radio-group>
      <el-radio-group v-model="radio2SiteMsg" @input="loadSiteMsgList" style="margin-left: 20px;">
        <el-radio-button label="全部"></el-radio-button>
        <el-radio-button label="已关注"></el-radio-button>
        <el-radio-button label="未关注"></el-radio-button>
      </el-radio-group>
      <el-input placeholder="请输入内容" v-model.trim="siteMsgFilter.Keywords" clearable
        style="margin-left: 20px;width:200px;" maxlength="50">
        <el-button slot="append" icon="el-icon-search" @click="loadSiteMsgList"></el-button>
      </el-input>
     
      <el-radio v-model="siteMsgSubsType" label="1" style="margin-left: 20px;">全部消息类型</el-radio>
      <el-radio v-model="siteMsgSubsType" label="2">已关注消息类型</el-radio>
      <el-link style="color:red;margin-left:10px;" @click="shopSiteMsgSubTypeHelp_Visable = true">关注类型怎么设置？</el-link>    

      <div v-if="siteMsgSubsType == 1" style="padding-top:4px;padding-bottom:4px;line-height: 32px;">
        <template>
          <el-popconfirm  title="确实要将当前页消息全部标记为已读吗？"  @confirm="onReadAllList">
            <el-button type="danger"    slot="reference"  style="margin-right: 4px;">一键已读</el-button>
          </el-popconfirm>
        </template>
        <el-button type="primary" @click="siteMsgSubClick({ subType1: '', subType2: '' })">全部</el-button>
        <el-dropdown v-for="item in siteMsgSubs" split-button type="primary" @command="siteMsgSubClick"
          @click="siteMsgSubClick({ subType1: item.label, subType2: '' })" style="margin-left: 4px;"> {{ item.label }}
          <el-dropdown-menu slot="dropdown">
            <template v-if="item.value && item.value.length > 0">
              <el-dropdown-item v-for="item2 in item.value" :command="{ subType1: item.label, subType2: item2 }">{{
                item2 }}</el-dropdown-item>
            </template>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div v-if="siteMsgSubsType == 2" style="padding-top:4px;padding-bottom:4px;">
        <template>
          <el-popconfirm  title="确实要将当前页消息全部标记为已读吗？"  @confirm="onReadAllList">
            <el-button type="danger"   slot="reference" style="margin-right: 4px;">一键已读</el-button>
          </el-popconfirm>
        </template>
        <el-button type="primary" @click="siteMsgSubClick({ subType1: '', subType2: '' })">全部</el-button>
        <el-dropdown v-for="item in siteMsgUserSubs" split-button type="primary" @command="siteMsgSubClick"
          @click="siteMsgSubClick({ subType1: item.label, subType2: '' })" style="margin-left: 4px;"> {{ item.label }}
          <el-dropdown-menu slot="dropdown">
            <template v-if="item.value && item.value.length > 0">
              <el-dropdown-item v-for="item2 in item.value" :command="{ subType1: item.label, subType2: item2 }">{{
                item2 }}</el-dropdown-item>
            </template>
          </el-dropdown-menu>
        </el-dropdown>
      </div>

      <el-row style="min-height: 500px;">
        <el-col :span="siteMsgRowform.id ? 17 : 24" style="min-height: 500px;">
          <el-table border :data="tabulardata" style="width: 1200px;height: 500px;" max-height="500"
            v-loading="siteMsgListLoading" @sort-change='siteMsgSortchange' @row-click="rowClick">
            <el-table-column prop="title" label="标题" sortable="custom">
              <template slot-scope="scope">
                <span
                  :style="{ color: scope.row.readTime ? '#909399' : 'black', overflow: 'hidden', whiteSpace: 'nowrap' }">
                  {{ scope.row.title }}({{ scope.row.content }}) </span>
              </template>
            </el-table-column>
            <el-table-column prop="subType" label="类型" sortable="custom" width="140"></el-table-column>
            <el-table-column prop="sendTime" label="发送时间" sortable="custom" width="160"></el-table-column>
            <!-- <el-table-column  label="操作"   width="100">
                <template slot-scope="scope">
                  <el-link type="primary"  @click="siteMsgClick(scope.row)" >查看_处理</el-link>
                </template>
              </el-table-column> -->
          </el-table>
        </el-col>
        <el-col :span="7" v-if="siteMsgRowform.id" style="padding: 0 10px;max-height:480px;overflow:auto;">
          <p><strong>主题：{{ siteMsgRowform.title }}</strong>
          </p>
          <p><strong>类型：{{ siteMsgRowform.subType }}</strong>
          </p>
          <p><strong>内容：</strong></p>
          <el-input type="textarea" :rows="6" v-model="siteMsgRowform.content" readonly></el-input>
          <p v-if="siteMsgRowform.linkType > 0 && siteMsgRowform.linkPath">
            <strong>链接处理：</strong>
            <!-- {{siteMsgRowform.linkPath}} -->
            <el-button type="primary" @click="siteMsgLinkClick(siteMsgRowform)">点击跳转</el-button>
          </p>
          <p><strong>发送人：</strong>{{ siteMsgRowform.senderName }}</p>
          <p><strong>发送时间：</strong>{{ siteMsgRowform.sendTime }}</p>
          <p v-if="siteMsgRowform.needExecRemark === 1">
            <strong>处理回复：</strong>
            <el-input type="textarea" :rows="6" placeholder="请回复处理内容" v-model="siteMsgRowform.execRemark">
            </el-input>
          </p>
          <p v-if="siteMsgRowform.execState >= 10">
            <strong>查看(处理)时间：</strong> {{ siteMsgRowform.execTime }}
          </p>
          <p v-if="siteMsgRowform.execState >= 10">
            <strong>查看(处理)人：</strong> {{ siteMsgRowform.execUserName }}
          </p>
          <template
            v-if="siteMsgRowform && siteMsgRowform.needExec === 1 && siteMsgRowform.execState == 0 && siteMsgRowform.execBtnText && siteMsgRowform.execBtnText.length > 0">
            <el-button type="primary" @click="siteMsgExecClick(true)">{{ siteMsgRowform.execBtnText.split(',')[0]
              }}</el-button>
            <el-button type="danger" @click="siteMsgExecClick(false)"
              v-if="siteMsgRowform.execBtnText.split(',').length > 1">{{ siteMsgRowform.execBtnText.split(',')[1]
              }}</el-button>
          </template>
        </el-col>
      </el-row>
      <div style="display: flex; justify-content: flex-end;">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pager.currentPage" :page-sizes="[100, 200, 300, 400]" :page-size="100"
          layout="total, sizes, prev, pager, next, jumper" :total="messagetotal">
        </el-pagination>
      </div>
    </el-dialog>

    <div v-show="showAiModal" class="search-style-item" @click="textFieldClickEvent" >
      <DBModule class="DBModule" v-if="showSuggestions" @close="colseSuggestions" @shrink="shrink"/>
    </div>

    <el-dialog title="下载中心" :visible.sync="showDownloadCenter" width="960px" height="500px">
    
      <span style="font-size:16px"><strong>小昀工具箱</strong></span>
      <!-- <el-button @click='testTool'>测试</el-button> -->
      <hr/>
      <span style="color:red;margin-right:20px">【云中城】点这里下载</span>
      <downloadfile style="margin-right:20px" url='https://*************:10010/media/video/20250716/1945450576335052800.zip' filename="小昀工具箱.zip"></downloadfile>
      <downloadfile style="margin-right:20px" url='https://*************:10010/media/video/20250527/1927286707745898496.exe' filename="小昀工具箱依赖包(1).exe"></downloadfile>
      <downloadfile style="margin-right:20px" url='https://*************:10010/media/video/20250527/1927286801740251136.exe' filename="小昀工具箱依赖包(2).exe"></downloadfile> 
      <br/>
      <span style="color:red;margin-right:20px">【通用】下载</span>
      <downloadfile style="margin-right:20px" url='https://nanc.yunhanmy.com:10010/media/video/20250716/1945450576335052800.zip' filename="小昀工具箱.zip"></downloadfile>
      <downloadfile style="margin-right:20px" url='https://nanc.yunhanmy.com:10010/media/video/20250527/1927286707745898496.exe' filename="小昀工具箱依赖包(1).exe"></downloadfile>
      <downloadfile style="margin-right:20px" url='https://nanc.yunhanmy.com:10010/media/video/20250527/1927286801740251136.exe' filename="小昀工具箱依赖包(2).exe"></downloadfile> 
      <br/>
      <span style="color:red">小昀工具箱，首次安装使用时，请先安装后面的两个依赖包（以后不用再装了）！
        <br/>下载完后，解压文件夹（建议解压到D盘或E盘），找到“小昀工具箱.exe”直接运行。
        <br/>也可以右键创建快捷方式到桌面，方便以后访问。</span>


    </el-dialog>
    <vxe-modal v-model="appTypeUserListDialog" size="medium" :width="900" :height="720" title="在线用户"   >

          <vxe-table
          border size="medium"
          show-overflow
          :data="appTypeUserListData"
          ref="xTable1"
          height="650"
          :row-config="{isHover: true}"
          :sort-config="{trigger: 'cell'}"
          :scroll-y="{enabled: true}">
          <vxe-column type="seq" width="60"></vxe-column>
          <vxe-column field="avatar" title="头像" width="60">
            <template #default="{ row }">
              <!-- <vxe-image :src="row.avatar" :width="40" circle></vxe-image>   -->
              <el-image 
    style="width: 40px; height: 40px"
    :src="row.avatar" 
    :preview-src-list="[row.avatar]">
  </el-image>           
            </template>
          </vxe-column>
          <vxe-column field="userName" title="姓名" sortable  width="80"></vxe-column>
          <vxe-column field="title" title="岗位" sortable width="160"></vxe-column>
          <vxe-column field="appType" title="应用" sortable  width="120"></vxe-column>
          <vxe-column field="deptFullName" sortable title="部门" ></vxe-column>
        </vxe-table>                
    </vxe-modal>

    


  </el-container>
</template>
<script>
import ramePage from '@/views/admin/iframe'
import { mapGetters } from 'vuex'
import MyMenuItem from './components/my-menu-item'
import { listToTree, getTreeParents } from '@/utils'
import cesTable from "@/components/Table/table.vue";
import Sortable from 'sortablejs'
import { isExternalLink } from '@/utils/validate'
import { toLogout } from '@/router'
import { countCurrentReport } from '@/api/operatemanage/monitorreport'
import { getWookTeamUser, getHotSaleGoodChatUserListsAsync    } from '@/api/admin/user'
import {   getUnreadCount, pageGetUserNotices, getUserNoticeSub, getNoticeSubTypes, readNotice, ReadNoticeBatch, ExecNotice ,GetAppTypeOnlineUsers} from '@/api/admin/msgcenteruser'
import { getIsBindBuyNoMsg } from "@/api/inventory/warehousingordervide"
import { get, merge } from '@/api/vo/outSiteUser'
import store from '@/store'
import { authUShield } from '@/utils/ushieldauth'
import { isUShieldUser } from '@/api/admin/viewUShield'
import downloadmanagement from "./downloadmanagement.vue";
import searchPage from "@/views/kbs/searchEngine/searchPage.vue";
import { getUserInfo } from "@/api/operatemanage/productalllink/alllink";
import { setWaterMark } from '@/utils/setWaterMark.js'
import { formatTime } from "@/utils";
import { getAccessToken } from '@/api/admin/auth.js'
import { env } from 'echarts';
import { latestList, topsearch} from "@/api/kbs/qa.js"
import { keyWordSearch} from "@/api/kbs/searchEngine.js"
import _ from 'lodash'
import { set } from 'lodash';
import DBModule from './DBModule.vue'
import middlevue from "@/store/middle.js"
import downloadfile from "@/components/Comm/downloadfile.vue"

const signalR = require('@microsoft/signalr')
if (!Element.prototype.closest) {
  if (!Element.prototype.matches) {
    Element.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector
  }

  Element.prototype.closest = function (s) {
    var el = this
    if (!document.documentElement.contains(el)) return null
    do {
      if (el.matches(s)) return el
      el = el.parentElement
    } while (el !== null)
    return null
  }
}

export default {
  name: 'AppMain',
  components: {
    downloadmanagement,
    searchPage,
    MyMenuItem,
    ramePage,
    cesTable,
    DBModule,
    downloadfile
  },
  data() {
    return {
      searchpagetotal: 0,
      searchpage: {
      },
      keywords: '',
      searchHidden: false,
      searchData:null,
      that: this,
      showSuggestions: false,
      showData: [],
      messagetotal: 0,
      tabulardata: [],
      appTypeUserListData:[],
      cornermark: [],
      variable: null,
      siteMsgSubs: [],
      siteMsgUserSubs: [],
      siteMsgSubsType: '1',
      siteMsgRowform: {},
      siteMsgListLoading: false,
      shopSiteMsgSubTypeHelp_Visable: false,
      radio1SiteMsg: "待处理",
      radio2SiteMsg: "全部",
      siteMsgFilter: {
        StateType: 2, //消息状态类型：0或null全部，1已处理，2未处理。
        ReadType: null,  //查看类型：0或null全部，1只看关注
        SubType1: null,  //订阅类型1
        SubType2: null,  //订阅类型2
        Keywords: "",
      },
      relevancerules: {
        jstLoginName: [
          { required: true, message: '请输入聚水潭账号', trigger: 'change' }
        ],
        jstPassword: [
          { required: true, message: '请输入聚水潭密码', trigger: 'change' }
        ]
      },
      relevance: {
        jstLoginName: null,//聚水潭账号
        jstPassword: null,//聚水潭密码
        erpUserId: null,//erp用户id
      },
      maintenancedialog: false,//关联账户维护
      pager: { orderBy: '', isAsc: false, currentPage: 1, pageSize: 100 },
      stationDialog: false,
      appTypeUserListDialog:false,
      messagesNumber: null,
      iframeshow: false,
      showcount: 0,
      isUShield: false,
      openeds: [],
      menuTree: [],
      projectName: '昀晗贸易',
      projectNameShort: 'AD',
      avatarDefault: require('@/assets/images/avatar.png'),
      collapsedClass: 'menu-expanded',
      isCollapse: false,
      isPc: false,
      tabsList: [],
      wareCount: 0,
      onLineUserSum:{DingUserCount:0,OnlineCount:0,AppTypeCountList:[]},
      searchContent: '',
      timer: null,
      timerCount:false,
      rightMenu: {
        top: 0,
        left: 0,
        visible: false,
        selectedTab: {}
      },
      tabPosition: 'top', // top | bottom
      tabType: 'border-card', // '' | border-card | card
      showSidebarSearch: true,
      showSearchList: false,
      groups: [],
      sidebar: '',
      downloadmanagementdialog: false,
      userid: null,
      isClickClose: false,
      fouces: false,
      showFixedTag:[],
      showTest: false,
      showDownloadCenter:false,      
      xiaoyunToolOnline:false,
      showAiModal:false,  
      isFirst:true,
    }
  },
  computed: {
    ...mapGetters([
      'menus',
      'userName',
      'avatar'
    ]),
    menueTitles() {
      let parentTitles = []
      const path = this.$route.meta.path
      const menu = this.menus.find(m => m.path === path)
      if (menu && menu.id > 0) {
        const parents = getTreeParents(this.menuTree, menu.id)
        parentTitles = parents.map(p => p.label)
        parentTitles.push(menu.label)
      }
      return parentTitles
    },
    cachedViews() {
      return this.$store.getters.cachedViews
    },
    key() {
      const tabs = this.$store.getters.tabsList
      const fullPath = this.$route.fullPath
      const tab = tabs && tabs.find(t => t.fullPath === fullPath)
      const k = tab && tab._k ? tab._k : ''

      return this.$route.fullPath + k
    },
    showTabs() {
      return this.tabsList.length > 0
    },
    tabName() {
      return this.$route.path
    },
    canClose() {
      return (
        this.rightMenu.selectedTab?.meta &&
        this.rightMenu.selectedTab?.meta?.closable
      )
    },
    canCloseOthers() {
      const tabIndex = this.tabsList.findIndex(
        t => t.fullPath === this.rightMenu.selectedTab?.fullPath
      )
      return (
        (tabIndex === 0 && this.tabsList.length > 1) ||
        (tabIndex > 0 && this.tabsList.length > 2)
      )
    },
    canCloseRight() {
      const tabIndex = this.tabsList.findIndex(
        t => t.fullPath === this.rightMenu.selectedTab?.fullPath
      )
      return this.tabsList.length - 1 > tabIndex
    },
    canCloseLeft() {
      const hasHome = this.tabsList.findIndex(t => t.fullPath === '/') >= 0
      const tabIndex = this.tabsList.findIndex(t => t.fullPath === this.rightMenu.selectedTab?.fullPath)
      return tabIndex > (hasHome ? 1 : 0)
    },
    canCloseAll() {
      return this.tabsList.length > 1
    },
    showFixed(){
      return this.showFixedTag.find(item=>item.path === this.rightMenu.selectedTab.path)?.isFixed
    },
    showFixedSign(){
      return this.showFixedTag.find(item=>item.path === this.$route.path)?.isFixed
    }
  },
  watch: {
    $route() {
      this.addTab()
    },
    'rightMenu.visible'(value) {
      if (value) {
        document.body.addEventListener('click', this.closeRightMenu)
      } else {
        document.body.removeEventListener('click', this.closeRightMenu)
      }
    },
    tabsList() {
      const views = this.tabsList.map(t => t.name)
      this.$store.commit('tabsView/set_cached_view', views)
      this.$store.commit('app/saveTabsData', JSON.stringify(this.tabsList))
    }
  },
  async created() {
    // console.log("我存在")
    var port = window.location.port;
    if (port != "8001") {
      this.showTest = true
    }
    this.showFixedTag = JSON.parse(localStorage.getItem('fixedTab')) || []
    this.isPc = window.innerWidth >= 768
    this.collapsedClass = 'menu-expanded'

    // 还原会话tabs
    let sessionStorageTabs = sessionStorage.getItem('tabs')
    sessionStorageTabs = sessionStorageTabs ? JSON.parse(sessionStorageTabs) : []
    if (sessionStorageTabs && sessionStorageTabs.length > 0) {
      this.tabsList = sessionStorageTabs
    }

    if (this.menus && this.menus.length > 0) {
      const cloneMenus = _.cloneDeep(this.menus)
      // 检查外链
      cloneMenus.forEach(m => {
        if (!m.newWindow) {
          const newWindoww = isExternalLink(m.path)
          if (newWindoww == true) {
          } else {
            const nowrouter = m.path;
            var reg = RegExp(/http/);
            // if(reg.test(nowrouter)){
            // }
          }
        }
      })

      this.menuTree = listToTree(cloneMenus)
      console.log('我是menuTree', this.menuTree)
      this.openeds = this.menus.filter(l => l.opened).map(l => l.id + '')
      }
    this.addFixedTab()
    this.addTab()
    await this.initData()
    await this.getSiteMsgCount()
  },
  mounted() {
    this.setSort()
    this.bankuai();
    this.downloadSearch()
  },
  async beforeDestroy() {
    this.signalWareRconnection.stop()
  },
  methods: {
    shrink(){
      this.showAiModal = false
    },
    async testTool(){
      const response = await fetch('http://localhost:33231/api/erptool/erppub/showorderdetail?orderType=3&orderNo=*********');
     
    },  
    async showAppTypeUsers(appType){
      if(!appType)
        return;

      const respData= await GetAppTypeOnlineUsers({appType});
      if(respData?.success){
        this.appTypeUserListData=respData.data;
        this.appTypeUserListDialog=true;
      }
      else{
         this.$message.error(respData.message);
      }
      
    },
    colseSuggestions(){
      this.showSuggestions = false
    },
    fixedPage(isFixed) {
      const res = JSON.parse(localStorage.getItem('fixedTab')) || []
      const tab = this.tabsList.find(t => t.path === this.rightMenu.selectedTab.path)
      const flag = res.some(item => item.path === tab.path)
      if (isFixed && !flag) {
        res.push(tab)
        res.forEach(item => {
          item.isFixed = isFixed
        })
        localStorage.setItem('fixedTab', JSON.stringify(res))
      } else {
        res.splice(res.findIndex(item => item.path === tab.path), 1)
        localStorage.setItem('fixedTab', JSON.stringify(res))
      }
      this.showFixedTag = JSON.parse(localStorage.getItem('fixedTab')) || []
    },
    addFixedTab() {
      const route = JSON.parse(localStorage.getItem('fixedTab')) || []
      if (!route || route.length === 0) return
      const res = route.filter(item => item.meta.title || !item.meta.noTab)
      for (let i = 0; i < res.length; i++) {
        const exists = this.tabsList.some(item => item.path === res[i].path)
        if (!exists) {
          if (res[i].fullPath.indexOf("?") > -1) {
            let index = this.tabsList.findIndex(x => x.path == res[i].path);
            if (index > -1) {
              this.tabsList[index].fullPath = res[i].fullPath;
            }
          }
          const matchedIndex = res[i].matched && res[i].matched.length - 1
          let name = matchedIndex >= 0 && res[i].matched[matchedIndex].components.default.name
          name = name || res[i].name
          const tab = {
            name: name,
            path: res[i].path,
            fullPath: res[i].fullPath,
            meta: { ...res[i].meta }
          }
          if (tab.path === '/') {
            this.tabsList.unshift(tab)
          } else {
            this.tabsList.push(tab)
          }
        }
      }
    },
    async financialManagement(site) {
      if (site == "salary") {
        let resp = await getAccessToken();
        if (resp) {
          // 判断是否开发环境、测试环境
          var port = window.location.port;
          if (port == "8001") {
            window.open(`http://${window.location.hostname}:7001/#/?accessToken=${resp}`);
          } else {
            window.open(`http://${window.location.hostname}:7000/#/?accessToken=${resp}`);
          }
        }
      }else if (site == "department") {
        let resp = await getAccessToken();
        if (resp) {
          // 判断是否开发环境、测试环境
          var port = window.location.port;
          if (port == "8001") {
            window.open(`http://${window.location.hostname}:7003/#/?accessToken=${resp}`);
          } else {
            window.open(`http://${window.location.hostname}:7002/#/?accessToken=${resp}`);
          }
        }
      }else if (site == "RPABackendManagement") {
        let resp = await getAccessToken();
        if (resp) {
          // 判断是否开发环境、测试环境
          var port = window.location.port;
          if (port == "8001") {
            window.open(`http://*************:8001/#/login`);
          } else {
            window.open(`http://*************:8001/#/login`);
          }
        }
      }else if (site == "ShadowBladeInstallationPackage") {
        let resp = await getAccessToken();
        if (resp) {
          // 判断是否开发环境、测试环境
          var port = window.location.port;
          if (port == "8001") {
            window.open(`http://*************:81/shadowblade/shadowblade-1.2.1-setup.exe`);
          } else {
            window.open(`http://*************:81/shadowblade/shadowblade-1.2.1-setup.exe`);
          }
        }
      }else if (site == "DouYinTools") {
        let resp = await getAccessToken();
        if (resp) {
          // 判断是否开发环境、测试环境
          var port = window.location.port;
          if (port == "8001") {
            window.open(`http://192.168.16.20:30353/api/Matrix/Package/GetSetupPackageUrl?product=YunHan.Operator`);
          } else {
            window.open(`http://192.168.16.20:30353/api/Matrix/Package/GetSetupPackageUrl?product=YunHan.Operator`);
          }
        }
      }else if (site == "jstComein") {
        let resp = await getAccessToken();
        if (resp) {
          // 判断是否开发环境、测试环境
          var port = window.location.port;
          if (port == "8001") {
            window.open(`https://ww.erp321.com/epaas`);
          } else {
            window.open(`https://ww.erp321.com/epaas`);
          }
        }
      } else if (site == 'qynx') {
        let resp = await getAccessToken();
        if (resp) {
          // 判断是否开发环境、测试环境
          var port = window.location.port;
          if (port == "8001") {
            window.open(`http://${window.location.hostname}:7007/#/?accessToken=${resp}`);
          } else {
            window.open(`http://${window.location.hostname}:7006/#/?accessToken=${resp}`);
          }
        }
      } else if(site == 'dlfxzgl'){
        var port = window.location.port;
        let user= `{"username":"${this.$store.getters.token}"}`;
        let url = port == "8001"?'http://*************:31605/rebotlogin':'http://*************:30886/rebotlogin';
        if(url && url!=''){
          window.open(`${url}?parm=${encodeURIComponent(user)}`);
        }
      }else if (site == 'xyygl') {
        let resp = await getAccessToken();
        if (resp) {
          // 判断是否开发环境、测试环境
          var port = window.location.port;
          if (port == "8001") {
            window.open(`http://${window.location.hostname}:7005/#/?accessToken=${resp}`);
          } else {
            window.open(`http://${window.location.hostname}:7004/#/?accessToken=${resp}`);
          }
        }
      } else if (site == '1688xpzx'){
        let resp = await getAccessToken();
        if (resp) {
          // 判断是否开发环境、测试环境
          var port = window.location.port;
          if (port == "8001") {
            window.open(`http://${window.location.hostname}:8211/#/?accessToken=${resp}`);
          } else {
            window.open(`http://${window.location.hostname}:8210/#/?accessToken=${resp}`);
          }
        }
      }
    },
    //搜索引擎关闭
    searchEngineClose(isclose){
      if(isclose){
        this.showSuggestions = false
      }
      // this.showSuggestions = false
      this.searchContent = ''
    },
    searchMethod(){
      this.searchHidden = !this.searchHidden
      this.searchContent = ''
      if (this.searchHidden) {
          this.$nextTick(() => {
              this.$refs.searchInput.focus();
          });
      }
    },
    async goto(site) {
      if (site == "salary") {
        let resp = await getAccessToken();
        if (resp) {

          // window.open(`http://127.0.0.1:9000/#/?accessToken=${resp}`);

          // 判断是否开发环境、测试环境
          var port = window.location.port;
          var isDevelopment = port == "8002" || port == "8000";
          if (isDevelopment) {
            window.open(`http://**************:9000/#/?accessToken=${resp}`);
          } else {
            window.open(`http://**************:9001/#/?accessToken=${resp}`);
          }
        }
      }
    },

    //关联账户维护保存
    async onpreservation() {
      if (!this.relevance.jstLoginName || !this.relevance.jstPassword) {
        this.$message({ message: '请填写完整信息', type: 'warning' })
        return
      }
      const params = { ...this.relevance }
      const { success } = await merge(params)
      if (success) {
        this.$message({ message: '关联成功', type: 'success' })
        this.maintenancedialog = false
      }
    },
    //获取关联账户维护
    async accountMaintenance() {
      const { data, success } = await get();
      const res = await getUserInfo()
      let resdata = res.data;
      this.relevance.jstLoginName = data?.jstLoginName;
      this.relevance.jstPassword = data?.jstPassword;
      this.relevance.erpUserId = (data?.erpUserId) ? (data?.erpUserId) : (resdata?.id);
      this.maintenancedialog = true;
    },
    async rowClick(row, column, event) {
      await this.siteMsgClick(row)
    },
    handleSizeChange(val) {
      this.pager.pageSize = val;
      this.loadSiteMsgList();
    },
    handleCurrentChange(val) {
      this.pager.currentPage = val;
      this.loadSiteMsgList();
    },
    processResData(res) {
      // 生成一段代码：res.data返回的是字符串组数，将res.data每行数据按“-”分割，前面为大类后面为小类，去重后，生成label+value的数组，注意按大类分组，个别行只有大类没有小类。
      const categoryPairs = res.data.map(line => {
        const parts = line.split("-");
        const label = parts[0].trim();
        const value = parts.length > 1 ? parts[1].trim() : null; // 处理只有大类没有小类的情况
        return { label, value };
      });
    },
    clearInput(){
      this.isClickClose= true
      setTimeout(() => {
        this.isClickClose = false
      }, 500);
      this.searchContent = ''
    },
      clickOutsideEvent(event) {

        if(this.timerCount == false) return
        const inputElement = this.$refs.searchInput?.$el;
        // && !(inputElement.contains(event.target))
        // inputElement &&
        if ( !this.fouces ) {
          if (!this.isClickClose) {
            this.showSuggestions = false;
          }
          this.searchContent = '';
          this.searchData = null
        }
      },
      textFieldClickEvent(){
        this.showSuggestions = true
      },
      toponsearch(){
        var pager = this.$refs.pager.getPager()
        this.searchpage = {
          ...pager
        }
        this.$nextTick(()=>{
          this.handleInput(this.keywords);
        })
      },
      handleInput: _.throttle(async function (value) {
        this.keywords = value;
        if (!value){
          this.searchData.questionList = []
          return
        }
        this.timerCount = true
        value = value.replace(/\s/g, "")
        value = value.replace(/\s/g, "")

        var questionList = await topsearch({ keyword: value, latest: 0, ...this.searchpage })
        // var questionHot = await keyWordSearch({ keyword: value })
        // if(!questionList.success||!questionHot.success){
        //   return;
        // }

        this.searchpagetotal = questionList.data?.total??0;

        questionList = questionList.data?.list ?? [];


        // questionHot = questionHot.data?.list ? questionHot.data.list : [];
        var questionHot = [];

        if (questionList) {
          questionList.forEach(item => {
            if(item.showType == 0 && item.question){
              item.question = item.question.replace(/<[^>]+>/g, '')
              let oRegExp = new RegExp('(' + value + ')', 'ig');
              item.question = item.question.replace(oRegExp, `<span style="color:#409EFF;">$1</span>`)
            }else if(item.showType == 1 && item.answer){
              item.answer = item.answer.replace(/<[^>]+>/g, '')
              let oRegExp = new RegExp('(' + value + ')', 'ig');
              item.answer = item.answer.replace(oRegExp, `<span style="color:#409EFF;">$1</span>`)
            }
          })
        }
        // if (questionHot) {
        //   questionHot.forEach(item => {
        //     item.description = item.description.replace(/<[^>]+>/g, '')
        //     let oRegExp = new RegExp('(' + value + ')', 'ig');
        //     item.description = item.description.replace(oRegExp, `<span style="color:#409EFF;">$1</span>`)
        //   })
        // }
        this.searchData = {
          questionList, questionHot
        }

      }, 800),
      async onSearchEvent(e) {
        this.searchpage.currentPage = 1;
        this.showSuggestions = true
        this.showAiModal = true
        this.timerCount = true
        this.isFirst = false
        this.fouces = e;
        // this.showSuggestions = true
        return
        this.showSuggestions = true
        // this.$router.push({path: '/account/searchPage', query: {searchContent: e}})
        // this.searchContent = ''
      },
      //关联账户维护保存
      async onpreservation(){
        if(!this.relevance.jstLoginName || !this.relevance.jstPassword){
          this.$message({ message: '请填写完整信息', type: 'warning' })
          return
        }
        const params = {...this.relevance}
        const { success } = await merge(params)
        if (success) {
          this.$message({ message: '关联成功', type: 'success' })
          this.maintenancedialog = false
        }
      },
      //获取关联账户维护
      async accountMaintenance(){
        const { data, success } = await get();
        const res = await getUserInfo()
        let resdata = res.data;
        this.relevance.jstLoginName = data?.jstLoginName;
        this.relevance.jstPassword = data?.jstPassword;
        this.relevance.erpUserId = (data?.erpUserId) ? (data?.erpUserId) : (resdata?.id);
        this.maintenancedialog = true;
      },
      async rowClick(row, column, event){
        await this.siteMsgClick(row)
      },
      handleSizeChange(val) {
        this.pager.pageSize = val;
        this.loadSiteMsgList();
      },
      handleCurrentChange(val) {
        this.pager.currentPage = val;
        this.loadSiteMsgList();
      },
      processResData(res) {
        // 生成一段代码：res.data返回的是字符串组数，将res.data每行数据按“-”分割，前面为大类后面为小类，去重后，生成label+value的数组，注意按大类分组，个别行只有大类没有小类。
        const categoryPairs = res.data.map(line => {
          const parts = line.split("-");
          const label = parts[0].trim();
          const value = parts.length > 1 ? parts[1].trim() : null; // 处理只有大类没有小类的情况
          return { label, value };
        });

      const groupedByLabel = {};

      categoryPairs.forEach(pair => {
        if (!groupedByLabel[pair.label]) {
          groupedByLabel[pair.label] = [];
        }
        groupedByLabel[pair.label].push(pair.value);
      });

      const uniqueCategories = Object.entries(groupedByLabel).map(([label, values]) => {
        const uniqueValues = [...new Set(values)];
        return { label, value: uniqueValues.length > 1 ? uniqueValues : uniqueValues[0] }; // 如果只有一个值，直接返回值而不是数组
      });

      // 添加按大类排序
      uniqueCategories.sort((a, b) => a.label.localeCompare(b.label));

      return uniqueCategories;
    },
    /* 站内消息关注类型点击 */
    async siteMsgSubClick(pr) {

      this.siteMsgFilter.SubType1 = pr.subType1 ? pr.subType1 : "";
      this.siteMsgFilter.SubType2 = pr.subType2 ? pr.subType2 : "";

      await this.loadSiteMsgList();
    },
    async siteMsgSortchange(column) {
      if (!column.order) {
        this.pager.orderBy = "";
        this.pager.isAsc = false;
      }
      else {
        this.pager.orderBy = column.prop;
        this.pager.isAsc = column.order.indexOf("descending") == -1 ? true : false;
      }
      await this.loadSiteMsgList();
    },
    async loadSiteMsgList() {
      if (this.stationDialog === false) {
        const res_UserSub = await getUserNoticeSub();
        this.siteMsgUserSubs = this.processResData(res_UserSub);

        const res_SiteSubs = await getNoticeSubTypes();
        this.siteMsgSubs = this.processResData(res_SiteSubs);

        this.siteMsgFilter.Keywords = "";
        this.stationDialog = true;
      }

      if (this.radio1SiteMsg == "全部")
        this.siteMsgFilter.StateType = 0;
      if (this.radio1SiteMsg == "待处理")
        this.siteMsgFilter.StateType = 2;
      else if (this.radio1SiteMsg == "已处理")
        this.siteMsgFilter.StateType = 1;

      if (this.radio2SiteMsg == "全部")
        this.siteMsgFilter.ReadType = 0;
      else if (this.radio2SiteMsg == "已关注")
        this.siteMsgFilter.ReadType = 1;
      else if (this.radio2SiteMsg == "未关注")
        this.siteMsgFilter.ReadType = 2;

      const params = {
        ...this.siteMsgFilter,
        ...this.pager
      }
      this.siteMsgListLoading = true;
      const { data, success } = await pageGetUserNotices(params)
      this.siteMsgListLoading = false;
      if (!success) {
        return
      }
      this.tabulardata = data.list
      this.messagetotal = data.total;
      this.siteMsgRowform = {};
    },
    async onReadAllList(){
      let ids = this.tabulardata.filter(item => !item.readTime).map(item => item.id);
      if(ids && ids.length > 0){
        let batchRlt=await ReadNoticeBatch(ids);
        if(batchRlt?.success){
          for(let i=0;i<this.tabulardata.length;i++){
            let row=this.tabulardata[i];
            if (!row.readTime) {
              row.readTime = new Date();           
            }
          }
        }
      }
    },
    async siteMsgClick(row) {
      if (!row.readTime) {
        row.readTime = new Date();
        readNotice({ id: row.id });
      }

      this.siteMsgRowform = row;
    },
    async siteMsgExecClick(isOk) {
      if (this.siteMsgRowform.needExecRemark == 1 && !this.siteMsgRowform.execRemark) {
        this.$message({
          title: '提示',
          message: '请填写处理内容',
          type: 'warning'
        });
        return;
      }
      const res = await ExecNotice({ id: this.siteMsgRowform.id, isReject: isOk ? 0 : 1, execRemark: this.siteMsgRowform.execRemark });
      if (res.success) {
        this.$message({
          message: '操作成功!',
          type: 'success'
        })
      }
      this.loadSiteMsgList();
    },
    async siteMsgLinkClick(row) {
      if (!row.linkPath)
        return;
      if (row.linkType && row.linkType == 1) {
        this.$router.push({ path: row.linkPath });
        this.stationDialog = false;
      }
      else if (row.linkType && row.linkType == 2) {
        window.open(row.linkPath);
      }

    },
    async getSiteMsgCount(fromNotice) {
      if (fromNotice) {
        if (this.messagesNumber && this.messagesNumber > 0)
          this.messagesNumber += 1;
        else
          this.messagesNumber = 1;
        return;
      }


      const res = await getUnreadCount();
      this.messagesNumber = res.data;

    },
    getUserIP(callback) {
      var ip_dups = {};
      var RTCPeerConnection = window.RTCPeerConnection
        || window.mozRTCPeerConnection
        || window.webkitRTCPeerConnection;
      var useWebKit = !!window.webkitRTCPeerConnection;
      var mediaConstraints = {
        optional: [{ RtpDataChannels: true }]
      };
      var servers = {
        iceServers: [
          { urls: "stun:stun.services.mozilla.com" },
          { urls: "stun:stun.l.google.com:19302" },
        ]
      };
      var pc = new RTCPeerConnection(servers, mediaConstraints);
      function handleCandidate(candidate) {
        var ip_regex = /([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/
        var hasIp = ip_regex.exec(candidate)
        if (hasIp) {
          var ip_addr = ip_regex.exec(candidate)[1];
          if (ip_dups[ip_addr] === undefined)
            callback(ip_addr);
          ip_dups[ip_addr] = true;
        }
      }
      pc.onicecandidate = function (ice) {
        if (ice.candidate) {
          handleCandidate(ice.candidate.candidate);
        }
      };
      pc.createDataChannel("");
      pc.createOffer(function (result) {
        pc.setLocalDescription(result, function () { }, function () { });
      }, function () { });
      setTimeout(function () {
        var lines = pc.localDescription.sdp.split('\n');
        lines.forEach(function (line) {
          if (line.indexOf('a=candidate:') === 0)
            handleCandidate(line);
        });
      }, 1000);
    },
    async downloadSearch() {
      const nowTime = formatTime(new Date(), "YYMMDDHHmm");
      const res = await getUserInfo()
      let ressdata = res.data;
      let obj = {
        str1: ressdata?.nickName,
        str2: nowTime + ressdata?.id,
        str3: '',
        str4: '',
      }
      let ress = setWaterMark(obj)
      this.userid = res.data?.id;


    },
    getIP(callback) {
      let recode = {};
      let RTCPeerConnection = window.RTCPeerConnection || window.mozRTCPeerConnection || window.webkitRTCPeerConnection;
      if (!RTCPeerConnection) {
        let win = iframe.contentWindow;
        RTCPeerConnection = win.RTCPeerConnection || win.mozRTCPeerConnection || win.webkitRTCPeerConnection;
      }
      //创建实例，生成连接
      let pc = new RTCPeerConnection();
      // 匹配字符串中符合ip地址的字段
      function handleCandidate(candidate) {
        let ip_regexp = /([0-9]{1,3}(\.[0-9]{1,3}){3}|([a-f0-9]{1,4}((:[a-f0-9]{1,4}){7}|:+[a-f0-9]{1,4}){6}))/;
        let ip_isMatch = candidate.match(ip_regexp)[1];
        if (!recode[ip_isMatch]) {
          callback(ip_isMatch);
          recode[ip_isMatch] = true;
        }
      }
      //监听icecandidate事件
      pc.onicecandidate = (ice) => {
        if (ice.candidate) {
          handleCandidate(ice.candidate.candidate);
        }
      };
      //建立一个伪数据的通道
      pc.createDataChannel('');
      pc.createOffer((res) => {
        pc.setLocalDescription(res);
      }, () => { });

      //延迟，让一切都能完成
      setTimeout(() => {
        let lines = pc.localDescription.sdp.split('\n');
        lines.forEach(item => {
          if (item.indexOf('a=candidate:') === 0) {
            handleCandidate(item);
          }
        })
      }, 1000);
    },
    onShowWorkOrder(){
      this.$showDialogform({
          path: `@/views/project/WorkOrderList.vue`,
          title: '工单列表',
          autoTitle: false,
          args: {  mode: 3 },
          height: 650,
          width: '80%',
          callOk:null
      })
    },
    async downloadadministration() {
      this.downloadmanagementdialog = true;
      this.$nextTick(() => {
        this.$refs.downloadmanagement.getlist(this.userid);
      });
    },
    async linknewerp() {
      // const res = await getWookTeamUser()
      let user = `{"username":"${this.$store.getters.token}"}`;
      window.open(`http://***************:8000/?parm=${encodeURIComponent(user)}`);
    },
    async initData() {
      this.initSignalR()
      //this.initWareSignalR();
      setTimeout(async () => {
        await getIsBindBuyNoMsg()
      }, 1000)

      let res = await getHotSaleGoodChatUserListsAsync()
      if (!res?.success) return
      let count = 0
      res.data.forEach(e => {
        count = count + e.readNum
      });
      this.showcount = count

      // 是否是U盾用户
      let ushieldRes = await isUShieldUser();
      if (!ushieldRes?.success) return;
      this.isUShield = ushieldRes.data;
    },
    initSignalR() {
      if(!store.getters.token )
        return;

        this.xiaoyunToolChatWebsocket= new signalR.HubConnectionBuilder().withUrl('http://localhost:33231/api/erptool/toolschat', {
        accessTokenFactory: () => { return store.getters.token },
        skipNegotiation: true,
        transport: signalR.HttpTransportType.WebSockets
      
      }).withAutomaticReconnect({
        nextRetryDelayInMilliseconds: (retryContext) => {  
          return 2000; // 返回 null 停止重试
        }
      }).build()       
     
      this.xiaoyunToolChatWebsocket.onreconnected(connId=> {
        this.xiaoyunToolOnline=true;
      })     
      this.xiaoyunToolChatWebsocket.onreconnecting(connId=> {
        this.xiaoyunToolOnline=false;
      })   
     
      // 连接断开
      this.xiaoyunToolChatWebsocket.onclose(error => {
        this.xiaoyunToolOnline=false;       
      });
      this.xiaoyunToolChatWebsocket.start()
      .then(() => this.xiaoyunToolOnline=true)
      .catch(err => console.error("连接失败: ", err));


      this.signalRconnection = new signalR.HubConnectionBuilder().withUrl(process.env.VUE_APP_BASE_API_MsgCenter + '/ChatHub?AppType=ERP', {
        accessTokenFactory: () => { return store.getters.token },
        skipNegotiation: true,
        transport: signalR.HttpTransportType.WebSockets//,
       // qs:{AppType:'ERP'},
      }).withAutomaticReconnect([3000, 5000, 10000, 15000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000
        , 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000
        , 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000
        , 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000]).build()
      this.signalRconnection.on('ConnectSucceeded', data => { console.log(data) })
      this.signalRconnection.on('SaleGoodChatMessage', data => { this.showcountAdd() })
      this.signalRconnection.on('delMessageCount', data => { this.showcountDel(data) })

      let that = this;
      this.signalRconnection.on("SignalrMsg", function (msgtxt) {
        let msgor = JSON.parse(msgtxt);
        // console.log(msgor);

        if (!msgor.MsgType) {
          that.$notify({
            position: 'top-right',
            title: msgor.MsgTitle ? msgor.MsgTitle : '',
            message: msgor.MsgContent,
            dangerouslyUseHTMLString: true,
            duration: 10000
          });
        }
        else if (msgor.MsgType == "sysNotice") {
          that.$notify({
            position: 'top-left',
            title: msgor.MsgTitle ? msgor.MsgTitle : '',
            type: 'warning',
            message: msgor.MsgContent,
            dangerouslyUseHTMLString: true,
            duration: 0
          });
        }
        else if (msgor.MsgType == "success" || msgor.MsgType == "info" || msgor.MsgType == "error" || msgor.MsgType == "sitemsg") {
          if (msgor.MsgType == "sitemsg")
            that.getSiteMsgCount(true);

          that.$notify({
            position: 'top-right',
            title: msgor.MsgTitle ? msgor.MsgTitle : '',
            type: msgor.MsgType,
            message: msgor.MsgContent,
            dangerouslyUseHTMLString: true,
            duration: 10000
          });
        }
        else if (msgor.MsgType == "Inventory.WarehousingOrderVideo.IsBindCount") {
          that.$nextTick(() => {
            that.wareCount = msgor.ExtData.Data;
          })
        }
        else if(msgor.MsgType == "OnLineUserCountChange"){
          that.$nextTick(() => {
            that.onLineUserSum.DingUserCount =  msgor.ExtData.DingUserCount;
            that.onLineUserSum.OnlineCount =  msgor.ExtData.OnlineCount;
            that.onLineUserSum.AppTypeCountList=  msgor.ExtData.AppTypeCountList;
          })
        }
        else {
          //后端所有key在这个注册 ConstWebMsgKeyEnum
          if(msgor.ExtData){
            middlevue.$emit(msgor.MsgType,msgor.ExtData)
          }
        }
      });

      this.signalRconnection.start()
    },
    initWareSignalR() {
      this.signalWareRconnection = new signalR.HubConnectionBuilder().withUrl(process.env.VUE_APP_BASE_API_Inventory + '/ChatHub', {
        accessTokenFactory: () => { return store.getters.token },
        skipNegotiation: true,
        transport: signalR.HttpTransportType.WebSockets,
      }).withAutomaticReconnect([3000, 5000, 10000, 15000, 30000]).build();
      this.signalWareRconnection.on('ConnectSucceeded', data => { console.log('', data) })
      this.signalWareRconnection.on('IsBindCount', data => { this.reciveWareMessage(data) })
      this.signalWareRconnection.start()
    },
    async reciveWareMessage(msg) {
      console.log('msg', msg);
      this.$nextTick(() => {
        this.wareCount = msg.data
      })

    },
    showcountAdd() {
      this.showcount = parseInt(this.showcount) + 1
    },
    showcountDel(int) {
      this.showcount = (parseInt(this.showcount) - parseInt(int)) < 0 ? 0 : (parseInt(this.showcount) - parseInt(int))
    },
    // 点击选项卡
    onTabClick(tab) {
      if (tab.name && tab.name !== this.tabName) {
        let index = this.tabsList.findIndex(x => x.path == tab.name);
        if (index > -1) {
          this.$router.push(this.tabsList[index].fullPath);
        } else {
          this.$router.push(tab.name)
        }
      }
    },
    // 添加标签
    addTab() {
      const route = this.$route
      if (!route.meta.title || route.meta.noTab) {
        return
      }

      const exists = this.tabsList.some(item => item.path === route.path)
      if (exists) {
        if (route.fullPath.indexOf("?") > -1) {
          let index = this.tabsList.findIndex(x => x.path == route.path);
          if (index > -1) {
            this.tabsList[index].fullPath = route.fullPath;
          }
        }
        return
      }
      // 获取视图缓存名
      const matchedIndex = route.matched && route.matched.length - 1
      let name = matchedIndex >= 0 && route.matched[matchedIndex].components.default.name
      name = name || route.name

      const tab = {
        name: name,
        path: route.path,
        fullPath: route.fullPath,
        meta: { ...route.meta }
      }

      if (tab.path === '/') {
        this.tabsList.unshift(tab)
      } else {
        this.tabsList.push(tab)
      }
    },
    // 关闭选项卡
    onRemoveTab(targetPath) {
      this.rightMenu.visible = false
      const index = this.tabsList.findIndex(tab => tab.path === targetPath)
      this.tabsList.splice(index, 1)[0]
      const toTab = this.tabsList[index] ? this.tabsList[index] : this.tabsList[index - 1]

      if (toTab) {
        if (targetPath === this.$route.fullPath)
          this.$router.push(toTab.path)
        else if (targetPath === this.$route.path)
          this.$router.push(toTab.path)
      } else {
        this.$router.push('/')
      }
    },
    // 退出登录
    logout: function () {
      this.$confirm('确认退出吗?', '提示').then(() => {
        toLogout()
      }).catch(() => { })
    },
    // 设置
    Setting: function () {
      this.$router.push('/account/settings')
    },
    OperateReport: function () {
      this.$router.push('/operatemanage/order/reportlist')
    },
    queryTask: function () {
      this.$router.push('/account/task')
    },
    UserMessages: function () {
      this.$router.push('/account/chat')
    },
    // 移动端选择菜单
    onSelectMenu: function (tab) {

      if (!this.isPc && !this.isCollapse) {
        this.isCollapse = true
        // this.collapsedClass = 'menu-collapsed'
      }
    },
    // 折叠导航栏
    async onCollapse() {
      await this.bankuai();
      this.isCollapse = !this.isCollapse;
      this.showSidebarSearch = !this.showSidebarSearch;
      // this.collapsedClass = this.isCollapse ? 'menu-collapsed':'menu-expanded';
    },
    // tab打开右键菜单
    onOpenMenu(e) {
      const $tab = e.target.closest('.el-tabs__item')
      if ($tab) {
        const id = $tab.getAttribute('id')
        const path = id.replace(/^tab-/, '')
        this.rightMenu.selectedTab = this.tabsList.find(
          t => t.path === path
        )

        this.rightMenu.visible = true
        // this.rightMenu.left = e.x
        // this.$nextTick(() => {
        //   if (this.tabPosition === 'bottom') {
        //     this.rightMenu.top = e.y - this.$refs.rightMenu.offsetHeight
        //   } else if (this.tabPosition === 'top') {
        //     this.rightMenu.top = e.y
        //   }
        // })

        const menuMinWidth = 132
        const offsetLeft = this.$el.getBoundingClientRect().left // container margin left
        const offsetWidth = this.$el.offsetWidth // container width
        const maxLeft = offsetWidth - menuMinWidth // left boundary
        const left = e.clientX - offsetLeft + 0 // 15: margin right

        if (left > maxLeft) {
          this.rightMenu.left = maxLeft
        } else {
          this.rightMenu.left = left
        }

        this.$nextTick(() => {
          if (this.tabPosition === 'bottom') {
            this.rightMenu.top = e.clientY - this.$refs.rightMenu.offsetHeight
          } else if (this.tabPosition === 'top') {
            this.rightMenu.top = e.clientY
          }
        })
      }
    },
    // tab拖拽排序
    setSort() {
      const _this = this
      if (!this.$refs.tabs) {
        return
      }
      const el = this.$refs.tabs.$el.querySelector('[role="tablist"]')
      Sortable.create(el, {
        ghostClass: '', // Class name for the drop placeholder,
        draggable: '[role="tab"]:not([id="tab-/"])', // Specifies which items inside the element should be draggable
        // animation: 150,
        setData: function (dataTransfer) {
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          if (this.tabType === 'border-card') {
            const targetTab = _this.tabsList.splice(evt.oldIndex, 1)[0]
            _this.tabsList.splice(evt.newIndex, 0, targetTab)
          } else {
            const targetTab = _this.tabsList.splice(evt.oldIndex - 1, 1)[0]
            _this.tabsList.splice(evt.newIndex - 1, 0, targetTab)
          }
        }
      })
    },
    // 关闭tab右键菜单
    closeRightMenu() {
      this.rightMenu.visible = false
    },
    refreshCurrentTab() {
      const tab = this.tabsList.find(
        t => t.fullPath === this.rightMenu.selectedTab?.fullPath
      )
      tab._k = tab._k ? ++tab._k : 1
      this.$store.commit('app/saveTabsData', JSON.stringify(this.tabsList))
    },
    closecurrentTab() {
      this.rightMenu.selectedTab &&
        this.onRemoveTab(this.rightMenu.selectedTab?.fullPath)
    },
    closeOthersTabs() {
      const tab = this.rightMenu.selectedTab
      const tabIndex = this.tabsList.findIndex(
        t => t.fullPath === tab.fullPath
      )
      if (tabIndex === 0) {
        this.tabsList = [tab]
      } else {
        this.tabsList = [this.tabsList[0], tab]
      }
      this.$router.push(tab.fullPath)
    },
    closeAllTabs() {
      const tab = this.tabsList.find(t => t.fullPath === '/')
      this.tabsList = tab ? [tab] : []
      this.$router.push('/')
    },
    closeRightTabs() {
      const selectedPath = this.rightMenu.selectedTab.fullPath
      const tabIndex = this.tabsList.findIndex(
        t => t.fullPath === selectedPath
      )
      this.tabsList = this.tabsList.slice(0, tabIndex + 1)
      const toTab = this.tabsList[this.tabsList.length - 1]
      if (toTab) {
        selectedPath !== this.$route.fullPath && this.$router.push(toTab.path)
      } else {
        this.$router.push('/')
      }
    },
    closeLeftTabs() {
      const selectedPath = this.rightMenu.selectedTab.fullPath
      const tabIndex = this.tabsList.findIndex(
        t => t.fullPath === selectedPath
      )
      const tab = this.tabsList.find(t => t.fullPath === '/')
      this.tabsList = tab ? [tab, ...this.tabsList.slice(tabIndex)] : [...this.tabsList.slice(tabIndex)]
      const toTab = this.tabsList[this.tabsList.length - 1]
      if (toTab) {
        selectedPath !== this.$route.fullPath && this.$router.push(toTab.path)
      } else {
        this.$router.push('/')
      }
    },
    // async directWookTeam(){
    //   const res = await getWookTeamUser()
    //   //res.data.username="15521293287"
    //   var user= `{"username":"${res.data.username}","userpass":"11111111","userpass2":0,"code":""}`;
    //   window.open(`http://192.168.16.17:8081/?parm=${encodeURIComponent(user)}`);
    // }
    async directLuntan() {
      const res = await getWookTeamUser()
      //res.data.username="15521293287"
      var user = `{"username":"${res.data.username}"}`;
      window.open(`http://192.168.16.241:8004/?parm=${encodeURIComponent(user)}`);
    },
    async handleChange(val) {
      var menus = this.groups.filter(g => g.key == val);
      if (!menus || menus.length <= 0) return;
      var menu = menus[0];
      this.groups = []
      this.sidebar = ''
        this.showSearchList = false
      if (menu.path.indexOf("http") > -1&&menu.path.indexOf("://") > -1) {
        let resp = await getAccessToken();
        if (resp) {
            menu.path = menu.path + `?accessToken=${resp}`
            window.open(menu.path);
        }
      }
      // 如果是需要U盾权限的视图
      if (menu.isNeedUShield) {
        var result = await authUShield(menu.viewId);
        if (!result) return;
      }

      if (menu.isNewSiteMenu) {
        this.handleNewErpCommand(menu.code)
      } else {
        this.$router.push(menu.path)
      }
    },
    toSearch() {
      this.showSearchList = true
      setTimeout(() => {
        this.$refs.searchInput.focus()
      }, 200)
    },
    search(val) {
      this.groups = []

      // 深度遍历配置树, 摘取叶子节点作为路由部分
      function deepTravel(config, fuc) {
        if (Array.isArray(config)) {
          config.forEach(subConfig => {
            deepTravel(subConfig, fuc)
          })
        } else if (config.children) {
          config.children.forEach(subConfig => {
            deepTravel(subConfig, fuc)
          })
        } else {
          fuc(config)
        }
      }

      deepTravel(this.menuTree, viewConfig => {
        // 构造舞台view路由
        if (viewConfig.label.includes(val)) {
          const viewRouter = {}
          viewRouter.path = viewConfig.path
          viewRouter.label = viewConfig.label
          viewRouter.isNewSiteMenu = viewConfig.isNewSiteMenu
          viewRouter.isNeedUShield = viewConfig.isNeedUShield
          viewRouter.viewId = viewConfig.viewId
          viewRouter.code = viewConfig.code
          viewRouter.key = `${viewRouter.path}_${Math.random()}`
          this.groups.push(viewRouter)
        }
      })
    },
    // async initData(){
    //   const res = await countCurrentReport()
    //   this.showcount=res.data
    // },
    bankuai() {
      setTimeout(() => {
        var span = document.querySelectorAll('li .el-menu-item span');
        for (var i = 0; i < span.length; i++) {
          if (span[i].innerText == '缺货板块') {
            span[i].style.color = 'red';
          }
        }
      }, 100)
    },
    async linkwareorder() {
      this.$router.push({ path: '/storehouse/warehousingordervidetab' })
    },
    handleNewErpCommand(command) {
      // 判断是否开发环境、测试环境
      var port = window.location.port;
      var isDevelopment = port == "8002" || port == "8000";

      let user = `{"username":"${this.$store.getters.token}"}`;
      let url = '';
      if (command == 'teamwork') {
        url = isDevelopment ? 'http://192.168.16.21:30006/rebotlogin' : 'http://192.168.16.21:30005/rebotlogin';
      } else if (command == 'send') {
        url = isDevelopment ? 'http://192.168.16.21:30272/rebotlogin' : 'http://192.168.16.21:30574/rebotlogin';
      } else if (command == 'finance') {
        url = isDevelopment ? 'http://192.168.16.21:31491/rebotlogin' : 'http://192.168.16.21:30695/rebotlogin';
      }

      if (url && url != '') {
        window.open(`${url}?parm=${encodeURIComponent(user)}`);
      }
    },
    // 下载U盾插件
    DownloadUShield() {
      window.open('https://nanc.yunhanmy.com:10010/share/sense_shield_installer_pub_2.5.0.60698-web.rar');
    },
    async linkOperatkj(site){
      if(site == 'kj'){
        let resp = await getAccessToken();
        if (resp) {
          // 判断是否开发环境、测试环境

          var port = window.location.port;
          var hostName = window.location.hostname
          // 线上环境
          var isOnLine = hostName == '*************'||hostName == '**************'
          if (port == "8001"&&isOnLine) {
            window.open(`http://${hostName}:8280/?accessToken=${encodeURIComponent(resp)}`);
          } else if(isOnLine) {
            window.open(`http://${hostName}:8281/?accessToken=${encodeURIComponent(resp)}`);
          }else{
            //本地开发跳转测试
            window.open(`http://**************:8280/?accessToken=${encodeURIComponent(resp)}`);
          }
        }
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.AI_icon{
  width: 25px;
  height: 25px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28px;
  color: #64c5b1;
}
.container ::v-deep .el-tabs__item:focus.is-active.is-focus:not(:active) {
  -webkit-box-shadow: none;
  box-shadow: none;
  border-radius: unset;
}
::v-deep .el-tabs__item{
position: relative !important;
}
.header {
  z-index: 1;
}

.footer {
  padding: 0px;
  overflow: hidden;
}

.el-breadcrumb {
  line-height: 50px !important;
}

.navbar ::v-deep {

  .el-breadcrumb__inner,
  .el-breadcrumb__separator {
    color: #f4f4f5;
  }

  .el-breadcrumb__item:last-child .el-breadcrumb__inner,
  .el-breadcrumb__item:last-child .el-breadcrumb__inner a,
  .el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover,
  .el-breadcrumb__item:last-child .el-breadcrumb__inner:hover {
    color: #fff;
  }

  .el-dropdown {
    color: #fff;
  }
}
.fixedSign_item{
  position: absolute;
  top: 0;
  right: 0;
  border-top: 5px solid #BEBEBE;
  border-left: 5px solid transparent;
  border-right: 5px solid #BEBEBE;
  border-bottom: 5px solid transparent;
}

.DBModule{
  width: 100%;
  height: 90vh;
}
</style>
<style lang="scss" scoped>

.center-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.relative-container {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  border: 0;
}

.relative-container ::v-deep .custom-input .el-input__inner {
  height: 44px;
}

::v-deep .relative-container .el-input__inner {
  border: 0;
}

// .wide-input {
//   width: 400px;
// }

// .narrow-input {
//   width: 280px;
// }

// ::v-deep .el-input__inner {
//   height: 35px;
//   line-height: 44px;
//   border-radius: 7px;
//   background-color: #40c8ff;
// }

::v-deep .custom-inputt .el-input__inner {
  height: 35px;
  line-height: 44px;
  border-radius: 7px;
  background-color: #57acff;
}

.wide-input {
  width: 400px;
}

.narrow-input {
  width: 200px;
}

.demo-table-expand {
  font-size: 0;
}

.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}

.contextmenu {
  margin: 0;
  background: #fff;
  z-index: 3000;
  position: absolute;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
  min-width: 130px;

  li {
    margin: 0;
    padding: 7px 16px;
    cursor: pointer;

    &:hover {
      background: #eee;
    }

    i {
      margin-right: 8px;
      font-size: 14px;
      vertical-align: -1px;
    }

    span {
      margin-left: 22px;
    }
  }

  .el-divider--horizontal {
    margin: 5px 0px;
  }
}

.user-avatar {
  margin: 7px 8px 7px 0;
  vertical-align: top;
  background-color: transparent;
}


.search-display {
  position: relative;
  width: 80%;
  margin: 0 auto;
  height: 36px;
  border-bottom: 1px rgb(185, 190, 195) solid;
  cursor: pointer;

  .el-icon-search {
    position: absolute;
    left: 1px;
    top: 10px;
    color: rgb(185, 190, 195);
  }
}

.search {
  // margin-top: 20px;
  width: 177px;
  padding-left: 15px;
}

</style>
<style>
@media screen and (max-width: 680px) {
  .collapsedLogo {
    display: none;
  }

  .el-dialog {
    width: 90% !important;
  }

  .content-expanded {
    max-width: 100% !important;
    max-height: calc(100% - 80px);
  }

  .el-menu--collapse {
    width: 100% !important;
  }

  .el-date-editor.el-input,
  .el-date-editor.el-input__inner,
  .el-cascader.el-cascader--medium {
    width: 100% !important;
  }

  .el-message-box {
    width: 80%;
  }

}

.fixed-container {
  position: fixed;
  top: 25px;
  left: 50%;
  transform: translate(-50%, -50%);
  /* z-index: 3000; */
  z-index: 98;
}

/* .custom-input {
  height: 44px;
} */

.search-style-item {
  position: fixed;
  /* top: 465px; */
  top: 51%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* top: 99%;
  left: 50%;
  transform: translate(-50%, 0); */
  width: 78vw;
  border-radius: 10px;

  background: #FFFFFF;
  box-shadow: 0px 0px 16px 0px rgba(0, 0, 0,0.5);
  border-radius: 7px;
  z-index: 2000;
}



</style>
