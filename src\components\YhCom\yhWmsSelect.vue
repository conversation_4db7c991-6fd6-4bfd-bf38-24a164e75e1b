<template>
    <div>
        <el-popover placement="bottom-start" width="200" trigger="hover">
            <div v-if="checkedWarehouses.length > 0">
                <div style="height: 500px;overflow: auto;">
                  <div v-for="item in getName().list" :key="item" style="line-height: 25px;"> {{ item }}</div>
                </div>
            </div>
            <div v-else>暂未选择仓库</div>
            <el-button slot="reference" @click="openCheckLog" height="30" style="width: 110px;">
                {{ getName().name }}
            </el-button>
        </el-popover>

        <el-dialog title="仓库选择" :visible.sync="chooseWmsVisible" :before-close="cancle" v-dialogDrag
            :append-to-body="true" width="60%">
            <div class="dialog-header">
                <el-input v-model="keywords" style="width:200px;margin-right: 5px;" placeholder="请输入关键字进行检索"
                    clearable />
                <div @click="isSelectAll(true)" class="btnCss primary">全选</div>
                <div @click="isSelectAll(false)" class="btnCss danger">反选</div>
                <div @click="selectByRegion('义乌')" class="btnCss success">义乌</div>
                <div @click="selectByRegion('南昌')" class="btnCss success">南昌</div>
                <el-checkbox v-model="isUse" @change="changeViewStatus" style="margin-right: 5px;">只查看启用</el-checkbox>
                <el-radio-group v-model="radio" @change="changeWms" style="display: flex;align-items: center;">
                    <el-radio v-for="item in wmsList" :label="item.value" :key="item.value" :tabindex="item.value">{{
                        item.label }}</el-radio>
                </el-radio-group>
            </div>
            <div style="height:450px;overflow:auto">
                <el-checkbox-group v-model="checkedWarehouses" @change="handleCheckChange">
                    <el-row>
                        <el-col v-for="item in viewWmsList" :key="item.value ? item.value : item.label" :span="6">
                            <el-checkbox :label="item.value" style="margin-bottom: 5px;">
                                <span
                                    :style="keywords && item.label.indexOf(keywords) >= 0 ? 'background-color:yellow' : ''">
                                    {{ item.label }}
                                </span>
                            </el-checkbox>
                        </el-col>
                    </el-row>
                </el-checkbox-group>
            </div>

            <div style="display: flex;justify-content: end;margin-top: 10px;">
                <el-button @click="GetCommonWarehouseAsync" v-throttle="1000">选取常用仓库</el-button>
                <el-button @click="SetCommonWarehouseAsync" v-throttle="1000">设置为常用仓库</el-button>
                <el-button @click="clearWms" v-throttle="1000">清空</el-button>
                <el-button @click="cancle" v-throttle="1000">取消</el-button>
                <el-button type="primary" @click="confirm" v-throttle="1000">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getAllWarehouse } from "@/api/inventory/warehouse";
import { GetVxeTableColumnCacheAsync, SetVxeTableColumnCacheAsync } from '@/api/admin/business'
const wmsList = [
    { value: 1, label: '全发货仓', key: '全发货仓1' },
    { value: 2, label: '发货内仓', key: '发货内仓2' },
    { value: 3, label: '发货外仓', key: '发货外仓3' },
    { value: 4, label: '内仓', key: '内仓4' },
    { value: 5, label: '外仓', key: '外仓5' }
]
export default {
    props: {
        values: {
            type: Array,
            default() {
                return [];//默认选择仓库
            }
        },
        addAllWarehouseConcept: {
            type: Boolean,
            default() {
                return false;//默认不添加全仓概念
            }
        },
        placeholderText: {
            type: String,
            default() {
                return '请选择仓库'
            }
        }
    },
    data() {
        return {
            tableId: 'wmsSelect20250403092136',
            wmsList,
            checkedWarehouses: [],//已经选中的仓库
            chooseWmsVisible: false,//仓库弹窗
            allWareHouse: [],//所有仓库
            isUse: true,//只查看启用
            viewWmsList: [],//显示的仓库列表
            useWms: [],//已启用仓库
            allUseId: [],//已启用仓库id
            allWmsId: [],//所有仓库id
            lastSelectList: [],//radioGroup上次选择的仓库id
            checkedWmsInfo: {//记录上一次选择的信息
                checkWms: [],
                radio: null,
                isUse: false,
            },
            radio: null,//单选框值
            keywords: '',//搜索关键字
        }
    },
    async created() {
        this.$set(this, 'checkedWarehouses', JSON.parse(JSON.stringify(this.values)))
        await this.init();
    },
    mounted() { },
    methods: {
        async init() {
            try {
                let { data, success } = await getAllWarehouse()
                if (success) {
                    if (this.addAllWarehouseConcept) {
                        data.unshift({ wms_co_id: -1, name: '全仓', isUse: 1 })
                    }
                    this.allWareHouse = JSON.parse(JSON.stringify(data))
                    this.viewWmsList = data.filter(item => item.isUse == 1).map(item => {
                        return { value: item.wms_co_id, label: item.name, };
                    });
                    //已启用仓库
                    this.useWms = data.filter(item => item.isUse == 1)
                    //已启用仓库所有id
                    this.allUseId = data.filter(item => item.isUse == 1).map(item => item.wms_co_id)
                    //所有仓库id
                    this.allWmsId = data.map(item => item.wms_co_id)
                }
            } catch (error) {
                console.log(error);
            }
        },
        changeWms(type) {
            let filterCondition
            if (this.lastSelectList && this.lastSelectList.length > 0) {
                this.$set(this, 'checkedWarehouses', this.checkedWarehouses.filter(item => !this.lastSelectList.some(item1 => item1 == item)))
            }
            const source = this.isUse ? this.useWms : this.allWareHouse;
            switch (type) {
                case 1:
                    filterCondition = item => item.isSendWarehouse === '是';
                    break;
                case 2:
                    filterCondition = item => item.isWc === 0 && item.isSendWarehouse === '是';
                    break;
                case 3:
                    filterCondition = item => item.isWc === 1 && item.isSendWarehouse === '是';
                    break;
                case 4:
                    filterCondition = item => item.isWc === 0;
                    break;
                case 5:
                    filterCondition = item => item.isWc === 1;
                    break;
            }
            const res = source.filter(filterCondition).map(item => item.wms_co_id);
            this.$set(this, 'checkedWarehouses', res)
        },
        //根据地区选择仓库
        selectByRegion(name) {
            this.keywords = name
            const res = this.isUse ? this.useWms.filter(item => item.region?.includes(name)).map(item => item.wms_co_id) : this.allWareHouse.filter(item => item.region?.includes(name)).map(item => item.wms_co_id)
            this.checkedWarehouses = res
            this.radio = null
        },
        //全选/反选
        isSelectAll(isAll) {
            if (isAll) {
                this.checkedWarehouses = this.isUse ? this.allUseId : this.allWmsId
            } else {
                this.checkedWarehouses = this.isUse ? this.allUseId.filter(item => !this.checkedWarehouses.includes(item)) : this.allWmsId.filter(item => !this.checkedWarehouses.includes(item))
            }
        },
        getName() {
            if (!this.checkedWarehouses || this.checkedWarehouses.length == 0) {
                return {
                    name: this.placeholderText,
                    list: []
                }
            }
            const name = this.allWareHouse.find(item => item.wms_co_id == this.checkedWarehouses[0])?.name || this.placeholderText
            const wmsNum = this.checkedWarehouses.length - 1
            const wms = this.isUse ? this.useWms.filter(item => this.checkedWarehouses?.includes(item.wms_co_id)) : this.allWareHouse.filter(item => this.checkedWarehouses?.includes(item.wms_co_id))
            return {
                name: `${name?.substr(0, 5)}${wmsNum != 0 ? '+' + wmsNum : ''} `,
                list: wms.map(item => item.name)
            }
        },
        changeViewStatus(val) {
            if (val) {
                this.$set(this, 'viewWmsList', this.allWareHouse.filter(item => item.isUse == 1).map(item => {
                    return { value: item.wms_co_id, label: item.name, };
                }));

            } else {
                this.$set(this, 'viewWmsList', this.allWareHouse.map(item => {
                    return { value: item.wms_co_id, label: item.name, };
                }))
            }
            if (this.radio) {
                this.changeWms(this.radio)
            }
        },
        openCheckLog() {
            this.checkedWmsInfo = {
                checkWms: JSON.parse(JSON.stringify(this.checkedWarehouses)),
                radio: this.radio,
                isUse: this.isUse
            }
            this.$nextTick(() => {
                this.changeViewStatus(this.isUse)
            })
            this.chooseWmsVisible = true;
        },
        //设置常用仓库
        async SetCommonWarehouseAsync() {
            if (!this.checkedWarehouses || this.checkedWarehouses.length == 0) return this.$message.error('请先选择仓库')
            const ColumnConfig = {
                checkedWarehouses: this.checkedWarehouses,
                tableId: this.tableId,
                isUse: this.isUse,
                // radio: this.radio
            }
            const { success } = await SetVxeTableColumnCacheAsync({ tableId: this.tableId, ColumnConfig: JSON.stringify(ColumnConfig) })
            if (!success) return this.$message.error('设置常用仓库失败')
            this.$message.success('设置常用仓库成功')
        },
        //获取常用仓库
        async GetCommonWarehouseAsync() {
            const { data, success } = await GetVxeTableColumnCacheAsync({ tableId: this.tableId })
            if (!success) return this.$message.error('获取常用仓库失败')
            if (data) {
                const res = JSON.parse(data)
                this.$set(this, 'checkedWarehouses', res.checkedWarehouses)
                this.$set(this, 'radio', null)
                this.$set(this, 'isUse', res.isUse)
                this.$nextTick(() => {
                    this.changeViewStatus(res.isUse)
                })
            }
        },
        //清空
        clearWms() {
            this.$set(this, 'checkedWarehouses', [])
            this.$set(this, 'radio', null)
        },
        //确定
        confirm() {
            const checkWms = this.allWareHouse.filter(item => this.checkedWarehouses.includes(item.wms_co_id))
            this.checkedWarehouses = this.isUse ? this.allUseId.filter(item => this.checkedWarehouses.includes(item)) : this.allWmsId.filter(item => this.checkedWarehouses.includes(item))
            this.$emit('onChange', {
                checkedWarehouses: this.checkedWarehouses,
                checkWms,
                allWareHouse: this.allWareHouse
            })
            this.chooseWmsVisible = false
        },
        //取消
        cancle() {
            this.$set(this, 'checkedWarehouses', this.checkedWmsInfo.checkWms)
            this.$set(this, 'radio', this.checkedWmsInfo.radio)
            this.$set(this, 'isUse', this.checkedWmsInfo.isUse)
            this.chooseWmsVisible = false
        },
    }
}
</script>

<style scoped lang="scss">
.dialog-header {
    display: flex;
    margin-bottom: 10px;
    align-items: center;
}

.btnCss {
    height: 25px;
    line-height: 25px;
    width: 60px;
    color: #fff;
    font-family: inherit;
    text-align: center;
    font-size: 12px;
    margin-right: 5px;
    cursor: pointer;
    border-radius: 10px;

    :last-child {
        margin-right: 0;
    }
}

.primary {
    background-color: #409eff;
}

.danger {
    background-color: #f78787;
}

.success {
    background-color: #85ce61;
}
</style>