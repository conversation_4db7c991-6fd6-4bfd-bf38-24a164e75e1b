import FileSaver from 'file-saver'
const XLSX = require('xlsx')

/**
 * el-table数据导出execl文件
 * 
 * @param {*} id:el-table元素id
 * @param {*} fileName :导出文件名称
 * @returns 
 */
function exportExecl(id,fileName){
    let excelName = fileName||'导出表格.xlsx';
      var xlsxParam = { raw: true };
      let tables=document.getElementById(id);
       if(!tables) return
        
        tables = document.getElementById(id).cloneNode(true);
      
      if (tables.querySelector('.vxe-table--render-wrapper') !== null) {
        let neitable = tables.querySelector('.vxe-table--render-wrapper');
        neitable.removeChild(tables.querySelector('.vxe-table--fixed-wrapper'))
      }
      
      let table_book = XLSX.utils.table_to_book(tables,xlsxParam);
      var table_write = XLSX.write(table_book, {
          bookType: "xlsx",
          bookSST: true,
          type: "array"
      });
      try {
          FileSaver.saveAs(
              new Blob([table_write], { type: "application/octet-stream" }),
              excelName
          );
      } catch (e) {
             console.log(e, table_write);
      }
      return table_write;

}
export default exportExecl
