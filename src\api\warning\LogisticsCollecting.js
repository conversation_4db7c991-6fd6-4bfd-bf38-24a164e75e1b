import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Warning}/LogisticsCollecting/`

//查询数据 PageGetData
export const PageGetData = (params, config = {}) => request.post(apiPrefix + 'PageGetData', params, config)

//获取所有的仓库负责人设置 GetLogisticsWmsHeads
export const GetLogisticsWmsHeads = (params, config = {}) => request.post(apiPrefix + 'GetLogisticsWmsHeads', params, config)

//保存仓库负责人设置 UpdateLogisticsWmsHeads
export const UpdateLogisticsWmsHeads = (params, config = {}) => request.post(apiPrefix + 'UpdateLogisticsWmsHeads', params, config)

//发货仓维度获取趋势汇总 GetStatDataBySendWms
export const GetStatDataBySendWms = (params, config = {}) => request.post(apiPrefix + 'GetStatDataBySendWms', params, config)

//物流公司维度获取趋势汇总 GetStatDataBySendExpCmy
export const GetStatDataBySendExpCmy = (params, config = {}) => request.post(apiPrefix + 'GetStatDataBySendExpCmy', params, config)

//获取列 GetColumns
export const GetColumns = (params, config = {}) => request.post(apiPrefix + 'GetColumns', params, config)

//查询快递公司数据 GetExpressCompanies
export const GetExpressCompanies = (params, config = {}) => request.post(apiPrefix + 'GetExpressCompanies', params, config)

//数据导出 ExportData
export const ExportData = (params, config = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportData', params, config)

//获取平台 GetPlatforms
export const GetPlatforms = (params, config = {}) => request.post(apiPrefix + 'GetPlatforms', params, config)
