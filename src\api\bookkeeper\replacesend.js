import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/replacesend/`

export const pageReplaceSend= (params, config = {}) => {
    return request.post(apiPrefix + 'PageReplaceSendAsync', params, config )
}
export const exportReplaceSend = (params, config = {responseType: 'blob'}) => {
    return request.get(apiPrefix + `ExportReplaceSendAsync`,  {params, ...config})
}