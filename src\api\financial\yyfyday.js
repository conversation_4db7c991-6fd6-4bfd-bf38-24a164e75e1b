import request from '@/utils/request'

const <PERSON><PERSON><PERSON>uPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Shizhitouday/`

const TuijianPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Tuijianday/`

const KuaiShouPrefix = `${process.env.VUE_APP_BASE_API_Financial}/kuaishouday/`

const TaobaokePrefix = `${process.env.VUE_APP_BASE_API_Financial}/Taobaokeday/`

const TaotetuiguangPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Taotetuiguangday/`

const ZhitongchePrefix = `${process.env.VUE_APP_BASE_API_Financial}/Zhitongcheday/`

const TuoguanYGPrefix = `${process.env.VUE_APP_BASE_API_Financial}/TuoguanYG/`

const TaoteshangpingPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Taoteshangpingday/`

const DahuixiongPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Dahuixiongday/`

const TaolijingPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Taolijingday/`

const YingxiaoPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Yingxiaoday/`

const CuishouPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Cuishouday/`

const AccountResultPrefix = `${process.env.VUE_APP_BASE_API_Financial}/AccountResultday/`

const CostDiffProductPrefix = `${process.env.VUE_APP_BASE_API_Financial}/CostDiffProductday/`

const CostDifforderPrefix = `${process.env.VUE_APP_BASE_API_Financial}/CostDifforderday/`

const PingduoduoPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Pingduoduoday/`

const UnusualPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Unusualday/`

const CostDiffDividePrefix = `${process.env.VUE_APP_BASE_API_Financial}/CostDiffDivideday/`

//const FinancialReportPrefix = `${process.env.VUE_APP_BASE_API_Financial}/FinancialReportday/`
const FinancialReportPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/FinancialReportday/`

export const importShizhitouAsync = (params, config = {}) => { return request.post(ShizhitouPrefix + 'ImportShizhitouDayAsync', params, config) }
export const getShizhitouList = (params, config = {}) => { return request.get(ShizhitouPrefix + 'GetShizhitouDayList', { params: params, ...config }) }
export const deleteShizhitouBatch = (params, config = {}) => { return request.get(ShizhitouPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const importTuijianAsync = (params, config = {}) => { return request.post(TuijianPrefix + 'ImportTuijianDayAsync', params, config) }
export const getTuijianList = (params, config = {}) => { return request.get(TuijianPrefix + 'GetTuijianDayList', { params: params, ...config }) }
export const deleteTuijianBatch = (params, config = {}) => { return request.get(TuijianPrefix + 'DeleteBatchAsync', { params: params, ...config }) }
export const queryTuiJianAnalysis = (params, config = {}) => { return request.get(TuijianPrefix + 'QueryTuiJianAnalysisAsync', { params: params, ...config }) }
export const getTuijianAllSiteList = (params, config = {}) => { return request.get(TuijianPrefix + 'GetTuijianAllSiteList', { params: params, ...config }) }
export const allSiteDeleteBatchAsync = (params, config = {}) => { return request.get(TuijianPrefix + 'AllSiteDeleteBatchAsync', { params: params, ...config }) }
export const importTuijianallsiteAsync = (params, config = {}) => { return request.post(TuijianPrefix + 'ImportTuijianallsiteAsync', params, config) }


export const importKuaiShouAdFlowAsync = (params, config = {}) => { return request.post(KuaiShouPrefix + 'ImportKuaiShouAdFlowDayAsync', params, config) }
export const getKuaiShouAdFlowList = (params, config = {}) => { return request.get(KuaiShouPrefix + 'GetAdFlowDayList', { params: params, ...config }) }
export const queryAdFlowAnalysisAsync = (params, config = {}) => { return request.get(KuaiShouPrefix + 'QueryAdFlowAnalysis', { params: params, ...config }) }
export const queryAdCostAnalysisAsync = (params, config = {}) => { return request.get(KuaiShouPrefix + 'QueryAdCostAnalysis', { params: params, ...config }) }
export const deleteKuaiShouAdFlowBatch = (params, config = {}) => { return request.get(KuaiShouPrefix + 'DeleteAdFlowBatchAsync', { params: params, ...config }) }
export const importKuaiShouAdCostAsync = (params, config = {}) => { return request.post(KuaiShouPrefix + 'ImportKuaiShouAdCostDayAsync', params, config) }
export const getKuaiShouAdCostList = (params, config = {}) => { return request.get(KuaiShouPrefix + 'GetAdCostDayList', { params: params, ...config }) }
export const getAdFlowReportPageList = (params, config = {}) => { return request.get(KuaiShouPrefix + 'GetAdFlowReportPageList', { params: params, ...config }) }
export const deleteKuaiShouAdCostBatch = (params, config = {}) => { return request.get(KuaiShouPrefix + 'DeleteAdCostBatchAsync', { params: params, ...config }) }
export const exportKuaiShouAdFlowReport = (params, config = { responseType: 'blob' }) => {
    return request.post(KuaiShouPrefix + 'ExportAdFlowReportList', params, config)
}
// 文件导入-快手运营费用-直播推广
export const importZhiBoTuiGuangDay_KuaiShouAsync = (params, config = {}) => { return request.post(KuaiShouPrefix + 'ImportZhiBoTuiGuangDay_KuaiShouAsync', params, config) }
// 获取快手运营费用-直播推广列表
export const getZhiBoTuiGuangDay_KuaiShouPageList = (params, config = {}) => { return request.get(KuaiShouPrefix + 'GetZhiBoTuiGuangDay_KuaiShouPageList', { params: params, ...config }) }
// 删除批次快手运营费用-直播推广
export const deleteZhiBoTuiGuangDay_KuaiShouBatchAsync = (params, config = {}) => { return request.get(KuaiShouPrefix + 'DeleteZhiBoTuiGuangDay_KuaiShouBatchAsync', { params: params, ...config }) }

export const importTaobaokeAsync = (params, config = {}) => { return request.post(TaobaokePrefix + 'ImportTaobaokeDayAsync', params, config) }
export const getTaobaokeList = (params, config = {}) => { return request.get(TaobaokePrefix + 'GetTaobaokeDayList', { params: params, ...config }) }
export const deleteTaobaokeBatch = (params, config = {}) => { return request.get(TaobaokePrefix + 'DeleteBatchAsync', { params: params, ...config }) }
export const queryTaoBaoKeAnalysis = (params, config = {}) => { return request.get(TaobaokePrefix + 'QueryTaoBaoKeAnalysisAsync', { params: params, ...config }) }

export const importTaotetuiguangAsync = (params, config = {}) => { return request.post(TaotetuiguangPrefix + 'ImportTaotetuiguangDayAsync', params, config) }
export const getTaotetuiguangList = (params, config = {}) => { return request.get(TaotetuiguangPrefix + 'GetTaotetuiguangDayList', { params: params, ...config }) }
export const deleteTaotetuiguangBatch = (params, config = {}) => { return request.get(TaotetuiguangPrefix + 'DeleteBatchAsync', { params: params, ...config }) }
export const importZhitongcheAsync = (params, config = {}) => { return request.post(ZhitongchePrefix + 'ImportZhitongcheDayAsync', params, config) }
export const importTGCZhitongcheDay = (params, config = {}) => { return request.post(ZhitongchePrefix + 'ImportTGCZhitongcheDayAsync', params, config) }
export const getTGCZhitongcheDayList = (params, config = {}) => { return request.get(ZhitongchePrefix + 'GetTGCZhitongcheDayList', { params: params, ...config }) }
export const DeleteTGCZhitongcheDayBatch = (params, config = {}) => { return request.get(ZhitongchePrefix + 'DeleteTGCZhitongcheDayBatchAsync', { params: params, ...config }) }
export const getZhitongcheList = (params, config = {}) => { return request.get(ZhitongchePrefix + 'GetZhitongcheDayList', { params: params, ...config }) }
export const deleteZhitongcheBatch = (params, config = {}) => { return request.get(ZhitongchePrefix + 'DeleteBatchAsync', { params: params, ...config }) }
export const queryZhiTongCheAnalysis = (params, config = {}) => { return request.get(ZhitongchePrefix + 'QueryZhiTongCheAnalysisAsync', { params: params, ...config }) }
export const importTaoteshangpingAsync = (params, config = {}) => { return request.post(TaoteshangpingPrefix + 'ImportTaoteshangpingDayAsync', params, config) }
export const getTaoteshangpingList = (params, config = {}) => { return request.get(TaoteshangpingPrefix + 'GetTaoteshangpingDayList', { params: params, ...config }) }
export const deleteTaoteshangpingBatch = (params, config = {}) => { return request.get(TaoteshangpingPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const importDahuixiongAsync = (params, config = {}) => { return request.post(DahuixiongPrefix + 'ImportDahuixiongDayAsync', params, config) }
export const getDahuixiongList = (params, config = {}) => { return request.get(DahuixiongPrefix + 'GetDahuixiongDayList', { params: params, ...config }) }
export const deleteDahuixiongBatch = (params, config = {}) => { return request.get(DahuixiongPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const importTaolijingAsync = (params, config = {}) => { return request.post(TaolijingPrefix + 'ImportTaolijingDayAsync', params, config) }
export const getTaolijingList = (params, config = {}) => { return request.get(TaolijingPrefix + 'GetTaolijingDayList', { params: params, ...config }) }
export const deleteTaolijingBatch = (params, config = {}) => { return request.get(TaolijingPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const importYingxiaoAsync = (params, config = {}) => { return request.post(YingxiaoPrefix + 'ImportYingxiaoDayAsync', params, config) }
export const getYingxiaoList = (params, config = {}) => { return request.get(YingxiaoPrefix + 'GetYingxiaoDayList', { params: params, ...config }) }
export const deleteYingxiaoBatch = (params, config = {}) => { return request.get(YingxiaoPrefix + 'DeleteBatchAsync', { params: params, ...config }) }
export const queryYingXiaoAnalysis = (params, config = {}) => { return request.get(YingxiaoPrefix + 'QueryYingXiaoAnalysisAsync', { params: params, ...config }) }
export const getYingxiaoDayAnalysis = (params, config = {}) => { return request.get(YingxiaoPrefix + 'GetYingxiaoDayAnalysisList', { params: params, ...config }) }

export const importCuishouAsync = (params, config = {}) => { return request.post(CuishouPrefix + 'ImportCuishouDayAsync', params, config) }
export const getCuishouList = (params, config = {}) => { return request.get(CuishouPrefix + 'GetCuishouDayList', { params: params, ...config }) }
export const deleteCuishouBatch = (params, config = {}) => { return request.get(CuishouPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const accountResultAsync = (params, config = {}) => { return request.get(AccountResultPrefix + 'AccountDataDayAsync', { params: params, ...config }) }
export const getAccountResultList = (params, config = {}) => { return request.get(AccountResultPrefix + 'GetAccountResultDayList', { params: params, ...config }) }
export const deleteAccountResultBatch = (params, config = {}) => { return request.get(AccountResultPrefix + 'DeleteBatchAsync', { params: params, ...config }) }
export const queryAccountResultAnalysis = (params, config = {}) => { return request.get(AccountResultPrefix + 'QueryAccountResultAnalysisAsync', { params: params, ...config }) }



export const importPingduoduoAsync = (params, config = {}) => { return request.post(PingduoduoPrefix + 'ImportPingduoduoDayAsync', params, config) }
export const getPingduoduoList = (params, config = {}) => { return request.get(PingduoduoPrefix + 'GetPingduoduoDayList', { params: params, ...config }) }
export const deletePingduoduoBatch = (params, config = {}) => { return request.get(PingduoduoPrefix + 'DeleteBatchAsync', { params: params, ...config }) }
export const getPingduoduoDaynalysis = (params, config = {}) => { return request.get(PingduoduoPrefix + 'GetPingduoduoDaynalysisAsync', { params: params, ...config }) }


export const chooseUnusualGroupCompute = (params, config = {}) => { return request.get(UnusualPrefix + 'ChooseGroupCompute', { params: params, ...config }) }


export const getUnusualList = (params, config = {}) => { return request.get(UnusualPrefix + 'GetUnusualDayList', { params: params, ...config }) }
export const deleteUnusualBatch = (params, config = {}) => { return request.get(UnusualPrefix + 'DeleteBatchAsync', { params: params, ...config }) }


const TotalByfeeTypePrefix = `${process.env.VUE_APP_BASE_API_Financial}/TotalByfeeTypeday/`
export const getTotalByfeeTypeList = (params, config = {}) => { return request.get(TotalByfeeTypePrefix + 'GetTotalByfeeTypeDayList', { params: params, ...config }) }


export const importCostDiffProductAsync = (params, config = {}) => { return request.post(CostDiffProductPrefix + 'ImportCostDiffProductDayAsync', params, config) }
export const getCostDiffProductList = (params, config = {}) => { return request.get(CostDiffProductPrefix + 'GetCostDiffProductDayList', { params: params, ...config }) }
export const deleteCostDiffProductBatch = (params, config = {}) => { return request.get(CostDiffProductPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const importCostDifforderAsync = (params, config = {}) => { return request.post(CostDifforderPrefix + 'ImportCostDifforderDayAsync', params, config) }
export const getCostDifforderList = (params, config = {}) => { return request.get(CostDifforderPrefix + 'GetCostDifforderDayList', { params: params, ...config }) }
export const deleteCostDifforderBatch = (params, config = {}) => { return request.get(CostDifforderPrefix + 'DeleteBatchAsync', { params: params, ...config }) }


export const accountCostDiffAsync = (params, config = {}) => { return request.get(CostDiffDividePrefix + 'AccountCostDiffDayAsync', { params: params, ...config }) }
export const getCostDiffDivideList = (params, config = {}) => { return request.get(CostDiffDividePrefix + 'GetCostDiffDivideDayList', { params: params, ...config }) }
export const deleteCostDiffDivideBatch = (params, config = {}) => { return request.get(CostDiffDividePrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const getFinancialReportList = (params, config = {}) => { return request.get(FinancialReportPrefix + 'GetFinancialReportDayList', { params: params, ...config }) }

const YinglimofangDayPrefix = `${process.env.VUE_APP_BASE_API_Financial}/YinglimofangDay/`

const WanxiangtaiDayPrefix = `${process.env.VUE_APP_BASE_API_Financial}/WanxiangtaiDay/`

export const importYinglimofangDayAsync = (params, config = {}) => { return request.post(YinglimofangDayPrefix + 'ImportYinglimofangDayAsync', params, config) }
export const getYinglimofangDayList = (params, config = {}) => { return request.get(YinglimofangDayPrefix + 'GetYinglimofangDayList', { params: params, ...config }) }
export const deleteYinglimofangDayBatch = (params, config = {}) => { return request.get(YinglimofangDayPrefix + 'DeleteBatchAsync', { params: params, ...config }) }
export const queryYlmfAnalysis = (params, config = {}) => { return request.get(YinglimofangDayPrefix + 'QueryYlmfAnalysisAsync', { params: params, ...config }) }

export const importWanxiangtaiDayAsync = (params, config = {}) => { return request.post(WanxiangtaiDayPrefix + 'ImportWanxiangtaiDayAsync', params, config) }
export const getWanxiangtaiDayList = (params, config = {}) => { return request.get(WanxiangtaiDayPrefix + 'GetWanxiangtaiDayList', { params: params, ...config }) }
export const deleteWanxiangtaiDayBatch = (params, config = {}) => { return request.get(WanxiangtaiDayPrefix + 'DeleteBatchAsync', { params: params, ...config }) }
export const queryWanxiangtaiAnalysis = (params, config = {}) => { return request.get(WanxiangtaiDayPrefix + 'QueryWanxiangtaiAnalysisAsync', { params: params, ...config }) }
export const exportWanxiangtaiDayList = (params, config = { responseType: 'blob' }) => { return request.get(WanxiangtaiDayPrefix + 'ExportWanxiangtaiDayList', { params: params, ...config }) }


export const imporSiteWidePromotionInfoAsync = (params, config = {}) => { return request.post(WanxiangtaiDayPrefix + 'ImportSiteWidePromotionInfoAsync', params, config) }
export const geSiteWidePromotionInfoList = (params, config = {}) => { return request.get(WanxiangtaiDayPrefix + 'GetSiteWidePromotionInfoList', { params: params, ...config }) }
export const deleteSiteWidePromotionInfoBatch = (params, config = {}) => { return request.get(WanxiangtaiDayPrefix + 'DeleteSiteWidePromotionInfoBatchAsync', { params: params, ...config }) }
export const querySiteWidePromotionInfoAnalysis = (params, config = {}) => { return request.get(WanxiangtaiDayPrefix + 'QuerySiteWidePromotionInfoAnalysisAsync', { params: params, ...config }) }


const ShoudanlijingDayPrefix = `${process.env.VUE_APP_BASE_API_Financial}/ShoudanlijingDay/`
export const importShoudanlijingDayAsync = (params, config = {}) => { return request.post(ShoudanlijingDayPrefix + 'ImportShoudanlijingDayAsync', params, config) }
export const getShoudanlijingDayList = (params, config = {}) => { return request.get(ShoudanlijingDayPrefix + 'GetShoudanlijingDayList', { params: params, ...config }) }
export const deleteShoudanlijingDayBatch = (params, config = {}) => { return request.get(ShoudanlijingDayPrefix + 'DeleteBatchAsync', { params: params, ...config }) }
export const queryShouDanLiJingAnalysis = (params, config = {}) => { return request.get(ShoudanlijingDayPrefix + 'QueryShouDanLiJingAnalysisAsync', { params: params, ...config }) }

const PddTaobaokePrefix = `${process.env.VUE_APP_BASE_API_Financial}/PddTaobaoke/`
export const importPddTaobaokeAsync = (params, config = {}) => { return request.post(PddTaobaokePrefix + 'ImportPddTaobaokeAsync', params, config) }
export const getPddTaobaokeList = (params, config = {}) => { return request.get(PddTaobaokePrefix + 'GetPddTaobaokeList', { params: params, ...config }) }
export const deletePddTaobaokeBatch = (params, config = {}) => { return request.get(PddTaobaokePrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const queryPddTaobaokeAnalysis = (params, config = {}) => { return request.get(PddTaobaokePrefix + 'QueryPddTaobaokeAnalysisAsync', { params: params, ...config }) }

//淘工厂引力魔方
export const importTGCYinLiMoFangDay = (params, config = {}) => { return request.post(ZhitongchePrefix + 'ImportTGCYinLiMoFangDayAsync', params, config) }
export const getTGCYinLiMoFangDayList = (params, config = {}) => { return request.get(ZhitongchePrefix + 'GetTGCYinLiMoFangDayList', { params: params, ...config }) }
export const DeleteTGCYinLiMoFangDayBatch = (params, config = {}) => { return request.get(ZhitongchePrefix + 'DeleteTGCYinLiMoFangDayBatchAsync', { params: params, ...config }) }

//淘工厂阿里妈妈代投
export const importTGCAliMaMaDaiTouDay = (params, config = {}) => { return request.post(ZhitongchePrefix + 'ImportTGCAliMaMaDaiTouDayAsync', params, config) }
export const getAliMaMaDaiTouDayList = (params, config = {}) => { return request.get(ZhitongchePrefix + 'GetTGCAliMaMaDaiTouDayList', { params: params, ...config }) }
export const DeleteTGCAliMaMaDaiTouDayBatch = (params, config = {}) => { return request.get(ZhitongchePrefix + 'DeleteTGCAliMaMaDaiTouDayBatchAsync', { params: params, ...config }) }
//苏宁直通车
export const importSuNingZhitongcheDay = (params, config = {}) => { return request.post(ZhitongchePrefix + 'ImportSuNingZhitongcheDayAsync', params, config) }
export const getSuNingZhitongcheDayList = (params, config = {}) => { return request.get(ZhitongchePrefix + 'GetSuNingZhitongcheDayList', { params: params, ...config }) }
export const DeleteSuNingZhitongcheDayBatch = (params, config = {}) => { return request.get(ZhitongchePrefix + 'DeleteSuNingZhitongcheDayBatchAsync', { params: params, ...config }) }

//苏宁万向台
export const importSuNingWanXiangTaiDay = (params, config = {}) => { return request.post(ZhitongchePrefix + 'ImportSuNingWanXiangTaiDayAsync', params, config) }
export const getSuNingWanXiangTaiDayList = (params, config = {}) => { return request.get(ZhitongchePrefix + 'GetSuNingWanXiangTaiDayList', { params: params, ...config }) }
export const DeleteSuNingWanXiangTaiDayBatch = (params, config = {}) => { return request.get(ZhitongchePrefix + 'DeleteSuNingWanXiangTaiDayBatchAsync', { params: params, ...config }) }



export const importPingduoduoBiaoZhun = (params, config = {}) => { return request.post(PingduoduoPrefix + 'ImportPingduoduoBiaoZhunDayAsync', params, config) }
export const getPingduoduoBiaoZhunList = (params, config = {}) => { return request.get(PingduoduoPrefix + 'GetPingduoduoBiaoZhunDayList', { params: params, ...config }) }
export const deletePingduoduoBiaoZhunBatch = (params, config = {}) => { return request.get(PingduoduoPrefix + 'DeleteBatchBiaoZhunAsync', { params: params, ...config }) }

export const importPingduoduoZhiNeng = (params, config = {}) => { return request.post(PingduoduoPrefix + 'ImportPingduoduoZhiNengDayAsync', params, config) }
export const getPingduoduoZhiNengList = (params, config = {}) => { return request.get(PingduoduoPrefix + 'GetPingduoduoZhiNengDayList', { params: params, ...config }) }
export const deletePingduoduoZhiNengBatch = (params, config = {}) => { return request.get(PingduoduoPrefix + 'DeleteZhiNengBatchAsync', { params: params, ...config }) }


export const importEvaluationCourtesy = (params, config = {}) => { return request.post(PingduoduoPrefix + 'ImportEvaluationCourtesyAsync', params, config) }
export const getPingduoduoEvaluationCourtesyList = (params, config = {}) => { return request.get(PingduoduoPrefix + 'GetPingduoduoEvaluationCourtesyList', { params: params, ...config }) }
export const deleteEvaluationCourtesyBatch = (params, config = {}) => { return request.get(PingduoduoPrefix + 'DeleteEvaluationCourtesyBatchAsync', { params: params, ...config }) }


export const importTuoguanYGAsync = (params, config = {}) => { return request.post(TuoguanYGPrefix + 'ImportTuoguanYGAsync', params, config) }
export const getTuoguanYGList = (params, config = {}) => { return request.get(TuoguanYGPrefix + 'GetTuoguanYGList', { params: params, ...config }) }
export const deleteBatchAsync = (params, config = {}) => { return request.get(TuoguanYGPrefix + 'DeleteBatchAsync', { params: params, ...config }) }
export const exportTuoguanYGList = (params, config = { responseType: 'blob' }) => { return request.get(TuoguanYGPrefix + `ExportTuoguanYGList`, { params, ...config }) }

export const importMonthTuoguanYGAsync = (params, config = {}) => { return request.post(TuoguanYGPrefix + 'ImportMonthTuoguanYGAsync', params, config) }
export const getMonthTuoguanYGList = (params, config = {}) => { return request.post(TuoguanYGPrefix + 'GetMonthTuoguanYGList', params, config) }
export const deleteMonthBatchAsync = (params, config = {}) => { return request.get(TuoguanYGPrefix + 'DeleteMonthBatchAsync', { params: params, ...config }) }

export const exportMonthTuoguanYGAsync = (params, config = { responseType: 'blob' }) => { return request.post(TuoguanYGPrefix + 'ExportMonthTuoguanYGAsync', params, config) }

export const getMonthTuoguanYGGroupList = (params, config = {}) => { return request.post(TuoguanYGPrefix + 'GetMonthTuoguanYGGroupList', params, config) }
export const exportMonthTuoguanYGGroupAsync = (params, config = { responseType: 'blob' }) => { return request.post(TuoguanYGPrefix + 'ExportMonthTuoguanYGGroupAsync', params, config) }

const DayReportV2Prefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/DayReportV2/`
export const importTaobaoStarRiver = (params, config = {}) => { return request.post(DayReportV2Prefix + 'ImportTaobaoStarRiver', params, config) }
export const getTaobaoStarRiverPageList = (params, config = {}) => { return request.get(DayReportV2Prefix + 'GetTaobaoStarRiverPageList', { params: params, ...config }) }
export const deleteTaobaoStarRiver = (params, config = {}) => { return request.post(DayReportV2Prefix + 'DeleteTaobaoStarRiver', params, config) }

//先行赔付
const ImportPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/Import/`
export const importAdvanceCompensation = (params, config = {}) => { return request.post(ImportPrefix + 'ImportAdvanceCompensation', params, config) }
export const getAdvanceCompensationPageList = (params, config = {}) => { return request.get(DayReportV2Prefix + 'GetAdvanceCompensationPageList', { params: params, ...config }) }
