import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/crossborder/`

//跨境收支明细 -环世，获取流水类型
export const GetWwlcargoTransactionFlowTypeList = (params, config = {}) => { return request.get(apiPrefix + 'GetWwlcargoTransactionFlowTypeList', { params, ...config }) }

//希音销售明细查询—自营
export const getCodesalesthemeanalysis_SheInSelf = (params, config = {}) => { return request.get(apiPrefix + 'GetCodesalesthemeanalysis_SheInSelf', { params, ...config }) }

//希音销售明细导入—自营
export const importCodesalesthemeanalysis_SheInSelf = (params, config = {}) => { return request.post(apiPrefix + 'ImportCodesalesthemeanalysis_SheInSelfAsync', params, config) }
//希音账单费用导入
export const importSelfZhangDan_SheInAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportSelfZhangDan_SheInAsync', params, config) }

//希音自营账单费用查询
export const getSheInBillingSelfAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetSheInBillingSelfAsync', { params, ...config }) }

//希音自营库存损耗明细分页查询
export const pageSheInInvertoryLossResult = (params, config = {}) => { return request.post(apiPrefix + 'PageSheInInvertoryLossResult', params, config) }


//希音自营库存损耗明细——导入
export const importInventoryLoss_SheInSelf = (params, config = {}) => { return request.post(apiPrefix + 'ImportInventoryLoss_SheInSelf', params, config) }

//跨境结算总结-希音全托交易收入
export const pageSettleDetail_SheInResult = (params, config = {}) => { return request.post(apiPrefix + 'PageSettleDetail_SheInResult', params, config) }

//跨境结算总结-希音全托交易导入
export const importSettleDetail_SheInAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportSettleDetail_SheInAsync', params, config) }

//跨境结算总结-希音全托账单费用结算
export const getSheIn_FullTrustBillSettlementPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetSheIn_FullTrustBillSettlementPageList', params, config) }

//跨境结算总结-希音全托账单费用结算-补扣款分类列表
export const getReplenishmentDeductionCategoryList = (params, config = {}) => { return request.post(apiPrefix + 'GetReplenishmentDeductionCategoryList', params, config) }

//跨境结算总结-希音全托账单费用结算——导入
export const importSheIn_FullTrustBillSettlementAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportSheIn_FullTrustBillSettlementAsync', params, config) }

//跨境结算总结-希音全托账单费用结算——导出
export const exportSheIn_FullTrustBillSettlement = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportSheIn_FullTrustBillSettlement', params, config) }

//跨境结算总结-希音全托交易收入——导出
export const exportSettleDetail_SheInResult = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportSettleDetail_SheInResult', params, config) }

//分页查询
export const pageProductDayReport = (params, config = {}) => { return request.post(apiPrefix + 'PageProductDayReport', params, config) }
//趋势图
export const queryDayReportAnalysisAsync = (params, config = {}) => { return request.get(apiPrefix + 'QueryDayReportAnalysisAsync', { params, ...config }) }
//导出
export const exportProductDayReport = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportProductDayReport', params, config) }
//新增确认日报数据
export const insertDailyReportConfirmList = (params, config = {}) => { return request.post(apiPrefix + 'InsertDailyReportConfirmList', params, config) }

//希音自营 计算日报
export const calDayRepoty_SheInSelfAsync = (params, config = {}) => { return request.get(apiPrefix + 'CalDayRepoty_SheInSelfAsync', { params, ...config }) }
//日报-日报确认-查询
export const getKJ_DailyReportDataItemConfirmList = (params, config = {}) => { return request.post(apiPrefix + 'GetKJ_DailyReportDataItemConfirmList', params, config) }

//日报-日报确认-确认
export const addKJ_DailyReportDataItemConfirm = (params, config = {}) => { return request.post(apiPrefix + 'AddKJ_DailyReportDataItemConfirm', params, config) }

//日报-日报确认-一键确认
export const bath_AddKJ_DailyReportDataItemConfirm = (params, config = {}) => { return request.post(apiPrefix + 'Bath_AddKJ_DailyReportDataItemConfirm', params, config) }
//日报-日报确认-保存
// export const insertDailyReportItemConfirmList = (params, config = {}) => { return request.post(apiPrefix + 'InsertDailyReportItemConfirmList', params, config) }

//希音,拼多多全托,拼多多半托-计算日报
export const calDayRepoty_KJAsync = (params, config = {}) => { return request.get(apiPrefix + 'CalDayRepoty_KJAsync', { params, ...config }) }

//pdd半托  订单日报查询
export const productOrderDayReport = (params, config = {}) => { return request.post(apiPrefix + 'PageProductOrderDayReport', params, config) }
//pdd半托  订单日报导出
export const exportProductOrderDayReport = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportProductOrderDayReport', params, config) }

//pdd半托  获取订单日报汇总趋势图数据
export const queryProductOrderDayReportSumChart = (params, config = {}) => { return request.get(apiPrefix + 'QueryProductOrderDayReportSumChart', { params, ...config }) }

//TEMU半托账单费用-违规类型下拉框
export const getIllegalDeductionViolationTypeList = (params, config) => { return request.post(apiPrefix + 'GetIllegalDeduction_ViolationTypeList', params, config) }


//TEMU-半托取消订单 列表查询
export const getCodeSalesCancellationOrder_Temu_BanTuoPageList = (params, config) => { return request.post(apiPrefix + 'GetCodeSalesCancellationOrder_Temu_BanTuoPageList', params, config) }

//TEMU-半托取消订单 导入
export const importCodeSalesCancellationOrder_Temu_BanTuoAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportCodeSalesCancellationOrder_Temu_BanTuoAsync', params, config) }

// 销售主题分析列表
export const pageCodeSalesThemeAnalysisKJAsync = (params, config = {}) => {
    return request.get(apiPrefix + 'PageCodeSalesThemeAnalysisKJAsync', { params: params, ...config })
}

//海外仓-产品成本 列表查询
export const pageOverseasDepotResult = (params, config) => { return request.post(apiPrefix + 'PageOverseasDepotResult', params, config) }

//海外仓-产品成本 导入
export const import_OverseasDepot = (params, config = {}) => { return request.post(apiPrefix + 'Import_OverseasDepot', params, config) }

//海外仓-产品成本 仓库下拉
export const getDepotNameList = (params, config = {}) => { return request.get(apiPrefix + 'GetDepotNameList', { params, ...config }) }

//跨境账单费用-希音全托损耗明细
export const importAfterSalesSubjectAnalysis = (params, config = {}) => { return request.post(apiPrefix + 'ImportAfterSalesSubjectAnalysisAsync', params, config) }

//TEMU半托账单费用导入
export const importBillingCharge = (params, config = {}) => { return request.post(apiPrefix + 'ImportBillingChargeAsync', params, config) }

// 新版销售主题导入
export const importCodeSalesThemeAnalysis = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportCodeSalesThemeAnalysisAsync', params, config)
}

//毛三趋势图
export const getproductinfo = (params, config = {}) => { return request.get(apiPrefix + 'PrDirection', { params, ...config }) }

//SHEIN全托销售明细
export const exportCodeSalesSheIn = (params, config = {}) => {
    return request.post(apiPrefix + 'ExportCodeSalesSheIn', params, config)
}

//SHEIN全托账单费用
export const exporBillingChargeSheIn = (params, config = {}) => {
    return request.post(apiPrefix + 'ExporBillingChargeSheIn', params, config)
}

//SHEIN全托库存损耗明细
export const exportInventorySheIn = (params, config = {}) => {
    return request.post(apiPrefix + 'ExportInventorySheIn', params, config)
}


//SHEIN自营销售明细
export const exportCodeSalesSheInSelf = (params, config = {}) => {
    return request.post(apiPrefix + 'ExportCodeSalesSheInSelf', params, config)
}


//SHEIN自营账单费用
export const exporBillingChargeSheInSelf = (params, config = {}) => {
    return request.post(apiPrefix + 'ExporBillingChargeSheInSelf', params, config)
}

//SHEIN自营库存损耗明细
export const exportInventorySheInSelf = (params, config = {}) => {
    return request.post(apiPrefix + 'ExportInventorySheInSelf', params, config)
}

//Temu-半托销售明细导出   
export const codeSalesThemeAnalysis_BanTuo_Temu_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'CodeSalesThemeAnalysis_BanTuo_Temu_Export', params, config)
}

//Temu-Temu-半托取消订单  
export const codeSalesCancellationOrder_Temu_BanTuo_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'CodeSalesCancellationOrder_Temu_BanTuo_Export', params, config)
}


//海外仓-上架装卸费  列表查询
export const pageShelvesHandlingResult = (params, config) => { return request.post(apiPrefix + 'PageShelvesHandlingResult', params, config) }

//海外仓-上架装卸费  导出
export const shelvesHandling_Export = (params, config = {}) => { return request.post(apiPrefix + 'ShelvesHandling_Export', params, config) }

//海外仓-上架装卸费 下拉查询
export const getShelvesHandlingDepotNameList = (params, config = {}) => { return request.get(apiPrefix + 'GetShelvesHandlingDepotNameList', { params, ...config }) }


//跨境仓库运费重量区间 列表查询
export const getWarehouseWeightRangePageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseWeightRangePageList', params, config)
}

//跨境仓库运费重量区间 导入
export const import_WarehouseWeightRange = (params, config = {}) => { return request.post(apiPrefix + 'Import_WarehouseWeightRange', params, config) }

//跨境仓库运费重量区间 导出
export const exportWarehouseWeightRange = (params, config = {}) => {
    return request.post(apiPrefix + 'ExportWarehouseWeightRange', params, config)
}


//跨境操作费重量区间 列表查询
export const pageOperatingFeesResult = (params, config = {}) => {
    return request.post(apiPrefix + 'PageOperatingFeesResult', params, config)
}

//跨境操作费重量区间 导入
export const importmport_OperatingFees = (params, config = {}) => { return request.post(apiPrefix + 'Import_OperatingFees', params, config) }

//跨境操作费重量区间 导出
export const exportOperatingFeesResult = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportOperatingFeesResult', params, config) }


//海外仓预估费用 列表查询
export const getWarehouseEstimatePageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseEstimatePageList', params, config)
}

//海外仓预估费用 新增/编辑
export const editWarehouseEstimateAsync = (params, config) => { return request.post(apiPrefix + 'EditWarehouseEstimateAsync', params, config) }

//运营工资维护左侧生效时间获取(跨境)
export const getDirectorGroupEffectiveTimeList = (params, config = {}) => { return request.get(apiPrefix + 'GetDirectorGroupEffectiveTimeList', { params, ...config }) }

// 运营工资维护根据生效日期获取组长
export const pageDirectorGroupWagesByEffectiveTime = (params, config = {}) => { return request.post(apiPrefix + 'PageDirectorGroupWagesByEffectiveTime', params, config) }

// 运营工资维护 新增运营工资
export const updateDirectorGroupWages = (params, config = {}) => { return request.post(apiPrefix + 'UpdateDirectorGroupWages', params, config) }

// 运营工资维护 获取组长
export const getDirectorGroupNameList = (params, config = {}) => { return request.get(apiPrefix + 'GetDirectorGroupNameList', { params, ...config }) }

//运营工资维护 删除
export const deleteDirectorGroupWages = (params, config = {}) => { return request.post(apiPrefix + 'DeleteDirectorGroupWages', params, config) }

//运营工资维护 提醒弹出框 列表
export const pageDirectorGroupTipsList = (params, config = {}) => { return request.post(apiPrefix + 'PageDirectorGroupTipsList', params, config) }

//TEMU-半托活动价 导入
export const imporDirectorGroupWagesAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImporDirectorGroupWagesAsync', params, config) }


//跨境店铺违规明细 TEMU全托-违规明细 查询列表
export const pageTemuShopViolationAsync = (params, config) => { return request.post(apiPrefix + 'PageTemuShopViolationAsync', params, config) }

//跨境店铺违规明细 TEMU全托-违规明细 导入
export const imporTemuShopViolationAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImporTemuShopViolationAsync', params, config) }

//跨境店铺违规明细 TEMU全托-违规明细 导出
export const exportTemuShopViolation = (params, config = {}) => {
    return request.post(apiPrefix + 'ExportTemuShopViolation', params, config)
}

//跨境店铺违规明细 TEMU半托-违规明细 查询列表
export const pageTemuBanShopViolationAsync = (params, config) => { return request.post(apiPrefix + 'PageTemuBanShopViolationAsync', params, config) }

//跨境店铺违规明细 TEMU半托-违规明细 拆分订单号
export const temuBanShopViolationHistoryDataProcessAsync = (params, config) => { return request.post(apiPrefix + 'TemuBanShopViolationHistoryDataProcessAsync', params, config) }

//跨境店铺违规明细 TEMU半托-违规明细 导入
export const imporTemuBanShopViolationAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImporTemuBanShopViolationAsync', params, config) }

//跨境店铺违规明细 TEMU半托-违规明细 导出
export const exportTemuBanShopViolation = (params, config = {}) => {
    return request.post(apiPrefix + 'ExportTemuBanShopViolation', params, config)
}

//跨境店铺违规明细 TEMU半托-违规明细 违规类型下拉
export const getViolationTypeList = (params, config = {}) => { return request.get(apiPrefix + 'GetViolationTypeList', { params, ...config }) }


//跨境店铺违规明细 SHEIN-违规明细 查询列表
export const pageSheInShopViolationAsync = (params, config) => { return request.post(apiPrefix + 'PageSheInShopViolationAsync', params, config) }

//跨境店铺违规明细 SHEIN-违规明细 导入
export const imporSheInShopViolationAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImporSheInShopViolationAsync', params, config) }

//跨境店铺违规明细 SHEIN-违规明细 导出
export const exportSheInShopViolation = (params, config = {}) => {
    return request.post(apiPrefix + 'ExportSheInShopViolation', params, config)
}
//跨境店铺违规明细 SHEIN-违规明细 申诉状态列表
export const getAppealStatusList = (params, config = {}) => { return request.get(apiPrefix + 'GetAppealStatusList', { params, ...config }) }
// 跨境销售明细-TEMU全托售后明细-导出
export const codeSalesThemeAnalysisK_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'CodeSalesThemeAnalysisK_Export  ', params, config)
}
// 跨境销售明细-TEMU半托售后明细-导出
export const afterSaleDetails_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'AfterSaleDetails_Export', params, config)
}


// 跨境账单费用-TEMU半托账单费用-导出
export const billingCharge_Temu_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'BillingCharge_Temu_Export', params, config)
}

// 跨境账单费用-TEMU全托弃货-导出
export const billingCharge_derelict_PddTemu_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'BillingCharge_derelict_PddTemu_Export', params, config)
}

// 跨境账单费用-TEMU全托环保费-导出
export const billingCharge_HbKf_PddTemu_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'BillingCharge_HbKf_PddTemu_Export', params, config)
}

// 跨境账单费用-TEMU全托售后赔付-导出
export const billingCharge_Shpf_PddTemu_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'BillingCharge_Shpf_PddTemu_Export', params, config)
}

// 跨境账单费用-TEMUTEMU全托仓储综合服务费-导出
export const billingCharge_ThFw_PddTemu_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'BillingCharge_ThFw_PddTemu_Export', params, config)
}

// 跨境账单费用-TEMU全托商品补寄赔付金-导出
export const billingCharge_Temu_ExportillingCharge_Spbj_PddTemu_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'BillingCharge_Spbj_PddTemu_Export', params, config)
}


// 跨境结算数据-TEMU-全托交易收入-导出
export const transactionIncome_PddTemu_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'TransactionIncome_PddTemu_Export', params, config)
}
// 跨境结算数据-TEMU-全托售后预留/释放金额-导出
export const reserveAmountAfterSale_PddTemu_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'ReserveAmountAfterSale_PddTemu_Export', params, config)
}
// 跨境结算数据-TEMU-半托交易收入-导出
export const settleAccount_Temu_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'SettleAccount_Temu_Export', params, config)
}
// 跨境结算数据-TEMU-半托售后退款-导出
export const halfSalesDetails_RefundAfterSale_Temu_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'HalfSalesDetails_RefundAfterSale_Temu_Export', params, config)
}
// 跨境结算数据-TEMU-半托运费收入-导出
export const halfSalesDetails_FreightRevenue_Temu_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'HalfSalesDetails_FreightRevenue_Temu_Export', params, config)
}
// 跨境结算数据-TEMU-半托运费退款-导出
export const halfSalesDetails_FreightRefund_Temu_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'HalfSalesDetails_FreightRefund_Temu_Export', params, config)
}
// 跨境结算数据-TEMU-半托运输费用-导出
export const transportationCost_Temu_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'TransportationCost_Temu_Export', params, config)
}

//TEMU-半托活动价 导入
export const import_ActivePrice = (params, config = {}) => { return request.post(apiPrefix + 'Import_ActivePrice', params, config) }

//TEMU-半托活动价 查询列表
export const pageActivePriceAsync = (params, config) => { return request.post(apiPrefix + 'PageActivePriceAsync', params, config) }

//TEMU-半托活动价 导出
export const exportActivePrice = (params, config = {}) => {
    return request.post(apiPrefix + 'ExportActivePrice', params, config)
}

//跨境日报数据维护-厂家代发 导入
export const ImportDropshippingCostsAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportDropshippingCostsAsync', params, config) }

//跨境日报数据维护-厂家代发 查询列
export const PageDropshippingCostsAsync = (params, config) => { return request.post(apiPrefix + 'PageDropshippingCostsAsync', params, config) }

//跨境日报数据维护-厂家代发 查明细列
export const PageDropCostsDetailAsync = (params, config) => { return request.post(apiPrefix + 'PageDropCostsDetailAsync', params, config) }

//跨境日报数据维护-厂家代发 导出
export const exportDropshippingCosts = (params, config = {}) => {
    return request.post(apiPrefix + 'ExportDropshippingCosts', params, config)
}
//跨境日报数据维护-是否预估尾程
export const updateIssuingCost_BanTuo_TemuIsEstimateLastFeeAsync = (params, config = {}) => { return request.post(apiPrefix + 'UpdateIssuingCost_BanTuo_TemuIsEstimateLastFeeAsync', params, config) }

//跨境日报数据维护-代发成本 查看日志
export const getKJ_OperateLogPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetKJ_OperateLogPageList', params, config) }

//跨境日报数据维护-代发成本 导出
export const exportDropCostsDetail = (params, config = {}) => {
    return request.post(apiPrefix + 'ExportDropCostsDetail  ', params, config)
}

//跨境日报数据维护-代发成本 查看消耗明细导出
export const issuingCost_BanTuo_Temu_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'IssuingCost_BanTuo_Temu_Export  ', params, config)
}

//跨境日报数据维护-代发成本 查看结余列表
export const pageDropshippingCostsCashSurplus = (params, config) => { return request.post(apiPrefix + 'PageDropshippingCostsCashSurplus', params, config) }

//跨境日报数据维护-查看结余列表 导出
export const exportDropCostsCashSurplus = (params, config = {}) => {
    return request.post(apiPrefix + 'ExportDropCostsCashSurplus  ', params, config)
}


//跨境看板-跨境消耗看板-产品费用列表查询
export const GetDayRptProductFreightConsumeAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetDayRptProductFreightConsumeAsync', params, config) }

//跨境看板-跨境消耗看板-采购费用列表查询
export const GetDayRptPurchaseFreightConsumeAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetDayRptPurchaseFreightConsumeAsync', params, config) }

//跨境看板-跨境消耗看板-采购费用 导出
export const DayRptPurchaseFreightConsume_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'DayRptPurchaseFreightConsume_Export', params, config)
}

//跨境看板-跨境消耗看板-产品费用 导出
export const DayRptProductFreightConsume_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'DayRptProductFreightConsume_Export', params, config)
}

//跨境看板-跨境消耗看板-头程-运费列表
export const getFirstLegBalance_Temu_BantuoNewPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetFirstLegBalance_Temu_BantuoNewPageList', params, config) }

//跨境看板-跨境消耗看板-头程-运费列表 导出
export const firstLegBalance_Temu_BantuoNew_Export = (params, config = {}) => {
    return request.post(apiPrefix + 'FirstLegBalance_Temu_BantuoNew_Export', params, config)
}


//跨境结余费用-产品运费 查询列表
export const GetDayRptProductFreight_KJPageList = (params, config) => { return request.post(apiPrefix + 'GetDayRptProductFreight_KJPageList', params, config) }

//跨境结余费用-产品运费 产品运费导出
export const DayRptProductFreight_KJ_Export = (params, config) => { return request.post(apiPrefix + 'DayRptProductFreight_KJ_Export', params, config) }

//跨境结余费用-采购运费 查询列表
export const GetDayRptPurchaseFreight_KJPageList = (params, config) => { return request.post(apiPrefix + 'GetDayRptPurchaseFreight_KJPageList', params, config) }

//跨境结余费用-采购运费 采购运费导出
export const DayRptPurchaseFreight_KJ_Export = (params, config) => { return request.post(apiPrefix + 'DayRptPurchaseFreight_KJ_Export', params, config) }

//跨境结余费用-头程费用 查询列表
export const GetFirstLegUse_BanTuo_TemuPageList = (params, config) => { return request.post(apiPrefix + 'GetFirstLegUse_BanTuo_TemuPageList', params, config) }

//跨境结余费用-头程费用 查询列表(2024.11.29新版)
export const GetFirstLegBalance_Temu_BantuoPageList = (params, config) => { return request.post(apiPrefix + 'GetFirstLegBalance_Temu_BantuoPageList', params, config) }

//跨境结余费用-采购运费 头程费用导出(2024.11.29新版)
export const FirstLegBalance_Temu_Bantuo_Export = (params, config) => { return request.post(apiPrefix + 'FirstLegBalance_Temu_Bantuo_Export', params, config) }

//跨境结余费用-头程费用 头程费用导出
export const FirstLegUse_BanTuo_Temu_Export = (params, config) => { return request.post(apiPrefix + 'FirstLegUse_BanTuo_Temu_Export', params, config) }


//跨境结余费用-头程费用 消耗明细导出
export const firstLegDetail_Temu_Bantuo_Export = (params, config) => { return request.post(apiPrefix + 'FirstLegDetail_Temu_Bantuo_Export', params, config) }


//海外仓收支明细-各仓库查询列
export const PageWarehouseListAsync = (params, config) => { return request.post(apiPrefix + 'PageWarehouseListAsync', params, config) }

//海外仓收支明细-各仓库数据导入 
export const ImportWarehouseAsync = (params, config) => { return request.post(apiPrefix + 'ImportWarehouseAsync', params, config) }

//海外仓收支明细-获取业务类型
export const GetBusinessTypeList = (params, config = {}) => { return request.get(apiPrefix + 'GetBusinessTypeList', { params, ...config }) }

//海外仓收支明细-批量【核对】财务类型
export const UpdateWarehouseByCheckStatusAsync = (params, config) => { return request.post(apiPrefix + 'UpdateWarehouseByCheckStatusAsync', params, config) }

//海外仓收支明细-批量【修改】财务类型 
export const UpdateWarehouseAsync = (params, config) => { return request.post(apiPrefix + 'UpdateWarehouseAsync', params, config) }

//海外仓收支明细-查看日志
export const PageWarehouseFeesLogAsync = (params, config) => { return request.post(apiPrefix + 'PageWarehouseFeesLogAsync', params, config) }

//海外仓收支明细-导出 佳速达
export const ExportFeesShipOut = (params, config) => { return request.post(apiPrefix + 'ExportFeesShipOut', params, config) }

//海外仓收支明细-导出 九方
export const ExportFeesTofba = (params, config) => { return request.post(apiPrefix + 'ExportFeesTofba', params, config) }

//海外仓收支明细-导出 赤道
export const ExportSogoodseller = (params, config) => { return request.post(apiPrefix + 'ExportSogoodseller', params, config) }

//海外仓收支明细-导出 左海
export const ExportZuoHai = (params, config) => { return request.post(apiPrefix + 'ExportZuoHai', params, config) }

//海外仓收支明细-导出 环世
export const ExportFeesWwlcargo = (params, config) => { return request.post(apiPrefix + 'ExportFeesWwlcargo', params, config) }

//海外仓收支明细-导出 收支明细
export const ExportFeesOverWarehouseAsync = (params, config) => { return request.post(apiPrefix + 'ExportFeesOverWarehouseAsync', params, config) }

//海外仓收支明细-查收支明细列
export const PageFeesOverWarehouseAsync = (params, config) => { return request.post(apiPrefix + 'PageFeesOverWarehouseAsync', params, config) }

//海外仓收支明细-查收支明细列-修改期初人民币
export const UpdateRateAsync = (params, config) => { return request.post(apiPrefix + 'UpdateRateAsync', params, config) }

//海外仓收支明细-计算平台收支明细
export const calcKJWarehouseByDate = (params, config) => { return request.post(apiPrefix + 'CalcKJWarehouseByDate', params, config) }


//TEMU全托违规扣款 列表查询
export const getBillingCharge_Wgkk_PddTemuPageList = (params, config) => { return request.post(apiPrefix + 'GetBillingCharge_Wgkk_PddTemuPageList', params, config) }

//TEMU全托违规扣款 导出
export const billingCharge_Wgkk_PddTemu_Export = (params, config) => { return request.post(apiPrefix + 'BillingCharge_Wgkk_PddTemu_Export', params, config) }
//TEMU全托违规扣款 导入
export const import_BillingCharge_Wgkk_PddTemu = (params, config = {}) => { return request.post(apiPrefix + 'Import_BillingCharge_Wgkk_PddTemu', params, config) }

//半托计算追溯记录-列表
export const getTemu_BantuoDayReportChangeLogList = (params, config) => { return request.post(apiPrefix + 'GetTemu_BantuoDayReportChangeLogList', params, config) }

//跨境日报数据维护-仓储费导出
export const storageCharges_BanTuo_Temu_Export = (params, config) => { return request.post(apiPrefix + 'StorageCharges_BanTuo_Temu_Export', params, config) }

//跨境日报数据维护-头程导出
export const firstLeg_BanTuo_Temu_Export = (params, config) => { return request.post(apiPrefix + 'FirstLeg_BanTuo_Temu_Export', params, config) }

//跨境日报获取模块列表
export const getPurchaseLogModuleAsync = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPurchaseLogModuleAsync', { params: params, ...config })
}

export const getPurchaseLogOneAsync = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPurchaseLogOneAsync', { params: params, ...config })
}

//添加列表信息
export const addPurchaseLog = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'AddPurchaseLog', params, config)
}

//删除列表信息
export const deletePurchaseLog = (params, config = {}) => {
    return request.put(apiPrefix + 'DeletePurchaseLog', params, config)
}
//排序
export const updateSortPurchaseLogOneAsync = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'UpdateSortPurchaseLogOneAsync', params, config)
}

//跨境首页-列表数据查询
export const getCrossBorderHomePage_Kj = (params, config = {}) => { return request.post(apiPrefix + 'GetCrossBorderHomePage_Kj', params, config) }

//跨境首页-导入
export const importTemuListingAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportTemuListingAsync', params, config) }

//跨境首页-查询Temu 快递/包装费单条数据
export const getTemuExpressPackage = (params, config = {}) => { return request.get(apiPrefix + 'GetTemuExpressPackage', { params, ...config }) }

//跨境首页-新增或编辑Temu 快递/包装费
export const addOrEditTemuExpressPackage = (params, config = {}) => { return request.post(apiPrefix + 'AddOrEditTemuExpressPackage', params, config) }

//跨境首页-跨境首页趋势图
export const getCrossBorderHomePage_KjAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'GetCrossBorderHomePage_KjAnalysis', { params, ...config }) }

//跨境首页 系列编码盈亏订单量 弹出框
export const getPlatformOrderCount = (params, config = {}) => { return request.post(apiPrefix + 'GetPlatformOrderCount', params, config) }

// 跨境首页运营人员业绩——导出
export const exportCrossBorderHomePage_Kj = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportCrossBorderHomePage_Kj', params, config)
}

// 跨境首页店铺违规情况——导出
export const exportShopViolationHomePage_Kj = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportShopViolationHomePage_Kj', params, config)
}

// 跨境首页系列编码盈亏——导出
export const exportStylCodeHomePage_Kj = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportStylCodeHomePage_Kj', params, config)
}


//添加公告点击信息
export const addPurchaseClickLog = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'AddPurchaseClickLogAsync', params, config)
}
// //点击事件
export const getPurchaseLogAsync = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPurchaseLogAsync', { params: params, ...config })
}
//上传视频
export const addPurchaseUplod = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'AddPurchaseUplodAsync', params, config)
}

//跨境首页-店铺违规情况 列表查询
export const getShopViolationList = (params, config = {}) => { return request.post(apiPrefix + 'GetShopViolationList', params, config) }

//跨境首页-列表编码盈亏 列表查询
export const GetStylCodeList = (params, config = {}) => { return request.post(apiPrefix + 'GetStylCodeList', params, config) }

//跨境首页-列表编码盈亏 趋势图
export const GetHomePageStylCode_KjAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'GetHomePageStylCode_KjAnalysis', { params: params, ...config }) }

//跨境日报数据维护-头程费用列表
export const GetTemu_Bantuo_FirstLegCostPageList = (params, config) => { return request.post(apiPrefix + 'GetTemu_Bantuo_FirstLegCostPageList', params, config) }

//跨境日报数据维护-头程费用导出
export const GetTemu_Bantuo_FirstLegCost_Export = (params, config) => { return request.post(apiPrefix + 'GetTemu_Bantuo_FirstLegCost_Export', params, config) }

//跨境日报数据维护-头程费用查看消耗明细
export const GetFirstLegDetail_Temu_BantuoPageList = (params, config) => { return request.post(apiPrefix + 'GetFirstLegDetail_Temu_BantuoPageList', params, config) }

 //跨境日报 批量计算  
export const batchCalDayRepoty_KJAsync = (params, config = {}) => { return request.get(apiPrefix + 'BatchCalDayRepoty_KJAsync', { params: params, ...config }) }

//跨境店铺平台库存 TEMU全托-平台库存  列表
export const pageInventoryTemuList = (params, config = {}) => { return request.post(apiPrefix + 'PageInventoryTemuList', params, config) }
//跨境店铺平台库存 TEMU全托-平台库存  导入
export const importInventoryTemu = (params, config = {}) => { return request.post(apiPrefix + 'ImportInventoryTemu', params, config) }
//跨境店铺平台库存 TEMU全托-平台库存  导出
export const exportInventoryTemu = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportInventoryTemu', params, config)
} 

//跨境店铺平台库存 SHEIN全托-平台库存  列表
export const pageInventorySheInList = (params, config = {}) => { return request.post(apiPrefix + 'PageInventorySheInList', params, config) }
//跨境店铺平台库存 SHEIN全托-平台库存  导入
export const importInventorySheIn = (params, config = {}) => { return request.post(apiPrefix + 'ImportInventorySheIn', params, config) }
//跨境店铺平台库存 SHEIN全托-平台库存  导出
export const exportInventoryPlatBySheIn = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportInventoryPlatBySheIn', params, config)
}


//跨境店铺平台库存 SHEIN自营-平台库存  列表
export const pageInventorySheInSelfList = (params, config = {}) => { return request.post(apiPrefix + 'PageInventorySheInSelfList', params, config) }
//跨境店铺平台库存 SHEIN自营-平台库存  导入
export const importInventorySheInSelf = (params, config = {}) => { return request.post(apiPrefix + 'ImportInventorySheInSelf', params, config) }
//跨境店铺平台库存 SHEIN自营-平台库存  导出
export const exportInventoryPlatBySheInSelf = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportInventoryPlatBySheInSelf', params, config)
}
//跨境日报数据维护-其他平台的头程费用列表
export const getFirstLegOtherPageList = (params, config) => { return request.post(apiPrefix + 'GetFirstLegOtherPageList', params, config) }

//跨境日报数据维护-其他平台的头程费用列表 导出
export const firstLegOther_Export = (params, config) => { return request.post(apiPrefix + 'FirstLegOther_Export', params, config) }
 
//跨境日报数据维护-其他平台的头程费用列表 导入
export const importFirstLegOther  = (params, config) => { return request.post(apiPrefix + 'ImportFirstLegOther ', params, config) }

//跨境日报数据维护-其他平台的头程费用列表 费用计算
export const calOtherTouChen_KJAsync = (params, config = {}) => { return request.get(apiPrefix + 'CalOtherTouChen_KJAsync', { params, ...config }) }