 <template>
    <el-table-column :fixed="column.fixed" v-if="!(column.display==false||(typeof column.display=='function'&& column.display(column)==false))
                     &&(!column.permission||(column.permission&&checkPermission(column.permission)))" :prop="column.prop" :label="column.label"
                     :width="column.width" :min-width="column.minwidth" :align="column.align" :class-name="(!column.classname?'':column.classname)"
                     :sortable="column.sortable" :render-header="column.require?renderHeader:null" :show-overflow-tooltip="showoverflowtooltip"
                     :column-key="(column.columnKey? column.columnKey: column.prop)">
        <template #header v-if="column.tipmesg || column.isRequired">
            <span class="grid-header">
                <span v-if="column.isRequired" style="color:red">*</span>
                <span>{{column.label}}</span>
                <span v-if="column.tipmesg">
                    <el-tooltip class="item" effect="dark" :content="column.tipmesg" placement="top-end">
                        <span><i class="el-icon-question"></i></span>
                    </el-tooltip>
                </span>
            </span>
        </template>
        <template slot-scope="scope">
            <span v-if="column.type==='html'" v-html="((column.formatter && tonumfuc(column.formatter(scope.row), column.label))||tonumfuc(scope.row[column.prop], column.label))" @click="handleClick($event,scope.row[column.prop])"></span>
            <!-- <div v-if="column.type==='html'" v-html="((column.formatter && column.formatter(scope.row))||scope.row[column.prop])"></div> -->
            <div class="wendang" v-if="column.type==='editor'" v-html="scope.row[column.prop]" @click="showImg($event)">{{tonumfuc(scope.row[column.prop], column.label)}}</div>
            <span v-if="column.type==='format'">{{tonumfuc(column.formatter(scope.row), column.label) }} </span>
            <span v-if="column.type==='button'">
                <span v-for="(btn,btnIndex) in column.btnList" :key="btn.label">
                    <el-link v-if="(!btn.permission||(btn.permission&&checkPermission(btn.permission)))&&!(btn.hasOwnProperty('ishide')&&((typeof btn.ishide=='function'&& btn.ishide(that,scope.row)==true)||btn.ishide==true))" :style="{'margin-left': '3px'}" :disabled="(btn.hasOwnProperty('display')&&((typeof btn.display=='function'&& btn.display(scope.row)==true)||btn.display==true))" :type="btn.type || type" :size="btn.size || size " :icon="btn.icon" @click="btn.handle(that,scope.row)">{{btn.label}} <span v-if="btn.htmlformatter" v-html="btn.htmlformatter(scope.row)"></span><span v-if="btnIndex<(column.btnList.length-1)"></span></el-link>
                </span>
            </span>
            <template v-if="column.type==='star'">
                <span v-for="(item,i) in scope.row.star" :key="i">
                <i v-if="item[column.starProp]>0&&item[column.starProp]<10" style="color:red">个</i>
                <i v-else-if="item[column.starProp]>=10&&item[column.starProp]<100" style="color:red">十</i>
                <i v-else-if="item[column.starProp]>=100&&item[column.starProp]<1000" style="color:red">百</i>
                <i v-else-if="item[column.starProp]>=1000&&item[column.starProp]<10000" style="color:red">千</i>
                <i v-else-if="item[column.starProp]>=10000&&item[column.starProp]<100000" style="color:red">万</i>
                <i v-else-if="item[column.starProp]>=100000" style="color:red">萬</i>
                <i v-else-if="item[column.starProp]>-10&&item[column.starProp]<0" style="color:rgb(8, 108, 11)">个</i>
                <i v-else-if="item[column.starProp]>-100&&item[column.starProp]<=-10" style="color:rgb(8, 108, 11)">十</i>
                <i v-else-if="item[column.starProp]>-1000&&item[column.starProp]<=-100" style="color:rgb(8, 108, 11)">百</i>
                <i v-else-if="item[column.starProp]>-10000&&item[column.starProp]<=-1000" style="color:rgb(8, 108, 11)">千</i>
                <i v-else-if="item[column.starProp]>-100000&&item[column.starProp]<=-10000" style="color:rgb(8, 108, 11)">万</i>
                <i v-else-if="item[column.starProp]<=-100000" style="color:rgb(8, 108, 11)">萬</i>
                <i v-else style="color:gray">X</i>
            </span>
            </template>
            <span v-if="column.type==='star4'">
                <span v-for="(item,i) in scope.row.star" :key="i">
                    <!-- <i v-if="item.profit3==1" class="el-icon-profit3-on" style="color:red"></i>
                      <i v-else-if="item.profit3==0" class="el-icon-profit3-on" style="color:rgb(8, 108, 11)"></i>
                      <i v-else class="el-icon-profit3-off" style="color:green"></i> -->
                    <i v-if="item.profit33>0&&item.profit33<10" style="color:red">个</i>
                    <i v-else-if="item.profit33>=10&&item.profit33<100" style="color:red">十</i>
                    <i v-else-if="item.profit33>=100&&item.profit33<1000" style="color:red">百</i>
                    <i v-else-if="item.profit33>=1000&&item.profit33<10000" style="color:red">千</i>
                    <i v-else-if="item.profit33>=10000&&item.profit33<100000" style="color:red">万</i>
                    <i v-else-if="item.profit33>=100000" style="color:red">萬</i>

                    <i v-else-if="item.profit33>-10&&item.profit33<0" style="color:rgb(8, 108, 11)">个</i>
                    <i v-else-if="item.profit33>-100&&item.profit33<=-10" style="color:rgb(8, 108, 11)">十</i>
                    <i v-else-if="item.profit33>-1000&&item.profit33<=-100" style="color:rgb(8, 108, 11)">百</i>
                    <i v-else-if="item.profit33>-10000&&item.profit33<=-1000" style="color:rgb(8, 108, 11)">千</i>
                    <i v-else-if="item.profit33>-100000&&item.profit33<=-10000" style="color:rgb(8, 108, 11)">万</i>
                    <i v-else-if="item.profit33<=-100000" style="color:rgb(8, 108, 11)">萬</i>
                    <i v-else style="color:gray">X</i>
                </span>
            </span>
            <span v-if="column.type==='newstar'">
                    <i v-if="scope.row.star==null"></i>
                    <i v-else-if="scope.row.star==0"></i>
                    <i v-else-if="scope.row.star==1" class="el-icon-star-on" style="color:red"></i>
                    <i v-else-if="scope.row.star==2" class="el-icon-star-on" style="color:orange"></i>
                    <i v-else-if="scope.row.star==3" class="el-icon-star-on" style="color:yellow"></i>
                    <i v-else-if="scope.row.star==4" class="el-icon-star-on" style="color:green"></i>
                    <i v-else-if="scope.row.star==5" class="el-icon-star-on" style="color:blue"></i>
                    <i v-else-if="scope.row.star==6" class="el-icon-star-on" style="color:indigo"></i>
                    <i v-else-if="scope.row.star==7" class="el-icon-star-on" style="color:purple"></i>
                    <i v-else style="color:gray" class="el-icon-star-on"></i>
            </span>
            <span v-if="column.type==='flag'">
                <i v-if="scope.row.flag==null"></i>
                <i v-else-if="scope.row.flag==0"></i>
                <i v-else-if="scope.row.flag==1" class="el-icon-s-flag
                " style="color:red"></i>
                <i v-else-if="scope.row.flag==2" class="el-icon-s-flag
                " style="color:orange"></i>
                <i v-else-if="scope.row.flag==3" class="el-icon-s-flag
                " style="color:yellow"></i>
                <i v-else-if="scope.row.flag==4" class="el-icon-s-flag
                " style="color:green"></i>
                <i v-else-if="scope.row.flag==5" class="el-icon-s-flag
                " style="color:blue"></i>
                <i v-else-if="scope.row.flag==6" class="el-icon-s-flag
                " style="color:indigo"></i>
                <i v-else-if="scope.row.flag==7" class="el-icon-s-flag
                " style="color:purple"></i>
                <i v-else style="color:gray" class="el-icon-s-flag
                "></i>
           </span>
           <span v-if="column.type==='copy'" style="color: green;">
                <div class="relativebox">
                    <el-tooltip effect="dark" :content="scope.row[column.prop]" placement="top-start">
                    <div class="textover" style="width: 80%;">{{ tonumfuc(scope.row[column.prop], column.label) }}</div>
                    </el-tooltip>

                    <div class="copyhover" @click="copytext(scope.row[column.prop])">
                        <i class="el-icon-document-copy"></i>
                    </div>
                </div>
           </span>
            <el-input v-if="column.type==='input'" v-model="scope.row[column.prop]" :size="size || btn.size" :disabled="column.isDisabled && column.isDisabled(scope.row)" @focus="column.focus && column.focus(scope.row)"></el-input>
            <el-input v-if="column.type==='inputtext'" type="text" :maxlength="column.maxlength?column.maxlength:400" v-model="scope.row[column.prop]" :size="size || btn.size" :disabled="column.isDisabled && column.isDisabled(scope.row)" @focus="column.focus && column.focus(scope.row)"></el-input>
            <el-input v-if="column.type==='inputtexttrim'" type="text" :maxlength="column.maxlength?column.maxlength:400" v-model.trim="scope.row[column.prop]" :size="size || btn.size" :disabled="column.isDisabled && column.isDisabled(scope.row)" @focus="column.focus && column.focus(scope.row)"></el-input>
            <el-input-number v-if="column.type==='inputnumber'" :style="'width:'+(column.width? (column.width-20)+ 'px;':'100%')+';'" :min="(column.min||column.min===0)?column.min:-999999999" :max="column.max?column.max:999999999" :precision="column.precision?column.precision:0" :controls="!!column.controls" v-model.number="scope.row[column.prop]" :size="size || btn.size" :disabled="column.isDisabled && column.isDisabled(scope.row)" @focus="column.focus && column.focus(scope.row)" @change="column.change && column.change(scope.row)"></el-input-number>
            <el-select v-if="column.type==='select'" v-model="scope.row[column.prop]" :size="size || btn.size" :props="column.props" :disabled="column.isDisabled && column.isDisabled(scope.row)" @change='column.change && column.change(that,scope.row)'>
                <el-option v-for="op in column.options" :label="op.label" :value="op.value" :key="op.value"></el-option>
            </el-select>
            <el-radio-group v-if="column.type==='radio'" v-model="scope.row[column.prop]" :disabled="column.isDisabled && column.isDisabled(scope.row)" :size="size || btn.size" @change='column.change && column.change(that,scope.row)'>
                <el-radio v-for="ra in column.radios" :label="ra.value" :key="ra.value">{{ra.label}}</el-radio>
            </el-radio-group>
            <el-checkbox-group v-if="column.type==='checkbox'" v-model="scope.row[column.prop]" :disabled="column.isDisabled && column.isDisabled(scope.row)" :size="size || btn.size" @change='column.change && column.change(that,scope.row)'>
                <el-checkbox v-for="ra in column.checkboxs" :label="ra.value" :key='ra.value'>{{ra.label}}</el-checkbox>
            </el-checkbox-group>
            <el-rate v-if="column.type==='rate'" v-model="scope.row[column.prop]" :disabled="column.isDisabled && column.isDisabled(scope.row)" :size="size || btn.size" @change='column.change && column.change(scope.row)'></el-rate>
            <el-switch v-if="column.type==='switch'" v-model="scope.row[column.prop]" :size="size || btn.size" :active-value='column.values&&column.values[0]' :inactive-value='column.values&&column.values[1]' :disabled="column.isDisabled && column.isDisabled(scope.row,that)" @change='column.change && column.change(scope.row,that)'></el-switch>

            <el-image v-if="column.type==='image'" :src="(!!formatImg(scope.row[column.prop])?formatImg(scope.row[column.prop])[0]:imagedefault)" fit="contain" @click="preview(scope.row[column.prop])" style="max-width: 150px;max-height: 150px;margin:-5px 0px -5px -5px;" />
            <!-- style="width: 100px; height: 100px" -->
            <template v-if="column.type==='images' && scope.row[column.prop]&& scope.row[column.prop].length>2">
                <template v-if="scope.row[column.prop][0]=='[' && JSON.parse(scope.row[column.prop]).length>1">
                    <el-badge :value="JSON.parse(scope.row[column.prop]).length" style="margin-top:10px;margin-right:40px;">
                        <el-image v-if="column.type==='images' && scope.row[column.prop]&& scope.row[column.prop].length>2" :src="(formatImg(scope.row[column.prop])? formatImg(scope.row[column.prop])[0].url : formatImg(scope.row[column.prop]) )" class="imgstyle" :preview-src-list="(scope.row[column.prop][0]=='['
                ?(()=>{
                    let tempArray=JSON.parse(scope.row[column.prop]);
                    let tempRltArr=[];
                    tempArray.forEach(x=>tempRltArr.push(x.url));
                    return tempRltArr
                })()
                :(()=>{
                    return [scope.row[column.prop]]
                })()  )">
                        </el-image>
                    </el-badge>
                </template>
                <template v-else>
                    <el-image v-if="column.type==='images' && scope.row[column.prop]&& scope.row[column.prop].length>2" :src="formatImg(scope.row[column.prop])[0].url ? formatImg(scope.row[column.prop])[0].url : formatImg(scope.row[column.prop])[0]" class="imgstyle" :preview-src-list="(scope.row[column.prop][0]=='['
                ?(()=>{
                    let tempArray=JSON.parse(scope.row[column.prop]);
                    let tempRltArr=[];
                    tempArray.forEach(x=>tempRltArr.push(x.url));
                    return tempRltArr
                })()
                :(()=>{
                    return [scope.row[column.prop]]
                })()  )">
                    </el-image>
                </template>

            </template>

            <template v-if="column.type==='imagess'">
                <template>
                    <el-badge :value="scope.row[column.prop].length" style="margin-top:10px;margin-right:40px;">
                        <el-image  class="imgstyle" :src="formatImg(scope.row[column.prop])?formatImg(scope.row[column.prop])[0]:imagedefault" fit="fill" :preview-src-list="scope.row[column.prop]">
                        </el-image>
                    </el-badge>
                </template>
            </template>

            <template v-if="column.type=='ddAvatar' && column.ddInfo">
                <!-- {{ column.ddInfo.type }} -->
                <!-- 1运营组  2运营  3采购  4Userid  5dduserId -->
                    <el-image class="userAvatar"  :src="`http://192.168.16.240:8001/api/Admin/User/GetUserAvatar?type=${column.ddInfo.type}&id=${scope.row[column.ddInfo.prop]}`"
                    :preview-src-list="[`http://192.168.16.240:8001/api/Admin/User/GetUserAvatar?type=${column.ddInfo.type}&id=${scope.row[column.ddInfo.prop]}`]"/>
                </template>

            <template v-if="column.type=='ddTalk' &&scope.row[column.ddInfo.name]">
                <div class="nameBox" v-if="scope.row[column.ddInfo.prop] && scope.row[column.ddInfo.name] && scope.row[column.ddInfo.name]!= ' '">
                    <el-image :src="ddLogo" style="border-radius:5px;" class="ddTalk"
                        @click="startSession(scope.row[column.ddInfo.prop],column.ddInfo.type)" />
                        <el-button type="text" style="cursor: pointer;max-width: 40px;" v-if="column.handle && typeof column.handle == 'function'" @click="column.handle&&column.handle(that,scope.row)">{{ column.formatter ? column.formatter(scope.row) : scope.row[column.ddInfo.name] }}</el-button>
                    <div class="name" v-else> {{ column.formatter ? column.formatter(scope.row) : scope.row[column.ddInfo.name] }}</div>
                </div>
                <div v-else></div>
            </template>

            <template v-if="column.type==='files'  && scope.row[column.prop]&& scope.row[column.prop].length>2">
                <template v-if=" JSON.parse(scope.row[column.prop]).length>1">
                    <span style="color:blue;cursor:pointer;"  @click="downloadFiles(scope.row[column.prop])">{{JSON.parse(scope.row[column.prop]).length}}个文件</span>
                </template>
                <template v-else>
                    <span style="color:blue;cursor:pointer;"  @click="downloadFile(scope.row[column.prop])">{{JSON.parse(scope.row[column.prop])[0].name}}</span>
                </template>
            </template>

            <el-image v-if="column.type==='imageGoodsCode'" :src="scope.row[column.prop]" fit="contain" @click="previewImageGoodsCode(scope.row[column.prop],scope.row[column.goods.code],scope.row[column.goods.name])" style="width: 50px;height: 50px;margin:-5px 0px -5px -5px;" />
            <el-slider v-if="column.type==='slider'" v-model="scope.row[column.prop]" :disabled="column.isDisabled && column.isDisabled(scope.row)" :size="size || btn.size" @change='column.change && column.change(scope.row)'></el-slider>
            <el-popover v-if="column.type==='popover'" :disabled="column.isDisabled" :size="size || btn.size" ref="popover3" :placement="column.popover&&column.popover.placement||bottom" :width="column.popover&&column.popover.width||200" :trigger="column.popover&&column.popover.trigger||click" @show="column.popover&&column.popover.onShow(scope.row)">
                <span slot="reference" style="color:blue;cursor:pointer;">
                    {{tonumfuc(scope.row[column.prop], column.label)}}
                </span>
                <div v-if="column.popover&&column.popover.contentType=='custom'" v-html="column.popover&&column.popover.content"></div>
                <el-descriptions v-if="column.popover&&column.popover.contentType=='descriptions'" title="column.popover&&column.popover.title">
                    <el-descriptions-item v-for="desc in column.popover.descData" :label="desc.label" :key="desc.label">{{desc.value}}</el-descriptions-item>
                </el-descriptions>
            </el-popover>
            <!-- 标签 -->
            <span v-if="column.type==='click'" :style="column.style==null?'color:blue;cursor:pointer;':typeof(column.style)=='function'?column.style(that,scope.row,column,scope.row[column.prop]):column.style" @click="column.handle&&column.handle(that,scope.row,column,scope.row[column.prop])">{{(column.formatter && tonumfuc(column.formatter(scope.row), column.label)) || tonumfuc(scope.row[column.prop], column.label)}}</span>
            <span v-if="column.type==='orderLogInfo'" :style="column.style==null?'color:blue;cursor:pointer;':typeof(column.style)=='function'?column.style(that,scope.row,column,scope.row[column.prop]):column.style" @click="showLogDetail(scope.row[column.prop],column.orderType,scope.row)">{{(column.formatter && tonumfuc(column.formatter(scope.row), column.label)) || tonumfuc(scope.row[column.prop], column.label)}}</span>
            <span v-if="column.type === 'companyclick'">
              <template v-for="(field, index) in column.seriesList">
                <span
                  :key="`${field}-${scope.$index}`"
                  @click="column.handle&&column.handle(that,scope.row,field,column,scope.row[column.prop])"
                  :style="column.style==null?'color:blue;cursor:pointer;':typeof(column.style)=='function'?column.style(that,scope.row,column,scope.row[column.prop]):column.style">
                  {{ scope.row[field] ? scope.row[field] : '' }}
                </span>
                <span v-if="index < column.seriesList.length - 1 && scope.row[field] !== null && (scope.row[column.seriesList[index + 1]] !== null || index === column.seriesList.length - 2)">-</span>
              </template>
            </span>

            <span v-if="column.type === 'starclick'" style="position: relative;">
              <span
                :style="column.style != null ? 'color: blue; cursor: pointer;' : typeof(column.style) == 'function' ? column.style(that, scope.row, column, scope.row[column.prop]) : column.style"
                @click="column.handle && column.handle(that, scope.row, column, scope.row[column.prop])"
              >
                {{ (column.formatter && tonumfuc(column.formatter(scope.row), column.label)) || tonumfuc(scope.row[column.prop][0], column.label) }}
              </span>
              <span
                v-if="scope.row[column.prop][1] !== undefined"
                class="item"
                :style="{
                  position: 'absolute',
                  top: '-8px',
                  // top: '{{scope.row[column.prop][1]+650}}px',
                  right: '-70px',
                  backgroundColor: 'transparent',
                  border: 'none',
                  fontSize: '12px',
                  transform: 'translateY(-2px)',
                  color: scope.row[column.prop][1] > 0 ? 'red' : (scope.row[column.prop][1] < 0 ? 'green' : 'gray')
                }"
              >
                {{ scope.row[column.prop][1] > 0 ? '+' + scope.row[column.prop][1] : (scope.row[column.prop][1] === 0 ? '0' : scope.row[column.prop][1]) }}
              </span>
            </span>
            <!-- <span v-if="column.type==='click'"  :style="column.style==null?'color:blue;cursor:pointer;':typeof(column.style)=='function'?column.style(that,scope.row,column,scope.row[column.prop]):column.style"
                  @click="column.handle&&column.handle(that,scope.row,column,scope.row[column.prop])">{{column.text==null?scope.row[column.prop]:column.text}}</span> -->
            <el-progress v-if="column.type==='progress'" :text-inside="true" :stroke-width="20" :percentage=" column.prop ? Number(scope.row[column.prop]) : 0"></el-progress>
            <!-- <div class="grid-header" v-if="column.type=='template'" > {{(column.formatter && column.formatter(scope.row)) || scope.row[column.prop]}}
                     <el-tooltip class="item"  effect="dark" content="内部订单号数量" placement="top-end">
                        <span><i class="el-icon-question"></i></span>
                     </el-tooltip>
                </div> -->
            <!-- <span v-if="column.type=='template'"
                    :style="column.itemStyle && column.itemStyle(scope.row)" :size="size || btn.size"
                    :class="column.itemClass && column.column.itemClass(scope.row)">{{(column.formatter && column.formatter(scope.row)) || scope.row[column.prop]}}</span> -->
            <!-- 默认 -->
            <span v-if="column.type=='custom'||!column.type" :style="column.itemStyle && column.itemStyle(scope.row)" :size="size || btn.size" :class="column.itemClass && column.column.itemClass(scope.row)">
              <template v-if="canJump(scope.row, column)">
                <a @click="handleLinkClick(scope.row, column)" style="color: blue; cursor: pointer;"> {{ (() => {
                      if (column.formatter)
                        return tonumfuc(column.formatter(scope.row), column.label);
                      else
                      return tonumfuc(scope.row[column.prop], column.label);
                    })() }} </a>
              </template>
              <template v-else>
                {{ (() => {
                    if (column.formatter)
                      return tonumfuc(column.formatter(scope.row), column.label);
                    else
                    return tonumfuc(scope.row[column.prop], column.label);
                  })() }}
              </template>
            </span>
            <!-- (column.formatter && column.formatter(scope.row)) || scope.row[column.prop] -->

            <div v-if="column.type=='video'">
                <div style="position: relative;">
                    <el-image :src="scope.row[column.prop]" fit="contain" style="width: 50px;height: 50px;margin:-5px 0px -5px -5px;" />

                    <span style="display: block;position: absolute;top: 20%;left: 10px;">
                        <a size="mini" class="el-link el-link--primary is-underline" @click="playerVideo(scope.row[column.videoprop])" style="margin-left: 3px;color:#ccc;font-size: 13px;">
                            <i class="el-icon-video-play"></i>
                        </a></span>
                </div>
            </div>


            <div v-if="column.type=='xptooltip'">
                <el-tooltip effect="dark"  placement="top-start">
                    <div slot="content">
                        <div  v-for="(item,i) in scope.row[column.props]" :key="i">{{item.platformName}}:
                            {{column.prop=='totalLastMonthSaleCount'?item.lastMonthSaleCount:
                            column.prop=='totalLastMonthSaleAmount'?item.lastMonthSaleAmount:
                            column.prop=='totalLastMonthProfitAmount'?item.lastMonthProfitAmount:
                            column.prop=='totalLastMonthProfitRate'?item.lastMonthProfitRate:'' }}
                            <br/></div>
                    </div>
                    <div class="textover" style="width: 80%;">{{ (column.formatter && tonumfuc(column.formatter(scope.row), column.label)) || tonumfuc(scope.row[column.prop], column.label) }}</div>
                </el-tooltip>
            </div>

            <div v-if="column.type=='imgvideo'">
                <div style="position: relative;">
                    <el-image :src="scope.row[column.prop]" fit="contain" @error="videoImgErrorEvent(scope.row[column.videoprop],scope.row.id)" style="width: 50px;height: 50px;margin:-5px 0px -5px -5px;" />

                    <span style="display: block;position: absolute;top: 20%;left: 10px;">
                        <a size="mini" class="el-link el-link--primary is-underline" @click="playerVideo(scope.row[column.videoprop])" style="margin-left: 3px;color:#ccc;font-size: 13px;">
                            <i class="el-icon-video-play"></i>
                        </a></span>
                </div>
            </div>

            <div v-if="column.type=='ddingtalk'">
                <div style="position: relative; width: 100%;">

                    <div style="display: flex; flex-direction: row;" v-if="scope.row[column.prop]">
                        <span>
                            {{tonumfuc(scope.row[column.prop], column.label)}}
                        </span>

                        <img src="@/static/images/dingdingicon.png" fit="contain" @click="OpenDdingPicking(scope.row[column.dingdingcode])" style="width: 15px;height: 15px;margin:5px  0px 0px 10px; " />

                    </div>
                </div>
            </div>


            <template v-if="column.type == 'mancolor'">
                <div class="chart-container">
                     <div style="width: 100%; height: 100%; margin: 0;"
                     :style="{ height: scope.row[column.propheight] + '%',
                     backgroundColor: scope.row[column.prop] === 0 ? '#F56C6C'
                     : (scope.row[column.prop] < 31 && scope.row[column.prop] > 0) ? '#67C23A'
                     : (scope.row[column.prop] >= 31 && scope.row[column.prop] <= 60) ? '#E6A23C'
                     : scope.row[column.prop] > 60 ? '#F56C6C'
                     : '#F56C6C' } " class="height-chart">
                     </div>
                     <div class="height-data">{{ (parseInt(scope.row[column.prop] * 10000) / 10000).toFixed(1) }}</div>
                </div>
            </template>

            <template v-if="column.type==='UrgencyButton'">
                <template v-if="scope.row.taskUrgency==1  ">
                    <el-button type="danger" size="small" plain  @click="column.handle&&column.handle(that,scope.row,column,scope.row[column.prop])">{{scope.row.taskUrgencyName}}</el-button>
                </template>
                <template v-else-if="( scope.row.taskUrgency==2)">
                    <el-button type="primary" size="mini" plain  @click="column.handle&&column.handle(that,scope.row,column,scope.row[column.prop])">{{scope.row.taskUrgencyName}}</el-button>
                </template>
                <template v-else-if="( scope.row.taskUrgency==0)">
                    <el-button type="warning" size="mini" plain  @click="column.handle&&column.handle(that,scope.row,column,scope.row[column.prop])">{{scope.row.taskUrgencyName}}</el-button>
                </template>
                <template v-else>
                    <el-button plain size="mini"   @click="column.handle&&column.handle(that,scope.row,column,scope.row[column.prop])">{{scope.row.taskUrgencyName}}</el-button>
                </template>
            </template>
            <template v-if="column.type==='clickflag'">
                <template v-if="scope.row.markCssName=='0'  ">
                    <img src="@/static/images/flagOff.png" fit="contain"
                    @click="column.handle&&column.handle(that,scope.row,column,scope.row[column.prop])" style="width: 15px;height: 15px;margin:5px  0px 0px 10px; " />

                </template>

                <template v-else>
                    <img src="@/static/images/flagOn.png" fit="contain"
                    @click="column.handle&&column.handle(that,scope.row,column,scope.row[column.prop])" style="width: 15px;height: 15px;margin:5px  0px 0px 10px; " />
                </template>
            </template>
            <template v-if="column.type==='inputEdit'">
                <template v-if="scope.row.isEdit===0  ">
                    <span>
                        {{scope.row[column.prop]}}
                    </span>
                </template>
                <template v-else>
                    <el-input-number  :style="'width:'+(column.width? (column.width-20)+ 'px;':'100%')+';'" :min="column.min?column.min:-999999999" :max="column.max?column.max:999999999" :precision="column.precision?column.precision:0" :controls="!!column.controls" v-model.number="scope.row[column.prop]" :size="size || btn.size" :disabled="column.isDisabled && column.isDisabled(scope.row)" @focus="column.focus && column.focus(scope.row)" @change="column.change && column.change(scope.row)"></el-input-number>
                </template>
            </template>

        </template>
    </el-table-column>
</template>
<script>
    import { tonumfuc } from '@/utils/tonumqian.js'
    import {matchImg} from '@/utils/getCols'
    import { SaveProductIdViewLog } from '@/api/operatemanage/PddChart'
    import { canJump, onJumpLink } from '@/utils/tools';
    export default {
        components: {},
        props: {
            that: { type: Object, default: this },
            column: { type: Object, default: () => { } },
            // 表格型号：mini,medium,small "../static/excel/order/订单物流预警明细模板.xlsx"
            size: { type: String, default: 'mini' },
            type: { type: String, default: 'primary' },
            descData: { type: Array, default: () => [] },
            showoverflowtooltip: {type: Boolean, default: true},

        },
        data() {
            return {
                tonumfuc,
                ddLogo: require('@/static/images/dingding.png'),
                imgPreview: { img: "", show: false },
                showImage: false,
                imgList: [],
                ImgWith: null,
                imagedefault: require('@/assets/images/detault.jpeg'),
            }
        },
        watch: {
            imgList(val, value) {
                this.$nextTick(() => {
                    let imgwith = this.$refs.imgdialog.width;
                    this.ImgWith = `${imgwith + 50}px`;
                });
            },
        },
        methods: {
            canJump,
            async handleLinkClick(row, column) {
              if (!this.canJump(row, column)) return;
              try {
                await onJumpLink(row[column.prop], column.prop);
              } catch (err) {
                if (err.message === '不支持多个进行跳转') {
                  this.$message.error('不支持多个进行跳转');
                } else {
                  this.$message.error('小昀工具箱不在线，请开启后使用！');
                }
              }
            },
            handleClick(e,prop){
                if (!prop) return
                if (!e.target.parentNode.innerHTML.includes('复') && !e.target.parentNode.innerHTML.includes('查 ')) return
                let res = JSON.parse(JSON.stringify(prop));
                if (res.length > 6) {
                    res = res.substring(0, 2) + '**' + res.substring(res.length - 2, res.length);
                }
                if (e.target.innerHTML == '复') {
                    var _this = this;
                    this.$copyText(prop).then(function (e) {
                        _this.$message({ message: "内容已复制到剪切板！", type: "success" });
                    }, function (e) {
                        _this.$message({ message: "抱歉，复制失败！", type: "warning" });
                    })
                    this.sendLog(prop, '复制宝贝ID', 'ERP')
                } else if (e.target.innerHTML == '查 ') {
                    if (e.target.parentNode.innerHTML.includes(res)) {
                        e.target.parentNode.innerHTML = e.target.parentNode.innerHTML.replace(res, prop)
                    }
                    this.sendLog(prop, '查看宝贝ID', 'ERP')
                } else {
                    if (res == e.target.innerHTML || prop == e.target.innerHTML) {
                        this.sendLog(prop, '打开链接', 'ERP')
                    }
                }
            },
            async sendLog(proCode, action, source) {
                await SaveProductIdViewLog({ proCode, action, source })
            },
            formatImg(img){
                return matchImg(img)
            },
            startSession(id, type){
                this.$emit('startSession', id, type)
            },
            showLogDetail(val,orderType,row) {
                let params = {val,orderType,platform:row.platform ? row.platform : row.platForm}
                this.$emit('showLogDetail', params)
            },
            downloadFile(files){
                if(files){
                    let jsonF=JSON.parse(files);
                    if(jsonF && jsonF.length>0)
                        window.open(jsonF[0].url);
                }
            },
            downloadFiles(files){
                if(files){
                    let jsonF=JSON.parse(files);

                    this.$showDialogform({
                        path: `@/views/base/DownloadFilesForm.vue`,
                        title: '文件列表',
                        autoTitle: false,
                        args: { files:jsonF, mode: 3 },
                        height: 300,
                        width: '600px',
                        callOk:null
                    })
                }

            },
            copytext(e) {
                let textarea = document.createElement("textarea")
                textarea.value = e
                textarea.readOnly = "readOnly"
                document.body.appendChild(textarea)
                textarea.select()
                let result = document.execCommand("copy")
                if (result) {
                    this.$message({
                    message: '复制成功',
                    type: 'success'
                    })
                }
                textarea.remove()
            },
            OpenDdingPicking(id) {
                if (id) {
                    window.location.href = "dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=" + id;
                } else {
                    this.$message({ type: 'warning', message: "钉钉号未设置" });
                }
            },
            select(rows, row) {
                this.$emit('select', rows, row);
            },
            // 全选
            selectAll(rows) {
                this.$emit('select', rows)
            },
            sortchange(column) {
                this.$emit('sortchange', column)
            },
            cellclick(row, column, cell, event) {
                this.$emit('cellclick', row, column, cell, event)
            },
            clearSort() {
                this.$refs.cesTable.clearSort();
            },
            btnHand(hand) {
                console.log("hand", hand)
                this.$emit(hand)
            },
            async preview(imgUrl) {
                this.$emit('preview', !!imgUrl ? imgUrl : this.imagedefault)
            },
            async playerVideo(videoUrl) {
                this.$emit('playerVideo', videoUrl)
            }
            ,
            async closeFunc() {
                this.showImage = false;
            },
            // 图片点击放大
            showImg(e) {
                if (e.target.tagName == 'IMG') {
                    this.$emit('showImg', e)
                }
            },
            //商品编码大图预览 增加自定义相机按钮code商品编码，name商品名称
            async previewImageGoodsCode(imgUrl, code, name) {
                this.$emit('previewImageGoodsCode', imgUrl, code, name)
            },
            //视频图片加载失败方法
            async videoImgErrorEvent(videoUrl, id) {
                console.log(id);
                this.$emit('videoimgErrorEvent', videoUrl, id)
            }
        },
    }
</script>
<style lang="scss" scoped>
.imgstyle{
    width: 50px;
    height: 50px;
    margin:-5px 0px -5px -5px;
}
.chart-container {
    display: flex;
    flex-direction: column-reverse;
    height: 70px;
    width: 100%;
    background-color: #f5f5f5;
    position: relative;

}

.height-chart {
    transition: height 0.3s;
    min-height: 10px;
}

.heightdata {
    font-size: 12px;
    text-align: center;
    margin-top: -20px;
    position: absolute;
    top: 80%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.copyhover{
  display: none;
}
.relativebox{
  width: 80%;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}
.relativebox:hover{
  width: 80%;
}
.textover{
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}
.relativebox:hover .textover{
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.relativebox:hover .copyhover{
  display: block;
  position: absolute;
  top: 50%;
  left: 75%;
  margin: 0 10px;
  z-index: 99;
  transform: translate(-50%,-50%);
  color: #409EFF;
  font-weight: 600;
}

.ddTalk ::v-deep img {
    max-width: 20px !important;
    max-height: 20px !important;
    min-height: 20px !important;
    min-width: 20px !important;
    vertical-align: middle;
    cursor: pointer;
}

.nameBox {
    display: flex;
    align-items: center;

    .name {
        max-width: 40px;
    }
}

.userAvatar ::v-deep img {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50%;
}
</style>
