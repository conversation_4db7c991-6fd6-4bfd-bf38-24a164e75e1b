import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Kj}/SkuPricing/`


//查询仓库列表
export const getWarehouseList =(params,config) =>{return request.get(apiPrefix + 'GetWarehouseList',params,config)}

//计算sku定价
export const calSkuPricing =(params,config) =>{return request.post(apiPrefix + 'calSkuPricing',params,config)}

//保存sku定价
export const saveSkuPricing =(params,config) =>{return request.post(apiPrefix + 'saveSkuPricing',params,config)}

//查询sku定价列表数据
export const pageListSkuPricing =(params,config) =>{return request.post(apiPrefix + 'pageListSkuPricing',params,config)}

//导出Temu半托店铺SKU定价列表 
export const skuExport =(params,config={responseType: 'blob'}) =>{return request.post(apiPrefix + 'export',params,config)}

//sku定价删除
export const deleteSkuPricing =(params,config) =>{return request.post(apiPrefix + 'deleteSkuPricing',params,config)}


//查询货柜批次下拉列表数据
export const listContainerBatch =(params,config) =>{return request.get(apiPrefix + 'listContainerBatch',params,config)}

//保存货柜批次
export const saveContainerBatch =(params,config) =>{return request.post(apiPrefix + 'saveContainerBatch',params,config)}

//删除货柜批次
export const deleteContainerBatch =(params,config) =>{return request.post(apiPrefix + 'deleteContainerBatch',params,config)}


//查询批次分页列表
export const pageListContainerBatch =(params,config) =>{return request.post(apiPrefix + 'pageListContainerBatch',params,config)}

//模糊查询sku记录
export const fuzzyQuerySku =(params,config) =>{return request.get(apiPrefix + `fuzzyQuerySku?skuKeyWorld=${params.skuKeyWorld}&limitCount=${params.limitCount}`,params,config)}

//更新备注
export const upDateRemarks =(params,config) =>{return request.post(apiPrefix + 'upDateRemarks',params,config)}

//获取仓库物流报价列表分类
export const getShippingMethodFeeGroupList =(params,config) =>{return request.get(apiPrefix + 'getShippingMethodFeeGroupList',params,config)}

//查询海外仓物流报价列表
export const getShippingMethodFeePageList =(params,config) =>{return request.post(apiPrefix + 'getShippingMethodFeePageList',params,config)}

//导出  海外仓物流报价
export const exportWarehouseShipmentPricing =(params,config={responseType: 'blob'}) =>{return request.post(apiPrefix + 'exportWarehouseShipmentPricing',params,config)}

//导入  海外仓物流报价
export const importWarehouseShipmentPricing =(params,config) =>{return request.post(apiPrefix + 'importWarehouseShipmentPricing',params,config)}

//获取仓库物流报价列表导入日志
export const getImportLogPageList =(params,config) =>{return request.post(apiPrefix + 'getImportLogPageList',params,config)}
