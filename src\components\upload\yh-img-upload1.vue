<template>
    <div>
        <el-upload action="/api/uploadnew/file/UploadCommonFileAsync" list-type="picture-card"
            :on-preview="handlePictureCardPreview" :on-remove="handleRemove" :multiple="false" :limit="limit"
            :disabled="disabled" :file-list="fileList" :on-success="handleAvatarSuccess"
            :class="{ disabled: fileList.length >= limit }" accept=".img,.jpg,.jpeg,.png,.bmp">
            <i class="el-icon-plus"></i>
        </el-upload>
        <!-- <el-dialog :visible.sync="dialogVisible" append-to-body style="width:99%">
            <img style="max-width:98%;max-height:98%;" :src="dialogImageUrl" alt="">
        </el-dialog> -->
        <div style="display:none">
            <el-image ref="imgShow" style="width: 100px; height: 100px" :src="dialogImageUrl" :preview-src-list="srcList">
            </el-image>
            {{ dialogImageUrl }}
        </div>
    </div>
</template>

<script>

export default {
    name: 'YhImgUpload',
    props: {
        limit: {
            type: Number,
            default: 1
        },
        value: {
            type: String,
            default () {
                return ""
            }
        },
        disabled: {
            type: Boolean,
            default () {
                return false
            }
        },
    },
    data () {
        return {
            fileList: [],
            dialogImageUrl: '',
            dialogVisible: false,
            handleSelf: false,
            srcList: []
        }
    },
    computed: {

    },
    watch: {
        value (newVal, oldVal) {
            if (newVal == null || newVal == "" || newVal == undefined) {
                this.fileList = [];
                this.handleSelf = false;
            }

            if (this.handleSelf)
                return;

            var tempList = [];
            if (newVal) {

                if (newVal.indexOf('[') == 0) {
                    tempList = JSON.parse(newVal);
                } else {
                    tempList.push({ name: "", url: newVal });
                }
                //如果数组长度不同，或字段内容不同，赋值。
                if (this.fileList.length != tempList.length || (tempList.length > 0 && tempList[tempList.length - 1].url != this.fileList[this.fileList.length - 1].url)) {
                    this.fileList = tempList;
                }
            } else {
                if (this.fileList.length > 0)
                    this.fileList = [];
            }

            this.handleSelf = false;

        },
        value: 'valuefuc'
    },
    mounted () {
        //多图片地址逗号传入数据库
        if (this.value) { 
            if (this.limit <= 1) {
                let arrvalue = this.value.split(',');
                arrvalue.forEach(a => { 
                    this.fileList.push({ name: "", url: a });
                });
            } 
        }
    },
    methods: {
        valuefuc (e) {
            this.fileList = [];
            if (e) {
                let arrvalue = e.split(',');
                arrvalue.forEach(a => { 
                    this.fileList.push({ name: "", url: a });
                });
                //this.fileList.push({ name: "", url: e });
            }
            
        },
        handleAvatarSuccess: function (response, file, fileList) {


            if (response && response.success && response.data.url) {
                this.handleSelf = true;
                if (this.limit == 1) {
                    let newValue = response.data.url;
                    this.fileList = [{ name: file.name, url: response.data.url }];
                    this.$emit("update:value", newValue);
                } else {

                    var tempList = [];
                    this.fileList.forEach(x => {
                        tempList.push({ name: x.name, url: x.url });
                    });
                    tempList.push({ name: file.name, url: response.data.url });
                    this.fileList.push({ uid: file.uid, name: file.name, url: response.data.url });
                    let newValue = JSON.stringify(tempList);
                    this.$emit("update:value", newValue);
                }
            }
        },
        handleRemove (file, fileList) {

            this.handleSelf = true;

            this.removeFile(file.uid);
            if (this.limit == 1) {
                this.$emit("update:value", "");
            } else {
                if (fileList == null || fileList.length == 0) {
                    this.$emit("update:value", "");
                }
                else {
                    var tempList = [];
                    fileList.forEach(x => {
                        tempList.push({ name: x.name, url: x.url });
                    });

                    let newValue = JSON.stringify(tempList);
                    this.$emit("update:value", newValue);
                }

            }
        },
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url;
            this.dialogVisible = true;
            this.srcList = [];
            this.fileList.forEach(x => {
                this.srcList.push(x.url);
            });

            this.$refs.imgShow.showViewer = true;

        },
        findFileIndex (uid) {
            for (var i = 0; i < this.fileList.length; i++) {
                if (this.fileList[i].uid == uid) {
                    return i;
                }
            }

            return -1;
        },
        removeFile (uid) {

            let idx = this.findFileIndex(uid);
            if (idx > -1) {
                this.fileList.splice(idx, 1);
            }
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep .el-upload-list--picture-card .el-upload-list__item {
    width: 60px;
    height: 60px;
}

::v-deep .el-upload--picture-card {
    width: 60px;
    height: 60px;
    line-height: 64px;
}

::v-deep .disabled .el-upload--picture-card {
    display: none;
}
</style>

