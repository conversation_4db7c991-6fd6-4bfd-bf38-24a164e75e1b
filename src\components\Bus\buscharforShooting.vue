<template>
    <div style="height:100%;padding-left:10px;overflow: auto;">
        <div :id="'buschar'+randrom" :style="thisStyle" />
    </div>
</template>
<script>
    import * as echarts from 'echarts';
    export default {
        name: 'buschar',
        components: {},
        props: {
            action: { type: Function, default: null },
            parms: { type: Object, default: null },
            analysisData: { type: Object, default: null },
            toolbox: { type: Object, default: function(){
                return {
                    magicType: { show: true, type: ['line', 'bar'] },
                    dataView: { show: true, readOnly: true },
                    restore: { show: false },
                    saveAsImage: { show: true }
                }
            } },
            thisStyle: {
                type: Object,
                default: function () {
                    return {
                        width: '100%', height: '550px', 'box-sizing': 'border-box', 'line-height': '360px'
                    }
                }
            },
            gridStyle: {
                type: Object, default: function () {
                    return {
                        top: '20%',
                        left: '5%',
                        right: '4%',
                        bottom: '5%',
                        containLabel: false
                    }
                }
            },
            legendPistion: {
                type: Object,
            }
        },
        data () {
            return {
                that: this,
                randrom: "",
                period: 0,
                pageLoading: false,
                listLoading: false,
                procode: '',
                myChart:null,
            }
        },
        created () {
            var e = 10;
            var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
                a = t.length,
                n = "";
            for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
            this.randrom = n;
        },
        mounted () {
            this.initcharts();
    window.addEventListener('resize', this.handleResizeChart);
    },
    destroyed () {
    window.removeEventListener('resize', this.handleResizeChart);
  },
    methods: {
        //显示数值
        onHandleShow (showlable) {
            // var chartDom = document.getElementById('buschar' + this.randrom);
            //     this.myChart = echarts.init(chartDom);
            //     this.myChart.clear();
                var option = this.Getoptions(this.analysisData);
            if (showlable) {
                option.series.forEach(item => {
            item.label = {
                show: true,
                position: 'top',
                fontSize: 12,
                formatter: (params) => {
                    let value = params.value;
                    let formattedValue;

                    // 判断是否是百分比
                    if (params.seriesName.includes("%") || 
                        params.seriesName.includes("占比") || 
                        params.seriesName.includes("率")) {
                        
                        formattedValue = (value > 100 ? (value / 100) : value).toFixed(2) + '%';
                    } else {
                        // 普通数值格式化
                        if (Math.abs(value) >= 1000) {
                            formattedValue = Math.abs(value).toLocaleString();
                        } else if (Math.abs(value) >= 100) {
                            formattedValue = Math.abs(value).toFixed(0);
                        } else {
                            formattedValue = Math.abs(value).toFixed(2);
                        }
                    }

                    // 处理负号
                    return value < 0 ? `-${formattedValue}` : formattedValue;
                            }
                        };
            })
            } else {
                option.series.forEach(item => {
                    item.label = {
                        show:false,
                }    
                })
            }
            option && this.myChart.setOption(option);
        },
            initcharts () {
                let that = this;
                this.$nextTick(() => {
                    var chartDom = document.getElementById('buschar' + that.randrom);
                    this.myChart = echarts.init(chartDom);
                    this.myChart.clear();
                    if (this.analysisData && this.analysisData.series) {
                        var option = this.Getoptions(this.analysisData);
                        option && this.myChart.setOption(option);
                    }
                    if(this.action != null){
                        this.myChart.on('click', async params => {
                            if (params.seriesType == "bar") {
                                console.log(params);
                                await  this.action(params);
                            }
                        })
                    }
                });
            },
            randomString () {
                var e = 10;
                var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
                    a = t.length,
                    n = "";
                for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
                return n
            },
            Getoptions (element) {
                var series = []

                element.series.forEach(s => {
                    series.push({ smooth: true, ...s })
                })
                var yAxis = []
                if (Array.isArray(element.yAxis)) {
                    element.yAxis.forEach(s => {
                        yAxis.push({
                            type: 'value', minInterval: 1, offset: s.offset, splitLine: s.splitLine, position: s.position, name: s.name, max: s.max, min: s.min, axisLabel: {
                                formatter: function (value) {
                                    if (value >= 100000000) {
                                        value = (value / 100000000).toFixed(1)  + 'Y';
                                    }
                                    if (value >= 10000000) {
                                        value = (value / 10000000).toFixed(1)  + 'KW';
                                    }
                                    if (value >= 10000) {
                                        value = (value / 10000).toFixed(1)  + 'W';
                                    }
                                    if (value >= 1000) {
                                        value = (value / 1000).toFixed(1)  + 'K';
                                    }
                                    if (value <= -100000000) {
                                        value = (value / 100000000).toFixed(1)  + 'Y';
                                    }
                                    if (value <= -10000000) {
                                        value = (value / 10000000).toFixed(1) + 'KW';
                                    }
                                    if (value <= -10000) {
                                        value = (value / 10000).toFixed(1)  + 'W';
                                    }
                                    if (value <= -1000) {
                                        value = (value / 1000).toFixed(1)  + 'K';
                                    }
                                    return value + s.unit;
                                }
                            }
                        })
                    })
                } else {
                    yAxis = { ...element.yAxis };
                }


                var selectedLegend = {};//{};
                if (element.selectedLegend) {
                    element.legend.forEach(f => {
                        //if(!element.selectedLegend.includes(f)) selectedLegend["'"+f+"'"]=false
                        if (!element.selectedLegend.includes(f)) selectedLegend[f] = false
                    })
                }
                var option = {
                    title: { text: element.title },
                    tooltip: { trigger: 'axis' },
                    legend: {
                        selected: selectedLegend,
                        data: element.legend,
                        bottom:this.legendPistion?.bottom
                    },
                    grid: this.gridStyle,
                    toolbox: {
                        feature: this.toolbox
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross'
                        },
                        padding: [5, 10]
                    },
                    xAxis: {
                        type: 'category',
                        data: element.xAxis
                    },
                    yAxis: yAxis,
                    series: series
                };
                return option;
        },
    handleResizeChart () {
      if (this.myChart) {
        this.myChart.resize();
      }
    }
        }
    }
</script>
 
