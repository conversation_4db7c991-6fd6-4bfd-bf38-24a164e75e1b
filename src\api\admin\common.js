import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API}/common/`


export const getAnalysisCommonResponse = (params, config = {}) => {
  return request.post(apiPrefix + 'GetAnalysisResponseAsync', params, config)
}


//发送消息通知
export const PostNoticeAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'PostNoticeAsync', params, config)
}


//发送站内消息通知
export const PostSiteMsgAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'PostSiteMsgAsync', params, config)
}