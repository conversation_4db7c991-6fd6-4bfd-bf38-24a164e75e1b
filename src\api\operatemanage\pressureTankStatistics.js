import request from "@/utils/request";
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/PressureTankStatistics/`;

//分页获取压力罐统计 GetPressureTankStatisticsPage
export const GetPressureTankStatisticsPage = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetPressureTankStatisticsPage",
    params,
    config
  );
};

//导出压力罐统计  ExportPressureTankStatistics
export const ExportPressureTankStatistics = (
  params,
  config = { responseType: "blob" }
) => {
  return request.post(
    apiPrefix + "ExportPressureTankStatistics",
    params,
    config
  );
};

//保存压力罐统计 SavePressureTankStatistics
export const SavePressureTankStatistics = (params, config = {}) => {
  return request.post(apiPrefix + "SavePressureTankStatistics", params, config);
};

//获取压力罐日志 GetPressureTankStatisticsLog
export const GetPressureTankStatisticsLog = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetPressureTankStatisticsLog",
    params,
    config
  );
};

//获取压力罐明细 GetPressureTankStatisticsInfo
export const GetPressureTankStatisticsInfo = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetPressureTankStatisticsInfo",
    params,
    config
  );
};
