<template>
    <div>
        <el-row>
            <el-row>
                <div style="display: inline-block;">
                    <div style="display: flex;flex-direction: row;">
                        <el-upload ref="upload" :disabled="disabledd" class="inline-block" action="#"
                            :auto-upload="true" :multiple="multiple" :limit="limit" :show-file-list="false"
                            :accept="accepttyes" :http-request="UpSuccessload" :on-exceed="exceed" :file-list="retdata">
                            <el-button size="mini" type="primary" :disabled="disabledd" :v-loading="uploading">点击上传
                                <i class="el-icon-upload el-icon--right"></i>
                            </el-button>
                        </el-upload>
                        <div class="boxx" v-if="ispastee && reveal">
                            <el-tooltip class="item" effect="dark" content="点击灰框可直接贴图" placement="top-start">
                                <pastimgVue @callback="callback" :ispaste="ispastee" :keyy="[keys[0], keys[1]]">
                                </pastimgVue>
                            </el-tooltip>
                        </div>
                        <div v-if="( noDel ? false : !ispastee ) && reveal">
                            <el-tooltip class="item" effect="dark" content="图片上传已超出最大限制" placement="top">
                                <i class="el-icon-warning-outline"></i>
                            </el-tooltip>
                        </div>
                    </div>
                    <el-button v-if="isdown" @click="downExecl" style=" margin-left: 20px;font-size:14px"
                        type="text">下载</el-button>
                </div>
            </el-row>
            <div class="linecontent">
                <div v-for="(i, j) in retdata" :key="j">
                    <div class="linerow img-hover"
                        :style="minisize ? { height: '60px', width: '60px' } : { height: '35px', width: '35px' }">
                        <el-tooltip v-if="i.fileName?.indexOf('.xlsx') > -1 || i.fileName?.indexOf('.xlsm') > -1"
                            class="item" effect="dark" :content="i.fileName" placement="bottom">
                            <template>
                                <img src="@/static/images/excel.jpg" @click="downExecl(i)"
                                    style="height: 100%; width: 100%;" mode="aspectFit" crossOrigin='anonymous' />
                            </template>
                        </el-tooltip>
                        <el-tooltip v-else-if="i.fileName?.indexOf('.doc') > -1 || i.fileName?.indexOf('.docx') > -1"
                            class="item" effect="dark" :content="i.fileName" placement="bottom">
                            <template>
                                <img src="@/static/images/word.jpg" @click="downExecl(i)"
                                    style="height: 100%; width: 100%;" mode="aspectFit" crossOrigin='anonymous' />
                            </template>
                        </el-tooltip>
                        <el-tooltip v-else-if="i.fileName?.indexOf('.txt') > -1" class="item" effect="dark"
                            :content="i.fileName" placement="bottom">
                            <template>
                                <img src="@/static/images/txt.jpg" @click="downExecl(i)"
                                    style="height: 100%; width: 100%;" mode="aspectFit" crossOrigin='anonymous' />
                            </template>
                        </el-tooltip>
                        <el-tooltip v-else-if="i.fileName?.indexOf('.pdf') > -1" class="item" effect="dark"
                            :content="i.fileName" placement="bottom">
                            <template>
                                <img src="@/static/images/pdf.jpg" @click="downExecl(i)"
                                    style="height: 100%; width: 100%;" mode="aspectFit" crossOrigin='anonymous' />
                            </template>
                        </el-tooltip>
                        <img v-else :src="i.url" @click="showImg(i, j)" style="height: 100%; width: 100%;"
                            mode="aspectFit" @dragover="onDragOver" @dragend="onDragEnd(j)" />
                        <i v-if="retdata.length != null && !noDel" class="el-icon-error close-img"
                            @click.stop="removefile(i)"></i>
                        <div v-if="retdata.length != null" class="close-img-dask" />
                    </div>
                </div>
            </div>
            <el-image-viewer v-if="showGoodsImage" :url-list="imglist" :initialIndex="imgindex" :on-close="closeFunc"
                style="z-index:9999;" />
            <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer"
                :append-to-body="true">
                <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
                <span slot="footer" class="dialog-footer">
                    <el-button @click="closeVideoPlyer">关闭</el-button>
                </span>
            </el-dialog>
        </el-row>
    </div>
</template>

<script>
function pause(msec) {
    return new Promise(
        (resolve, reject) => {
            setTimeout(resolve, msec || 500);
        }
    );
}
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
import ElImageViewer from './imageviewer.vue';
import MyContainer from "@/components/my-container";
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
import pastimgVue from './pastimg.vue';
export default {
    components: { MyContainer, ElImageViewer, videoplayer, pastimgVue },
    props: {
        uploadInfo: { type: Array, default: function () { return [] } },
        // accepttyes:{ type: String, default:".excel,.jpg,.jpeg" },
        accepttyes: { type: String, default: ".xlsx,.xlsm," },
        limit: { type: Number, default: 10000 },
        delfunction: { type: Function, default: null },
        islook: { type: Boolean, default: false },
        isdown: { type: Boolean, default: false },
        minisize: { type: Boolean, default: true },
        multiple: { type: Boolean, default: false },
        imgmaxsize: { type: Number, default: 1000 },
        filemaxsize: { type: Number, default: 1000 },
        keys: { type: Array, default: function () { return [1, 1] } },
        ispaste: { type: Boolean, default: true },
        disabled: { type: Boolean, default: false },
        noDel: { type: Boolean, default: false },
        isImage: { type: Boolean, default: false },
        reveal: { type: Boolean, default: true },//是否使用粘贴组件
        value: { type: Array, default: () => [] }, // 支持 v-model
    },
    data() {
        return {
            ispastee: true,
            disabledd: false,
            retdata: [],
            accepttypeinfo: "*",
            limitnum: 100,
            uploading: false,
            imgindex: 0,
            imglist: [],
            showGoodsImage: false,
            dialogVisible: false,
            videoplayerReload: false,
            isEmitting: false, // 防止无限循环的标志
        };
    },
    watch: {
        // 监听 uploadInfo 变化，确保数据能正确回显
        uploadInfo: {
            handler(newVal) {
                if (newVal && newVal.length >= 0 && !this.isEmitting) {
                    this.isEmitting = true;
                    // 标准化数据格式，确保回显正常
                    this.retdata = this.normalizeData(newVal);
                    setTimeout(() => {
                        this.isEmitting = false;
                    }, 0);
                }
            },
            deep: true,
            immediate: true
        },
        // 监听 value 变化，支持 v-model
        value: {
            handler(newVal) {
                if (newVal && newVal.length >= 0 && !this.isEmitting) {
                    this.isEmitting = true;
                    // 标准化数据格式，确保回显正常
                    this.retdata = this.normalizeData(newVal);
                    setTimeout(() => {
                        this.isEmitting = false;
                    }, 0);
                }
            },
            deep: true,
            immediate: true
        },
        retdata: {
            handler: 'retdatafuc',
            deep: true
        }
    },
    async mounted() {
        this.ispastee = this.ispaste;
        this.disabledd = this.disabled;
        // 避免在初始化时触发循环
        this.isEmitting = true;
        // 标准化初始数据格式
        this.retdata = this.normalizeData(this.uploadInfo);
        this.$nextTick(() => {
            this.updatePasteStatus();
            this.isEmitting = false;

            // 如果有初始数据，主动触发一次回调确保父组件能处理数据格式
            if (this.retdata && this.retdata.length > 0) {
                this.$nextTick(() => {
                    // 这里不使用 emitToParent，因为不需要设置 isEmitting 标志
                    this.$emit('callback', this.retdata);
                    this.$emit('input', this.retdata);
                });
            }
        });
    },
    methods: {
        // 标准化数据格式 - 将字符串数组转换为对象数组
        normalizeData(data) {
            if (!data || !Array.isArray(data)) {
                return [];
            }

            return data.map((item, index) => {
                // 如果已经是对象格式，直接返回
                if (typeof item === 'object' && item !== null && item.url) {
                    return item;
                }

                // 如果是字符串格式，转换为对象格式
                if (typeof item === 'string') {
                    return {
                        url: item,
                        uid: Date.now() + index, // 生成唯一ID
                        status: 'success',
                        fileName: this.getFileNameFromUrl(item),
                        upLoadPhotoId: 0
                    };
                }

                return item;
            });
        },

        // 从URL中提取文件名
        getFileNameFromUrl(url) {
            if (!url) return 'unknown';
            const parts = url.split('/');
            return parts[parts.length - 1] || 'unknown';
        },

        // 触发父组件回调的公共方法
        emitToParent() {
            this.isEmitting = true;
            this.$emit('callback', this.retdata);
            this.$emit('input', this.retdata);
            setTimeout(() => {
                this.isEmitting = false;
            }, 0);
        },

        // 更新粘贴和上传按钮的状态
        updatePasteStatus() {
            const imgType = ['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp', 'apng', 'bmp', 'JPG', 'JPEG', 'PNG', 'GIF', 'SVG', 'WEBP', 'APNG', 'BMP']
            const fileType = ['xlsx', 'xlsm', 'doc', 'docx', 'pdf', 'txt']
            var isnowarr = [];
            var isfilearr = [];

            if (this.retdata?.length == 0) {
                this.disabledd = false;
            } else {
                this.retdata.map((item) => {
                    if (fileType.includes(item?.url?.split('.')[item?.url?.split('.').length - 1])) {
                        isfilearr.push(item);
                    } else if (imgType.includes(item?.url?.split('.')[item?.url?.split('.').length - 1])) {
                        isnowarr.push(item);
                    }
                })
            }

            if (!this.disabled) {
                if (isnowarr.length >= this.imgmaxsize || this.noDel) {
                    this.ispastee = false;
                } else {
                    this.ispastee = true;
                }
                if (isfilearr.length >= this.filemaxsize) {
                    this.disabledd = true;
                } else {
                    this.disabledd = false;
                }
            }
        },

        retdatafuc(e) {
            // 防止无限循环：如果正在发出事件，则跳过
            if (this.isEmitting) {
                return;
            }

            this.$nextTick(() => {
                // 更新粘贴和上传按钮状态
                this.updatePasteStatus();
                // 触发父组件回调
                this.emitToParent();
            })
        },
        playVideo(videoUrl) {
            this.videoplayerReload = false;
            this.videoplayerReload = true;
            this.dialogVisible = true;
            this.videoUrl = videoUrl;
        },
        async closeVideoPlyer() {
            this.dialogVisible = false;
            this.videoplayerReload = false;
        },
        setData(array) {
            this.retdata = array;
            // 外部设置数据后更新状态并通知父组件
            this.updatePasteStatus();
            this.emitToParent();
        },
        isformat(val) {
            const img = ['jpg', 'png', 'tif', 'gif', 'svg', 'webp', 'apng'];
            return img.some(type => val.toLowerCase().includes(type.toLowerCase()));
        },

        // 判断文件类型用于显示对应图标
        getFileType(fileName) {
            if (!fileName) return 'unknown';
            const ext = fileName.toLowerCase();
            if (ext.includes('.xlsx') || ext.includes('.xlsm')) return 'excel';
            if (ext.includes('.doc') || ext.includes('.docx')) return 'word';
            if (ext.includes('.pdf')) return 'pdf';
            if (ext.includes('.txt')) return 'txt';
            return 'image';
        },
        onDragOver(event) {
            event.preventDefault()
            const { src } = event.target
            this.targetSrc = src
        },
        onDragEnd(idx) {
            for (let num in this.retdata) {
                if (this.retdata[num].url == this.targetSrc) {
                    [this.retdata[idx], this.retdata[num]] = [this.retdata[num], this.retdata[idx]]
                    this.retdata = [...this.retdata]
                    // 拖拽排序后更新状态并通知父组件（顺序改变也需要通知）
                    this.updatePasteStatus();
                    this.emitToParent();
                }
            }
        },
        //获取返回值
        getReturns() {
            if (this.uploading) {
                this.$message({ message: "正在上传，请稍等", type: "warning" });
                return { success: false };
            }
            var curdata = [];
            this.retdata.forEach(function (item) {
                curdata.push({ domain: item.domain, fileName: item.fileName, relativePath: item.relativePath, url: item.url, upLoadPhotoId: item.upLoadPhotoId })
            });
            return { success: true, data: curdata };
        },
        //下载execl文件
        async downExecl(data) {
            // for(let num in this.retdata)
            // {
            //     await pause(500);
            //     await this.downfile(this.retdata[num]);
            // }
            await pause(500);
            await this.downfile(data);
        },
        //下载文件
        async downfile(file) {
            var xhr = new XMLHttpRequest();
            xhr.open('GET', file.url, true);
            xhr.responseType = 'arraybuffer'; // 返回类型blob
            xhr.onload = function () {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    let blob = this.response;
                    let downLoadUrl = window.URL.createObjectURL(new Blob([blob], { type: 'video/mp4' }));
                    let a = document.createElement('a');
                    a.download = file.fileName;
                    a.href = downLoadUrl;
                    a.style.display = 'none';
                    document.body.appendChild(a);
                    a.click();
                    a.remove();
                };
            };
            xhr.send();
        },
        //移除文件
        async removefile(file) {
            if (this.islook) {
                return;
            }
            //发出父级页面移除请求
            var that = this;
            if (file?.upLoadPhotoId > 0) {
                that.$confirm('此操作将会彻底删除该文件，是否执行')
                    .then(async () => {
                        await that.delfunction(file);
                        for (let num in that.retdata) {
                            if (that.retdata[num].uid == file.uid) {
                                that.retdata.splice(num, 1)
                            }
                        }
                        // 删除文件后手动更新状态并触发事件
                        that.updatePasteStatus(); // 手动更新 ispastee 状态
                        that.emitToParent();
                    })
                    .catch(_ => {
                    });
            } else {
                for (let num in this.retdata) {
                    if (this.retdata[num].uid == file.uid) {
                        this.retdata.splice(num, 1)
                        this.$refs.upload.clearFiles();
                    }
                }
                // 删除文件后手动更新状态并触发事件
                this.updatePasteStatus(); // 手动更新 ispastee 状态
                this.emitToParent();
            }
        },
        //上传超出提出
        exceed() {
            this.$message({ message: `最多只能上传${this.limit ? this.limit : 0}张`, type: "warning" });
        },
        //上传方法
        async UpSuccessload(item) {
            if (this.retdata.length >= this.limit) {
                return this.exceed();
            }
            this.uploading = true;
            await this.AjaxFile(item.file, 0, "");
            this.uploading = false;
        },
        //切片上传
        async AjaxFile(file, i, batchnumber) {
            var name = file.name; //文件名
            var size = file.size; //总大小
            var shardSize = 15 * 1024 * 1024;//2m
            var shardCount = Math.ceil(size / shardSize); //总片数
            if (i >= shardCount) {
                return;
            }
            //计算每一片的起始与结束位置
            var start = i * shardSize;
            var end = Math.min(size, start + shardSize);
            //构造一个表单，FormData是HTML5新增的
            i = i + 1;
            var form = new FormData();
            form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
            form.append("batchnumber", batchnumber);
            form.append("fileName", name);
            form.append("total", shardCount); //总片数
            form.append("index", i); //当前是第几片
            const res = await xMTVideoUploadBlockAsync(form);
            if (res?.success) {
                if (i == shardCount) {
                    res.data.fileName = name;
                    let buforeData = [];
                    let fileInfo = { url: res.data.url, name: name };
                    buforeData.push(fileInfo);
                    this.$emit('beforeUpload', buforeData);
                    res.data.uid = file.uid;
                    res.data.upLoadPhotoId = 0;
                    this.retdata.push(res.data);
                    if (this.retdata.length > this.limit) {
                        this.$message({ message: "超出上传数量限制", type: "warning" });
                        this.retdata.splice(this.retdata.length - 1, 1);
                        // 删除超出限制的文件后更新状态
                        this.updatePasteStatus();
                        return;
                    }
                    const fileName = res.data.fileName;
                    const fileType = fileName.substring(fileName.lastIndexOf('.'));
                    if (!this.isImage) {
                        // 根据 accepttyes 动态判断允许的文件类型
                        const allowedTypes = this.accepttyes.split(',').map(type => type.trim());
                        const isValidType = allowedTypes.some(type => type === fileType || type === fileType.toLowerCase());

                        if (isValidType) {
                            // 上传成功后更新状态并触发事件
                            this.updatePasteStatus(); // 手动更新状态
                            this.emitToParent();
                        } else {
                            this.$nextTick(() => {
                                this.retdata.splice(this.retdata.length - 1, 1);
                                // 删除无效文件后更新状态
                                this.updatePasteStatus();
                            })
                            this.$message({ message: `只能上传以下类型的文件: ${this.accepttyes}`, type: "warning" });
                            return
                        }
                    } else {
                        // 图片上传成功后更新状态并触发事件
                        this.updatePasteStatus(); // 手动更新状态
                        this.emitToParent();
                    }
                } else {
                    await this.AjaxFile(file, i, res.data);
                }
            } else {
                this.$message({ message: res?.msg, type: "warning" });
            }
        },
        //完成表单界面显示图片
        OpenExeclOnline(e) {
            if (e.url.split('.')[e.url.split('.').length - 1] == "mp4") {
                this.playVideo(e.url);
            } else {
                let url = process.env.VUE_APP_DOMAIN_NAME + "/" + e.relativePath
                let officeUrl = 'https://view.xdocin.com/view?src=' + url
                // 在新窗口打开编码后 的链接
                window.open(officeUrl, '_blank')
            }
        },
        //完成表单界面显示图片
        async showImg(item, j) {
            this.imglist = [];
            for (let num in this.retdata) {
                this.imglist.push(this.retdata[num].url);
            }
            this.imgindex = j;
            this.showGoodsImage = true;
        },
        //完成表单界面关闭图片
        async closeFunc() {
            this.showGoodsImage = false;
        },
        callback(e) {
            // 检查上传数量限制
            if (this.retdata.length >= this.limit) {
                this.$message({ message: `最多只能上传${this.limit}张`, type: "warning" });
                return;
            }

            // 防止重复添加相同的文件
            const existingFile = this.retdata.find(item =>
                item.url === e.data.url ||
                (item.uid && item.uid === e.data.uid)
            );

            if (!existingFile) {
                this.retdata.push(e.data);
                // 粘贴上传后更新状态并通知父组件
                this.updatePasteStatus();
                this.emitToParent();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.infinite-list-item {
    list-style: none;
    margin: 0;
    padding: 0;
}

.infinite-list-item span {
    width: 50px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.boxx {
    height: 28px;
    width: 28px;
    display: inline-block;
    margin-left: 5px;
}

.linecontent {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    overflow: hidden;
    overflow-x: auto;
    white-space: nowrap;
    margin-top: 10px;
}

.linerow {
    display: inline-block;
    margin: 5px;
}

.img-hover {
    position: relative;
    border: 1px solid rgb(237, 238, 240);
}

.img-hover:hover {
    cursor: pointer;

    .close-img,
    .close-img-dask {
        display: block;
    }
}

.close-img {
    display: none;
    position: absolute;
    right: -6px;
    top: -6px;
    color: rgb(255, 0, 0);
    font-size: 18px;
    z-index: 99999;
}

.inline-block {
    display: inline-block;

}



//   .close-img-dask {

//     display: none;

//     position: absolute;

//     top: 0;

//     left: 0;

//     width: 100%;

//     height: 40%;

//     background-color: rgba(0, 0, 0, .5) !important;

//     transition: opacity .3s;</style>
