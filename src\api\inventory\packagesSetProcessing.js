import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_PackProcess}/PackagesSetProcessing/`
//新增和更新员工，新增和更新薪资核算信息
export const addOrUpdatePersonnelPositionAsync =(params,config) =>{return request.post(apiPrefix + 'AddOrUpdatePersonnelPositionAsync',params,config)}

//人员排序
export const sortPersonnelPosition =(params,config) =>{return request.post(apiPrefix + 'SortPersonnelPosition',params,config)}

//删除人员信息
export const delPersonnelPosition =(params,config) =>{return request.get(apiPrefix + 'DelPersonnelPosition', {params: params,  ...config })}

//设置是否核算
export const setPersonnelIsHsAsync =(params,config) =>{return request.get(apiPrefix + 'SetPersonnelIsHsAsync', {params: params,  ...config })}

//获取员工设置列表/获取薪资核算列表
export const getPersonnelPositionAsync =(params,config) =>{return request.post(apiPrefix + 'GetPersonnelPositionAsync',params,config)}

//导入员工设置/薪资
export const importPackagesPersonnel =(params,config) =>{return request.post(apiPrefix + 'ImportPackagesPersonnel',params,config)}

//工作统计列表
export const getPersonnelPositionWorkInfo =(params,config) =>{return request.get(apiPrefix + 'GetPersonnelPositionWorkInfo', {params: params,  ...config })}

//历史版本存储
export const runCalculatePersonnelWorkInfo =(params,config) =>{return request.get(apiPrefix + 'RunCalculatePersonnelWorkInfo', {params: params,  ...config })}

//获取历史版本下拉信息
export const getHistoryVersionInfo =(params,config) =>{return request.get(apiPrefix + 'GetHistoryVersionInfo', {params: params,  ...config })}

//删除历史版本
export const delHistoryVersionInfo =(params,config) =>{return request.get(apiPrefix + 'DelHistoryVersionInfo', {params: params,  ...config })}

//薪资核算列表历史版本
export const getHistorytPersonnelPositionInfo =(params,config) =>{return request.post(apiPrefix + 'GetHistorytPersonnelPositionInfo', {...params,  ...config })}

//加工记录列表历史版本
export const getHistoryRecordTaskInfo =(params,config) =>{return request.get(apiPrefix + 'GetHistoryRecordTaskInfo', {params: params,  ...config })}

//加工调入调出记录列表历史版本
export const getHistoryRecordCallInOutTaskInfo =(params,config) =>{return request.get(apiPrefix + 'GetHistoryRecordCallInOutTaskInfo', {params: params,  ...config })}

//工作统计列表历史版本 /
export const getHistoryPersonnelPositionWorkInfo =(params,config) =>{return request.get(apiPrefix + 'GetHistoryPersonnelPositionWorkInfo', {params: params,  ...config })}

//钉钉发送薪资通知 /
export const sendSalery =(params,config) =>{return request.post(apiPrefix + 'SendSalery',params,config)}

//导出成品信息历史版本 /
// export const exportHistoryPackagesProcessingList =(params,config) =>{return request.post(apiPrefix + 'ExportHistoryPackagesProcessingList',params,config)}

//导出半成品信息 /
// export const exportPackagesProcessingDetialList =(params,config) =>{return request.post(apiPrefix + 'ExportPackagesProcessingDetialList',params,config)}

//获取设置信息根据设置id /
export const getPackagesSetDataById =(params,config) =>{return request.get(apiPrefix + 'GetPackagesSetDataById', {params: params,  ...config })}

//获取设置信息根据类型 //
export const getPackagesSetData =(params,config) =>{return request.get(apiPrefix + 'GetPackagesSetData', {params: params,  ...config })}

//新增编辑保存 /
export const savePackagesSet =(params,config) =>{return request.post(apiPrefix + 'SavePackagesSet',params,config)}

//删除设置
export const deletePackagesSet =(params,config) =>{return request.get(apiPrefix + 'DeletePackagesSet', {params: params,  ...config })}

//设置保存排序 /
export const saveDataOrderListDataAsync =(params,config) =>{return request.post(apiPrefix + 'SaveDataOrderListDataAsync',params,config)}

//统计列表历史版本
export const getHistoryPackagesListInfo =(params,config) =>{return request.post(apiPrefix + 'GetHistoryPackagesListInfo',params,config)}

//历史统计列表加工明细
export const getHistoryPackagesDetialTaskInfo =(params,config) =>{return request.get(apiPrefix + 'GetHistoryPackagesDetialTaskInfo', {params: params,  ...config })}

//历史统计列表右边弹窗
export const getPackagesDetialByIdAsync =(params,config) =>{return request.get(apiPrefix + 'GetPackagesDetialByIdAsync', {params: params,  ...config })}

//导出成品历史版本
export const exportHistoryPackagesProcessingList = (params, config = {responseType: 'blob'}) =>{return request.post(apiPrefix + 'ExportHistoryPackagesProcessingList',params,config)}

//导出半成品

export const exportPackagesProcessingDetialList = (params, config = {responseType: 'blob'}) =>{return request.post(apiPrefix + 'ExportPackagesProcessingDetialList',params,config)}

//每日订单统计
export const getStatDailyOrderList =(params,config) =>{return request.post(apiPrefix + 'GetStatDailyOrderList', params,config)}

//获取卸货历史数据
export const getHisUnloadListAsync =(params,config) =>{return request.post(apiPrefix + 'GetunloadListAsync', params,config)}

//导出卸货历史数据
export const getUnloadExportData =(params,config = {responseType: 'blob'}) =>{return request.post(apiPrefix + 'GetUnloadExportData', params,config)}

//获取移箱入库历史数据
export const getHisMoveStockListAsync =(params,config) =>{return request.post(apiPrefix + 'GetMoveStockListAsync', params,config)}

//获取移箱入库历史搜索人员
export const getHisMoveStockSelMemberAsync =(params,config) =>{return request.get(apiPrefix + 'GetMoveStockSelMemberAsync', {params: params,  ...config })}

//导出移箱入库历史数据
export const getHisMoveStockExportData =(params,config = {responseType: 'blob'}) =>{return request.post(apiPrefix + 'GetMoveStockExportData', params,config)}

//获取明细操作
export const getPackagesProcessingRecordListAsync1 = (params, config = {}) => { return request.get(apiPrefix + 'GetPackagesProcessingRecordListAsync', { params: params, ...config }) }

//绩效模板查看
export const getPerformTempInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformTempInfo', { params: params, ...config }) }

//编辑绩效模板
export const editPackProcessPerformanceTemplate =(params,config) =>{return request.post(apiPrefix + 'EditPackProcessPerformanceTemplate', params,config)}

//绩效考核查看
export const getPerformAssessInfo =(params,config) =>{return request.post(apiPrefix + 'GetPerformAssessInfo', params,config)}

//保存绩效打分
export const savePackProcessPerformanceAssess =(params,config) =>{return request.post(apiPrefix + 'SavePackProcessPerformanceAssess', params,config)}

//考核确认
export const confirmPerformAssess =(params,config) =>{return request.post(apiPrefix + 'ConfirmPerformAssess', params,config)}

//考核取消确认
export const unConfirmPerformAssess =(params,config) =>{return request.post(apiPrefix + 'UnConfirmPerformAssess', params,config)}

//考核取消确认
export const getHistoryPerformAssessInfo =(params,config) =>{return request.post(apiPrefix + 'GetHistoryPerformAssessInfo', params,config)}

//新增编辑绩效模板
export const addOrUpdatePackProcessPerformanceTemplate =(params,config) =>{return request.post(apiPrefix + 'AddOrUpdatePackProcessPerformanceTemplate', params,config)}

//删除绩效模板
export const delPackProcessPerformanceTemplate =(params,config) =>{return request.get(apiPrefix + 'DelPackProcessPerformanceTemplate', { params: params, ...config })}

//薪资核算导出
export const exportPersonnelSalary = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPersonnelSalary',  params, config ) }

//薪资核算历史版本导出
export const exportPersonnelSalaryHistory = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPersonnelSalaryHistory',  params, config) }


//批量打印绩效模板
export const printPerformAssessAllList =(params,config) =>{return request.post(apiPrefix + 'PrintPerformAssessAllList', params,config)}


//绩效考核模板保存排序
export const sortPerformTempList =(params,config) =>{return request.post(apiPrefix + 'SortPerformTempList', params,config)}

//新增编辑绩效模板
export const confirmPerformAssessAll = (params,config) =>{return request.post(apiPrefix + 'ConfirmPerformAssessAll', params,config)}

//新增模板备注
export const addPerformTempRemark = (params,config) =>{return request.post(apiPrefix + 'AddPerformTempRemark', params,config)}

//删除模板备注
export const delPerformTempRemark = (params, config = {}) => { return request.delete(apiPrefix + 'DelPerformTempRemark', { params: params, ...config })}

//备注编辑回显数据
export const getPerformTempRemark = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformTempRemark', { params: params, ...config }) }

//新增编辑绩效模板
export const resetSalaryData = (params,config) =>{return request.post(apiPrefix + 'ResetSalaryData', params,config)}

//员工设置工种列表
export const getWorkTypeList = (params, config = {}) => { return request.get(apiPrefix + 'GetWorkTypeList', { params: params, ...config }) }

//获取确定版本信息
export const getMainStatVersionInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetMainStatVersionInfo', { params: params, ...config }) }

//获取版本信息
export const getMainStatVersionList = (params, config = {}) => { return request.get(apiPrefix + 'GetMainStatVersionList', { params: params, ...config }) }

//新增版本信息
export const addainStatVersionList = (params,config) =>{return request.post(apiPrefix + 'AddainStatVersionList', params,config)}

//版本存档
export const saveMainStatVersionList = (params,config) =>{return request.post(apiPrefix + 'SaveMainStatVersionList', params,config)}

//确定版本
export const confirmMainStatVersionList = (params,config) =>{return request.post(apiPrefix + 'ConfirmMainStatVersionList', params,config)}

//获取信息
export const getMainStatVersionInfoById = (params, config = {}) => { return request.get(apiPrefix + 'GetMainStatVersionInfoById', { params: params, ...config }) }

//删除版本
export const delMainStatVersionInfoById = (params, config = {}) => { return request.delete(apiPrefix + 'DelMainStatVersionInfoById', { params: params, ...config })}

//获取调配工价设置
export const getDeployPriceSet = (params, config = {}) => { return request.get(apiPrefix + 'GetDeployPriceSet', { params: params, ...config }) }

//编辑调配工价设置
export const editDeployPriceSet = (params,config) =>{return request.post(apiPrefix + 'EditDeployPriceSet', params,config)}

//删除调配工价设置
export const delDeployPriceSet = (params, config = {}) => { return request.get(apiPrefix + 'DelDeployPriceSet', { params: params, ...config }) }

//保存排除类型
export const saveExcludeType = (params,config) =>{return request.post(apiPrefix + 'SaveExcludeType', params,config)}

//获取调配工价配置日志
export const getDeployPriceLogList = (params,config) =>{return request.post(apiPrefix + 'GetDeployPriceLogList', params,config)}

//分页获取历史版本信息
export const getHistoryVersionInfoPage = (params,config) =>{return request.post(apiPrefix + 'GetHistoryVersionInfoPage', params,config)}

//删除回收站中历史版本
export const delFromRecycleBin = (params, config = {}) => { return request.get(apiPrefix + 'DelFromRecycleBin', { params: params, ...config }) }

//恢复回收站中历史版本
export const restoreFromRecycleBin = (params, config = {}) => { return request.get(apiPrefix + 'RestoreFromRecycleBin', { params: params, ...config }) }
