import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/warehousingordervideo/`

// 入库拍摄查询分页
export const pageWarehousingOrderVideo = (params, config = {}) => {
    return request.get(apiPrefix + 'PageWarehousingOrderVideoAsync', { params: params, ...config }) }

// 入库拍摄加急查询分页
export const pageWarehousingOrderVideoUrgent = (params, config = {}) => {
    return request.get(apiPrefix + 'PageWarehousingOrderVideoUrgentAsync', { params: params, ...config }) }

// 采购单数据分页
export const pagePurchaseOrder = (params, config = {}) => {
    return request.get(apiPrefix + 'PagePurchaseOrderAsync', { params: params, ...config }) }

// 入库拍摄明细分页
export const pageWarehousingOrderVideoDetail = (params, config = {}) => {
    return request.get(apiPrefix + 'PageWarehousingOrderVideoDetailAsync', { params: params, ...config }) }

// 采购单远程搜索
export const getListBuyNo = (params, config = {}) => {
    return request.get(apiPrefix + 'GetListBuyNoAsync', { params: params, ...config }) }

// 绑定采购单
export const bindWarehousingBuyNo = (params, config = {}) => {
    return request.post(apiPrefix + 'BindWarehousingBuyNoAsync', params, config) }

// 设置金额
export const saveWarehousingAmont = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveWarehousingAmontAsync', params, config) }

//  导出数据
export const exportWarehousingOrderVideoListAsync = (params, config = {responseType: 'blob'}) => {
  return request.post(apiPrefix + 'ExportWarehousingOrderVideoListAsync', params, config) }

// 未绑定采购单条数
export const getIsBindBuyNoMsg = (params, config = {}) => {
    return request.get(apiPrefix + 'GetIsBindBuyNoMsgAsync', { params: params, ...config }) }

// 操作日志
export const getPurchaseQualityGoodsRecordLogAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseQualityGoodsRecordLogAsync', params, config) }    

// 质检备注
export const getWarehousingVideoRemarkAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehousingVideoRemarkAsync', params, config) }        