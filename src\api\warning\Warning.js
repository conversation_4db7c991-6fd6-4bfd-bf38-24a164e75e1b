import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Warning}/Warning/`

// 物流预警-查询
export const getLogisticsWarningPageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetLogisticsWarningPageList', params, config) }

// 物流预警-导出前的分组
export const getLogisticsWarningGroupWareData = (params, config = {}) => {
  return request.post(apiPrefix + 'GetLogisticsWarningGroupWareData', params, config) }

// 物流预警-导出
export const exportLogisticsWarningDataAyync = (params, config = {responseType: 'blob'}) => {
  return request.post(apiPrefix + 'ExportLogisticsWarningDataAyync', params, config) }

// 物流预警-导入
export const importLogisticsWarningDataAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'ImportLogisticsWarningDataAsync', params, config) }

// 设置机器人操作时间结果（揽收）
export const noticeLogisticsWarning = (params, config = {}) => {
  return request.post(apiPrefix + 'NoticeLogisticsWarning', params, config) }
