import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/allocate/`
export const inventoryQuery = (params, config = {}) => { return request.get(apiPrefix + 'InventoryQueryAsync',  { params: params, ...config })}
export const getAllocateList = (params, config = {}) => { return request.get(apiPrefix + 'GetAllocateListAsync',  { params: params, ...config })}
export const creatAllocate = (params, config = {}) => { return request.post(apiPrefix + 'CreatAllocateAsync', params, config) }
export const warehouseTransferDetails = (params, config = {}) => { return request.get(apiPrefix + 'WarehouseTransferDetails',  { params: params, ...config })}
export const exportAllocateList = (params, config = {responseType: 'blob'}) => { return request.get(apiPrefix + 'ExportAllocateListAsync',  { params: params, ...config })}
export const getUnBindRecord = (params, config = {}) => { return request.get(apiPrefix + 'GetUnBindRecord',  { params: params, ...config })}
//获取通知条件
export const getInventoryAllocateSetting=(params, config = {}) => { return request.post(apiPrefix + 'GetInventoryAllocateSets',  { params: params, ...config })}
//获取调拨记录
export const getInventoryAllocate = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'GetInventoryAllocate', params, config)
  }
//设置通知条件
export const setInventoryAllocateSetting = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'SetInventoryAllocate', params, config)
  }
//导出数据
export const exportInventoryAllot = (params, config = { responseType: 'blob' }) => {
  params = params || {}
  return request.post(apiPrefix + 'ExportInventoryAllot', params, config)
}
//展示导出日志
export const getInventoryAllotLog= (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'GetInventoryAllotLog', params, config)
}
//一键通知
export const clickNotifyInventoryAllocate= (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'ClickNotifyInventoryAllocate', params, config)
}
//h5界面获取通知信息
export const GetH5PageData= (params, config = {}) =>{
  params = params || {}
  return request.get(apiPrefix + 'GetH5PageData', params, config)
}