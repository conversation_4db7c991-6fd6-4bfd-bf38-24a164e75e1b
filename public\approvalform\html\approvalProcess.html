<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="referrer" content="no-referrer" />
  <!-- elment样式 -->
  <link rel="stylesheet" href="/approvalform/html/api/elment.css" />
  <!-- vue -->
  <script src="/approvalform/html/api/vue.min.js"></script>
  <!-- elment脚本 -->
  <script src="/approvalform/html/api/elment.js"></script>
  <!-- jquery -->
  <script src="/approvalform/html/api/jquery.min.js"></script>

  <script src="/approvalform/html/api/html2canvas.js"></script>

  <title>采购运费核对</title>
  <style type="text/css">
    .linebreak {
      overflow: hidden;
      /*超出部分隐藏*/

      text-overflow: ellipsis;
      /* 超出部分显示省略号 */
      white-space: normal;
      /*规定段落中的文本不进行换行 */
      width: 100%;
    }

    .linebreak1 {
      /* 超出隐藏,给省略号,换三行 */
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      width: 100%;
    }

    ::v-deep .custom-image img {
      max-width: 40px !important;
      max-height: 40px !important;
    }

    .circle-badge {
      position: absolute;
      top: 0px;
      right: 58px;
      background-color: red;
      color: white;
      font-size: 10px;
      width: 18px;
      height: 18px;
      line-height: 18px;
      text-align: center;
      border-radius: 50%;
    }
    .image-slot{
        color: #8d8d8d;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        width: 100%;
    }

    .imgcss img {
      min-width: 55px !important;
      min-height: 55px !important;
      width: 55px !important;
      height: 55px !important;
    }

    html,
    body {
      height: 100%;
      overflow-y: hidden;
    }
  </style>
</head>

<body>
  <div id="app" style="margin:0 auto;height: 100%;">
    <div ref="oneboxx" style="height: 98%;">
      <el-descriptions title="申请信息" :column="7" border style="margin: 20px;">
        <el-descriptions-item label="所在部门">{{extData.deptName}}</el-descriptions-item>
        <el-descriptions-item label="申请事由">{{extData.applyReason}}</el-descriptions-item>
        <el-descriptions-item label="使用部门">{{extData.useWare}}</el-descriptions-item>
        <el-descriptions-item label="使用平台">{{extData.usePlatform}}</el-descriptions-item>
        <el-descriptions-item label="费用分类">{{extData.expenseType}}</el-descriptions-item>
        <el-descriptions-item label="开户行">{{extData.openingBank}}</el-descriptions-item>
        <el-descriptions-item label="最晚支付日期">{{extData.lastPaymentDate}}</el-descriptions-item>
        <el-descriptions-item label="金额(元)">{{extData.totalPrice}}</el-descriptions-item>
        <el-descriptions-item label="大写">{{extData.totalPriceCN}}</el-descriptions-item>
        <el-descriptions-item label="付款方式">{{extData.payment}}</el-descriptions-item>
        <el-descriptions-item label="账户名">{{extData.accountName}}</el-descriptions-item>
        <el-descriptions-item label="账号">{{extData.accountNumber}}</el-descriptions-item>
        <el-descriptions-item label="核算单位">{{extData.checkUnit}}</el-descriptions-item>
      </el-descriptions>
      <el-table ref="table" v-if="tableshow" :data="list" row-key="id" border style="width: 100%" height="80%" show-overflow-tooltip :show-summary="true" :summary-method="getSummaries">
        <el-table-column type="index" label="#" align="center" width="50"></el-table-column>
        <el-table-column label="发生日期" prop="fsYmdDate" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="区域费用" prop="regionCost" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="入库单号" prop="warehousingNo" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="采购单号1" prop="buyNo1" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="采购单号2" prop="buyNo2" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="采购" prop="brandName" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="仓库" prop="warehouseName" width="170" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="单票合计" prop="totalPerTicket" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="单号1费用" prop="orderFee1" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="单号2费用" prop="orderFee2" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="托运费" prop="haulage" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="送货费" prop="deliveryFee" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="提货费" prop="pickUpFee" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="货拉拉" prop="huoLaLa" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="装车费" prop="loadingFee" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="备注" prop="remark" width="120" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="凭证1" prop="proof1" width="120" :show-overflow-tooltip="true">
            <template #default="scope">
                <div style="width: 100%; height: 100%; position: relative;">
                    <div v-if="imagechange(scope.row.proof1, 2).length>1" style="z-index: 99; position: absolute; top: 0; right: 0; width: 20px; height: 20px; border-radius: 10px; color: white; background-color: red; display: flex; justify-content: center; align-items: center;">{{imagechange(scope.row.proof1, 2).length}}</div>
                    <el-image style="height: 50px; width: auto;" fill="none" :src="imagechange(scope.row.proof1)" :preview-src-list="imagechange(scope.row.proof1, 2)" lazy>
                        <div slot="error" class="image-slot">
                            暂无图片
                        </div>
                    </el-image>
                </div>
            </template>
        </el-table-column>
        <el-table-column label="凭证2" prop="proof2" width="120" :show-overflow-tooltip="true">
            <template #default="scope">
                <div style="width: 100%; height: 100%; position: relative;">
                    <div v-if="imagechange(scope.row.proof2, 2).length>1" style="z-index: 99;position: absolute; top: 0; right: 0; width: 20px; height: 20px; border-radius: 10px; color: white; background-color: red; display: flex; justify-content: center; align-items: center;">{{imagechange(scope.row.proof2, 2).length}}</div>
                    <el-image style="height: 50px; width: auto;" fill="none" :src="imagechange(scope.row.proof2)" :preview-src-list="imagechange(scope.row.proof2, 2)" lazy>
                        <div slot="error" class="image-slot">
                            暂无图片
                        </div>
                    </el-image>
                </div>
            </template>
        </el-table-column>
        </el-table-column>
        <el-table-column label="凭证3" prop="proof3" width="120" :show-overflow-tooltip="true">
            <template #default="scope">
                <div style="width: 100%; height: 100%; position: relative;">
                    <div v-if="imagechange(scope.row.proof3, 2).length>1" style="z-index: 99;position: absolute; top: 0; right: 0; width: 20px; height: 20px; border-radius: 10px; color: white; background-color: red; display: flex; justify-content: center; align-items: center;">{{imagechange(scope.row.proof3, 2).length}}</div>
                    <el-image style="height: 50px; width: auto;" fill="none" :src="imagechange(scope.row.proof3)" :preview-src-list="imagechange(scope.row.proof3, 2)" lazy>
                        <div slot="error" class="image-slot">
                            暂无图片
                        </div>
                    </el-image>
                </div>
            </template>
        </el-table-column>
        </el-table-column>
        <el-table-column label="凭证4" prop="proof4" width="120" :show-overflow-tooltip="true">
            <template #default="scope">
                <div style="width: 100%; height: 100%; position: relative;">
                    <div v-if="imagechange(scope.row.proof4, 2).length>1" style="z-index: 99;position: absolute; top: 0; right: 0; width: 20px; height: 20px; border-radius: 10px; color: white; background-color: red; display: flex; justify-content: center; align-items: center;">{{imagechange(scope.row.proof4, 2).length}}</div>
                    <el-image style="height: 50px; width: auto;" fill="none" :src="imagechange(scope.row.proof4)" :preview-src-list="imagechange(scope.row.proof4, 2)" lazy>
                        <div slot="error" class="image-slot">
                            暂无图片
                        </div>
                    </el-image>
                </div>
            </template>
        </el-table-column>
        </el-table-column>
      </el-table>
    </div>
  </div>
  <script>
    var vm = new Vue({
      el: "#app",
      data: function () {
        return {
          thisLonding: true,
          list: [],
          extData: {
            lastPaymentDate: "",
            deptName: "",
            totalPrice: "",
            applyReason: "",
            totalPriceCN: "",
            usePlatform: "",
            payment: "",
            expenseType: "",
            accountName: "",
            openingBank: "",
            accountNumber: "",
            checkUnit: "",
            useWare: "",
          },
          tableshow: true,
        };
      },
      created() { },
      async mounted() {
        this.getStyleSheetInfo();
      },
      methods: {
        imagechange(val, paper = 1){
            if(!val){
                return "";
            }
            let imagArr = JSON.parse(val);
            if(paper == 1){
                return imagArr[0].url;
            }
            let newImagArr = [];
            imagArr.map((item)=>{
                newImagArr.push(item.url);
            })
            return newImagArr;
        },
        async getStyleSheetInfo() {
          var me = this;
          let searchURL = window.location.search;
          searchURL = searchURL.substring(1, searchURL.length);
          let noticeId = searchURL.split("=")[1];
          me.thisLonding = true;
          me.tableshow = false;
          let parm = {};
          //console.log("Request URL:", "/api/Inventory/PurchaseCostVerify/GetApplyH5DataPage?batchNo=" + this.batchNo);
          $.ajax({
            url: "/api/inventory/PurchaseCostVerify/GetApplyH5DataPage?batchNo=" + noticeId,
            type: "POST",
            headers: {
              'Content-Type': 'application/json'
            },
            dataType: "json",
            data: parm,
            success: function (response) {
              // 在这里打印获取到的数据进行调试
              me.list = response.data.list;
              me.extData = response.data?.extData
              for (let i = 0; i < me.list.length; i++) {
                me.list[i].fsYmdDate = me.list[i].fsYmdDate.substring(0, 10);
              }
             me.tableshow = true;

            },
            error: function (xhr, textStatus, errorThrown) {
              console.log("Error: ", errorThrown);
            },
          });
        },
        getSummaries(param) {
          console.log("getSummaries",1);
          const { columns, data } = param
          const sums = []
          columns.forEach((column, index) => {
              if (index === 0) {
                  sums[index] = '合计';
                  return;
              }
              if (data == null)
                  return;
              const values = data.map(item => Number(item[column.property]));
              if (column.property == 'totalPerTicket' || column.property == 'orderFee1'
                || column.property == 'orderFee2' || column.property == 'haulage'
                || column.property == 'deliveryFee' || column.property == 'pickUpFee' || column.property == 'huoLaLa' || column.property == 'loadingFee') {
                  sums[index] = values.reduce((prev, curr) => {
                      const value = Number(curr);
                      if (!isNaN(value)) {
                          return prev + curr;
                      } else {
                          return prev;
                      }
                  }, 0).toFixed(2);
              }
          });
          return sums
        }
      },
    });
  </script>
</body>

</html>
