import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Financial}/SampleVerify/`

///查询寄样拿样
export const QuerySampleVerify = (params, config = {}) => {return request.post(apiPrefix + 'QuerySampleVerify',  params, config)}
///导出寄样拿样
export const ExportSampleVerify = (params, config = { responseType: 'blob' }) => {return request.post(apiPrefix + 'ExportSampleVerify',  params, config)}
///获取审批编码
export const QueryBusinesseId = (params, config = {}) => {return request.get(apiPrefix + 'QueryBusinesseId', { params: params, ...config })}
