import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_MonthBookKeeper}/FinancialReport/`

// export const pageFinancialReport = (params, config = {}) => {return request.get(apiPrefix + 'GetFinancialReportList', { params: params, ...config })}
// export const getOperatingProfitAnalysisSum = (params, config = {}) => {return request.get(apiPrefix + 'GetOperatingProfitAnalysisSum', { params: params, ...config })}
// export const getShopProfitAnalysisSum = (params, config = {}) => {return request.get(apiPrefix + 'GetShopProfitAnalysisSum', { params: params, ...config })}
export const exportFinancialReport = (params, config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportFinancialReport', params, config)}
// export const exportMonthReportDetail2 = (params, config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportMonthReportDetail2', params, config)}
// export const exportOperatingProfitAnalysisSum = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'ExportOperatingProfitAnalysisSumAsync',  params,config)}
// export const exportShopProfitAnalysisSum = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'ExportShopProfitAnalysisSumAsync',  params,config)}
// export const getProductProfitAnalysis = (params, config = {}) => {return request.get(apiPrefix + 'GetProductProfitAnalysis', { params: params, ...config })}
// export const getDetail1Tx = (params, config = {}) => {return request.get(apiPrefix + 'GetDetail1Tx', { params: params, ...config })}
// export const getDetail2Tx = (params, config = {}) => {return request.get(apiPrefix + 'GetDetail2Tx', { params: params, ...config })}
// export const getDetail1Pdd = (params, config = {}) => {return request.get(apiPrefix + 'GetDetail1Pdd', { params: params, ...config })}
// export const getDetail2Pdd = (params, config = {}) => {return request.get(apiPrefix + 'GetDetail2Pdd', { params: params, ...config })}


export const GetMonthBusinessWiseManReport = (params, config = {}) => { return request.post(apiPrefix + 'GetMonthBusinessWiseManReport', params, config) }
export const ExportMonthBusinessWiseManReport = (params, config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportMonthBusinessWiseManReport', params, config)}



export const GetMonthBusinessReport = (params, config = {}) => { return request.post(apiPrefix + 'GetMonthBusinessReport', params, config) }
export const ExportMonthBusinessReport = (params, config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportMonthBusinessReport', params, config)}


