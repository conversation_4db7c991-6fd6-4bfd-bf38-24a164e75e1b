import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/purchaseorder/`
  
export const editPurchaseOrder =(params,config) =>{return request.post(apiPrefix + 'EditPurchaseOrderAsync',params,config)}
export const editPurchasePlan =(params,config) =>{return request.post(apiPrefix + 'EditPurchasePlanAsync',params,config)}
export const getLastUpdateTimeyPurchase = (params, config = {}) => {return request.get(apiPrefix + 'GetLastUpdateTimePurchaseAsync', { params: params, ...config })}
export const getLastUpdateTimeyPurchasePlan = (params, config = {}) => {return request.get(apiPrefix + 'GetLastUpdateTimePurchasePlanAsync', { params: params, ...config })}
export const getLastUpdateTimeyPurchasePlan2 = (params, config = {}) => {return request.get(apiPrefix + 'GetLastUpdateTimePurchasePlan2Async', { params: params, ...config })}
export const getLastUpdateTimeWarehousingOrder = (params, config = {}) => {return request.get(apiPrefix + 'GetLastUpdateTimeWarehousingOrderAsync', { params: params, ...config })}
export const pageGoodsCodeRecord = (params, config = {}) => {return request.get(apiPrefix + 'PageGoodsCodeRecordAsync', { params: params, ...config })}
export const getPurchasePlan = (params, config = {}) => {return request.get(apiPrefix + 'GetPurchasePlanAsync', { params: params, ...config })}
export const getPurchaseOrder = (params, config = {}) => {return request.get(apiPrefix + 'GetPurchaseOrderAsync', { params: params, ...config })}
export const PagePurOrder = (params, config = {}) => {return request.get(apiPrefix + 'PagePurOrderAsync',  { params: params, ...config })}
export const queryPurchaseOrderDetail = (params, config = {}) => {return request.get(apiPrefix + 'QueryPurchaseOrderDetailAsync',  { params: params, ...config })}

export const queryWarehousingOrderDetail = (params, config = {}) => {return request.get(apiPrefix + 'QueryWarehousingOrderDetailAsync', { params: params, ...config })}
export const pageWarehousingOrder = (params, config = {}) => {return request.post(apiPrefix + 'PageWarehousingOrderAsync',  params, config)}
export const importPurchaseOrder = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchaseOrderAsync', params, config)}
export const importWarehousingOrder = (params, config = {}) => { return request.post(apiPrefix + 'ImportWarehousingOrderAsync', params, config)}
export const importPurchasePlan = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchasePlanAsync', params, config)}
export const exportPurchasePlan = (params, config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportPurchasePlanAsync', params, config)}
export const queryLogistics = (params, config = {}) => {return request.get(apiPrefix + 'QueryLogisticsAsync', { params: params, ...config })}

export const getPurchasePlan2Parm = (params, config = {}) => {return request.get(apiPrefix + 'GetPurchasePlan2ParmAsync', { params: params, ...config })}
export const editPurchasePlan2Parm = (params, config = {}) => {return request.post(apiPrefix + 'EditPurchasePlan2ParmAsync',params, config)}
export const editPurchasePlan2 = (params, config = {}) => {return request.post(apiPrefix + 'EditPurchasePlan2Async',  params, config)}
export const pagePurchasePlan2 = (params, config = {}) => {return request.post(apiPrefix + 'PagePurchasePlan2Async',  params, config)}
export const getPurchasePlan2 = (params, config = {}) => {return request.get(apiPrefix + 'GetPurchasePlan2Async', { params: params, ...config })}
export const pagePurchasePlan2Record = (params, config = {}) => {return request.get(apiPrefix + 'PagePurchasePlan2RecordAsync', { params: params, ...config })}
export const importPurchasePlan2 = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchasePlan2Async', params, config)}
export const exportPurchasePlan2 = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'ExportPurchasePlan2Async',  params, config)}
export const exportPurchaseOrder = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'ExportPurchaseOrderAsync',  params, config)}
//获取30天销售量
// export const getOrderChartsOrderCount = (params,config ={}) =>{
//     return request.post(apiPrefix+'GetPurOrderChartsOrderCountAsync', params, config)
// }
export const getOrderChartsOrderCount = (params, config = {}) => {return request.get(apiPrefix + 'GetPurOrderChartsOrderCountAsync',  { params: params, ...config })}

export const PreviewCreate1688PurchaseOrder = (params, config = {}) => {return request.post(apiPrefix + 'PreviewCreate1688PurchaseOrder', params, config)}

//生成1688订单 Create1688PurchaseOrder       
export const Create1688PurchaseOrder = (params, config = {}) => {return request.post(apiPrefix + 'Create1688PurchaseOrder', params, config)}

//保存采购常用备注 SaveInventoryUserCommonSet
export const SaveInventoryUserCommonSet = (params, config = {}) => {return request.post(apiPrefix + 'SaveInventoryUserCommonSet', params, config)}

//删除采购常用备注 DeleteInventoryUserCommonSet
export const DeleteInventoryUserCommonSet = (params, config = {}) => {return request.post(apiPrefix + 'DeleteInventoryUserCommonSet', params, config)}

//查看采购单改价记录 GetAliCalBackChangePriceRecordList
export const GetAliCalBackChangePriceRecordList = (params, config = {}) => {return request.post(apiPrefix + 'GetAliCalBackChangePriceRecordList',  params, config )}

//查询阿里账号 GetBianMaBrandAccountList
export const GetBianMaBrandAccountList = (params, config = {}) => {return request.post(apiPrefix + 'GetBianMaBrandAccountList', params, config)}

//导出阿里账号 ExportDeptComplaintFile
export const ExportAliAccountList = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'ExportAliAccountList', params, config)}

//编辑阿里账号 UpdateBianMaBrandAccount
export const UpdateBianMaBrandAccount = (params, config = {}) => {return request.post(apiPrefix + 'UpdateBianMaBrandAccount', params, config)}

//批量编辑阿里账号 BulkUpdateBianMaBrandAccount
export const BulkUpdateBianMaBrandAccount = (params, config = {}) => {return request.post(apiPrefix + 'BulkUpdateBianMaBrandAccount', params, config)}

//导入阿里账号 ExportBianMaBrandAccountFile
export const ExportBianMaBrandAccountFile = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'ExportBianMaBrandAccountFile', params, config)}

//获取阿里供应商ID，聊天使用 GetAliSupperChatIdByEdit
export const GetAliSupperChatIdByEdit = (params, config = {}) => {return request.post(apiPrefix + 'GetAliSupperChatIdByEdit', params, config)}

//采购单跟进发起聊天 GetAliSupperChatIdByShow  
export const GetAliSupperChatIdByShow = (params, config = {}) => {return request.post(apiPrefix + 'GetAliSupperChatIdByShow', params, config)}