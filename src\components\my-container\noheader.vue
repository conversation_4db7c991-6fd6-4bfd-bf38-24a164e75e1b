<template>
  <section style="height:100%;">
    <!-- style="height:100%;position:relative;   style="padding:0px 5px;"" -->
    <el-container style="height:100%;">
      <el-main style="height:100%;">
        <slot />
      </el-main>
      <el-footer height style="padding:0;">
        <slot name="footer" />
      </el-footer>
    </el-container>
  </section>
</template>

<script>
/**
 * 容器组件
 * 使用说明
<my-container>
  <template #header>
    <el-form />
  </template>
  <el-table />
  <template #footer>
    <my-pagination />
  </template>
</my-container>
 */

export default {
  name: 'MyContainer'
}
</script>
