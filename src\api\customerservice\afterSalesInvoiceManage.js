
import request from '@/utils/request'
const GroupPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/AfterSalesInvoiceManage/`

//分页获取售后发票管理(客服)分页
export const getAfterSalesInvoiceManageCustomerPage = (params, config = {}) => { return request.post(GroupPrefix + 'GetAfterSalesInvoiceManageCustomerPage', params, config) }

//保存售后发票数据
export const saveAfterSalesInvoiceManageCustomer = (params, config = {}) => { return request.post(GroupPrefix + 'SaveAfterSalesInvoiceManageCustomer', params, config) }

//作废售后发票数据
export const nullifyAfterSalesInvoiceManageData = (params, config = {}) => { return request.post(GroupPrefix + 'NullifyAfterSalesInvoiceManageData', params, config) }

//验证售后发票管理订单
export const verifyAfterSalesInvoiceManageOrder = (params, config = {}) => { return request.post(GroupPrefix + 'VerifyAfterSalesInvoiceManageOrder', params, config) }
