import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API}/ViewUShield/`

// 获取U盾用户的角色
export const GetRoleTrees = (params, config = {}) => {
  return request.get(apiPrefix + 'GetRoleTrees', { params: params, ...config })
}
// 获取所有需要U盾的视图
export const Getlist = (params, config = {}) => {
  return request.get(apiPrefix + 'Getlist', { params: params, ...config })
}
// 查询角色U盾视图权限
export const GetRoleUShieldViewPermissionList = (params, config = {}) => {
  return request.get(apiPrefix + 'GetRoleUShieldViewPermissionList', {
    params: params,
    ...config
  })
}
// 查询用户U盾视图权限
export const GetUserUShieldViewPermissionList = (params, config = {}) => {
  return request.get(apiPrefix + 'GetUserUShieldViewPermissionList', {
    params: params,
    ...config
  })
}

// 保存角色
export const RoleUShieldAssign = (params, config = {}) => {
  return request.post(apiPrefix + 'RoleUShieldAssign', params, config)
}
// 保存用户
export const UserUShieldAssign = (params, config = {}) => {
  return request.post(apiPrefix + 'UserUShieldAssign', params, config)
}

// U盾页
const apiPrefix2 = `${process.env.VUE_APP_BASE_API}/UserUShield/`
// 查询分页用户U盾
export const GetPage = (params, config = {}) => {
  return request.post(apiPrefix2 + 'GetPage', params, config)
}
// 增删改查
export const SaveUserUShield = (params, config = {}) => {
  return request.post(apiPrefix2 + 'SaveUserUShield', params, config)
}
export const DelUserUShield = (params, config = {}) => {
  return request.delete(apiPrefix2 + 'DelUserUShield', {
    params: params,
    ...config
  })
}

// 授权
export const ushieldAuth = (params, config = {}) => { return request.post(apiPrefix2 + 'ushieldauth', params, { ...config }) }

// 是否配置了U盾
export const isUShieldUser = () => { return request.get(apiPrefix2 + 'IsUShieldUser') }
