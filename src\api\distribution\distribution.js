import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Distribution}/Distribution/`

//商品信息
export const DistributionDetailsList = (params, config = {}) => { return request.get(apiPrefix + 'DistributionDetailsAdminList', {params: params}, config) }

//类目
export const getDistributionCategoryList = (params, config = {}) => { return request.get(apiPrefix + 'GetDistributionCategoryList', params, config) }

//批量编辑供货价
export const updateSkuBaseSale = (params,config={}) => { return request.post(apiPrefix + 'UpdateSkuBaseSale', params, config) }
