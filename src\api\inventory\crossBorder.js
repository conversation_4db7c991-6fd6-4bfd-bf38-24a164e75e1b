import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/CrossBorder/`

//分页获取出库箱接口
export const getCrossBorderOutBoxPcPage = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'GetCrossBorderOutBoxPcPage', params, config)
}

//获取出库箱明细
export const getCrossBorderOutBoxEntity = (params, config = {}) => { return request.get(apiPrefix + 'GetCrossBorderOutBoxEntity', { params: params, ...config }) }

//分页获取装箱柜接口
export const getCrossBorderPackingCabinetPcPage = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'GetCrossBorderPackingCabinetPcPage', params, config)
}

//导出出库箱
// export const ExportOutBoxAsync=(params,config = { responseType: 'blob' }) =>{return request.get(apiPrefix+'ExportOutBoxAsync',{params:params,...config})}
export const exportOutBoxAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportOutBoxAsync', params, config) }

//导出装箱柜
export const exportPackingCabinetAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPackingCabinetAsync', params, config) }

//更新出库箱数据
export const UpdateCrossBorderOutBoxPcPage = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'UpdateCrossBorderOutBoxPcPage', params, config)
}

//更新装箱柜数据
export const UpdateCrossBorderPackingCabinetPcPage = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'UpdateCrossBorderPackingCabinetPcPage', params, config)
}

//获取日志
export const GetCrossBorderOutBoxLogEntity = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'GetCrossBorderOutBoxLogEntity', params, config)
}

//导入装箱柜数据
export const ImportCrossBorderPackingCabinetPcPage = (params, config = {}) => { return request.post(apiPrefix + 'ImportCrossBorderPackingCabinetPcPage',  params, config ) }

//导入出库箱数据
export const ImportCrossBorderOutBoxPcPage = (params, config = {}) => { return request.post(apiPrefix + 'ImportCrossBorderOutBoxPcPage',  params, config ) }

//跨境出库 装箱柜 修改备注
export const UpdateCrossBorderPackingCabinetRemark = (params, config = {}) => { return request.post(apiPrefix + 'UpdateCrossBorderPackingCabinetRemark',  params, config ) }

