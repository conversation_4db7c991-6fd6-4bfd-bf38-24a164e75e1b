import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_JAVA_API_BLADEGATEWAY}/yunhan-gis-tradingchain/`
// const apiPrefix = `/yunhan-gis-tradingchain/`


// 查询员工列表
// export const employeeList = (params, config = {}) => { return request.post(apiPrefix + 'employeeList', params, config) }


//仓库监控列表
export const equipmentAlarmLogPage = (params, config = {}) => {return request.post(apiPrefix + 'equipmentAlarmLogPage',  params,  config )}

//配置列表
export const equipmentAlarmSettingPage = (params, config = {}) => {return request.post(apiPrefix + 'equipmentAlarmSettingPage',  params,  config )}

//配置列表薪资
export const equipmentAlarmSettingSubmit = (params, config = {}) => {return request.post(apiPrefix + 'equipmentAlarmSettingSubmit',  params,  config )}

//删除
export const equipmentAlarmSettingRemove = (params, config = {}) => { return request.post(apiPrefix + 'equipmentAlarmSettingRemove?ids='+params.id, params, config) }

// //字典列表
// export const dictionary = (params, config = {}) => {return request.post(apiPrefix + 'dictionary?code='+params,  params,  config )}

// //获取列表下拉
// export const departmentRelationList = (params, config = {}) => {return request.post(apiPrefix + 'departmentRelationList',  params,  config )}

// //获取所有区域 传1
// export const departmentListByParentId = (params, config = {}) => {return request.post(apiPrefix + 'departmentListByParentId',  params,  config )}

//新增配置
export const specialWarehouseSubmit = (params, config = {}) => {return request.post(apiPrefix + 'specialWarehouseSubmit',  params,  config )}


//top列表
export const specialWarehousePage = (params, config = {}) => {return request.post(apiPrefix + 'specialWarehousePage',  params,  config )}

//删除
export const specialWarehouseRemove = (params, config = {}) => { return request.post(apiPrefix + 'specialWarehouseRemove?ids='+params.id, params, config) }


//下拉获取
export const specialWarehouseShippingAreaList = (params, config = {}) => {
    return request.get(apiPrefix + 'specialWarehouseShippingAreaList', { params: params, ...config })
  }

  //计算
export const specialWarehouseExpressFeeCalculate = (params, config = {}) => {return request.post(apiPrefix + 'specialWarehouseExpressFeeCalculate',  params,  config )}

//导入
export const specialWarehouseExpressFeeImport = (params, config = {}) => {return request.post(apiPrefix + 'specialWarehouseExpressFeeImport',  params,  config )}

//运营部虚假宣传
//top列表
export const opsFalsePublicityPage = (params, config = {}) => {return request.post(apiPrefix + 'opsFalsePublicityPage',  params,  config )}
//导入
export const opsFalsePublicityImport = (params, config = {}) => {return request.post(apiPrefix + 'opsFalsePublicityImport',  params,  config )}
//删除
export const opsFalsePublicityRemove = (params, config = {}) => {return request.post(apiPrefix + 'opsFalsePublicityRemove?ids=' + params.ids, params, config) }

//京麦库存分页 jdSkuRecord/page
export const jdSkuRecordPage = (params, config = {}) => {return request.post(apiPrefix + 'jdSkuRecord/page',  params,  config )}

//京麦库存批量修改 jdSkuRecord/oneClickSending
export const jdSkuRecordOneClickSending = (params, config = {}) => {return request.post(apiPrefix + 'jdSkuRecord/oneClickSending',  params,  config )}

//京麦库存日志 /jdSkuSyncStockRecord/page
export const jdSkuSyncStockRecordPage = (params, config = {}) => {return request.post(apiPrefix + 'jdSkuSyncStockRecord/page',  params,  config )}

//设置同步设置百分比 /jdSkuRecord/setting
export const jdSkuRecordSetting = (params, config = {}) => {return request.post(apiPrefix + `jdSkuRecord/setting`, params, config )}

//获取百分比 jdSkuRecord/getSetting
export const jdSkuRecordGetSetting = (params, config = {}) => {return request.post(apiPrefix + 'jdSkuRecord/getSetting',  params,  config )}

//从京麦同步sku详细数据 jdSkuRecord/syncJdSkuRecord
export const jdSkuRecordSyncJdSkuRecord = (params, config = {}) => {return request.post(apiPrefix + 'jdSkuRecord/syncJdSkuRecord',  params,  config )}

//京喜nnn库存分页 jdNNSkuStatistic/page
export const jdNNSkuStatisticPage = (params, config = {}) => {return request.post(apiPrefix + 'jdNNSkuStatistic/page',  params,  config )}

// 京麦库存导出
export const jdSkuRecordExport = (params, config = {}) => {return request.post(apiPrefix + 'jdSkuRecord/export',  params,  {...config, responseType: 'blob'} )}

// 获取聚水潭可用库存列表
export const getJstUsableQtyList = (params, config = {}) => {return request.post(apiPrefix + 'jdSkuRecord/getJstUsableQtyList',  params,  config )}

// 分仓配置 - 列表
export const warehouseConfigList = (params, config = {}) => {return request.post(apiPrefix + 'warehouse-config/list',  params,  config )}

// 分仓配置 - 保存
export const warehouseConfigSave = (params, config = {}) => {return request.post(apiPrefix + 'warehouse-config/save',  params,  config )}

// 分仓配置 - 批量保存
export const warehouseConfigBatchSave = (params, config = {}) => {return request.post(apiPrefix + 'warehouse-config/batch-save',  params,  config )}

// 分仓配置 - 更新
export const warehouseConfigUpdate = (params, config = {}) => {return request.post(apiPrefix + 'warehouse-config/update',  params,  config )}

// 分仓配置 - 删除
export const warehouseConfigRemove = (params, config = {}) => {return request.post(apiPrefix + 'warehouse-config/remove?ids=' + params.id,   config )}

// 店铺操作日志
export const shopOperationLogPage = (params, config = {}) => {return request.post(apiPrefix + 'shop-operation-log/page',  params,  config )}

export const generateJXNNSKUData = (params, config = {}) => { return request.post(apiPrefix + 'jdSkuRecord/generateJXNNSKUData', params, config) }

export const taskStatusJXNNSKU = (params, config = {}) => {
  return request.get(apiPrefix + 'jdSkuRecord/taskStatus', { params: params, ...config })
}

//京麦库存 一键发送
// export const oneClickSending = (params, config = {}) => {return request.post(apiPrefix + 'jdSkuRecord/oneClickSending',  params,  config )}

export const selectWarehousePage = (params, config = {}) => {return request.post(apiPrefix + 'warehouse/selectWarehousePage',  params,  config )}

export const getRefundSatisfactionPage = (params, config = {}) => {return request.post(apiPrefix + 'taobao-refund-satisfaction/page',  params,  config )}

export const exportRefundSatisfaction = (params, config = {}) => {
  return request.post(apiPrefix + 'taobao-refund-satisfaction/export', params, {
    ...config,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 修改退款满意度分组
export const updateRefundSatisfactionGroup = (params, config = {}) => {return request.post(apiPrefix + 'taobao-refund-satisfaction/update-group?id='+params.id + '&groupName='+params.groupName,  config )}

// 获取退款满意度分组列表
export const getRefundSatisfactionGroups = (params, config = {}) => {return request.get(apiPrefix + 'taobao-refund-satisfaction/groups',  { params: params, ...config })}

// 获取退款满意度店铺列表
export const getRefundSatisfactionShops = (params, config = {}) => {return request.get(apiPrefix + 'taobao-refund-satisfaction/shops',  { params: params, ...config })}

// 导入聚水潭库存到商家全国仓
export const jdSkuJstStockImport = (file, config = {}) => {
  const formData = new FormData()
  formData.append('stockFile', file)
  return request.post(apiPrefix + 'jdSkuRecord/jdSkuJstStockImport', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    ...config
  })
}
