
import request from '@/utils/request'
const GroupPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/KuaiShouInquirs/`

//获取组distinct
export const getKuaiShouGroup = (params, config = {}) => { return request.get(GroupPrefix + 'GetKuaiShouGroupNameList', { params: params, ...config }) }
//分页获取分组
export const getKuaiShouGroupPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetKuaiShouGroupPageList', params, config) }
//新增-组
export const addKuaiShouGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'AddKuaiShouGroupAsync', params, config) }
//删除-组
export const deleteKuaiShouGroupAsync = (params, config = {}) => { return request.get(GroupPrefix + 'DeleteKuaiShouGroupAsync', { params: params, ...config }) }
//修改-组
export const updateKuaiShouGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'UpdateKuaiShouGroupAsync', params, config) }
//导入-组
export const importKuaiShouGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportKuaiShouGroupAsync', params, config) }
//获取分组修改日志
export const GetGroupLogList = (params, config = {}) => { return request.post(GroupPrefix + 'GetGroupLogList', params, config) }
//咨询数据没有匹配到分组的
export const GetKuaiShouInquirsNotExistsList = (params, config = {}) => { return request.post(GroupPrefix + 'GetKuaiShouInquirsNotExistsList', params, config) }


//分页获取咨询数据
export const getKuaiShouInquirsPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetKuaiShouInquirsPageList', params, config) }
//删除咨询数据
export const deleteKuaiShouInquirsAsync = (params, config = {}) => { return request.get(GroupPrefix + 'DeleteKuaiShouInquirsAsync', { params: params, ...config }) }
//导入-咨询数据
export const importKuaiShouInquirsAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportKuaiShouInquirsAsync', params, config) }


//个人效率统计-售前
export const getKuaiShouPersonalEfficiencyPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetKuaiShouPersonalEfficiencyPageList', params, config) }
//个人效率统计-店铺个人效率-售前
export const getKuaiShouShopPersonalEfficiencyPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetKuaiShouShopPersonalEfficiencyPageList', params, config) }
//个人效率统计-个人趋势图-售前
export const getKuaiShouPersonalEfficiencyChat = (params, config = {}) => { return request.post(GroupPrefix + 'GetKuaiShouPersonalEfficiencyChat', params, config) }
//个人效率统计导出
export const exportKuaiShouPersonalEfficiencyList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportKuaiShouPersonalEfficiencyList', params, config)
}

//组效率统计-分页查询
export const getKuaiShouGroupEfficiencyPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetKuaiShouGroupEfficiencyPageList', params, config) }
//组效率统计-组趋势图
export const getKuaiShouGroupEfficiencyChat = (params, config = {}) => { return request.post(GroupPrefix + 'GetKuaiShouGroupEfficiencyChat', params, config) }
//组效率统计导出
export const exportKuaiShouGroupEfficiencyList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportKuaiShouGroupEfficiencyList', params, config)
}

//店效率统计-分页查询
export const getKuaiShouShopEfficiencyPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetKuaiShouShopEfficiencyPageList', params, config) }
//店效率统计-铺趋势图
export const getKuaiShouShopEfficiencyChat = (params, config = {}) => { return request.post(GroupPrefix + 'GetKuaiShouShopEfficiencyChat', params, config) }
//店效率统计导出
export const exportKuaiShouEfficiencyList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportKuaiShouEfficiencyList', params, config)
}

//店铺组效率
export const getJdInquirsStatisticsByShopListMonth = (params, config = {}) => { return request.post(GroupPrefix + 'GetJdInquirsStatisticsByShopListMonth', params, config) }


//批量离组
export const batchUpdateLeaveDateAsync = (params, config = {}) => { return request.post(GroupPrefix + 'BatchUpdateLeaveDateAsync',  params, config ) }




