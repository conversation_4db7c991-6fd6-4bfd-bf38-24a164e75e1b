import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/StyleCodeManage/`

//系列编码获取数据
export const getStyleCodeSelectList = (params, config = {}) => { return request.post(apiPrefix + 'GetStyleCodeSelectList', params, config)}

//质检公司下拉数据 ==
export const getQualityCheckCompanySelectList = (params, config = {}) => { return request.post(apiPrefix + 'GetQualityCheckCompanySelectList', params, config)}

//质检报告列表数据 ====
export const getStyleQualityCheckRecordList = (params, config = {}) => { return request.post(apiPrefix + 'GetStyleQualityCheckRecordList', params, config)}

//获取质检报告数据 ===
export const getStyleQualityCheckReportById = (params, config = {}) => { return request.post(apiPrefix + 'GetStyleQualityCheckReportById', params, config)}

//保存质检报告 ====
export const saveStyleQualityCheckReport = (params, config = {}) => { return request.post(apiPrefix + 'SaveStyleQualityCheckReport', params, config)}

//删除质检报告 =====
export const delStyleQualityCheckReportByIds = (params, config = {}) => { return request.post(apiPrefix + 'DelStyleQualityCheckReportByIds', params, config)}

//质检公司列表数据
export const getQualityCheckCompanyList = (params, config = {}) => { return request.post(apiPrefix + 'GetQualityCheckCompanyList', params, config)}

//保存质检公司数据
export const saveQualityCheckCompany = (params, config = {}) => { return request.post(apiPrefix + 'SaveQualityCheckCompany', params, config)}

//删除质检公司数据
export const delQualityCheckCompanyById = (params, config = {}) => { return request.post(apiPrefix + 'DelQualityCheckCompanyById', params, config)}