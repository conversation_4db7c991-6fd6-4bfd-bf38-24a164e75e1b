import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/pddonlyrefund/`

export const getPddShGroup = (params, config = {}) => { return request.get(apiPrefix + 'GetPddShGroup', { params: params, ...config }) }

export const importPddonlyRefundOrderAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportPddonlyRefundOrderAsync', params, config)
}

export const getPddOnlyRefundOrderPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPddOnlyRefundOrderPageList', params, config)
}

export const getPddOnlyRefundReceivePageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPddOnlyRefundReceivePageList', params, config)
}

export const getPddOnlyRefundChatPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPddOnlyRefundOrderChatPageList', params, config)
}
export const exportPddOnlyRefundOrderChatList = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportPddOnlyRefundOrderChatList', params, config)
}

export const getPddOnlyRefundReceivePageMapList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPddOnlyRefundOrderMapPageList', params, config)
}
export const GetOrderChatListByOrderNo = (orderNo, config = {}) => {
    return request.get(apiPrefix + 'GetOrderChatListByOrderNo?saleAfterNo=' + orderNo, {}, config)
}


export const exportPddOnlyRefundOrderList = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportPddOnlyRefundOrderList', params, config)
}

export const getPddOnlyRefundReceiveUserPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPddOnlyRefundReceiveUserPageList', params, config)
}
export const getPddOnlyRefundReceiveGroupChat = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPddOnlyRefundReceiveGroupChat', params, config)
}
export const exportPddOnlyRefundReceiveUserList = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportPddOnlyRefundReceiveUserList', params, config)
}


//分页查询 拼多多仅退款责任
export const PagePddOnlyRefundZrList = (params, config = {}) => {
    return request.post(apiPrefix + 'PagePddOnlyRefundZrList', params, config)
}