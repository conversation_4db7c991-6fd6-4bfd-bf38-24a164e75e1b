import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/monitorreport/`

export const createReport = (params, config = {}) => {          return request.post(apiPrefix + 'CreateReportAsync', params, config)}
export const createMergeReport = (params, config = {}) => {     return request.post(apiPrefix + 'CreateMergeReportAsync', params, config)}
export const flowReport = (params, config = {}) => {            return request.post(apiPrefix + 'FlowReportAsync', params, config)}
export const finishReport = (params, config = {}) => {          return request.post(apiPrefix + 'FinishReportAsync', params, config)}
export const getReport = (params, config = {}) => {             return request.get(apiPrefix + 'GetReportAsync', { params: params, ...config })  }
export const getReportMerge = (params, config = {}) => {        return request.get(apiPrefix + 'GetReportMergeAsync', { params: params, ...config })  }
export const countCurrentReport = (params, config = {}) => {    return request.get(apiPrefix + 'CountCurrentReportAsync', { params: params, ...config })  }
export const pageReport = (params, config = {}) => {            return request.get(apiPrefix + 'PageReportAsync', { params: params, ...config })  }
