import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Kj}/stock/`
//获取同步海外仓库存数据
export const queryList =(params,config) =>{return request.post(apiPrefix + 'stock',params,config)}

//获取同步海外仓库存数据 同步页面
export const stockArchive =(params,config) =>{return request.post(apiPrefix + 'stockArchive',params,config)}

//删除异常数据
export const deleteStock =(params,config) =>{return request.post(apiPrefix + 'delete',params,config)}

//删除异常数据
export const getStockLog =(params,config) =>{return request.post(apiPrefix + 'getStockModifyRecord',params,config)}

//导出同步仓库数据
export const exportStock =(params,config={responseType: 'blob'} ) =>{return request.post(apiPrefix + 'export',params,config)}

//海外仓库存管理查看日志
export const getBlackListConfigLogPageList =(params,config) =>{return request.post(apiPrefix + 'getBlackListConfigLogPageList',params,config)}
