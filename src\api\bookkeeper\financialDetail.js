import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/finacialDetail/`

export const getFinancialDetail_ZFB = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialDetail_ZFB', { params, ...config }) }
export const getCaiNiaoYiZhanPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetCaiNiaoYiZhanPageList', { params, ...config }) }
export const getSaleThemeAnalysisPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetSaleThemeAnalysisPageList', { params, ...config }) }
export const exportGatherSaleThemeAnalysisList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + `ExportGatherSaleThemeAnalysisList`, { params, ...config }) }
export const getSaleDetailPddPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetSaleDetailPddPageList', { params, ...config }) }
export const getSaleDetailPDD = (params, config = {}) => { return request.get(apiPrefix + 'GetSaleDetailPDD', { params, ...config }) }
export const getSaleDetailDY = (params, config = {}) => { return request.get(apiPrefix + 'GetSaleDetailDY', { params, ...config }) }
export const getFinancialDetailDY = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialDetailDY', { params, ...config }) }
export const getFinancialDetailJD = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialDetailJD', { params, ...config }) }
export const getBaoCaiDetailJD = (params, config = {}) => { return request.get(apiPrefix + 'GetBaoCaiDetailJD', { params, ...config }) }
export const getFinancialDetailPDD = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialDetailPDD', { params, ...config }) }
export const getRecoganizePddDic = (params, config = {}) => { return request.get(apiPrefix + 'GetRecoganizePddDic', { params, ...config }) }
export const getSaleAfterThemeAnalysisDetail = (params, config = {}) => { return request.get(apiPrefix + 'GetSaleAfterThemeAnalysisDetail', { params, ...config }) }
export const exportGatherSaleAfterThemeAnalysisList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + `ExportGatherSaleAfterThemeAnalysisList`, { params, ...config }) }
export const getFinancialDetailPddPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialDetailPddPageList', { params, ...config }) }
export const getQianNiuIdPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetQianNiuIdPageList', { params, ...config }) }
export const getQianNiuDetailPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetQianNiuDetailPageList', { params, ...config }) }
export const getDataUploadList = (params, config = {}) => { return request.post(apiPrefix + 'GetDataUploadList', params, config) }
export const getNoDataUploadList = (params, config = {}) => { return request.post(apiPrefix + 'GetNoDataUploadList', params, config) }
export const getDataCalcList = (params, config = {}) => { return request.post(apiPrefix + 'GetDataCalcList', params, config) }
export const pageMonthReportDetail2Async = (params, config = {}) => { return request.get(apiPrefix + 'PageMonthReportDetail2Async', { params: params, ...config }) }
export const getFinancialInAndOutDetailPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialInAndOutDetailPageList', { params, ...config }) }
export const getFinancialResultPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialResultPageList', { params, ...config }) }

export const postCalcTask = (params, config = {}) => { return request.post(apiPrefix + 'PostCalcTask', params, config) }
export const postCalcTaskBatch = (params, config = {}) => { return request.post(apiPrefix + 'PostCalcTaskBatch', params, config) }
export const exportFinacialResult = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + `ExportFinacialResult`, { params, ...config }) }
export const getFinancialResultIdPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialResultIdPageList', { params, ...config }) }
export const exportFinacialIdResult = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + `ExportFinacialResultId`, { params, ...config }) }

//dayreport

export const importFinacialZFBDay = (params, config = {}) => { return request.post(apiPrefix + 'ImportFinacialZFBDayAsync', params, config) }

// 拼多多账单分页
export const getBillFeePageList = (params, config = {}) => { return request.get(apiPrefix + 'GetBillFeePageList', { params, ...config }) }

//月报计算（已上传）
export const exportDataUploadList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + `ExportDataUploadList`, { params, ...config }) }

