      import request from '@/utils/request'
      const EmpAchievementPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/EmpAchievement/`
        export const getEmpAchievementList = (params, config = {}) => {return request.get(EmpAchievementPrefix + 'GetEmpAchievementList', { params: params, ...config })}
        export const getShopAchievementList = (params, config = {}) => {return request.get(EmpAchievementPrefix + 'GetShopAchievementList', { params: params, ...config })}