import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Express}/expressPackage/`

//PageExpressImgPackage 分页查询图片包
export const pageExpressImgPackage = (params, config = {}) => { return request.post(apiPrefix + 'PageExpressImgPackage', params, config) }

//GetExpressPackageCostList     获取图片包成本列表
export const getExpressPackageCostList = (params, config = {}) => { return request.get(apiPrefix + 'GetExpressPackageCostList', { params: params, ...config }) }

//保存耗材成本 SetExpressPackageCost
export const setExpressPackageCost = (params, config = {}) => { return request.post(apiPrefix + 'SetExpressPackageCost', params, config) }

//GetAllPackageTypes 获取所有图片包类型
export const getAllPackageTypes = (params, config = {}) => { return request.get(apiPrefix + 'GetAllPackageTypes', { params: params, ...config }) }

//耗材下拉接口
export const getExpressPackageCostSelectList = (params, config = {}) => { return request.get(apiPrefix + 'GetExpressPackageCostSelectList', { params: params, ...config }) }

//快递包材核对-分页查询
export const pageExpressPackageMaterialCheckPackage = (params, config = {}) => { return request.post(apiPrefix + 'PageExpressPackageMaterialCheckPackage', params, config) }

//快递包材核对-操作人数据
export const getExpressPackageCorrectionSelectList = (params, config = {}) => { return request.get(apiPrefix + 'GetExpressPackageCorrectionSelectList', { params: params, ...config }) }

//快递包材识别-编辑快递包装耗材识别信息
export const editExpressImgPackageAsync = (params, config = {}) => { return request.post(apiPrefix + 'EditExpressImgPackageAsync', params, config) }

//快递包材识别-操作日志
export const pageExpressPackagelogs = (params, config = {}) => { return request.post(apiPrefix + 'PageExpressPackagelogs', params, config) }

//快递包材识别-导出
export const exportExpressImgPackage = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportExpressImgPackage', params, config) }

//30天耗材列表
export const getMonthImgPackageStatistical = (params, config = {}) => { return request.post(apiPrefix + 'GetMonthImgPackageStatistical', params, config) }

