server {
    listen 8002;

	large_client_header_buffers 4 10m;  # 读取大型客户端请求头的缓冲区的最大数量和大小
    client_max_body_size 300m;    #设置nginx能处理的最大请求主体大小。
    client_body_buffer_size 20m;  #请求主体的缓冲区大小。
    proxy_connect_timeout 600;
    proxy_read_timeout 600;
    proxy_send_timeout 600;
    proxy_buffer_size 64k;
    proxy_buffers   4 32k;
    proxy_busy_buffers_size 64k;
    proxy_temp_file_write_size 64k;

	#允许跨域请求的域，*代表所有
	add_header 'Access-Control-Allow-Origin' *;
	#允许带上cookie请求
	add_header 'Access-Control-Allow-Credentials' 'true';
	#允许请求的方法，比如 GET/POST/PUT/DELETE
	add_header 'Access-Control-Allow-Methods' *;
	#允许请求的header
	add_header 'Access-Control-Allow-Headers' *;

    location / {
        root /var/www;
        index  index.html index.htm;
        #try_files $uri $uri/ /index.html;
	    try_files $uri $uri/ @router;
    }
	location @router {
        rewrite ^.*$ /index.html last;
    }
	location ~* /api/admin/ {
		rewrite ^.+/api/admin/?(.*)$ /$1 break;
		proxy_http_version 1.1; 
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
		include uwsgi_params;
		proxy_pass http://192.168.16.241:8000;
    }
	location ~* /api/express/ {
		rewrite ^.+/api/express/?(.*)$ /$1 break;
		include uwsgi_params;
		proxy_pass http://192.168.16.241:8010;
    }
	location ~* /api/order/ {
		rewrite ^.+/api/order/?(.*)$ /$1 break;
		include uwsgi_params;
		proxy_pass http://192.168.16.241:8020;
    }
	location ~* /api/operatemanage/ {
		rewrite ^.+/api/operatemanage/?(.*)$ /$1 break;
		include uwsgi_params;
		proxy_pass http://192.168.16.241:8030;
    }
	location ~* /api/inventory/ {
		rewrite ^.+/api/inventory/?(.*)$ /$1 break;
		include uwsgi_params;
		proxy_pass http://192.168.16.241:8040;
    }
	location ~* /api/customerservice/ {
		rewrite ^.+/api/customerservice/?(.*)$ /$1 break;
		include uwsgi_params;
		proxy_pass http://192.168.16.241:8100;
    }
	location ~* /api/financial/ {
		rewrite ^.+/api/financial/?(.*)$ /$1 break;
		include uwsgi_params;
		proxy_pass http://192.168.16.241:8060;
    }
	location ~* /api/bookkeeper/ {
		rewrite ^.+/api/bookkeeper/?(.*)$ /$1 break;
		include uwsgi_params;
		proxy_pass http://192.168.16.241:8090;
    }
	location ~* /api/profit/ {
		rewrite ^.+/api/profit/?(.*)$ /$1 break;
		include uwsgi_params;
		proxy_pass http://192.168.16.241:8190;
    }
	location ~* /api/media/ {
		rewrite ^.+/api/media/?(.*)$ /$1 break;
		include uwsgi_params;
		proxy_pass http://192.168.16.241:8300;
    }
	location ~* /api/upload/ {
		rewrite ^.+/api/upload/?(.*)$ /$1 break;
		include uwsgi_params;
		proxy_pass http://192.168.16.240:8070;
    }
	location ~* /api/uploadnew/ {
		rewrite ^.+/api/uploadnew/?(.*)$ /$1 break;
		include uwsgi_params;
		proxy_pass http://192.168.90.12:8003;
    }
	location ~* /api/uploadddh5/ {
		rewrite ^.+/api/uploadnew/?(.*)$ /$1 break;
		include uwsgi_params;
		proxy_pass http://192.168.90.12:8003;
    }
	location ~* /api/teamwork/ {
		rewrite /api/teamwork/(.*) /test/api/teamwork/$1 break;
		include uwsgi_params;
		proxy_pass http://192.168.16.22:32347;
    }
}
