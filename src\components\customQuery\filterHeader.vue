<template>
  <div v-if="condition" class="header">
    <div>
      <el-radio-group v-model="condition.logic" @change="change">
        <el-radio label="And">并且</el-radio>
        <el-radio label="Or">或者</el-radio>
      </el-radio-group>
    </div>
    <div>
      <el-button icon="el-icon-plus" circle @click="add" />
      <el-button icon="el-icon-minus" circle @click="remove" />
    </div>
  </div>
</template>

<script>

export default {
  model: {
    prop: 'value',
    event: 'update'
  },
  props: {
    value: {
      type: Object,
      default: {}
    }
  },
  data() {
    return { condition: null }
  },
  mounted() {
    this.condition = this.value
  },
  methods: {
    add() {
      this.condition.filters.push({ field: null, operator: null, value: null, logic: 'And', filters: [] })
      this.change()
    },
    remove() {
      this.$set(this.condition, 'filters', [])
      this.change()
    },
    change() {
      this.$emit('update', this.condition)
    }
  }
}
</script>

<style scoped lang="scss">
.header {
    display: flex;
    justify-content: space-between;
    padding: 5px;
    padding-right: 0px;
    margin-bottom: 10px;
}
</style>
