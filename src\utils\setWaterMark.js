let setWatermark = (obj) => {
    let id = 'isyhcode'
   
    if (document.getElementById(id) !== null) {
        document.body.removeChild(document.getElementById(id))
    }
   
    let can = document.createElement('canvas')
    // 设置canvas画布大小
    can.width = 250
    can.height = 130
   
    let cans = can.getContext('2d')
    cans.rotate(-90 / 180)
    cans.font = '15px Vedana'
    cans.fillStyle = '#000000'
    cans.textAlign = 'center'
    cans.textBaseline = 'Middle'
    cans.fillText(obj.str1, can.width / 2, can.height) // 水印在画布的位置x，y轴
    cans.fillText(obj.str2, can.width / 2, can.height + 22)
    cans.fillText(obj.str3, can.width / 2, can.height + 44)
    cans.fillText(obj.str4, can.width / 2, can.height + 66)
   
   
    let div = document.createElement('div')
    div.id = id
    div.style.pointerEvents = 'none'
    div.style.top = '25px'
    div.style.right = '0px'
    div.style.opacity = '0.1'
    div.style.position = 'fixed'
    div.style.zIndex = '100000'
    div.style.width = document.documentElement.clientWidth + 'px'
    div.style.height = document.documentElement.clientHeight + 'px'
    div.style.background = 'url(' + can.toDataURL('image/png') + ') left top repeat'
    document.body.appendChild(div)
    return id
   }
   
   
   export const setWaterMark = (obj) => {
    let id = setWatermark(obj)
    if (document.getElementById(id) === null) {
        id = setWatermark(obj)
    }
   }
   
   
   export const removeWatermark = () => {
    let id = 'isyhcode'
    if (document.getElementById(id) !== null) {
        document.body.removeChild(document.getElementById(id))
    }
   }