import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_VerifyOrder}/prePackScan/`

//预包扫码 Scan
export const scan = (params, config = {}) => {
    return request.post(apiPrefix + 'Scan', params, config)
}

//获取扫码记录  PageGetScanRecord
export const pageGetScanRecord = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetScanRecord', params, config)
}

//手动打印电子面单 Print
export const rpaPrint = (params, config = {}) => {
    return request.post(apiPrefix + 'RpaPrint', params, config)
}

//统计每天工作量 GetScanPrintStat
export const getScanPrintStat = (params, config = {}) => {
    return request.post(apiPrefix + 'GetScanPrintStat', params, config)
}

//统计每天工作量趋势图 GetStatTrendChart
export const getStatTrendChart = (params, config = {}) => {
    return request.post(apiPrefix + 'GetStatTrendChart', params, config)
}

//查询未扫码商品编码 PageGetNoScan
export const pageGetNoScan = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetNoScan', params, config)
}

//导出扫码记录 ExportScanRecord
export const exportScanRecord = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportScanRecord', params, config)
}

//导出异常编码 ExportNoScan
export const exportNoScan = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportNoScan', params, config)
}

//查询发货仓统计 PageGetWmsStat
export const pageGetWmsStat = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetWmsStat', params, config)
}

//导出发货仓统计 ExportWmsStat
export const exportWmsStat = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportWmsStat', params, config)
}

//获取蓄单时间 GetMobilizeHour
export const getMobilizeHour = (params, config = {}) => {
    return request.post(apiPrefix + 'GetMobilizeHour', params, config)
}

//设置蓄单时间  SetMobilizeHour
export const setMobilizeHour = (params, config = {}) => {
    return request.post(apiPrefix + 'SetMobilizeHour', params, config)
}

//GetMatchOrRelease 获取匹配或释放记录
export const getMatchOrReleases = (params, config = {}) => {
    return request.post(apiPrefix + 'GetMatchOrReleases', params, config)
}

//获取预包扫码仓库
export const getWcScanSendWmses= (params, config = {}) => {
    return request.post(apiPrefix + 'GetWcScanSendWmses', params, config)
}
