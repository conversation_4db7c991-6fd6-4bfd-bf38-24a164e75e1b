import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_ImportInventory}/purchaseordernew/`

//导入采购个人周转天数
export const importPurchaseIndividualTurnoverDayAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchaseIndividualTurnoverDayAsync', params, config) }

//导入货盘资金合计
export const importPurchaseTotalInventoryFunds = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchaseTotalInventoryFunds', params, config) }
