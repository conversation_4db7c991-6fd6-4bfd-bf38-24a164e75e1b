import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/styleCodeRptData/`

//获取系列编码销售在途 GetStyleCodeSaleInTransitList
export const getStyleCodeSaleInTransitList = (params, config = {}) => { return request.post(apiPrefix + 'GetStyleCodeSaleInTransitList', params, config) }

//获取系列编码采购在途 GetStyleCodeProcureInTransitList
export const getStyleCodeProcureInTransitList = (params, config = {}) => { return request.post(apiPrefix + 'GetStyleCodeProcureInTransitList', params, config) }

//获取系列编码库存资金 GetStyleCodeInventoryFundsList
export const getStyleCodeInventoryFundsList = (params, config = {}) => { return request.post(apiPrefix + 'GetStyleCodeInventoryFundsList', params, config) }

//导入销售在途 ImportStyleCodeSaleInTransit
export const importStyleCodeSaleInTransit = (params, config = {}) => { return request.post(apiPrefix + 'ImportStyleCodeSaleInTransit', params, config) }

//导入采购在途 ImportStyleCodeProcureInTransit
export const importStyleCodeProcureInTransit = (params, config = {}) => { return request.post(apiPrefix + 'ImportStyleCodeProcureInTransit', params, config) }

//导入库存资金 ImportStyleCodeInventoryFunds
export const importStyleCodeInventoryFunds = (params, config = {}) => { return request.post(apiPrefix + 'ImportStyleCodeInventoryFunds', params, config) }

//系列编码报表第二个页签 PageSimilarityAsync
export const pageSimilarityAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageSimilarityAsync', params, config) }

//系列编码报表第二个页签 PageSimilarityAsync
export const SimilarityAnalysis = (params, config = {}) => { return request.post(apiPrefix + 'SimilarityAnalysis', params, config) }

//系列编码报表 PageStyleCodeRptListAsync
export const pageStyleCodeRptListAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageStyleCodeRptListAsync', params, config) }

//系列编码报表趋势图
export const StyleCodeRptAnalysis = (params, config = {}) => { return request.post(apiPrefix + 'StyleCodeRptAnalysis', params, config) }

//导出系列编码报表数据
export const exportStyleCodeRptListAsync = (params,config ={responseType: 'blob'}) =>{
    return request.post(apiPrefix + 'ExportStyleCodeRptListAsync', params, config)
}

//相似数据 PageStyleCodeDtlRptListAsync   
export const pageStyleCodeDtlRptListAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageStyleCodeDtlRptListAsync', params, config) }

//导出相似列表 ExportStyleCodeDtlRptListAsync
export const exportStyleCodeDtlRptListAsync = (params,config ={responseType: 'blob'}) =>{
    return request.post(apiPrefix + 'ExportStyleCodeDtlRptListAsync', params, config)
}

//主链接 PageStyleCodeGoodsDtlRptByProCodeListAsync
export const pageStyleCodeGoodsDtlRptByProCodeListAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageStyleCodeGoodsDtlRptByProCodeListAsync', params, config) }

//系列编码 PageStyleCodeGoodsDtlRptListAsync  
export const pageStyleCodeGoodsDtlRptListAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageStyleCodeGoodsDtlRptListAsync', params, config) }

//获取系列编码报表详情头部数据 GetMainSaleProductInfoByStyleCode
export const getMainSaleProductInfoByStyleCode = (params, config = {}) => { return request.post(apiPrefix + 'GetMainSaleProductInfoByStyleCode', params, config) }

//导出
export const ExportSimilarityAsync =(params,config ={responseType: 'blob'}) =>{
    return request.post(apiPrefix + 'ExportSimilarityAsync',params, config)
}

//系列编码全平台报表
export const pageStyleCodeAllPlatformRptAsync =  (params, config = {}) => { return request.post(apiPrefix + 'PageStyleCodeAllPlatformRptAsync', params, config) }

//系列编码全平台报表售卖的运营组数量和产品数量
export const getStyleSaleGroupInfo =  (params, config = {}) => { return request.post(apiPrefix + 'GetStyleSaleGroupInfo', params, config) }

//系列编码全平台报表售卖的平台订单占比
export const getStyleSalePlatformOrderCountInfo =  (params, config = {}) => { return request.post(apiPrefix + 'GetStyleSalePlatformOrderCountInfo', params, config) }

//系列编码全平台报表缺货数据
export const getStyleOutOfStockData =   (params, config = {}) => { return request.post(apiPrefix + 'GetStyleOutOfStockData', params, config) }

//导出系列编码全平台报表
export const exportStyleCodeAllPlatformRptAsync = (params,config ={responseType: 'blob'}) =>{
    return request.post(apiPrefix + 'ExportStyleCodeAllPlatformRptAsync', params, config)
}

//系列编码全平台报表(运营)
export const pageOperateStyleCodeAllPlatformRptAsync =  (params, config = {}) => { return request.post(apiPrefix + 'PageOpearteStyleCodeAllPlatformRptAsync', params, config) }
 
//系列编码全平台报表售卖的平台订单占比(运营)
export const getOperateStyleSalePlatformOrderCountInfo =  (params, config = {}) => { return request.post(apiPrefix + 'GetOpearteStyleSalePlatformOrderCountInfo', params, config) }

//系列编码全平台报表缺货数据(运营)
export const getOperateStyleOutOfStockData =   (params, config = {}) => { return request.post(apiPrefix + 'GetOpearteStyleOutOfStockData', params, config) }

//导出系列编码全平台报表(运营)
export const exportOperateStyleCodeAllPlatformRptAsync = (params,config ={responseType: 'blob'}) =>{
    return request.post(apiPrefix + 'ExportOpearteStyleCodeAllPlatformRptAsync', params, config)
}

//系列编码订单数报表
export const pageStyleCodeOrderCountRptAsync =  (params, config = {}) => { return request.post(apiPrefix + 'PageStyleCodeOrderCountRptAsync', params, config) }

//系列编码订单数趋势图
export const styleCodeOrderCountRptAnalysis =  (params, config = {}) => { return request.post(apiPrefix + 'StyleCodeOrderCountRptAnalysis', params, config) }

//系列编码批量设置负责人
export const BatchUpdateSeriesMainOwner =  (params, config = {}) => { return request.post(apiPrefix + 'BatchUpdateSeriesMainOwner', params, config) }

//拍照 SaveSimilarityVersion
export const saveSimilarityVersion =  (params, config = {}) => { return request.post(apiPrefix + 'SaveSimilarityVersion', params, config) }

//获取拍照版本号 GetSeriesMainVersion
export const getSeriesMainVersion =  (params, config = {}) => { return request.post(apiPrefix + 'GetSeriesMainVersion', params, config) }

//按存版本查询 PageSimilarityByVersionAsync
export const pageSimilarityByVersionAsync =  (params, config = {}) => { return request.post(apiPrefix + 'PageSimilarityByVersionAsync', params, config) }

//按版本导出 ExportSimilarityByVersionAsync
export const exportSimilarityByVersionAsync = (params,config ={responseType: 'blob'}) =>{
    return request.post(apiPrefix + 'ExportSimilarityByVersionAsync', params, config)
}

//PageStyleCodeDirectorFullInfoWarList
export const pageStyleCodeDirectorFullInfoWarList =  (params, config = {}) => { return request.post(apiPrefix + 'PageStyleCodeDirectorFullInfoWarList', params, config) }

//修改类目 SetStyleCodeBzCategory
export const setStyleCodeBzCategory =  (params, config = {}) => { return request.post(apiPrefix + 'SetStyleCodeBzCategory', params, config) }

export const pageStyleCodeDirectorFullInfoWarDtlList =  (params, config = {}) => { return request.post(apiPrefix + 'pageStyleCodeDirectorFullInfoWarDtlList', params, config) }

//备忘录历史分页 GetStyleCodeUserTrackLog
export const getStyleCodeUserTrackLog =  (params, config = {}) => { return request.post(apiPrefix + 'GetStyleCodeUserTrackLog', params, config) }

//新增备忘录AddStyleCodeUserTrackLog    
export const addStyleCodeUserTrackLog =  (params, config = {}) => { return request.post(apiPrefix + 'AddStyleCodeUserTrackLog', params, config) }

//资金占用趋势图 SimilarityZJZYAnalysis
export const similarityZJZYAnalysis =  (params, config = {}) => { return request.post(apiPrefix + 'SimilarityZJZYAnalysis', params, config) }

//导入系列编码负责人 ImportBatchUpdateSeriesMainOwner
export const importBatchUpdateSeriesMainOwner =  (params, config = {}) => { return request.post(apiPrefix + 'ImportBatchUpdateSeriesMainOwner', params, config) }

//运营申报查询 PageStyleCodeGoodsStockApplyAsync
export const pageStyleCodeGoodsStockApplyAsync =  (params, config = {}) => { return request.post(apiPrefix + 'PageStyleCodeGoodsStockApplyAsync', params, config) }

//采购单跟进 PageStyleCodePurchaseOrderInfoAsync
export const pageStyleCodePurchaseOrderInfoAsync =  (params, config = {}) => { return request.post(apiPrefix + 'PageStyleCodePurchaseOrderInfoAsync', params, config) }

//发货地 GetSeriesMainPurchseSendGoodsInfo
export const getSeriesMainPurchseSendGoodsInfo =  (params, config = {}) => { return request.post(apiPrefix + 'GetSeriesMainPurchseSendGoodsInfo', params, config) }

//导入批量更新负责人 ImportSeriesMainOwners
export const importSeriesMainOwners =  (params, config = {}) => { return request.post(apiPrefix + 'ImportSeriesMainOwners', params, config) }

//负责人查询 GetSeriesMainOwnerLists
export const getSeriesMainOwnerLists =  (params, config = {}) => { return request.post(apiPrefix + 'GetSeriesMainOwnerLists', params, config) }

//根据id删除负责人 DeleteSeriesMainOwnerById
export const deleteSeriesMainOwnerById =  (params, config = {}) => { return request.post(apiPrefix + 'DeleteSeriesMainOwnerById', params, config) }

//保存负责人权限 SaveSeriesCodeOwnersPermissionsSet
export const saveSeriesCodeOwnersPermissionsSet =  (params, config = {}) => { return request.post(apiPrefix + 'SaveSeriesCodeOwnersPermissionsSet', params, config) }

//查询负责人的权限 GetSeriesCodeOwnersPermissionsSet
export const getSeriesCodeOwnersPermissionsSet =  (params, config = {}) => { return request.post(apiPrefix + 'GetSeriesCodeOwnersPermissionsSet', params, config) }

//获取当前用户有哪些权限 GetCurUserSeriesCodeOwnersPermissions
export const getCurUserSeriesCodeOwnersPermissions =  (params, config = {}) => { return request.post(apiPrefix + 'GetCurUserSeriesCodeOwnersPermissions', params, config) }

//获取主管 GetSeriesCodeOwnerLeaders   
export const getSeriesCodeOwnerLeaders =  (params, config = {}) => { return request.post(apiPrefix + 'GetSeriesCodeOwnerLeaders', params, config) }

//保存主管 SaveSeriesCodeOwnerLeaders  
export const SaveSeriesCodeOwnerLeaders =  (params, config = {}) => { return request.post(apiPrefix + 'SaveSeriesCodeOwnerLeaders', params, config) }


// 获取当前用户有哪些权限 ValidateUserIsOwnerLeader
export const ValidateUserIsOwnerLeader =  (params, config = {}) => { return request.post(apiPrefix + 'ValidateUserIsOwnerLeader', params, config) }



//团队业绩 PageTeamBkRptList
export const PageTeamBkRptList =  (params, config = {}) => { return request.post(apiPrefix + 'PageTeamBkRptList', params, config) }

//团队业绩导出
export const ExportTeamBkRptList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportTeamBkRptList', params, config) }