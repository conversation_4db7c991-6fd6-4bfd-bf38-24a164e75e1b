import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/purchaseordernew/`


// 新版采购建议分页
export const pagePurchaseNewPlan2 = (params, config = {}) => {
    return request.post(apiPrefix + 'PagePurchaseNewPlan2Async', params, config)
}

//修改新版采购建议，采购建议数
export const BatchUpdatePurchaseNewPlan2 = (params, config = {}) => {
    return request.post(apiPrefix + 'BatchUpdatePurchaseNewPlan2', params, config)
}

//修改采购建议备注
export const batchUpdateGoodsRemark = (params, config = {}) => {
    return request.post(apiPrefix + 'BatchUpdateGoodsRemark', params, config)
}

// 新建采购单，建立钉钉流程
export const taskPurchaseDingDing = (params, config = {}) => {
    return request.post(apiPrefix + 'TaskPurchaseDingDingAsync', params, config)
}

//导出 明细
export const exportPurchaseNewPlan2 = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportPurchaseNewPlan2Async', params, config)
}


// 查询编码对应采购单
export const TaskGetPurchaseOrder = (params, config = {}) => {
    return request.get(apiPrefix + 'TaskGetPurchaseOrderAsync', { params: params, ...config })
}

// 采购单审批人
export const pagePurchaseOrderApprover = (params, config = {}) => {
    return request.get(apiPrefix + 'PagePurchaseOrderApproverAsync', { params: params, ...config })
}

//获取审批数据
export const GetPurchaseOrderApprover = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseOrderApproverAsync', params, config)
}

//保存审批数据
export const SavePurchaseOrderApprover = (params, config = {}) => {
    return request.post(apiPrefix + 'SavePurchaseOrderApproverAsync', params, config)
}

//删除用户
export const DelPurchaseOrderApprover = (params, config = {}) => {
    return request.post(apiPrefix + 'DelPurchaseOrderApproverAsync', params, config)
}


// 设置自动开单
export const taskSetPurchaseAutoOrder = (params, config = {}) => {
    return request.post(apiPrefix + 'TaskSetPurchaseAutoOrderAsync', params, config)
}

// 开单版本信息
export const getPurchaseAutoOrder = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPurchaseAutoOrderAsync', { params: params, ...config })
}

// 开单版本日志
export const getPurchaseAutoOrderDetail = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseAutoOrderDetailAsync', params, config)
}


//自动调拨查询
export const pageAutomaticAllocation = (params, config = {}) => {
    return request.post(apiPrefix + 'PageAutomaticAllocationAsync', params, config)
}

//自动开单仓库对应信息
export const getPurchaseOrderWarehouse = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseOrderWarehouseAsync', params, config)
}

//获取开单统计信息
export const getAutoStatistics = (params, config = {}) => { return request.post(apiPrefix + 'GetAutoStatistics', params, config) }
//获取自动开单统计明细
export const getAutoStatisticsDtl = (params, config = {}) => { return request.post(apiPrefix + 'GetAutoStatisticsDtl', params, config) }
//设置采购备注
export const setAutoPurOrderRmark = (params, config = {}) => { return request.post(apiPrefix + 'SetAutoPurOrderRmark', params, config) }
//获取采购备注日志
export const getPurchaseAutoOrderRecordLogList = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseAutoOrderRecordLogList', params, config) }
//获取自动开单统计架构
export const getAutoBrandDept = (params, config = {}) => { return request.post(apiPrefix + 'GetAutoBrandDept', params, config) }
//获取自动开单岗位
export const getAutoBrandTitle = (params, config = {}) => { return request.post(apiPrefix + 'GetAutoBrandTitle', params, config) }

//添加自动开单对应信息
export const addPurchaseOrderWarehouse = (params, config = {}) => {
    return request.post(apiPrefix + 'AddPurchaseOrderWarehouseAsync', params, config)
}

//采购价格区间
export const getPurchaseOrderPriceIntervalAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseOrderPriceIntervalAsync', params, config)
}

//采购个人周转天数分页查询
export const getPurchaseNewPlanTurnDayPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseNewPlanTurnDayPageList', params, config)
}

//采购个人周转天数趋势图
export const getPurchaseNewPlanTurnDayAnalysis = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseNewPlanTurnDayAnalysis', params, config)
}

//采购个人周转天数采购选择器数据
export const getPurchaseNewPlanTurnDayBrandList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPurchaseNewPlanTurnDayBrandList', { params: params, ...config })
}

//采购个人周转天数导出
export const exportPurchaseNewPlanTurnDayList = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportPurchaseNewPlanTurnDayList', params, config)
}

//采购个人周转天数导入
export const importPurchaseIndividualTurnoverDayAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchaseIndividualTurnoverDayAsync', params, config) }

//获取组织架构(采购个人周转天数-采购组)
export const getBrandDeptList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetBrandDeptList', params, config)
}

//获取组织架构新(缺货次数汇总-采购组)
export const getOutOfStockTimesSummaryPurchaseDeptList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetOutOfStockTimesSummaryPurchaseDeptList', params, config)
}

//获取组织架构新(采购部扣款责任-采购组)
export const getPurchaseIncreaseDecreasePriceDeptList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseIncreaseDecreasePriceDeptList', params, config)
}

//获取组织架构新(采购部扣款责任-采购组)
export const getPurchaseDeductionOnusDeptList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseDeductionOnusDeptList', params, config)
}

//获取组织架构新(采购个人周转天数-采购组)
export const getPurchaseNewPlanTurnDayDeptList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseNewPlanTurnDayDeptList', params, config)
}

//获取岗位(采购个人周转天数-岗位)
export const getBrandPositionList = (params, config = {}) => { return request.post(apiPrefix + 'GetBrandPositionList', params, config) }

//获取所有架构
export const getPurchaseDeptList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseDeptList', params, config)
}

//新品采购单审批分页查询
export const GetPurchaseOrderNewApprovePageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseOrderNewApprovePageList', params, config)
}

//新品采购单审批钉钉审批记录
export const GetPurchaseOrderNewApproveRecord = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchaseOrderNewApproveRecord', { params: params, ...config }) }

//新品采购单审批导出
export const ExportPurchaseOrderNewApprovePageList = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportPurchaseOrderNewApprovePageList', params, config)
}

//采购个人周转天数-获取设置
export const getPurchaseNewPlanTurnDaySet = (params, config = {}) => {
  return request.post(apiPrefix + 'GetPurchaseNewPlanTurnDaySet', params, config)
}

//采购个人周转天数-保存设置
export const savePurchaseNewPlanTurnDaySet = (params, config = {}) => {
  return request.post(apiPrefix + 'SavePurchaseNewPlanTurnDaySet', params, config)
}

//计算月销成本 RunTurnDay
export const runTurnDay = (params, config = {}) => {
  return request.post(apiPrefix + `RunTurnDay?dateTime=${params}`)
}


//获取配置
export const getPurchaseNewPlan2MarkerColorSet = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPurchaseNewPlan2MarkerColorSet', { params: params, ...config })
}

//保存配置
export const savePurchaseNewPlan2MarkerColorSet = (params, config = {}) => {
    return request.post(apiPrefix + 'SavePurchaseNewPlan2MarkerColorSet', params, config)
}


//获取其他人员下拉配置数据
export const getOtherBrandDataDropDown = (params, config = {}) => { return request.post(apiPrefix + 'GetOtherBrandDataDropDown', params, config) }
//获取其他数据人员设置
export const getOtherBrandDataSet = (params, config = {}) => { return request.post(apiPrefix + 'GetOtherBrandDataSet', params, config) }
//新增其他数据人员设置
export const saveOtherBrandDataSet = (params, config = {}) => { return request.post(apiPrefix + 'SaveOtherBrandDataSet', params, config) }
//删除其他数据人员设置
export const delOtherBrandDataSet = (params, config = {}) => { return request.post(apiPrefix + 'DelOtherBrandDataSet', params, config) }
//分页获取采购资金明细
export const getPurchaseFundDtlPage = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseFundDtlPage', params, config) }
//获取分组查询
export const getQueryGroupList = (params, config = {}) => { return request.post(apiPrefix + 'GetQueryGroupList', params, config) }
//获取分组查询
export const exportPurchaseFundDtl = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportPurchaseFundDtl', params, config)
}
//获取最新导入时间
export const getLastImportTime = (params, config = {}) => { return request.post(apiPrefix + 'GetLastImportTime', params, config) }

//获取采购资金明细趋势图 GetPurchaseFundDtlAnalysis
export const getPurchaseFundDtlAnalysis = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseFundDtlAnalysis', params, config) }

//GetSellCostStatus
export const GetSellCostStatus = (params, config = {}) => { return request.post(apiPrefix + 'GetSellCostStatus', params, config) }

// 分页获取滞销明细列表
export const getPurchaseNewInventoryUnsalableDtlPage = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseNewInventoryUnsalableDtlPage', params, config) }

// 导出滞销明细
// export const exportPurchaseNewInventoryUnsalableDtl = (params, config = {}) => { return request.post(apiPrefix + 'ExportPurchaseNewInventoryUnsalableDtl', params, config) }

// 导出滞销明细
export const exportPurchaseNewInventoryUnsalableDtl = (
    params,
    config = { responseType: "blob" }
  ) => {
    return request.post(
      apiPrefix + "ExportPurchaseNewInventoryUnsalableDtl",
      params,
      config
    );
  };

// 获取滞销明细趋势图
export const getPurchaseNewInventoryUnsalableDtlAnalysis = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseNewInventoryUnsalableDtlAnalysis', params, config) }

//采购个人周转天数-动销明细-分页查询
export const gtePurchaseNewInventoryDongXiaoDtlPage = (params, config = {}) => { return request.post(apiPrefix + 'GtePurchaseNewInventoryDongXiaoDtlPage', params, config) }

//采购个人周转天数-动销明细-导出
export const exportPurchaseNewInventoryDongXiaoDtl = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPurchaseNewInventoryDongXiaoDtl', params, config) }

//采购个人周转天数-动销明细-饼状图
export const getPurchaseNewInventoryDongXiaoPieChart = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseNewInventoryDongXiaoPieChart', params, config) }

//采购个人周转天数-动销明细-设置
export const editPurchaseNewInventoryDongXiaoSet = (params, config = {}) => { return request.post(apiPrefix + 'EditPurchaseNewInventoryDongXiaoSet', params, config) }

//采购个人周转天数-动销明细-获取设置
export const getPurchaseNewInventoryDongXiaoSet = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseNewInventoryDongXiaoSet', params, config) }

//采购个人周转天数-动销明细-获取采购单数据
export const getDongXiaoGoodsPurchaseOrder = (params, config = {}) => { return request.post(apiPrefix + 'GetDongXiaoGoodsPurchaseOrder', params, config) }

//采购个人周转天数-动销明细-获取采购单数据
export const getDongXiaoBrandFilterDropdownData = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseNewInventoryDongXiaoBrandFilterDropdownData', params, config) }

//采购个人周转天数-动销明细-获取采购单数据
export const getDongXiaoBrandFilterSet = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseNewInventoryDongXiaoBrandFilterSet', params, config) }

//采购个人周转天数-动销明细-获取采购单数据
export const saveDongXiaoBrandFilterSet = (params, config = {}) => { return request.post(apiPrefix + 'SavePurchaseNewInventoryDongXiaoBrandFilterSet', params, config) }

//采购个人周转天数-动销明细-获取采购单数据
export const delDongXiaoBrandFilterSet = (params, config = {}) => { return request.post(apiPrefix + 'DelPurchaseNewInventoryDongXiaoBrandFilterSet', params, config) }
