import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OpenPlatform}/ChooseGoodsCenter/`

//后台列表 GetChooseGoodsCenterProductSetList
export const getChooseGoodsCenterProductSetList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetChooseGoodsCenterProductSetList', params, config)
}

//获取业务类目  GetSetBusinessCategory
export const getSetBusinessCategory = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetSetBusinessCategory',params, config)
}

//修改产品设置 ChangeChooseGoodsCenterProductSet
export const changeChooseGoodsCenterProductSet = (params,config ={}) =>{
    return request.post(apiPrefix + 'ChangeChooseGoodsCenterProductSet',params, config)
}

//校验仓位 ValidateWarePosition
export const validateWarePosition = (params,config ={}) =>{
    return request.post(apiPrefix + 'ValidateWarePosition',params, config)
}