import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/InventoryOrder/`
//导入超卖
export const importStockExceedAndOtherWareAsync = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ImportStockExceedAndOtherWareAsync', params, config)
}
//实时缺货订单分页查询
export const getRealTimeLackOrderPageList = (params, config) => { return request.post(apiPrefix + 'GetRealTimeLackOrderPageList', params, config) }
//导入缺货订单
export const importRealTimeLackOrderAsync = (params, config) => { return request.post(apiPrefix + 'ImportRealTimeLackOrderAsync', params, config) }
//导入补货订单
export const importReplenishOrderAsync = (params, config) => { return request.post(apiPrefix + 'ImportReplenishOrderAsync', params, config) }

//实时补货订单分页查询
export const getReplenisOrderPageList = (params, config) => { return request.post(apiPrefix + 'GetReplenisOrderPageList', params, config) }

export const getInventoryOrderLackOrderMap = (params, config) => { return request.get(apiPrefix + 'GetInventoryOrderLackOrderMap', { params: params, ...config }) }

//仓库库位通道分页查询
export const getWareAislePageList = (params, config) => { return request.post(apiPrefix + 'GetWareAislePageList', params, config) }
//新增或编辑通道
export const addOrUpdateWareAisle = (params, config) => { return request.post(apiPrefix + 'AddOrUpdateWareAisle', params, config) }

export const getWareAisleUser = (params, config) => { return request.get(apiPrefix + 'GetWareAisleUser', { params: params, ...config }) }
