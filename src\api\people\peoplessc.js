import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_JAVA_API_BLADEGATEWAY}/yunhan-gis-personnel/`
// const apiPrefix = `/yunhan-gis-personnel/`


//获取全区域人员
export const allRegionPersonPage = (params, config = {}) => {
  return request.post(apiPrefix + 'allRegionPersonPage', params, config)
}

//招聘专员查询分页
export const recruitDirectorPage = (params, config = {}) => {
  return request.post(apiPrefix + 'recruitDirectorPage', params, config)
}

export const recruitDirectorRemove = (params) => {
    return request.post(apiPrefix + 'recruitDirectorRemove?ids='+params)
}

export const departmentDimensionRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'departmentDimensionRemove', params, config)
}

export const trainingDataRemove = (params) => {
    return request.post(apiPrefix + 'trainingDataRemove?ids='+params)
}

export const dimissionManageRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'dimissionManageRemove', params, config)
}

export const sscDataRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'sscDataRemove', params, config)
}


export const allRegionPersonRemove = (params, config = {}) => {
  return request.post(apiPrefix + 'allRegionPersonRemove', params, config)
}

//部门维度查询分页
export const departmentDimensionPage = (params, config = {}) => {
  return request.post(apiPrefix + 'departmentDimensionPage', params, config)
}

//全区域人员提交
export const allRegionPersonSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'allRegionPersonSubmit', params, config)
}
//    离职管理列表
export const dimissionManagePage = (params, config = {}) => {
    return request.post(apiPrefix + 'dimissionManagePage', params, config)
}

// 离职管理修改
export const dimissionManageSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'dimissionManageSubmit', params, config)
}


// sscDataPage   ssc 数据分页
export const sscDataPage = (params, config = {}) => {
    return request.post(apiPrefix + 'sscDataPage', params, config)
}

// trainingDataPage   培训数据分页
export const trainingDataPage = (params, config = {}) => {
    return request.post(apiPrefix + 'trainingDataPage', params, config)
}

// trainingDataSubmit  培训编辑
export const trainingDataSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'trainingDataSubmit', params, config)
}
// sscDataSubmit   ssc编辑
export const sscDataSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'sscDataSubmit', params, config)
}

// trainingDataArchive: 培训存档
export const trainingDataArchive = (params, config = {}) => {
    return request.post(apiPrefix + 'trainingDataArchive', params, config)
}
// departmentDimensionArchive  部门维度存档
export const departmentDimensionArchive = (params, config = {}) => {
    return request.post(apiPrefix + 'departmentDimensionArchive', params, config)
}
// dimissionManageArchive  离职管理 存档
export const dimissionManageArchive = (params, config = {}) => {
    return request.post(apiPrefix + 'dimissionManageArchive', params, config)
}
// recruitDirectorArchive   招聘专员 存档
export const recruitDirectorArchive = (params, config = {}) => {
    return request.post(apiPrefix + 'recruitDirectorArchive', params, config)
}
// sscDataArchive  SSC数据 存档
export const sscDataArchive = (params, config = {}) => {
    return request.post(apiPrefix + 'sscDataArchive', params, config)
}

// recruitDirectorExport  招聘专员导出
export const recruitDirectorExport = (params, config = { responseType: 'blob' }) => {
        return request.post(apiPrefix + 'recruitDirectorExport', params, config)
}
// departmentDimensionExport  部门维度导出
export const departmentDimensionExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'departmentDimensionExport', params, config)
}

// 部门维度导入
export const departmentDimensionImport = (params, config = {}) => {
    return request.post(apiPrefix + 'departmentDimensionImport', params, config)
}

// 离职管理: dimissionManageImport
export const dimissionManageImport = (params, config = {}) => {
    return request.post(apiPrefix + 'dimissionManageImport', params, config)
}
// 招聘专员: recruitDirectorImport
export const recruitDirectorImport = (params, config = {}) => {
    return request.post(apiPrefix + 'recruitDirectorImport', params, config)
}
// SSC数据: sscDataImport
export const sscDataImport = (params, config = {}) => {
    return request.post(apiPrefix + 'sscDataImport', params, config)
}
// 培训数据: trainingDataImport
export const trainingDataImport = (params, config = {}) => {
    return request.post(apiPrefix + 'trainingDataImport', params, config)
}

// recruitDirectorSubmit  招聘专员保存
export const recruitDirectorSubmit = (params, config = {}) => {
  return request.post(apiPrefix + 'recruitDirectorSubmit', params, config)
}

// departmentDimensionSubmit  部门维度保存
export const departmentDimensionSubmit = (params, config = {}) => {
  return request.post(apiPrefix + 'departmentDimensionSubmit', params, config)
}

// allRegionPersonArchive  QUANYU
export const allRegionPersonArchive = (params, config = {}) => {
    return request.post(apiPrefix + 'allRegionPersonArchive', params, config)
}

// 全区域: allRegionPersonImport
export const allRegionPersonImport = (params, config = {}) => {
    return request.post(apiPrefix + 'allRegionPersonImport', params, config)
}



// export const getTrendChart = (params, config = {}) => {
//     return request.post(apiPrefix + 'GetTrendChart', params, config)
// }

// export const Export = (params, config = { responseType: 'blob' }) => {
//     return request.post(apiPrefix + 'Export', params, config)
// }
// 运营能效-明细数据
export const getOperatePerformancePage = (params, config = {}) => {
  return request.post(apiPrefix + 'getOperatePerformancePage', params, config)
}

// 运营能效-明细数据趋势图
export const getOperatePerformanceReport = (params, config = {}) => {
  return request.post(apiPrefix + 'getOperatePerformanceReport', params, config)
}


//运营能效占比
export const getOperatePerformanceProportionPage = (params, config = {}) => {
    return request.post(apiPrefix + 'getOperatePerformanceProportionPage', params, config)
}

//运营能效统计图
export const getOperatePerformanceProportionPageReport  = (params, config = {}) => {
    return request.post(apiPrefix + 'getOperatePerformanceProportionPageReport', params, config)
}

//运营能效区域下拉
export const getOperateListValue  = (params, config = {}) => {
    return request.post(apiPrefix + 'getOperateListValue?fieldName='+params, config)
}

//运营能效下拉远程搜索
export const getOperateListValueRemote = (params, config = {}) => {
  const queryParams = new URLSearchParams({
    currentPage: params.currentPage,
    pageSize: params.pageSize,
    fieldName: params.fieldName,
    value: params.value,
  }).toString();
  return request.post(`${apiPrefix}getOperateListValue?${queryParams}`, {}, config);
}

//A页面列表接口
export const getOperateHumanEffectivenessList  = (params, config = {}) => {
    return request.post(apiPrefix + 'getOperateHumanEffectivenessList', params, config)
}

//A页面列表接口
export const getOperateHumanEffectivenessSubListReport  = (params, config = {}) => {
    return request.post(apiPrefix + 'getOperateHumanEffectivenessSubListReport', params, config)
}

//A页面列表接口
export const getOperateHumanEffectivenessReport  = (params, config = {}) => {
    return request.post(apiPrefix + 'getOperateHumanEffectivenessReport', params, config)
}


// 运营占比
export const getOperatePerformanceProportionExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'getOperatePerformanceProportionExport', params, config)
}

// 运营明细
export const getOperatePerformanceExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'getOperatePerformanceExport', params, config)
}

// 人效导出
export const getOperateHumanEffectivenessReportExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'getOperateHumanEffectivenessReportExport', params, config)
}

//创建会话接口 createNewSession
export const createNewSession = (params, config = {}) => {
    return request.post(apiPrefix + 'createNewSession', params, config)
}

//用户会话列表查询接口 getSessionList
export const getSessionList = (params, config = {}) => {
    return request.post(apiPrefix + 'getSessionList', params, config)
}

//AI聊天智能问答接口 pullExperienceMessage
export const pullExperienceMessage = (params, config = {}) => {
    return request.post(apiPrefix + 'pullExperienceMessage', params, config)
}

//会话聊天明细查询接口 getSessionContentList
export const getSessionContentList = (params, config = {}) => {
    return request.post(apiPrefix + 'getSessionContentList', params, config)
}

//会话删除 delSession
export const delSession = (params, config = {}) => {
    return request.post(apiPrefix + 'delSession', params, config)
}

//停止会话 stopExperienceMessage
export const stopExperienceMessage = (params, config = {}) => {
    return request.post(apiPrefix + 'stopExperienceMessage', params, config)
}

//人员分析查询接口
export const personAnalysisPage = (params, config = {}) => {
    return request.post(apiPrefix + 'personAnalysisPage', params, config)
}

//人员分析添加接口
export const personAnalysisSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'personAnalysisSubmit', params, config)
}

//人员分析删除接口
export const personAnalysisRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'personAnalysisRemove?ids='+params.ids, params, config)
}

//导入
export const personAnalysisImport = (params, config = {}) => {
    return request.post(apiPrefix + 'personAnalysisImport', params, config)
}
//导出
export const personAnalysisExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'personAnalysisExport', params, config)
}

// er/
//人员分析查询接口
export const monthArchivesAnalysisPage = (params, config = {}) => {
    return request.post(apiPrefix + 'monthArchivesAnalysisPage', params, config)
}

//人员分析添加接口
export const monthArchivesAnalysisSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'monthArchivesAnalysisSubmit', params, config)
}

//人员分析删除接口
export const monthArchivesAnalysisRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'monthArchivesAnalysisRemove?ids='+params.ids, params, config)
}

//导入
export const monthArchivesAnalysisImport = (params, config = {}) => {
    return request.post(apiPrefix + 'monthArchivesAnalysisImport', params, config)
}
//导出
export const monthArchivesAnalysisExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'monthArchivesAnalysisExport', params, config)
}

// er/
//人员分析查询接口
export const temporaryLaborCostsPage = (params, config = {}) => {
    return request.post(apiPrefix + 'temporaryLaborCostsPage', params, config)
}

//人员分析添加接口
export const temporaryLaborCostsSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'temporaryLaborCostsSubmit', params, config)
}

//人员分析删除接口
export const temporaryLaborCostsRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'temporaryLaborCostsRemove?ids='+params.ids, params, config)
}

//导入
export const temporaryLaborCostsImport = (params, config = {}) => {
    return request.post(apiPrefix + 'temporaryLaborCostsImport', params, config)
}
//导出
export const temporaryLaborCostsExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'temporaryLaborCostsExport', params, config)
}

//人员下拉1
export const personAnalysisListValue = (params, config = {}) => {
    return request.post(apiPrefix + 'personAnalysisListValue?fieldName='+params.fieldName, params, config)
}


//人员下拉2
export const monthArchivesAnalysisListValue = (params, config = {}) => {
    return request.post(apiPrefix + 'monthArchivesAnalysisListValue?fieldName='+params.fieldName, params, config)
}

//人员下拉3
export const temporaryLaborCostsListValue = (params, config = {}) => {
    return request.post(apiPrefix + 'temporaryLaborCostsListValue?fieldName='+params.fieldName, params, config)
}

//趋势图
export const basisTrendChart = (params, config = {}) => {
    return request.post(apiPrefix + 'homePage/basisTrendChart', params, config)
}

//下拉
export const blockList = (params, config = {}) => {
    return request.post(apiPrefix + 'homePage/blockList', params, config)
}

//下拉
export const getDataListByBlock = (params, config = {}) => {
    return request.post(apiPrefix + 'homePage/getDataListByBlock?blockType='+params.blockType, params, config)
}

//下拉
export const getDeptList = (params, config = {}) => {
    return request.post(apiPrefix + 'homePage/getDeptList?deptType='+params.deptType, params, config)
}

//趋势图
export const trendChart = (params, config = {}) => {
    return request.post(apiPrefix + 'homePage/trendChart', params, config)
}

// oa查询
export const oALedgerPage = (params, config = {}) => {
    return request.post(apiPrefix + 'oALedgerPage', params, config)
}

//导入
export const oALedgerImport = (params, config = {}) => {
    return request.post(apiPrefix + 'oALedgerImport', params, config)
}


//提交
export const oALedgerSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'oALedgerSubmit', params, config)
}

//删除 oALedgerRemove
export const oALedgerRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'oALedgerRemove?ids='+params.ids, params, config)
}

/////

// 采购预算
export const procurementBudgetTablePage = (params, config = {}) => {
    return request.post(apiPrefix + 'procurementBudgetTablePage', params, config)
}

//导入
export const procurementBudgetTableImport = (params, config = {}) => {
    return request.post(apiPrefix + 'procurementBudgetTableImport', params, config)
}


//提交
export const procurementBudgetTableSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'procurementBudgetTableSubmit', params, config)
}

//删除 procurementBudgetTableRemove
export const procurementBudgetTableRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'procurementBudgetTableRemove?ids='+params.ids, params, config)
}

///////员工餐
export const employeeMealPage = (params, config = {}) => {
    return request.post(apiPrefix + 'employeeMealPage', params, config)
}

//导入
export const employeeMealImport = (params, config = {}) => {
    return request.post(apiPrefix + 'employeeMealImport', params, config)
}


//提交
export const employeeMealSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'employeeMealSubmit', params, config)
}

//删除 employeeMealRemove
export const employeeMealRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'employeeMealRemove?ids='+params.ids, params, config)
}

///////电费
export const electricityBillPage = (params, config = {}) => {
    return request.post(apiPrefix + 'electricityBillPage', params, config)
}

//导入
export const electricityBillImport = (params, config = {}) => {
    return request.post(apiPrefix + 'electricityBillImport', params, config)
}


//提交
export const electricityBillSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'electricityBillSubmit', params, config)
}

//删除 employeeMealRemove
export const electricityBillRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'electricityBillRemove?ids='+params.ids, params, config)
}

///////工位
export const workstationTablePage = (params, config = {}) => {
    return request.post(apiPrefix + 'workstationTablePage', params, config)
}

//导入
export const workstationTableImport = (params, config = {}) => {
    return request.post(apiPrefix + 'workstationTableImport', params, config)
}


//提交
export const workstationTableSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'workstationTableSubmit', params, config)
}

//删除 employeeMealRemove
export const workstationTableRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'workstationTableRemove?ids='+params.ids, params, config)
}

///////总违纪
export const violationDisciplinePage = (params, config = {}) => {
    return request.post(apiPrefix + 'violationDisciplinePage', params, config)
}

//导入
export const departmentalViolationsImport = (params, config = {}) => {
    return request.post(apiPrefix + 'departmentalViolationsImport', params, config)
}


//提交
export const violationDisciplineSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'violationDisciplineSubmit', params, config)
}

//删除 employeeMealRemove
export const violationDisciplineRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'violationDisciplineRemove?ids='+params.ids, params, config)
}

///////部门违纪
export const departmentalViolationsPage = (params, config = {}) => {
    return request.post(apiPrefix + 'departmentalViolationsPage', params, config)
}

//导入
export const violationDisciplineImport = (params, config = {}) => {
    return request.post(apiPrefix + 'violationDisciplineImport', params, config)
}


//提交
export const departmentalViolationsSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'departmentalViolationsSubmit', params, config)
}

//删除 employeeMealRemove
export const departmentalViolationsRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'departmentalViolationsRemove?ids='+params.ids, params, config)
}

///////绩效乐捐
export const performanceDonationPage = (params, config = {}) => {
    return request.post(apiPrefix + 'performanceDonationPage', params, config)
}

//导入
export const performanceDonationImport = (params, config = {}) => {
    return request.post(apiPrefix + 'performanceDonationImport', params, config)
}


//提交
export const performanceDonationSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'performanceDonationSubmit', params, config)
}

//删除 employeeMealRemove
export const performanceDonationRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'performanceDonationRemove?ids='+params.ids, params, config)
}

///////员工活动台账
export const employeeActivityLedgerPage = (params, config = {}) => {
    return request.post(apiPrefix + 'employeeActivityLedgerPage', params, config)
}

//导入
export const employeeActivityLedgerImport = (params, config = {}) => {
    return request.post(apiPrefix + 'employeeActivityLedgerImport', params, config)
}


//提交
export const employeeActivityLedgerSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'employeeActivityLedgerSubmit', params, config)
}

//删除 employeeMealRemove
export const employeeActivityLedgerRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'employeeActivityLedgerRemove?ids='+params.ids, params, config)
}

///////公关
export const pRReceptionPage = (params, config = {}) => {
    return request.post(apiPrefix + 'pRReceptionPage', params, config)
}

//导入
export const pRReceptionImport = (params, config = {}) => {
    return request.post(apiPrefix + 'pRReceptionImport', params, config)
}


//提交
export const pRReceptionSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'pRReceptionSubmit', params, config)
}

//删除 employeeMealRemove
export const pRReceptionRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'pRReceptionRemove?ids='+params.ids, params, config)
}

///////押金
export const depositPage = (params, config = {}) => {
    return request.post(apiPrefix + 'depositPage', params, config)
}

//导入
export const depositImport = (params, config = {}) => {
    return request.post(apiPrefix + 'depositImport', params, config)
}


//提交
export const depositSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'depositSubmit', params, config)
}

//删除 employeeMealRemove
export const depositRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'depositRemove?ids='+params.ids, params, config)
}

///////满意度评分
export const satisfactionRatingPage = (params, config = {}) => {
    return request.post(apiPrefix + 'satisfactionRatingPage', params, config)
}

//导入
export const satisfactionRatingImport = (params, config = {}) => {
    return request.post(apiPrefix + 'satisfactionRatingImport', params, config)
}


//提交
export const satisfactionRatingSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'satisfactionRatingSubmit', params, config)
}

//删除 employeeMealRemove
export const satisfactionRatingRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'satisfactionRatingRemove?ids='+params.ids, params, config)
}

///////固定资产
export const fixedAssetsPage = (params, config = {}) => {
    return request.post(apiPrefix + 'fixedAssetsPage', params, config)
}

//导入
export const fixedAssetsImport = (params, config = {}) => {
    return request.post(apiPrefix + 'fixedAssetsImport', params, config)
}


//提交
export const fixedAssetsSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'fixedAssetsSubmit', params, config)
}

//删除 employeeMealRemove
export const fixedAssetsRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'fixedAssetsRemove?ids='+params.ids, params, config)
}

///////自查
export const regionalMonitoringSelfInspectionPage = (params, config = {}) => {
    return request.post(apiPrefix + 'regionalMonitoringSelfInspectionPage', params, config)
}

//导入
export const regionalMonitoringSelfInspectionImport = (params, config = {}) => {
    return request.post(apiPrefix + 'regionalMonitoringSelfInspectionImport', params, config)
}


//提交
export const regionalMonitoringSelfInspectionSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'regionalMonitoringSelfInspectionSubmit', params, config)
}

//删除 employeeMealRemove
export const regionalMonitoringSelfInspectionRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'regionalMonitoringSelfInspectionRemove?ids='+params.ids, params, config)
}

///////互查
export const regionalMonitoringMutualInspectionPage = (params, config = {}) => {
    return request.post(apiPrefix + 'regionalMonitoringMutualInspectionPage', params, config)
}

//导入
export const regionalMonitoringMutualInspectionImport = (params, config = {}) => {
    return request.post(apiPrefix + 'regionalMonitoringMutualInspectionImport', params, config)
}


//提交
export const regionalMonitoringMutualInspectionSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'regionalMonitoringMutualInspectionSubmit', params, config)
}

//删除 employeeMealRemove
export const regionalMonitoringMutualInspectionRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'regionalMonitoringMutualInspectionRemove?ids='+params.ids, params, config)
}

//仓储看板-导入
export const warehouseWaterElectricityImport = (params, config = {}) => {
  return request.post(apiPrefix + 'warehouseWaterElectricityImport', params, config)
}

//仓储看板-仓库下拉
export const warehouseWaterElectricityListValue = (params, config = {}) => {
  return request.post(apiPrefix + 'warehouseWaterElectricityListValue?fieldName='+params, config)
}

//仓储看板-查询分页
export const warehouseWaterElectricityPage = (params, config = {}) => {
  return request.post(apiPrefix + 'warehouseWaterElectricityPage', params, config)
}

//仓储看板-编辑
export const warehouseWaterElectricitySubmit = (params, config = {}) => {
  return request.post(apiPrefix + 'warehouseWaterElectricitySubmit', params, config)
}

//仓储看板-删除
export const warehouseWaterElectricityRemove = (params, config = {}) => {
  return request.post(apiPrefix + 'warehouseWaterElectricityRemove?ids='+params.ids, params, config)
}

//仓储看板-仓储乐捐-导入
export const warehouseDonateVoluntarilyImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseDonateVoluntarilyImport', params, config)
}

//仓储看板-仓储乐捐-分页查询
export const warehouseDonateVoluntarilyPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseDonateVoluntarilyPage', params, config)
}
//仓储看板-仓储乐捐-编辑
export const warehouseDonateVoluntarilySubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseDonateVoluntarilySubmit', params, config)
}
export const warehouseDonateVoluntarilyRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseDonateVoluntarilyRemove?ids='+params.ids, params, config)
}

///////仓储宿舍
export const warehouseHostelPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseHostelPage', params, config)
}

//导入
export const warehouseHostelImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseHostelImport', params, config)
}


//提交
export const warehouseHostelSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseHostelSubmit', params, config)
}

//删除 employeeMealRemove
export const warehouseHostelRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseHostelRemove?ids='+params.ids, params, config)
}

///////稽查
export const warehouseInspectionSituationPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseInspectionSituationPage', params, config)
}

//导入
export const warehouseInspectionSituationImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseInspectionSituationImport', params, config)
}


//提交
export const warehouseInspectionSituationSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseInspectionSituationSubmit', params, config)
}

//删除 employeeMealRemove
export const warehouseInspectionSituationRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseInspectionSituationRemove?ids='+params.ids, params, config)
}

///////工伤台账
export const warehouseWorkInjuryLedgerPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseWorkInjuryLedgerPage', params, config)
}

//导入
export const warehouseWorkInjuryLedgerImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseWorkInjuryLedgerImport', params, config)
}


//提交
export const warehouseWorkInjuryLedgerSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseWorkInjuryLedgerSubmit', params, config)
}

//删除 employeeMealRemove
export const warehouseWorkInjuryLedgerRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseWorkInjuryLedgerRemove?ids='+params.ids, params, config)
}

///////固定资产
export const warehouseFixedAssetsPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseFixedAssetsPage', params, config)
}

//导入
export const warehouseFixedAssetsImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseFixedAssetsImport', params, config)
}


//提交
export const warehouseFixedAssetsSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseFixedAssetsSubmit', params, config)
}

//删除 employeeMealRemove
export const warehouseFixedAssetsRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseFixedAssetsRemove?ids='+params.ids, params, config)
}

///////三方比价
export const warehousePriceComparisonPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehousePriceComparisonPage', params, config)
}

//导入
export const warehousePriceComparisonImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehousePriceComparisonImport', params, config)
}


//提交
export const warehousePriceComparisonSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehousePriceComparisonSubmit', params, config)
}

//删除 employeeMealRemove
export const warehousePriceComparisonRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehousePriceComparisonRemove?ids='+params.ids, params, config)
}

///////oa台账
export const warehouseOALedgerPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseOALedgerPage', params, config)
}

//导入
export const warehouseOALedgerImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseOALedgerImport', params, config)
}


//提交
export const warehouseOALedgerSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseOALedgerSubmit', params, config)
}

//删除 employeeMealRemove
export const warehouseOALedgerRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseOALedgerRemove?ids='+params.ids, params, config)
}


///////AI工具配置
export const aiToolConfigPage = (params, config = {}) => {
    return request.post(apiPrefix + 'aiToolConfigPage', params, config)
}

export const aiTableFieldConfigTree = (params,config ={}) =>{
    return request.get(apiPrefix+'aiTableFieldConfigTree?tableName='+params, params, config)
}

//下拉
export const aiToolConfigListValue = (params, regionName, value, config = {}) => {
    return request.post(apiPrefix + 'aiToolConfigListValue?fieldName='+params+'&regionName='+regionName+'&isMain='+value, config)
}


//提交
export const aiToolConfigSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'aiToolConfigSubmit', params, config)
}

//拷贝
export const copyAiToolConfig = (params, config = {}) => {
    return request.post(apiPrefix + 'copyAiToolConfig', params, config)
}

//删除 employeeMealRemove
export const aiToolConfigRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'aiToolConfigRemove?ids='+params.ids, params, config)
}

///////AI模型配置
export const aiLlmConfigPage = (params, config = {}) => {
    return request.post(apiPrefix + 'aiLlmConfigPage', params, config)
}

//下拉
export const aiLlmConfigListValue = (params, config = {}) => {
    return request.post(apiPrefix + 'aiLlmConfigListValue?fieldName='+params, config)
}


//提交
export const aiLlmConfigSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'aiLlmConfigSubmit', params, config)
}

//删除 employeeMealRemove
export const aiLlmConfigRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'aiLlmConfigRemove?ids='+params.ids, params, config)
}


export const aiDbConfigPage = (params, config = {}) => {
    return request.post(apiPrefix + 'aiDbConfigPage', params, config)
}

//下拉
export const aiDbConfigListValue = (params, config = {}) => {
    return request.post(apiPrefix + 'aiDbConfigListValue?fieldName='+params, config)
}


//提交
export const aiDbConfigSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'aiDbConfigSubmit', params, config)
}

//删除 employeeMealRemove
export const aiDbConfigRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'aiDbConfigRemove?ids='+params.ids, params, config)
}

export const aiTableFieldConfigListValue = (params,name, config = {}) => {
    return request.post(apiPrefix + 'aiTableFieldConfigListValue?fieldName='+params+'&databaseName='+name, config)
}

//查询
export const aiTableFieldConfigPage = (params, config = {}) => {
	return request.post(`${apiPrefix}aiTableFieldConfigPage`, params, config);
};

//创建
export const aiTableFieldConfigSubmit = (params, config = {}) => {
	return request.post(`${apiPrefix}aiTableFieldConfigSubmit`, params, config);
};

//删除
export const aiTableFieldConfigRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'aiTableFieldConfigRemove?ids='+params.ids, params, config)
}

export const aiTableFieldConfigImport = (params, config = {}) => {
    return request.post(apiPrefix + 'aiTableFieldConfigImport', params, config)
}


//行政数据可视化面板第一张表
export const selectAdministrativeData = (params, config = {}) => {
    return request.post(apiPrefix + 'selectAdministrativeData', params, config)
}

//行政数据可视化面板第二张表
export const selectAdministrativeDataTwo = (params, config = {}) => {
    return request.post(apiPrefix + 'selectAdministrativeDataTwo', params, config)
}

//行政数据可视化面板同比数据下拉接口
export const getDataListByBlockNew = (params, config = {}) => {
  return request.get(apiPrefix + 'getDataListByBlock', { params: params, ...config })
}

//仓储离职岗位排名-查询
export const rankingOfResignationPositionsInWarehousingPage = (params, config = {}) => {
    return request.post(apiPrefix + 'rankingOfResignationPositionsInWarehousingPage', params, config)
}

//仓储离职岗位排名-导入
export const rankingOfResignationPositionsInWarehousingImport = (params, config = {}) => {
    return request.post(apiPrefix + 'rankingOfResignationPositionsInWarehousingImport', params, config)
}

//仓储离职岗位排名-导出
export const rankingOfResignationPositionsInWarehousingExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'rankingOfResignationPositionsInWarehousingExport', params, config)
}

//仓储离职岗位排名-删除
export const rankingOfResignationPositionsInWarehousingRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'rankingOfResignationPositionsInWarehousingRemove?ids='+params.ids, params, config)
}

//仓储离职岗位排名-编辑
export const rankingOfResignationPositionsInWarehousingSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'rankingOfResignationPositionsInWarehousingSubmit', params, config)
}

//各区工伤赔付情况-编辑
export const workInjuryCompensationSituationInEachDistrictPage = (params, config = {}) => {
    return request.post(apiPrefix + 'workInjuryCompensationSituationInEachDistrictPage', params, config)
}

//各区工伤赔付情况-导入
export const workInjuryCompensationSituationInEachDistrictImport = (params, config = {}) => {
    return request.post(apiPrefix + 'workInjuryCompensationSituationInEachDistrictImport', params, config)
}

//各区工伤赔付情况-导出
export const workInjuryCompensationSituationInEachDistrictExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'workInjuryCompensationSituationInEachDistrictExport', params, config)
}

//各区工伤赔付情况-删除
export const workInjuryCompensationSituationInEachDistrictRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'workInjuryCompensationSituationInEachDistrictRemove?ids='+params.ids, params, config)
}

//各区工伤赔付情况-编辑
export const workInjuryCompensationSituationInEachDistrictSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'workInjuryCompensationSituationInEachDistrictSubmit', params, config)
}


//查询1
export const warehouseDormitoryOccupancyPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseDormitoryOccupancyPage', params, config)
}

//导入2
export const warehouseDormitoryOccupancyImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseDormitoryOccupancyImport', params, config)
}

//导出3
export const warehouseDormitoryOccupancyExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'warehouseDormitoryOccupancyExport', params, config)
}

//删除4
export const warehouseDormitoryOccupancyRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseDormitoryOccupancyRemove?ids='+params.ids, params, config)
}

//编辑5
export const warehouseDormitoryOccupancySubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseDormitoryOccupancySubmit', params, config)
}

//查询1
export const warehouseEmployerLiabilityInsurancePage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseEmployerLiabilityInsurancePage', params, config)
}

//导入2
export const warehouseEmployerLiabilityInsuranceImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseEmployerLiabilityInsuranceImport', params, config)
}

//导出3
export const warehouseEmployerLiabilityInsuranceExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'warehouseEmployerLiabilityInsuranceExport', params, config)
}

//删除4
export const warehouseEmployerLiabilityInsuranceRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseEmployerLiabilityInsuranceRemove?ids='+params.ids, params, config)
}

//编辑5
export const warehouseEmployerLiabilityInsuranceSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseEmployerLiabilityInsuranceSubmit', params, config)
}

//仓储行政看板-水电费-查询
export const warehouseUtilityBillsPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseUtilityBillsPage', params, config)
}

//仓储行政看板-水电费-导入
export const warehouseUtilityBillsImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseUtilityBillsImport', params, config)
}

//仓储行政看板-水电费-删除
export const warehouseUtilityBillsRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseUtilityBillsRemove?ids='+ params.ids, params, config)
}

//仓储行政看板-水电费-编辑
export const warehouseUtilityBillsSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseUtilityBillsSubmit', params, config)
}

//仓储行政看板-仓储稽查乐捐-查询
export const warehouseInspectionDonationPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseInspectionDonationPage', params, config)
}

//仓储行政看板-仓储稽查乐捐-导入
export const warehouseInspectionDonationImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseInspectionDonationImport', params, config)
}

//仓储行政看板-仓储稽查乐捐-删除
export const warehouseInspectionDonationRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseInspectionDonationRemove?ids='+ params.ids, params, config)
}

//仓储行政看板-仓储稽查乐捐-编辑
export const warehouseInspectionDonationSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseInspectionDonationSubmit', params, config)
}

//仓储行政看板-仓储维修-查询
export const warehouseMaintenancePage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseMaintenancePage', params, config)
}

//仓储行政看板-仓储维修-导入
export const warehouseMaintenanceImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseMaintenanceImport', params, config)
}

//仓储行政看板-仓储维修-删除
export const warehouseMaintenanceRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseMaintenanceRemove?ids='+ params.ids, params, config)
}

//仓储行政看板-仓储维修-编辑
export const warehouseMaintenanceSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseMaintenanceSubmit', params, config)
}

//仓储行政看板-仓储发货数据-查询
export const warehouseShipmentDataPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseShipmentDataPage', params, config)
}

//仓储行政看板-仓储发货数据-导入
export const warehouseShipmentDataImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseShipmentDataImport', params, config)
}

//仓储行政看板-仓储发货数据-删除
export const warehouseShipmentDataRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseShipmentDataRemove?ids='+ params.ids, params, config)
}

//仓储行政看板-仓储发货数据-编辑
export const warehouseShipmentDataSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseShipmentDataSubmit', params, config)
}

//仓储行政看板-邮政食堂费用收入明细-查询
export const warehouseCanteenIncomeDetailPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseCanteenIncomeDetailPage', params, config)
}

//仓储行政看板-邮政食堂费用收入明细-导入
export const warehouseCanteenIncomeDetailImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseCanteenIncomeDetailImport', params, config)
}

//仓储行政看板-邮政食堂费用收入明细-删除
export const warehouseCanteenIncomeDetailRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseCanteenIncomeDetailRemove?ids='+ params.ids, params, config)
}

//仓储行政看板-邮政食堂费用收入明细-编辑
export const warehouseCanteenIncomeDetailSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseCanteenIncomeDetailSubmit', params, config)
}

//仓储行政看板-食堂收支汇总表-查询
export const warehouseCanteenRevenueDataPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseCanteenRevenueDataPage', params, config)
}

//仓储行政看板-食堂收支汇总表-导入
export const warehouseCanteenRevenueDataImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseCanteenRevenueDataImport', params, config)
}

//仓储行政看板-食堂收支汇总表-删除
export const warehouseCanteenRevenueDataRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseCanteenRevenueDataRemove?ids='+ params.ids, params, config)
}

//仓储行政看板-食堂收支汇总表-编辑
export const warehouseCanteenRevenueDataSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseCanteenRevenueDataSubmit', params, config)
}


//查询1
export const warehouseThirdPartyPriceComparisonPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseThirdPartyPriceComparisonPage', params, config)
}

//导入2
export const warehouseThirdPartyPriceComparisonImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseThirdPartyPriceComparisonImport', params, config)
}

//导出3
export const warehouseThirdPartyPriceComparisonExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'warehouseThirdPartyPriceComparisonExport', params, config)
}

//删除4
export const warehouseThirdPartyPriceComparisonRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseThirdPartyPriceComparisonRemove?ids='+params.ids, params, config)
}

//编辑5
export const warehouseThirdPartyPriceComparisonSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseThirdPartyPriceComparisonSubmit', params, config)
}

//查询1
export const warehouseExpenseDetailsPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseExpenseDetailsPage', params, config)
}

//导入2
export const warehouseExpenseDetailsImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseExpenseDetailsImport', params, config)
}

//导出3
export const warehouseExpenseDetailsExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'warehouseExpenseDetailsExport', params, config)
}

//删除4
export const warehouseExpenseDetailsRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseExpenseDetailsRemove?ids='+params.ids, params, config)
}

//编辑5
export const warehouseExpenseDetailsSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseExpenseDetailsSubmit', params, config)
}

//查询1
export const warehouseIncomeDetailsPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseIncomeDetailsPage', params, config)
}

//导入2
export const warehouseIncomeDetailsImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseIncomeDetailsImport', params, config)
}

//导出3
export const warehouseIncomeDetailsExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'warehouseIncomeDetailsExport', params, config)
}

//删除4
export const warehouseIncomeDetailsRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseIncomeDetailsRemove?ids='+params.ids, params, config)
}

//编辑5
export const warehouseIncomeDetailsSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseIncomeDetailsSubmit', params, config)
}

//查询1
export const warehouseStaffMealsPage = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseStaffMealsPage', params, config)
}

//导入2
export const warehouseStaffMealsImport = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseStaffMealsImport', params, config)
}

//导出3
export const warehouseStaffMealsExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'warehouseStaffMealsExport', params, config)
}

//删除4
export const warehouseStaffMealsRemove = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseStaffMealsRemove?ids='+params.ids, params, config)
}

//编辑5
export const warehouseStaffMealsSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'warehouseStaffMealsSubmit', params, config)
}
//请休假流程自动化-查询
export const leaveApprovalPage = (params, config = {}) => {
    return request.post(apiPrefix + 'leaveApprovalPage', params, config)
}

//请休假流程自动化-定时同步请休假数据
export const synchronizeLeaveAndVacationData = (params, config = {}) => {
    return request.post(apiPrefix + 'synchronizeLeaveAndVacationData', params, config)
}

//请休假流程自动化-批量审批请休假数据
export const batchApprovalOfLeaveApplicationData = (params, config = {}) => {
    return request.post(apiPrefix + 'batchApprovalOfLeaveApplicationData', params, config)
}
