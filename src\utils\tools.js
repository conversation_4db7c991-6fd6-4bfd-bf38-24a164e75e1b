import dayjs from 'dayjs'
const Passs = [{ label: '通过', value: false }, { label: '不通过', value: true }]
const Rule = [{ label: '通票1', value: 0 }, { label: '通票2', value: 1 }, { label: '拉均重', value: 2 }, { label: '阶梯', value: 3 }]
const warehouseformartlist = [{ label: '义乌市昀晗贸易有限公司', value: 0 }, { label: '南昌昌东', value: 1 },
{ label: '昌北', value: 2 }, { label: '罗兵安徽仓', value: 3 }, { label: '上海暖锦贸易有限公司', value: 4 },
{ label: '昀晗上海分仓', value: 5 }, { label: 'JD-昀晗义乌仓', value: 8 }, { label: '南昌定制仓', value: 9 }, { label: '圆通孵化仓', value: 10 }, { label: '义乌圆通仓', value: 11 }, { label: '圆通5楼仓', value: 12 }, { label: '昀晗-包装厂', value: 13 }, { label: '西安重件仓', value: 14 }, { label: '杭州昀晗仓', value: 15 }, { label: '南昌裁剪仓', value: 16 }, { label: '义乌邮政仓', value: 17 }]

const warehouselist = [{ label: '未知', value: 999999 }, { label: '义乌市昀晗贸易有限公司', value: 0 }, { label: '南昌昌东', value: 1 }, { label: '南昌定制仓', value: 9 }, { label: 'JD-昀晗义乌仓', value: 8 }, { label: '圆通孵化仓', value: 10 }, { label: '义乌圆通仓', value: 11 }, { label: '圆通5楼仓', value: 12 }, { value: 13, label: '昀晗-包装厂' }, { label: '西安重件仓', value: 14 }, { label: '杭州昀晗仓', value: 15 }, { label: '南昌裁剪仓', value: 16 }, { label: '义乌邮政仓', value: 17 }, { label: '昀晗-测试分仓', value: 18 }, { label: '【昀晗-JS】', value: 19 }, { label: '昀晗-昆山仓', value: 20 }, { label: '【昀晗-金华仓】', value: 24 }, { label: '【昀晗-WH】', value: 25 }, { label: '西安分仓', value: 26 }, { label: '【昀晗-YY】', value: 27 }, { label: '圆通2栋6楼', value: 28 }, { label: '【昀晗-DG】', value: 29 }, { label: '【昀晗-SZ】', value: 31 }, { label: '【南昌芒果仓】', value: 32 }, { label: '【昀晗-JD】', value: 33 }, { label: '【昀晗-NC】', value: 35 }, { label: '【昀晗-JM】', value: 37 }, { label: '【昀晗-CS】', value: 38 }, { label: '【昀晗-FZ】', value: 39 }, { label: '【昀晗-DY】', value: 41 }, { label: '【东莞全品类仓】', value: 40 }, { label: '罗小曼推单仓', value: 42 }, { label: '侨宝推单仓', value: 43 }]
const sendWarehouse = [{ value: 1, label: '（本仓）' }, { value: 2, label: '义乌四楼' }, { value: 3, label: '南昌昌东' }, { value: 4, label: '南昌昌北' }, { value: 5, label: '昀晗跨境仓' }, { value: 6, label: '南昌高科云仓' }, { value: 7, label: '安徽' }, { value: 8, label: '上海暖锦' }, { value: 9, label: '上海分仓' }, { value: 12, label: '南昌定制仓' }, { label: '厂家代发', value: 91 }];
const sendWarehouse4HotGoodsBuildGoodsDocList = [
  { label: '义乌市昀晗贸易有限公司', value: 0 },
  { label: '南昌昌东', value: 1 },
  { label: '南昌定制仓', value: 9 },
  { label: 'JD-昀晗义乌仓', value: 8 },
  { label: '厂家代发', value: 91 },
  { label: '圆通孵化仓', value: 10 },
  { label: '圆通5楼仓', value: 12 }
]

const warehouseIdList = [{ label: '义乌市昀晗供应链管理有限公司', value: 10361546 }, { label: 'JD-昀晗义乌仓', value: 12079743 }, { label: '义乌圆通', value: 12529921 }, { label: '圆通孵化仓', value: 12532813 }, { label: '圆通5楼仓', value: 12796560 }]


const warehouseArea = [{ value: -1, label: '未知' }, { value: 0, label: '义乌仓' }, { value: 1, label: '南昌昌东' }, { value: 2, label: '南昌昌北' }, { value: 3, label: ' 安徽' }, { value: 4, label: ' 上海' }];
const sendwarehouselist = sendWarehouse;
const platformlist = [{ label: '未知', value: 0 }, { label: '天猫', value: 1 }, { label: '拼多多', value: 2 }, { value: 3, label: '跨境' }, { value: 4, label: '阿里巴巴' }, { value: 5, label: '其他' }, { value: 6, label: '抖音' }, { value: 7, label: '京东' }, { label: '淘工厂', value: 8 }, { label: '淘宝', value: 9 }, { value: 10, label: '苏宁' }, { value: 11, label: '分销' }, { value: 12, label: '希音-全托' }, { value: 16, label: '希音-自营' }, { value: 13, label: 'TEMU-全托' }, { value: 14, label: '快手' }, { value: 15, label: 'TEMU-半托' }, { value: 17, label: '抖音供销' }, { value: 18, label: 'TikTok全托' }, { value: 19, label: '希音半托' }, { value: 72, label: '京喜' }, { value: 73, label: '京喜nn' }, { value: 74, label: '京东自营入仓' }, { value: 20, label: '视频号' }, { value: 21, label: '小红书' }, { value: 22, label: 'TikTok半托' }, { value: 24, label: 'Shopee' }, { value: 25, label: '得物' },]
const platformlistKj = [{ label: '未知', value: 0 }, { value: 12, label: 'SHEIN全托' }, { value: 16, label: 'SHEIN自营' }, { value: 13, label: 'TEMU全托' }, { value: 15, label: 'TEMU半托' }, { value: 23, label: '跨境分销' }, { value: 18, label: 'TikTok全托' }, { value: 22, label: 'TikTok半托' }, { value: 19, label: 'SHEIN半托' }, { value: 24, label: 'Shopee' }]
const warehouseTypeListKj = [{ label: "佳速达", value: 1 }, { label: "九方", value: 2 }, { label: "亚吉达", value: 3 }, { label: "赤道", value: 4 }, { label: "左海", value: 5 }, { label: "环世", value: 6 },]
const platformlistDistribution = [{ label: '天猫', value: 1 }, { label: '拼多多', value: 2 }, { value: 5, label: '其他' }, { value: 6, label: '抖音' }, { value: 7, label: '京东' }, { label: '淘工厂', value: 8 }, { label: '淘宝', value: 9 }, { value: 12, label: '希音' }, { value: 13, label: '快手' }, { value: 14, label: '线下' }, { value: 15, label: '商家自有商城' }, { value: 16, label: '微信视频号' }, { value: 17, label: '小红书' }, { value: 18, label: '微盟' }, { value: 19, label: '快团团' }, { value: 20, label: '团好货' }, { value: 21, label: '淘宝特价' }, { value: 22, label: '有赞' }, { value: 23, label: '京东厂家直送' }, { value: 24, label: '1688' }]
const platformlisttx = [{ label: '未知', value: 0 }, { label: '淘系', value: 1 }, { label: '拼多多', value: 2 }, { value: 3, label: '跨境' }, { value: 4, label: '阿里巴巴' }, { value: 5, label: '其他' }, { value: 6, label: '抖音' }, { value: 7, label: '京东' }, { label: '淘工厂', value: 8 }, { value: 10, label: '苏宁' }, { value: 11, label: '分销' }, { value: 12, label: '希音' }, { value: 13, label: 'TEMU-全托' }, { value: 14, label: '快手' }, { value: 15, label: 'TEMU-半托' }, { value: 17, label: '抖音供销' }, { value: 20, label: '视频号' }, { value: 21, label: '小红书' }]

const companylist = [{ value: 1, label: '睿飞' }, { value: 2, label: '风睿' }, { value: 3, label: '亿之欢' }, { value: 4, label: '首力' }, { value: 5, label: '吉鹰' }, { value: 6, label: '昀晗' }
  , { value: 7, label: '昀沐' }, { value: 8, label: '巨晶' }, { value: 9, label: '沐果' }, { value: 20, label: '个人' }]
const detail2list = [{ value: 51, label: '万达办公房租' }, { value: 52, label: '万达宿舍' }, { value: 53, label: '圆通仓储/办公房租' }, { value: 54, label: '圆通宿舍房租' }, { value: 55, label: '诚信通仓库房租' },
{ value: 56, label: '诚信通宿舍' }, { value: 57, label: '上海云仓宿舍房租' }, { value: 58, label: '南昌办公' }, { value: 59, label: '南昌仓库房租' }, { value: 60, label: '南昌宿舍房租' },
{ value: 61, label: '维修费' }, { value: 62, label: '软件费(年付)' }, { value: 63, label: '保险费' }, { value: 64, label: '餐费' }, { value: 65, label: '软件费（月付）' },
{ value: 66, label: '日常记帐-线下支出' }, { value: 67, label: '水电费' }, { value: 68, label: '折旧' }, { value: 69, label: '招待费' }, { value: 70, label: '代理咨询费' },
{ value: 71, label: '税费' }, { value: 72, label: '做账费' }, { value: 73, label: '经理层总工资' }, { value: 74, label: '客服工资' }, { value: 75, label: '剩余工资' }]
const cashRedResonlist = [
  {
    value: 1, label: '仓库部', items: [{ value: 1, label: '错发' }, { value: 2, label: '漏发' }, { value: 3, label: '破损' }, { value: 4, label: '取消订单发出' }, { value: 5, label: '店铺投诉' }
      , { value: 6, label: '多发' }, { value: 7, label: '明显次品' }, { value: 8, label: '无法核实的漏发' }, { value: 0, label: '其他' }]
  },
  { value: 3, label: '运营部', items: [{ value: 21, label: '关联错误' }, { value: 22, label: '组合做错' }, { value: 23, label: '图片' }, { value: 0, label: '其他' }] },
  { value: 4, label: '采购原因', items: [{ value: 31, label: '缺货' }, { value: 0, label: '其他' }] },
  {
    value: 8, label: '公司承担', items: [{ value: 41, label: '补寄发票或质检报告合格证' }, { value: 42, label: '质保' }, { value: 43, label: '客户正常退换货' }, { value: 44, label: '快递超区退回重发' }, { value: 45, label: '客户体验买家原因' }
      , { value: 46, label: '虚假物流' }, { value: 47, label: '激励' }, { value: 48, label: '退差价' }, { value: 49, label: '退运费' }, { value: 0, label: '其他' }]
  },
  { value: 7, label: '快递原因', items: [{ value: 51, label: '丢件' }, { value: 52, label: '破损' }, { value: 53, label: '长期不跟新' }, { value: 54, label: '无揽件记录' }, { value: 0, label: '其他' }] },
  { value: 10, label: '厂家原因', items: [{ value: 61, label: '次品' }, { value: 62, label: '质量问题' }, { value: 63, label: '缺少配件' }, { value: 64, label: '发错货' }, { value: 0, label: '其他' }] },
  { value: 0, label: '其他', items: [] },
  // {value:11, label:'公司承担-无责任人'},{value:1, label:'仓库原因-有责任人'},{value:2, label:'客服原因-有责任人'},
  //                 {value:3, label:'运营原因-有责任人'},{value:4, label:'采购原因-有责任人'},{value:5, label:'行政原因-有责任人'},{value:10, label:'仓库部-无责任人'},
  //                 {value:12, label:'快递问题-无责任人'},{value:13, label:'厂家问题-无责任人'},{value:14, label:'运营部-无责任人'}
]
const enmSendWarehouse = [
  { value: 0, label: '未知' },
  { value: 1, label: '义乌市昀晗贸易有限公司' },
  { value: 2, label: '义乌四楼' },
  { value: 3, label: '南昌昌东' },
  { value: 4, label: '南昌昌北' },
  { value: 5, label: '昀晗跨境仓' },
  { value: 6, label: '南昌高科云仓' },
  { value: 7, label: '安徽' },
  { value: 8, label: '上海暖锦' },
  { value: 9, label: '上海分仓' },
  { value: 10, label: '江苏云仓' },
  { value: 11, label: 'JD-昀晗义乌仓' },
  { value: 12, label: '南昌定制仓' },
  { value: 13, label: '圆通孵化仓', },
  { value: 14, label: '义乌圆通仓' },
  { value: 15, label: '圆通5楼仓', },
  { value: 16, label: '昀晗-包装厂' },
  { label: '厂家代发', value: 91 }
]
const popularOptions = [
  { label: '【大侠-爆品】', value: '【大侠-爆品】' },
  { label: '【大侠-爆品-已核-已进货】', value: '【大侠-爆品-已核-已进货】' },
  { label: '【大侠-爆品-已核-未进货】', value: '【大侠-爆品-已核-未进货】' },
  { label: '【大侠-爆品-未核】', value: '【大侠-爆品-未核】' },
  { label: '【徐琛-爆品】', value: '【徐琛-爆品】' },
  { label: '【徐琛-爆品-已核-已进货】', value: '【徐琛-爆品-已核-已进货】' },
  { label: '【徐琛-爆品-已核-未进货】', value: '【徐琛-爆品-已核-未进货】' },
  { label: '【徐琛-爆品-未核】', value: '【徐琛-爆品-未核】' },
  { label: '【左玉玲-爆品】', value: '【左玉玲-爆品】' },
  { label: '【左玉玲-爆品-已核-已进货】', value: '【左玉玲-爆品-已核-已进货】' },
  { label: '【左玉玲-爆品-已核-未进货】', value: '【左玉玲-爆品-已核-未进货】' },
  { label: '【左玉玲-爆品-未核】', value: '【左玉玲-爆品-未核】' },
]
const popularOptions_more = [
  { label: '【大侠-爆品】', value: '【大侠-爆品】' },
  { label: '【大侠-爆品-已核-已进货】', value: '【大侠-爆品-已核-已进货】' },
  { label: '【大侠-爆品-已核-未进货】', value: '【大侠-爆品-已核-未进货】' },
  { label: '【大侠-爆品-未核】', value: '【大侠-爆品-未核】' },
  { label: '【徐琛-爆品】', value: '【徐琛-爆品】' },
  { label: '【徐琛-爆品-已核-已进货】', value: '【徐琛-爆品-已核-已进货】' },
  { label: '【徐琛-爆品-已核-未进货】', value: '【徐琛-爆品-已核-未进货】' },
  { label: '【徐琛-爆品-未核】', value: '【徐琛-爆品-未核】' },
  { label: '【左玉玲-爆品】', value: '【左玉玲-爆品】' },
  { label: '【左玉玲-爆品-已核-已进货】', value: '【左玉玲-爆品-已核-已进货】' },
  { label: '【左玉玲-爆品-已核-未进货】', value: '【左玉玲-爆品-已核-未进货】' },
  { label: '【左玉玲-爆品-未核】', value: '【左玉玲-爆品-未核】' },
  { label: '未设置', value: '未设置' },
]
const childcompany = [{ label: '义乌', value: '义乌' }, { label: '南昌', value: '南昌' }, { label: '武汉', value: '武汉' }, { label: '深圳', value: '深圳' }]
const bianmaplatform = [{ label: '国内', value: 1 }, { label: '跨境', value: 2 }]
const yesorno = [{ label: '是', value: 1 }, { label: '否', value: 0 }]
const yesornobool = [{ label: '是', value: true }, { label: '否', value: false }]
const yesornofinished = [{ label: '成品', value: true }, { label: '半成品', value: false }]
const bianmastatus = [{ label: '未知', value: 0 }, { label: '上架', value: 1 }, { label: '下架', value: 3 }]
const producmask = [{ label: '红', value: '1' }, { label: '绿', value: '2' }, { label: '黄', value: '3' }, { label: '蓝', value: '4' }, { label: '粉红', value: '5' }, { label: '灰色', value: '6' }, { label: '黑色', value: '7' }, { label: '深蓝色', value: '8' }]
//const producmask=[{label:'红',value:'1'},{label:'绿',value:'2'},{label:'黄',value:'3'},{label:'蓝',value:'4'},{label:'粉红',value:'5'},{label:'灰色',value:'6'},{label:'黑色',value:'7'},{label:'深蓝色',value:'8'}]
//const productstatus=[{label:'正常',value:1},{label:'缺货',value:2},{label:'停用',value:3}]
const expressCompany = [{ label: '圆通(返0.1)', value: 1 }, { label: '申通', value: 2 }, { label: '圆通(不返0.1)', value: 3 }, { label: '百世', value: 4 }, { label: '德邦', value: 5 }, { label: '邮政', value: 6 },
{ label: '中通', value: 7 }, { label: '顺丰次晨', value: 8 }, { label: '顺丰标快', value: 9 }, { label: '顺丰特惠', value: 10 }];
const orderpositionlist = [{ value: 0, label: '其他岗位' }, { value: 1, label: '审单' }, { value: 2, label: '组团打单' }, { value: 3, label: '组团' }, { value: 4, label: '配货' }, { value: 5, label: '套袋子' }
  , { value: 6, label: '机器包' }, { value: 7, label: '杂包' }, { value: 8, label: '大包打包' }, { value: 9, label: '补货上架' }]
const formatTime = (date, pattern) => {
  if (!date)
    return ''
  return dayjs(date).format(pattern)
}
const formatSecondToHour = (minutes) => {
  if (!minutes || minutes == 0)
    return ''
  var day = Math.floor(minutes / (60 * 24));
  if (day > 0)
    return (day + "天" + Math.floor((minutes % (60 * 24)) / 60) + "小时");
  else
    return (Math.floor(minutes / 60) + "小时" + (minutes % 60) + "分");
}
const formatMinuteToHour = (minutes) => {
  if (!minutes || minutes == 0)
    return ''
  var day = Math.floor(minutes / (60 * 24));
  if (day > 0)
    return (day + "天" + Math.floor((minutes % (60 * 24)) / 60) + "小时");
  else
    return (Math.floor(minutes / 60) + "小时" + Math.floor(minutes % 60) + "分");
}
//转换成小数
const formatSecondNewToHour = (minutes) => {
  if (!minutes || minutes == 0)
    return ''
  var day = Math.floor(minutes / (60 * 24));
  return (((day * 24 + Math.floor((minutes % (60 * 24)) / 60)) / 24).toFixed(1));
}
const formatTimerang = (StartDate, EndDate, pattern) => {
  if (!StartDate)
    return ''
  var _start = dayjs(StartDate).format(pattern);
  var _end = dayjs(EndDate).format(pattern);
  if (_start == _end)
    return _start;
  else
    return _start + '至' + _end;
}
const formatUpNumber = (value) => {
  if (value === 0) return "<span>0</span>";
  else if (value > 0) return `<i class="el-icon-top" style="color: green;font-weight: 800;"></i>${value}`;
  else if (value < 0) return `<i class="el-icon-bottom" style="color: red;font-weight: 800;"></i>${0 - value}`;
  else return "<span></span>";
}
const formatUpNumber1 = (value, diffvalue) => {
  return `<span class="bigbadge">${value}</span><sup class="bottombadge ${diffvalue == 0 ? "" : diffvalue > 0 ? "greencolor" : "redcolor"}">
              ${(diffvalue == 0 ? "" : diffvalue > 0 ? "+" : "") + parseFloat(diffvalue.toFixed(2))}</sup>`;
}
const formatProName = (name, count) => {
  if (count > 0) return `<a class="leftbadge redcolor"> ${count}</a><span>${name}</span>`;
  else return `<span size="small">${name}</span>`;
}
const formatKeyWord = (keyWord, type) => {
  if (!type) return `<span size="small" style="">${keyWord ?? ""}</span>`;
  else if (type == 1) return `<span size="small" style="color:green;">${keyWord ?? ""}</span>`;
  else if (type == 2) return `<span size="small" style="color:red;">${keyWord ?? ""}</span>`;
  else return ` `;
}
const formatmoney = (value) => {
  if (!value) return value
  var n = 2;
  value = parseFloat((value + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";
  var l = value.split(".")[0].split("").reverse(), r = value.split(".")[1];
  var t = "";
  for (var i = 0; i < l.length; i++) {
    t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
  }
  if (parseInt(r) > 0)
    return t.split("").reverse().join("") + "." + r;
  else
    return t.split("").reverse().join("");
}
const tothousands = (value, n) => {
  if (!value) return value
  if (!n) {
    var n = 2;
  }
  value = parseFloat((value + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";
  var l = value.split(".")[0].split("").reverse(), r = value.split(".")[1];
  var t = "";
  for (var i = 0; i < l.length; i++) {
    if (value > 0) {
      t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
    } else {
      t += l[i] + ((i + 2) % 4 == 0 && (i + 2) != l.length ? "," : "");
    }
  }
  if (parseInt(r) > 0)
    return t.split("").reverse().join("") + "." + r;
  else
    return t.split("").reverse().join("");
}
//格式化百分比
const formatPercen = (value) => {
  if (value == null) return "";

  return value.toFixed(2).toString() + "%";
}

const formatPass = (value) => {
  let info = ' '
  Passs.forEach(item => {
    if (item.value === value) info = item.label
  })
  return info
}
const formatRule = (value) => {
  let info = ' '
  Rule.forEach(item => { if (item.value === value) info = item.label })
  return info
}
const formatExpressCompany = (value) => {
  let info = ' '
  expressCompany.forEach(item => { if (item.value === value) info = item.label })
  return info
}
const formatWarehouse = (value) => {
  let info = ' '
  warehouseformartlist.forEach(item => { if (item.value === value) info = item.label })
  return info
}
const formatWarehouseNew = (value) => {
  let info = ' '
  warehouselist.forEach(item => { if (item.value === value) info = item.label })
  return info
}
const formatEnmSendWarehouse = (value) => {
  let info = ' '
  enmSendWarehouse.forEach(item => { if (item.value === value) info = item.label })
  return info
}
const formatSendWarehouse = (value) => {
  let info = ' '
  sendWarehouse.forEach(item => { if (item.value === value) info = item.label })
  return info
}
const formatWarehouseArea = (value) => {
  let info = ' '
  warehouseArea.forEach(item => { if (item.value === value) info = item.label })
  return info
}
const formatwarehouseIdList = (value) => {
  let info = ' '
  warehouseIdList.forEach(item => { if (item.value === value) info = item.label })
  return info
}

const formatPlatform = (value) => {
  let info = ' '
  platformlist.forEach(item => { if (item.value === value) info = item.label })
  if (value == 71) info = '京喜'
  if (value == 72) info = '京喜NN'
  if (value == 101) info = '天猫微信'
  if (value == 81) info = '淘工厂微信'
  if (value == 91) info = '淘宝微信'
  return info
}
const formatWarehouseTypeListKj = (value) => {
  let info = ' '
  warehouseTypeListKj.forEach(item => { if (item.value === value) info = item.label })
  return info
}
const formatPlatformkj = (value) => {
  let info = ' '
  platformlistKj.forEach(item => { if (item.value === value) info = item.label })
  return info
}

const formatPlatformDistribution = (value) => {
  let info = ' '
  platformlistDistribution.forEach(item => { if (item.value === value) info = item.label })
  return info
}
const formatPlatformtx = (value) => {
  let info = ' '
  platformlisttx.forEach(item => { if (item.value === value) info = item.label })
  return info
}

const formatBianMaPlatform = (value) => {
  let info = ' '
  bianmaplatform.forEach(item => { if (item.value === value) info = item.label })
  return info
}
const formatBianMaRemoveCalc = (values) => {
  if (!values) return ''
  var list = [{ label: '采购建议', value: 1 }, { label: '快递费分摊', value: 2 }]
  let info = ' '
  list.forEach(item => { if (values.indexOf(item.value) >= 0) info += `${item.label}、` })
  return info
}
const formatYesorno = (value) => {
  let info = ' '
  yesorno.forEach(item => { if (item.value === value) info = item.label })
  return info
}
const formatYesornoBool = (value) => {
  let info = ' '
  yesornobool.forEach(item => { if (item.value === value) info = item.label })
  return info
}
const formatIsError = (value) => {
  var yesornobool = [{ label: '正常', value: false }, { label: '异常', value: true }]
  let info = ' '
  yesornobool.forEach(item => { if (item.value === value) info = item.label })
  return info
}
const formatIsOutStock = (value) => {
  var yesornobool = [{ label: '正常', value: null }, { label: '正常', value: false }, { label: '缺货', value: true }]
  let info = ' '
  yesornobool.forEach(item => { if (item.value === value) info = item.label })
  var html = `<a href="javascript:void(0);"  style="color: ${value == true ? "#dc0909" : "#606266"};">${info}</a>`;
  return html
}
const formatIsCheckError = (value, isedit) => {
  var yesornobool = [{ label: '缺货', value: 0 }, { label: '审错', value: 1 }]
  if (!isedit)
    yesornobool.push({ label: '正常', value: null });
  let info = ' '
  yesornobool.forEach(item => { if (item.value === value) info = item.label })
  var html = `<a href="javascript:void(0);"  style="color: ${value == true ? "#dc0909" : "#606266"};">${info}</a>`;
  return html
}
const formatPurchasePlanError = (value) => {
  var yesornobool = [{ label: '正常', value: 0 }, { label: '进货', value: 1 }, { label: '催货', value: 2 }]
  let info = ' '
  yesornobool.forEach(item => { if (item.value === value) info = item.label })
  var html = `<a href="javascript:void(0);"  style="color: ${value == 0 ? "green" : value == 1 || value == 2 ? "red" : "#d4d423"};">${info}</a>`;
  return html
}
const formatYesornofinished = (value) => {
  let info = ' '
  yesornofinished.forEach(item => { if (item.value === value) info = item.label })
  return info
}
const formatbianmastatus = (value) => {
  let info = ' '
  bianmastatus.forEach(item => { if (item.value === value) info = item.label })
  return info
}
const formatproducmask = (value) => {
  //debugger
  let info = ''
  if (value) {
    var arry = value.split(',')
    producmask.forEach(item => {
      if (arry.indexOf(item.value) != -1) {
        if (info) info = info + '、' + item.label
        else info = item.label
      }
    })
  }
  return info
}

/*将[{value:,label:,parentvalue:}] 转成[{value:,label:,children:[]}]  */
const convertToChild = (list, rootvalue) => {
  const rootlist = [];
  list.forEach(f => {
    if (f.parentvalue == rootvalue) {
      var item = { value: f.value, label: f.label }
      var parent = list.filter(l => l.parentvalue === f.value)
      if (parent.length > 0)
        item.children = convertToChild(list, f.value)
      rootlist.push(item)
    }
  })
  return rootlist
}
const formatLink = (value, url) => {
  if (!value) return ''
  if (!url) return value;
  let res = JSON.parse(JSON.stringify(value));
  //给res脱敏保留前三后四
  if (res.length > 6) {
    res = res.substring(0, 2) + '**' + res.substring(res.length - 2, res.length);
  }
  var html = `<span style="cursor:pointer;color: #1000ff;">复</span>|<span style="cursor:pointer;color: #1000ff;">查 </span><a href="${url}" target="_blank" style="color: #1000ff;">${res}</a>`;
  // var html= `<el-link type="primary" href="${url}" target="_blank">${value}</el-link>`;
  return html
}
const formatNoLink = (value) => {
  var html = `<a href="javascript:void(0);"  style="color: #1000ff;">${value ?? ""}</a>`;
  return html
}
//type:0:天、1:月
const formatTurnoverRang = (value, type) => {
  //0:半个月内 1:15-30天 2:1-2个月 3:2-3个月 4:3-6个月 5:6-12个月 6:12个月以上
  var str = "";
  if (type == 0) {
    if (value == 0) str = "<15";
    else if (value == 1) str = "15-30";
    else if (value == 2) str = "30-60";
    else if (value == 3) str = "60-90";
    else if (value == 4) str = "90-180";
    else if (value == 5) str = "180-360";
    else if (value == 6) str = "12个月以上";
  }
  else {
    if (value == 0) str = "半个月内";
    else if (value == 1) str = "15-30天";
    else if (value == 2) str = "1-2个月";
    else if (value == 3) str = "2-3个月";
    else if (value == 4) str = "3-6个月";
    else if (value == 5) str = "6-12个月";
    else if (value == 6) str = "12个月以上";
  }
  return str
}
const formatFeeShareOper = (value) => {
  var str = "";
  if (value == 0) str = "按源数据分摊";
  else if (value == 1) str = "按产品ID分摊";
  else if (value == 2) str = "按店铺分摊";
  else if (value == 3) str = "按组分摊";
  else if (value == 4) str = "按所有组分摊";
  else if (value == 5) str = "按商品编码分摊";
  else if (value == 6) str = "按采购单分摊";
  else if (value == 7) str = "按组+店铺分摊";
  return str
}
const formatShareFeeType = (value) => {
  var str = "";
  if (value == 0) str = "提货";
  //else if(value==1) str="钉钉审批";
  else if (value == 2) str = "拍摄道具";
  else if (value == 3) str = "拿样品费";
  else if (value == 4) str = "样品费报销";
  else if (value == 5) str = "采购运费";
  else if (value == 6) str = "运营工资";
  else if (value == 7) str = "美工提成";
  else if (value == 8) str = "采购爆款提成";
  else if (value == 9) str = "运营下架费";
  else if (value == 10) str = "仓储损耗费";
  else if (value == 11) str = "加工部工资";
  else if (value == 12) str = "护墙角工资";
  else if (value == 13) str = "运营组工资";
  else if (value == 14) str = "美工提成";
  else if (value == 15) str = "新媒体提成";
  else if (value == 16) str = "采购提成";
  else if (detail2list.findIndex((item, index) => { return item.value == value }) > 0)
    str = detail2list.find((item, index) => { return item.value == value }).label;
  return str
}
const formatenmCashRedReson = (value, level) => {
  let label = ' ';
  if (level == 1) {
    cashRedResonlist.forEach(f => {
      if (f.value == value) {
        label = f.label;
        return;
      }
    });
  }
  else if (level == 2) {
    cashRedResonlist.forEach(f => {
      var reson = f.items.find(item => item.value == value);
      if (reson) {
        label = reson.label;
        return;
      }
    });
  }
  return label;
}
const jsonToQueryString = (value) => {
  var params = Object.keys(value).map(function (key) {
    var val = ""
    if (value[key] === null || value[key] === undefined) {
      val = "";
    }
    else {
      val = encodeURIComponent(value[key]);
    }
    return encodeURIComponent(key) + "=" + val;
  }).join("&");
  return params;
}

const htmlDecode = (text) => {
  let tmp = document.createElement('div')
  tmp.innerHTML = text
  const output = tmp.innerText || tmp.textContent
  tmp = null
  return output
}

/*日期加上几天*/
// const returnDate=(date,day)=>{

//   let dateend = new Date(date)
//         let year =dateend.getFullYear().toString()   //'2019'
//         let month = dateend.getMonth()+1 < 10 ? '0'+(date.getMonth()+1).toString():(date.getMonth()+1).toString()  //'04'
//         let da = dateend.getDate() < 10 ? '0'+date.getDate().toString():date.getDate().toString()  //'12'
//         let end = year + '-' + month + '-' + da  //当天'2019-04-12'

//         var datestart = new Date(dateend.setDate(dateend.getDate()-5));

//   return html
// }

/*宝贝ID加链接*/
const formatLinkProCode = (platform, proCode) => {
  //是否跨境
  let proCodeLink = proCode;
  if (proCode && proCode.toLowerCase().indexOf("kj") == 0) {
    proCodeLink = proCode.substring(2);
  }
  const arr = [1, '淘系', '天猫', 2, '拼多多', 8, '淘工厂', 9, '淘宝', 7, '京东', 4, '阿里巴巴', 6, '抖音', 14, '快手']
  if (arr.includes(platform)) {
    var proBaseUrl = '';
    switch (platform) {
      case 1://淘系
      case '淘系'://淘系
      case '天猫'://天猫
        proBaseUrl = "https://detail.tmall.com/item.htm?id=" + proCodeLink;
        break;
      case 2://拼多多
      case '拼多多'://拼多多
        proBaseUrl = "https://mobile.yangkeduo.com/goods2.html?goods_id=" + proCodeLink;
        break;
      case 8://淘系
      case '淘工厂'://淘系
        proBaseUrl = "https://detail.tmall.com/item.htm?id=" + proCodeLink;
        break;
      case 9://淘系
      case '淘宝'://淘系
        proBaseUrl = "https://detail.tmall.com/item.htm?id=" + proCodeLink;
        break;
      case 7://京东
      case '京东'://京东
        proBaseUrl = "https://item.jd.com/" + proCodeLink + ".html";
        break;
      case 4://阿里巴巴
      case '阿里巴巴'://阿里巴巴
        proBaseUrl = "https://detail.1688.com/offer/" + proCodeLink + ".html?spm=a26286.8251493.description.2.221425b2kIBGkR";
        break;
      case 6://抖音
      case '抖音'://抖音
        proBaseUrl = "https://haohuo.jinritemai.com/views/product/detail?id=" + proCodeLink;
        break;
      case 14://快手
      case '快手'://快手
        proBaseUrl = "https://app.kwaixiaodian.com/web/kwaishop-goods-detail-page-app?layoutType=4&source=productDetailB_pc_kwaixiaodian&id=" + proCodeLink;
        break;
    }
    if (proCode && proBaseUrl)
      return formatLink(proCode, proBaseUrl);
    return proCode;
  } else {
    return formatProCodeStutas3(proCode)
  }
}
const formatProCodeStutas3 = (value) => {
  if (value !== null && value !== undefined) {
    //是否跨境
    let proCodeLink = value;
    if (value && value.toLowerCase().indexOf("kj") == 0) {
      proCodeLink = value.substring(2);
    }
    let res = JSON.parse(JSON.stringify(value));
    //给res脱敏保留前三后四
    if (res.length > 6) {
      res = res.substring(0, 2) + '**' + res.substring(res.length - 2, res.length);
    }
    var html = `<span style="cursor:pointer;color: #1000ff;">复</span>|<span style="cursor:pointer;color: #1000ff;">查 </span><span>${res}</span>`;
    return html
  } else {
    return value
  }
}
/*拷贝复制 */
const doCopy = (val) => {
  let that = this;
  this.$copyText(val).then(function (e) {
    that.$message({ message: "内容已复制到剪切板！", type: "success" });
  }, function (e) {
    that.$message({ message: "抱歉，复制失败！", type: "warning" });
  })
}
const downMobanCommand = (command) => {
  var alink = document.createElement("a");
  alink.href = `../static/excel/${command}.xlsx`;
  alink.click();
}

const getUrlParam = (url) => {
  var params = [],
    h;
  var hash = url.slice(url.indexOf("?") + 1).split('&');
  for (var i = 0; i < hash.length; i++) {
    h = hash[i].split("=");
    params[h[0]] = decodeURIComponent(h[1]);
  }
  return params;
}

//缓存方法-设置缓存
const setStore = (name, content, maxAge = null) => {
  if (!global.window || !name) {
    return
  }
  if (typeof content !== 'string') {
    content = JSON.stringify(content)
  }
  //
  const storage = global.window.localStorage
  storage.setItem(name, content)
  //最大日期
  if (maxAge && !isNaN(parseInt(maxAge))) {
    const timeout = parseInt(new Date().getTime() / 1000)
    storage.setItem(`${name}_expire`, timeout + maxAge)
    //设置当前时间+maxAge
  }
}
//缓存方法-读取缓存
const getStore = name => {
  //如果没有参数名就不往下走了
  if (!global.window || !name) {
    return
  }
  const content = window.localStorage.getItem(name)
  const _expire = window.localStorage.getItem(`${name}_expire`)
  //取的时候先判断_expire是否有，如果当前日期大于设置localstorage的日期，就不往下走
  if (_expire) {
    const now = parseInt(new Date().getTime() / 1000)
    if (now > _expire) {
      return
    }
  }

  //如果小于就能取到这个值
  try {
    return JSON.parse(content)
  } catch (e) {
    return content
  }
}


const downloadLink = (link, fileName) => {
  // 创建一个虚拟 a 标签
  var anchor = document.createElement('a');

  // 获取链接的二进制数据
  fetch(link)
    .then(response => response.blob())
    .then(blob => {
      // 将数据转换为一个表示二进制数据的 URL
      var url = URL.createObjectURL(blob);

      // 设置 a 标签的属性
      anchor.href = url;
      anchor.download = fileName.trimEnd();

      // 模拟点击 a 标签进行下载
      anchor.click();

      // 释放 URL 对象
      URL.revokeObjectURL(url);
    });
}



//全链路待拍状态  -10申请退款、0待处理、1代拍已生成、2全部代拍中、999无需代拍
const AllLinkDaiPaiImpOrderDpStateOpts = [{ label: '申请退款', value: -10 },
{ label: '待处理', value: 0 },
{ label: '代拍已生成', value: 1 },
{ label: '代拍已下单', value: 2 },
{ label: '无需代拍', value: 999 },]
const fmtAllLinkDaiPaiImpOrderDpState = (v) => {
  let item = AllLinkDaiPaiImpOrderDpStateOpts.find(x => x.value == v);
  if (item && item.label)
    return item.label;
  else
    return ' ';
}

//全链路厂家代拍状态  0待下单、1已下单、2已付款、3已发货、999无需代拍
const AllLinkDaiPaiSpOrderDpStateOpts = [{ label: '申请退款', value: -10 },
{ label: '待下单', value: 0 },
{ label: '已下单', value: 1 },
{ label: '已付款', value: 2 },
{ label: '已发货', value: 3 },
{ label: '无需代拍', value: 999 },]
const fmtAllLinkDaiPaiSpOrderDpState = (v) => {
  let item = AllLinkDaiPaiSpOrderDpStateOpts.find(x => x.value == v);
  if (item && item.label)
    return item.label;
  else
    return ' ';
}

const pickerOptions = {
  shortcuts: [{
    text: '昨天',
    onClick(picker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
      end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
      picker.$emit('pick', [start, end]);
    }
  }, {
    text: '最近三天',
    onClick(picker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
      picker.$emit('pick', [start, end]);
    }
  }, {
    text: '最近一周',
    onClick(picker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      picker.$emit('pick', [start, end]);
    }
  }, {
    text: '最近半月',
    onClick(picker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
      picker.$emit('pick', [start, end]);
    }
  }, {
    text: '最近一月',
    onClick(picker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      picker.$emit('pick', [start, end]);
    }
  }, {
    text: '最近三月',
    onClick(picker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      picker.$emit('pick', [start, end]);
    }
  }]
}

// type为Data时的快捷选项
const pickerOptionsDate = {
  shortcuts: [
    {
      text: '昨天',
      onClick(picker) {
        picker.$emit('pick', dayjs().subtract(1, 'day').toDate()); // 昨天
      }
    },
    {
      text: '最近三天',
      onClick(picker) {
        picker.$emit('pick', dayjs().subtract(3, 'days').toDate()); // 最近三天
      }
    },
    {
      text: '最近一周',
      onClick(picker) {
        picker.$emit('pick', dayjs().subtract(7, 'days').toDate()); // 最近一周
      }
    },
    {
      text: '最近半月',
      onClick(picker) {
        picker.$emit('pick', dayjs().subtract(15, 'days').toDate()); // 最近半月
      }
    },
    {
      text: '最近一月',
      onClick(picker) {
        picker.$emit('pick', dayjs().subtract(1, 'month').toDate()); // 最近一月
      }
    },
    {
      text: '最近三月',
      onClick(picker) {
        picker.$emit('pick', dayjs().subtract(3, 'months').toDate()); // 最近三月
      }
    }
  ]
};

//利润状态 0空、1未算完进行中、2已算完无利润、3已算完询价中、4已选中
const AllLinkChooseProfitStateOpts = [{ label: '未算完进行中', value: 1 },
{ label: '已算完无利润', value: 2 },
{ label: '已算完询价中', value: 3 },
{ label: '已选中', value: 4 },]
const fmtAllLinkChooseProfitState = (v) => {
  let item = AllLinkChooseProfitStateOpts.find(x => x.value == v);
  if (item && item.label)
    return item.label;
  else
    return '未设置';
}


const ShootingVideoTaskUrgencyOptions = [{ value: 1, label: "加急" }, { value: 0, label: "紧急" }, { value: 2, label: "确认" }, { value: 9, label: "正常" }]


const DeductOrderZrDeptActions = [
  { zrDeptAction: '仓库入库', zrDept: '仓库', zrAction: '入库' },
  { zrDeptAction: '仓库发货', zrDept: '仓库', zrAction: '发货' },
  { zrDeptAction: '仓库审单', zrDept: '仓库', zrAction: '审单' },
  { zrDeptAction: '仓库库维', zrDept: '仓库', zrAction: '库维' },
  { zrDeptAction: '仓库打单', zrDept: '仓库', zrAction: '打单' },
  { zrDeptAction: '仓库配货', zrDept: '仓库', zrAction: '配货' },
  { zrDeptAction: '仓库打包', zrDept: '仓库', zrAction: '打包' },
  { zrDeptAction: '仓库集包', zrDept: '仓库', zrAction: '集包' },
  { zrDeptAction: '仓库加工', zrDept: '仓库', zrAction: '加工' },
  { zrDeptAction: '运营', zrDept: '运营', zrAction: '运营' },
  { zrDeptAction: '采购', zrDept: '采购', zrAction: '采购' },
  { zrDeptAction: '快递', zrDept: '快递', zrAction: '快递' },
  { zrDeptAction: '客服售前', zrDept: '客服', zrAction: '售前' },
  { zrDeptAction: '客服售后', zrDept: '客服', zrAction: '售后' },
  { zrDeptAction: '机器人待查物流', zrDept: '机器人', zrAction: '待查物流' },
  { zrDeptAction: '外部', zrDept: '外部', zrAction: '外部' },
];
const DeductOrderZrReasons = [
  "订单处理不及时",
  "发货超时",
  "假揽收/丢件",
  "库存不准",
  "揽收超时",
  "缺货",
  "采购缺货",
  "审单不及时",
  "停发",
  "无揽收/丢件",
  "中转超时",
  "备注不及时",
  "代拍下架禁用",
  "盘点导致缺货",
  "集包错分",
  "打单不及时",
  "运营起量",
  "配货不及时",
  "配货无后续",
  "发货超时",
  "无法获取电子面单",
  "售后退款",
  "停发不可达",
  "其他"
];


const DeductOrderZrType12 = [
  { "zrType12": "计算中", "zrType1": "计算中", "zrType2": "计算中" },
  { "zrType12": "其他-售后退款", "zrType1": "其他", "zrType2": "售后退款" },
  { "zrType12": "其他-面单错误", "zrType1": "其他", "zrType2": "面单错误" },
  { "zrType12": "缺货-采购缺货", "zrType1": "缺货", "zrType2": "采购缺货" },
  { "zrType12": "缺货-仓库盘点", "zrType1": "缺货", "zrType2": "仓库盘点" },
  { "zrType12": "缺货-运营起量", "zrType1": "缺货", "zrType2": "运营起量" },
  { "zrType12": "虚假轨迹-中转超时", "zrType1": "虚假轨迹", "zrType2": "中转超时" },
  { "zrType12": "虚假轨迹-一条揽收记录无后续", "zrType1": "虚假轨迹", "zrType2": "一条揽收记录无后续" },
  { "zrType12": "虚假轨迹-揽收超时", "zrType1": "虚假轨迹", "zrType2": "揽收超时" },
  { "zrType12": "虚假轨迹-未揽收", "zrType1": "虚假轨迹", "zrType2": "未揽收" },
  { "zrType12": "虚假轨迹-快递停发不可达", "zrType1": "虚假轨迹", "zrType2": "快递停发不可达" },
  { "zrType12": "延迟发货-运营处理不及时", "zrType1": "延迟发货", "zrType2": "运营处理不及时" },
  { "zrType12": "延迟发货-配货后无打包员", "zrType1": "延迟发货", "zrType2": "配货后无打包员" },
  { "zrType12": "延迟发货-打包超时", "zrType1": "延迟发货", "zrType2": "打包超时" },
  { "zrType12": "延迟发货-审单超时", "zrType1": "延迟发货", "zrType2": "审单超时" },
  { "zrType12": "延迟发货-补货超时", "zrType1": "延迟发货", "zrType2": "补货超时" },
  { "zrType12": "延迟发货-打单超时", "zrType1": "延迟发货", "zrType2": "打单超时" },
  { "zrType12": "延迟发货-配货超时", "zrType1": "延迟发货", "zrType2": "配货超时" },
  { "zrType12": "延迟发货-打包后丢件", "zrType1": "延迟发货", "zrType2": "打包后丢件" },
];


const DeductOrderZrType12TreeBz = {
  "缺货": [
    { "zrType12": "缺货-采购缺货", "zrType1": "缺货", "zrType2": "采购缺货" },
    { "zrType12": "缺货-仓库盘点", "zrType1": "缺货", "zrType2": "仓库盘点" },
    { "zrType12": "缺货-运营起量", "zrType1": "缺货", "zrType2": "运营起量" },
  ],
  "虚假轨迹": [
    { "zrType12": "虚假轨迹-中转超时", "zrType1": "虚假轨迹", "zrType2": "中转超时" },
    { "zrType12": "虚假轨迹-一条揽收记录无后续", "zrType1": "虚假轨迹", "zrType2": "一条揽收记录无后续" },
    { "zrType12": "虚假轨迹-揽收超时", "zrType1": "虚假轨迹", "zrType2": "揽收超时" },
    { "zrType12": "虚假轨迹-未揽收", "zrType1": "虚假轨迹", "zrType2": "未揽收" },
    { "zrType12": "虚假轨迹-快递停发不可达", "zrType1": "虚假轨迹", "zrType2": "快递停发不可达" },
  ],
  "延迟发货": [
    { "zrType12": "延迟发货-运营处理不及时", "zrType1": "延迟发货", "zrType2": "运营处理不及时" },
    { "zrType12": "延迟发货-配货后无打包员", "zrType1": "延迟发货", "zrType2": "配货后无打包员" },
    { "zrType12": "延迟发货-打包超时", "zrType1": "延迟发货", "zrType2": "打包超时" },
    { "zrType12": "延迟发货-审单超时", "zrType1": "延迟发货", "zrType2": "审单超时" },
    { "zrType12": "延迟发货-补货超时", "zrType1": "延迟发货", "zrType2": "补货超时" },
    { "zrType12": "延迟发货-打单超时", "zrType1": "延迟发货", "zrType2": "打单超时" },
    { "zrType12": "延迟发货-配货超时", "zrType1": "延迟发货", "zrType2": "配货超时" },
    { "zrType12": "延迟发货-打包后丢件", "zrType1": "延迟发货", "zrType2": "打包后丢件" },
  ],
  "其他": [
    { "zrType12": "其他-售后退款", "zrType1": "其他", "zrType2": "售后退款" },
    { "zrType12": "其他-面单错误", "zrType1": "其他", "zrType2": "面单错误" }
  ],
};


const DeductOrderZrType12Tree = {
  "缺货": [
    { "zrType12": "缺货-采购缺货", "zrType1": "缺货", "zrType2": "采购缺货" },
    { "zrType12": "缺货-仓库盘点", "zrType1": "缺货", "zrType2": "仓库盘点" },
    { "zrType12": "缺货-运营起量", "zrType1": "缺货", "zrType2": "运营起量" },
  ],
  "虚假轨迹": [
    { "zrType12": "虚假轨迹-中转超时", "zrType1": "虚假轨迹", "zrType2": "中转超时" },
    { "zrType12": "虚假轨迹-一条揽收记录无后续", "zrType1": "虚假轨迹", "zrType2": "一条揽收记录无后续" },
    { "zrType12": "虚假轨迹-揽收超时", "zrType1": "虚假轨迹", "zrType2": "揽收超时" },
    { "zrType12": "虚假轨迹-未揽收", "zrType1": "虚假轨迹", "zrType2": "未揽收" },
    { "zrType12": "虚假轨迹-快递停发不可达", "zrType1": "虚假轨迹", "zrType2": "快递停发不可达" },
  ],
  "延迟发货": [
    { "zrType12": "延迟发货-运营处理不及时", "zrType1": "延迟发货", "zrType2": "运营处理不及时" },
    { "zrType12": "延迟发货-配货后无打包员", "zrType1": "延迟发货", "zrType2": "配货后无打包员" },
    { "zrType12": "延迟发货-打包超时", "zrType1": "延迟发货", "zrType2": "打包超时" },
    { "zrType12": "延迟发货-审单超时", "zrType1": "延迟发货", "zrType2": "审单超时" },
    { "zrType12": "延迟发货-补货超时", "zrType1": "延迟发货", "zrType2": "补货超时" },
    { "zrType12": "延迟发货-打单超时", "zrType1": "延迟发货", "zrType2": "打单超时" },
    { "zrType12": "延迟发货-配货超时", "zrType1": "延迟发货", "zrType2": "配货超时" },
    { "zrType12": "延迟发货-打包后丢件", "zrType1": "延迟发货", "zrType2": "打包后丢件" },
  ],
  "计算中": [{ "zrType12": "计算中", "zrType1": "计算中", "zrType2": "计算中" },],
  "其他": [
    { "zrType12": "其他-售后退款", "zrType1": "其他", "zrType2": "售后退款" },
    { "zrType12": "其他-面单错误", "zrType1": "其他", "zrType2": "面单错误" }
  ],
};

// 允许跳转的字段（统一为小写）
const requestField = ['orderno', 'ordernoinner', 'procode', 'goodscode'];
// 跳转关键词：label中要包含这些关键词之一
const jumpKeywords = ['订单', '商品', '宝贝', 'ID', '编码', '单号'];
const canJumpLabelPattern = new RegExp(jumpKeywords.join('|'));
// 是否允许跳转的判断函数
const canJump = (row, column) => {
  const prop = (column.prop || '').toLowerCase();
  const allowProps = requestField;
  // ① 判断 prop 是否允许。例：{ prop: 'goodsCodes', label: '包材编码' }//则不允许跳转
  if (!allowProps.includes(prop)) return false;
  // ② 判断 label 是否包含“订单”或“商品”等关键词。例：{ prop: 'goodsCode', label: '包材编码' }//则不允许跳转
  const label = column.label || '';
  if (!canJumpLabelPattern.test(label)) return false;//目前没有label则不允许跳转，若要修改为没有label也允许跳转，那么可以去掉这行
  // ③ formatter 存在时，必须 formatter 后值与原始值一致。例：{ prop:code, formatter: (row) => row.name }//排序使用code，显示使用name
  if (typeof column.formatter === 'function') {
    return column.formatter(row) === row[column.prop];
  }
  return true;
};

// 执行跳转逻辑
const onJumpLink = async (value, prop) => {
  if (!value) return;
  // 如果是字符串并含有中/英文逗号，则认为是多个编号，不支持跳转。例：value=123,456,789
  if (typeof value === 'string' && /[,，]/.test(value)) {
    throw new Error('不支持多个进行跳转');
  }
  const propMap = {
    orderno: 1,
    ordernoinner: 2,
    procode: 3,
    goodscode: 4,
  };
  const orderType = propMap[prop?.toLowerCase()];
  if (!orderType) return;
  try {
    const url = `http://localhost:33231/api/erptool/erppub/showorderdetail?orderType=${orderType}&orderNo=${value}`;
    const res = await fetch(url);
    if (!res.ok) {
      throw new Error('小昀工具箱不在线，请开启后使用！');
    }
    const result = await res.json();
    if (!result || result.success === false) {
      throw new Error('小昀工具箱不在线，请开启后使用！');
    }
  } catch (err) {
    throw new Error(err.message || '请求异常，请检查网络或稍后重试');
  }
};

// 去除末尾无意义的0
const removeTrailingZeros = (numStr) => {
  // 如果没有小数点，直接返回
  if (numStr.indexOf('.') === -1) {
    return numStr;
  }
  // 去除末尾的0
  let result = numStr.replace(/0+$/, '');
  // 如果小数点后没有数字了，也去除小数点
  if (result.endsWith('.')) {
    result = result.slice(0, -1);
  }
  return result;
}

// 转换四位数段
const convertSegment = (num, digits, units) => {
  let result = '';
  let needZero = false;
  for (let i = 3; i >= 0; i--) {
    const digit = Math.floor(num / Math.pow(10, i)) % 10;

    if (digit > 0) {
      if (needZero) {
        result += '零';
        needZero = false;
      }
      result += digits[digit];
      if (i > 0) {
        result += units[i];
      }
    } else if (result) {
      needZero = true;
    }
  }
  return result;
}

// 转换整数部分
const convertIntegerPart = (num, digits, units, bigUnits) => {
  if (num === 0) return '';
  let result = '';
  let unitIndex = 0;
  while (num > 0) {
    const segment = num % 10000;
    if (segment > 0) {
      let segmentStr = convertSegment(segment, digits, units);
      if (unitIndex > 0) {
        segmentStr += bigUnits[unitIndex];
      }
      result = segmentStr + result;
    } else if (result && unitIndex > 0) {
      result = '零' + result;
    }
    num = Math.floor(num / 10000);
    unitIndex++;
  }
  return result;
}

// 数字转中文大写（支持4位小数，自动去除末尾零）
const numberToChinese = (num) => {
  const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
  const units = ['', '拾', '佰', '仟'];
  const bigUnits = ['', '万', '亿'];
  if (num === 0) return '零元整';
  // 先格式化为4位小数，然后去除末尾的0
  const formattedNum = removeTrailingZeros(parseFloat(num).toFixed(4));
  // 分离整数和小数部分
  const parts = formattedNum.toString().split('.');
  const integerPart = parseInt(parts[0]);
  let decimalPart = parts[1] || '';
  // 去除小数部分末尾的0
  decimalPart = decimalPart.replace(/0+$/, '');
  let result = '';
  // 处理整数部分
  if (integerPart === 0) {
    result = '零';
  } else {
    result = convertIntegerPart(integerPart, digits, units, bigUnits);
  }
  result += '元';
  // 处理小数部分
  if (!decimalPart || parseFloat('0.' + decimalPart) === 0) {
    result += '整';
  } else {
    // 处理角、分、厘、毫
    const jiao = parseInt(decimalPart.charAt(0) || '0');
    const fen = parseInt(decimalPart.charAt(1) || '0');
    const li = parseInt(decimalPart.charAt(2) || '0');
    const hao = parseInt(decimalPart.charAt(3) || '0');
    if (jiao > 0) {
      result += digits[jiao] + '角';
    }
    if (fen > 0) {
      if (jiao === 0) result += '零';
      result += digits[fen] + '分';
    }
    if (li > 0) {
      if (jiao === 0 && fen === 0) result += '零';
      result += digits[li] + '厘';
    }
    if (hao > 0) {
      if (jiao === 0 && fen === 0 && li === 0) result += '零';
      result += digits[hao] + '毫';
    }
  }
  return result;
}

export {
  warehouseTypeListKj,
  enmSendWarehouse,
  popularOptions,
  popularOptions_more,
  sendwarehouselist,
  sendWarehouse4HotGoodsBuildGoodsDocList,
  platformlist,
  platformlistKj,
  platformlisttx,
  platformlistDistribution,
  warehouselist,
  warehouseIdList,
  companylist,
  detail2list,
  cashRedResonlist,
  orderpositionlist,
  requestField,
  formatWarehouseTypeListKj,
  formatTime,
  formatSecondToHour, formatMinuteToHour,
  formatSecondNewToHour,
  formatTimerang,
  formatUpNumber,
  formatUpNumber1,
  formatProName,
  formatKeyWord,
  formatmoney,
  formatPercen,
  formatPass,
  formatRule,
  formatWarehouse,
  formatWarehouseNew,
  formatEnmSendWarehouse,
  formatSendWarehouse,
  formatWarehouseArea,
  formatwarehouseIdList,
  formatPlatform,
  formatPlatformkj,
  formatPlatformDistribution,
  formatPlatformtx,
  formatBianMaPlatform,
  formatBianMaRemoveCalc,
  formatYesorno,
  formatYesornoBool,
  formatIsError,
  formatIsOutStock,
  formatIsCheckError,
  formatPurchasePlanError,
  formatYesornofinished,
  formatbianmastatus,
  formatproducmask,
  convertToChild,
  formatLink,
  formatNoLink,
  formatExpressCompany,
  jsonToQueryString,
  htmlDecode,
  formatLinkProCode,
  formatTurnoverRang,
  formatFeeShareOper,
  formatShareFeeType,
  formatenmCashRedReson,
  doCopy,
  downMobanCommand,
  getUrlParam,
  setStore,
  getStore,
  downloadLink,
  AllLinkDaiPaiImpOrderDpStateOpts,
  fmtAllLinkDaiPaiImpOrderDpState,
  AllLinkDaiPaiSpOrderDpStateOpts,
  fmtAllLinkDaiPaiSpOrderDpState,
  pickerOptions,
  pickerOptionsDate,
  AllLinkChooseProfitStateOpts,
  fmtAllLinkChooseProfitState,
  ShootingVideoTaskUrgencyOptions,
  tothousands,
  onJumpLink,
  canJump,
  DeductOrderZrDeptActions,
  DeductOrderZrReasons,
  childcompany,
  DeductOrderZrType12,
  DeductOrderZrType12Tree,
  DeductOrderZrType12TreeBz,
  removeTrailingZeros,
  numberToChinese,//数字转文字
  convertIntegerPart,
  convertSegment,
  formatProCodeStutas3
}
