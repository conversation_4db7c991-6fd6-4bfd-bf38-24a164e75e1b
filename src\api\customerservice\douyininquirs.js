
import request from '@/utils/request'
const GroupPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/DouYinInquirs/`

//获取组distinct
export const getDouYinGroup = (params, config = {}) => { return request.get(GroupPrefix + 'GetDouYinGroup', { params: params, ...config }) }
//分页获取分组
export const getDouYinGroupPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetDouYinGroupPageList', params, config) }
//新增-组
export const addDouYinGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'AddDouYinGroupAsync', params, config) }
//删除-组
export const deleteDouYinGroupAsync = (params, config = {}) => { return request.get(GroupPrefix + 'DeleteDouYinGroupAsync', { params: params, ...config }) }
//修改-组
export const updateDouYinGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'UpdateDouYinGroupAsync', params, config) }
//导入-组
export const importDouYinGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportDouYinGroupAsync', params, config) }
//通过批次号删除组
// export const deleteBatchStrDouYinGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'DeleteBatchStrDouYinGroupAsync', { params: params, ...config }) }
export const deleteBatchStrDouYinGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'deleteBatchStrDouYinGroupAsync', params, config) }
//批量离组
export const batchUpdateLeaveDateAsync = (params, config = {}) => { return request.post(GroupPrefix + 'BatchUpdateLeaveDateAsync',  params, config ) }
//获取分组修改日志
export const GetGroupLogList = (params, config = {}) => { return request.post(GroupPrefix + 'GetGroupLogList', params, config) }
//获取分组修改日志
export const GetDouYinInquirsNotExistsList = (params, config = {}) => { return request.post(GroupPrefix + 'GetDouYinInquirsNotExistsList', params, config) }



//分页获取咨询数据
export const getDouYinInquirsPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetDouYinInquirsPageList', params, config) }
//删除咨询数据
export const deleteDouYinInquirsAsync = (params, config = {}) => { return request.get(GroupPrefix + 'DeleteDouYinInquirsAsync', { params: params, ...config }) }
//导入-咨询数据
export const importDouYinInquirsAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportDouYinInquirsAsync', params, config) }


//个人效率统计-售前
export const getDouYinPersonalEfficiencyPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetDouYinPersonalEfficiencyPageList', params, config) }
//个人效率统计-店铺个人效率-售前
export const getDouYinShopPersonalEfficiencyPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetDouYinShopPersonalEfficiencyPageList', params, config) }
//个人效率统计-个人趋势图-售前
export const getDouYinPersonalEfficiencyChat = (params, config = {}) => { return request.post(GroupPrefix + 'GetDouYinPersonalEfficiencyChat', params, config) }
//抖音个人效率统计导出
export const exportDouYinPersonalEfficiencyPageList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportDouYinPersonalEfficiencyPageList', params, config)
}

//组效率统计-分页查询
export const getDouYinGroupEfficiencyPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetDouYinGroupEfficiencyPageList', params, config) }
//组效率统计-组趋势图
export const getDouYinGroupEfficiencyChat = (params, config = {}) => { return request.post(GroupPrefix + 'GetDouYinGroupEfficiencyChat', params, config) }
//抖音组效率统计导出
export const exportDouYinGroupEfficiencyPageList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportDouYinGroupEfficiencyPageList', params, config)
}


//店效率统计-分页查询
export const getDouYinShopEfficiencyPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetDouYinShopEfficiencyPageList', params, config) }
//店效率统计-铺趋势图
export const getDouYinShopEfficiencyChat = (params, config = {}) => { return request.post(GroupPrefix + 'GetDouYinShopEfficiencyChat', params, config) }
//抖音店效率统计导出
export const exportDouYinShopEfficiencyPageList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportDouYinShopEfficiencyPageList', params, config)
}


//店铺组效率
export const getDyInquirsStatisticsByShopListMonth = (params, config = {}) => { return request.post(GroupPrefix + 'GetDyInquirsStatisticsByShopListMonth', params, config) }

//导入客服会话
export const importDouYinInquirsKfAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportDouYinInquirsKfAsync', params, config) }
//获取客服会话
export const getDouYinInquirsKfPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetDouYinInquirsKfPageList', params, config) }
//删除客服会话数据
export const deleteDouYinInquirsKfAsync = (params, config = {}) => { return request.get(GroupPrefix + 'DeleteDouYinInquirsKfAsync', { params: params, ...config }) }
//服务数据统计-组人
export const getDouYinPersonalEfficiencyKfPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetDouYinPersonalEfficiencyKfPageList', params, config) }
//服务数据统计-店人
export const getDouYinShopPersonalEfficiencyKfPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetDouYinShopPersonalEfficiencyKfPageList', params, config) }

