<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <!-- elment样式 -->
    <link rel="stylesheet" href="/approvalform/html/api/elment.css">
    <!-- vue -->
    <script src="/approvalform/html/api/vue.min.js"></script>
    <!-- elment脚本 -->
    <script src="/approvalform/html/api/elment.js"></script>
    <!-- jquery -->
    <script src="/approvalform/html/api/jquery.min.js"></script>

    <script src="/approvalform/html/api/html2canvas.js"></script>

    <title>运营申报</title>
    <style type="text/css">
        .linebreak {
            overflow: hidden;
            /*超出部分隐藏*/

            text-overflow: ellipsis;
            /* 超出部分显示省略号 */
            white-space: normal;
            /*规定段落中的文本不进行换行 */
            width: 100%;
        }
    </style>
</head>

<body>
    <div id="app" style="margin:0 auto;">
        <el-container direction="vertical" style="padding: 0px 10px 0px 10px ; border: 1px #ccc solid;">
            <template>
                <!-- <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="2" :xl="2">
                        <el-button type="primary" size="default" @click="tocreateimg()">下载图片</el-button>
                        
                    </el-col>
                </el-row> -->
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <div ref="oneboxx">
                            <el-table :data="list" style="width: 100%;margin-bottom: 20px;" row-key="id" border
                                default-expand-all :tree-props="{children: 'dtList', hasChildren: 'hasChildren'}">
                                <el-table-column prop="goodsCode" label="系列编码/商品编码" width="180">
                                </el-table-column>
                                <el-table-column prop="goodsName" label="产品名称" width="180">
                                </el-table-column>
                                <el-table-column prop="picture" label="产品图" width="100">
                                    <template slot-scope="scope">
                                        <el-image :src="scope.row.picture"  :preview-src-list="[scope.row.picture]"
                                            style="max-width: 50px; max-height: 50px; margin-left: -10px;" fit="fill" :lazy="true"></el-image>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="createdTime" label="生成时间">
                                </el-table-column>
                                <el-table-column prop="brandName" label="采购人员">
                                </el-table-column>
                                <el-table-column prop="purchaseOrderCreateTime" label="生成采购单时间">
                                </el-table-column>
                                <el-table-column prop="purchaseOrderAuditTime" label="采购单审核时间">
                                </el-table-column>
                                <el-table-column prop="supplier" label="最近供应商">
                                </el-table-column>
                                <el-table-column prop="buyNo" label="采购单号">
                                </el-table-column>
                                <el-table-column prop="indexNo" label="ERP单号">
                                </el-table-column>
                                <el-table-column prop="count" label="数量">
                                </el-table-column>
                                <el-table-column prop="status" label="采购单状态">
                                </el-table-column>
                                <el-table-column prop="directorName" label="运营">
                                </el-table-column>
                                <el-table-column prop="groupName" label="运营组">
                                </el-table-column>
                                <el-table-column prop="firstInboundTime" label="首次入库时间">
                                </el-table-column>
                                <el-table-column prop="warehouseName" label="仓库">
                                </el-table-column>
                            </el-table>
                        </div>
                    </el-col>
                </el-row>
            </template>
        </el-container>
    </div>
    <script>
        var vm = new Vue({
            el: '#app',
            data: function () {
                return {
                    //正式上线时，请切换为正式地址，末尾不许带 /
                    //测试：http://**************
                    //正式：http://************* 
                    thisInterfaceUrl: "http://**************",
                    thisFormData: {},
                    thisDoc: {},
                    thisDocGoods: [],
                    thisChooses: [],
                    thisSkus: [],
                    url1: "",
                    srcList1: [],
                    url2: "",
                    srcList2: [],
                    url3: "",
                    srcList3: [],
                    thisLonding: true,
                    list: [],
                }
            },
            created() {

            },
            async mounted() {
                this.getStyleSheetInfo();
            },
            methods: {
                async getStyleSheetInfo() {
                    var me = this;
                    let searchURL = window.location.search;
                    searchURL = searchURL.substring(1, searchURL.length);
                    let targetPageId = searchURL.split("&")[0].split("=")[1];
                    me.thisLonding = true;
                    let params = {
                        keywords: targetPageId,
                        orderBy: 'createdTime',
                        currentPage: 1,
                        pageSize: 10
                    };
                    $.ajax({
                        type: 'POST',
                        async: false,
                        url: `/api/Inventory/goodscodestock/GetPurchaseGoodsHtmlAsync`,
                        data: JSON.stringify(params),
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        success: function (result) {
                            me.thisLonding = false;
                            me.list = result.data.list
                            me.list.forEach(f => {
                                if (f.dtList!= null && f.dtList.length > 0) {
                                    f.dtList.forEach(d =>{
                                        d.hasChildren = true
                                    })
                                }
                            })
                            console.log('数据输出',me.list)
                        },
                        error: function (err) {
                            console.log(err);
                        }
                    })
                },
                tocreateimg() {
                    html2canvas(this.$refs.oneboxx).then((canvas) => {
                        let dataURL = canvas.toDataURL('image/png')
                        this.imgUrl = dataURL
                        console.log("生成图片", this.imgUrl);
                        //this.dialogTableVisible = true;
                    })
                },
                //转换成小数
                formatSecondNewToHour(minutes) {
                    if (!minutes || minutes == 0)
                        return ''
                    var day = Math.floor(minutes / (60 * 24));
                    return (((day * 24 + Math.floor((minutes % (60 * 24)) / 60)) / 24).toFixed(1));
                }
            }
        });
    </script>
</body>

</html>