import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_JAVA_API_BLADEGATEWAY}/yunhan-gis-tradingchain/preWarningWareSetting/`

//配置预警仓库-列表
export const preWarningWareHouseList = (params, config = {}) => { return request.post(apiPrefix + 'preWarningWareHouseList', params, config) }

//配置预警仓库-逻辑删除
// export const preWarningWareHouseRemove = (params, config = {}) => { return request.post(apiPrefix + 'preWarningWareHouseRemove', params, config) }
export const preWarningWareHouseRemove = (params, config = {}) => { return request.post(apiPrefix + 'preWarningWareHouseRemove?ids=' + params.id, params, config) }

//配置预警仓库-新增
export const preWarningWareHouseSave = (params, config = {}) => { return request.post(apiPrefix + 'preWarningWareHouseSave', params, config) }
