import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Media}/mediasceneset/`
//获取数据-ById
export const getMediaSceneSetDataById = (params, config = {}) => {
    return request.get(apiPrefix + 'GetMediaSceneSetDataByIdAsync', { params, ...config })
}
//获取数据
export const getMediaSceneSetData = (params, config = {}) => {
    return request.get(apiPrefix + 'GetMediaSceneSetDataAsync', { params, ...config })
}
//保存
export const saveMediaSceneSet = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveMediaSceneSetAsync', params, config)
}
//删除
export const deleteMediaSceneSet = (params, config = {}) => {
    return request.get(apiPrefix + 'DeleteMediaSceneSetAsync', { params, ...config })
}


