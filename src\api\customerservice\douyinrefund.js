
import request from '@/utils/request'
const GroupPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/DouYinRefund/`

//导入退款订单
export const importAlbbGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportAlbbGroupAsync', params, config) }

//仅退款订单数据-商品汇总维度
export const getOnlyRefundGoodsPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetOnlyRefundGoodsPageList', params, config) }
//仅退款订单数据-商品汇总维度-导出
export const exportOnlyRefundGoodsList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportOnlyRefundGoodsList', params, config)
}
//仅退款订单数据-商品汇总维度-已发货仅退款弹窗
export const getOnlyRefundGoodsYSendDtl = (params, config = {}) => { return request.post(GroupPrefix + 'GetOnlyRefundGoodsYSendDtl', params, config) }
//仅退款订单数据-商品汇总维度-已发货仅退款弹窗
export const getOnlyRefundGoodsResaonDtl = (params, config = {}) => { return request.post(GroupPrefix + 'GetOnlyRefundGoodsResaonDtl', params, config) }



//仅退款订单数据-订单维度
export const getOnlyRefundOrderPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetOnlyRefundOrderPageList', params, config) }
//仅退款订单数据-订单维度-导出
export const exportOnlyRefundOrderList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportOnlyRefundOrderList', params, config)
}

//退货退款订单数据-商品汇总维度
export const getBackRefundGoodsPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetBackRefundGoodsPageList', params, config) }
//退货退款订单数据-商品汇总维度-导出
export const exportBackRefundGoodsList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportBackRefundGoodsList', params, config)
}
//退货退款订单数据-商品汇总维度-退货退款总金额弹窗
export const getBackRefundGoodsBackAllAmountDtl = (params, config = {}) => { return request.post(GroupPrefix + 'GetBackRefundGoodsBackAllAmountDtl', params, config) }
//退货退款订单数据-商品汇总维度-售后原因弹窗
export const getBackRefundGoodsResaonDtl = (params, config = {}) => { return request.post(GroupPrefix + 'GetBackRefundGoodsResaonDtl', params, config) }
//退货退款订单数据-商品汇总维度-退货物流公司弹窗
export const getBackRefundGoodsComDtl = (params, config = {}) => { return request.post(GroupPrefix + 'GetBackRefundGoodsComDtl', params, config) }


//退货退款订单数据-订单维度
export const getBackRefundOrderPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetBackRefundOrderPageList', params, config) }
//退货退款订单数据-订单维度-导出
export const exportBackRefundOrderList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportBackRefundOrderList', params, config)
}

//退款订单数据-汇总
export const getSumRefundPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetSumRefundPageList', params, config) }
//退款订单数据-汇总-导出
export const exportSumRefundList = (params, config = { responseType: 'blob' }) => {
    return request.post(GroupPrefix + 'ExportSumRefundList', params, config)
}
//退款订单数据-汇总
export const getSumRefundGoodsDtlPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetSumRefundGoodsDtlPageList', params, config) }
//退款订单数据-汇总-退货退款总金额弹窗
export const getSumRefundBackAllAmountShopDtl = (params, config = {}) => { return request.post(GroupPrefix + 'GetSumRefundBackAllAmountShopDtl', params, config) }
//退款订单数据-汇总-退货物流公司弹窗
export const getSumRefundComShopDtl = (params, config = {}) => { return request.post(GroupPrefix + 'GetSumRefundComShopDtl', params, config) }
//退款订单数据-汇总-仅退款总金额弹窗
export const getSumRefundOnlyAllAmountShopDtl = (params, config = {}) => { return request.post(GroupPrefix + 'GetSumRefundOnlyAllAmountShopDtl', params, config) }
//退款订单数据-汇总-仅退款售后原因弹窗
export const getSumRefundOnlyResaonShopDtl = (params, config = {}) => { return request.post(GroupPrefix + 'GetSumRefundOnlyResaonShopDtl', params, config) }
//退款订单数据-汇总-退货退款售后原因弹窗
export const getSumRefundBackResaonShopDtl = (params, config = {}) => { return request.post(GroupPrefix + 'GetSumRefundBackResaonShopDtl', params, config) }


//批次号查询
export const GetImportDouYinRefundOrderBatchPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetImportDouYinRefundOrderBatchPageList', params, config) }
//批次号删除
export const DeleteImportDouYinRefundOrderBatch = (params, config = {}) => { return request.post(GroupPrefix + 'DeleteImportDouYinRefundOrderBatch', params, config) }

//抖音小额打款数据
export const getPettyPayment_DYPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetPettyPayment_DYPageList', params, config) }
//抖音小额打款数据——店铺弹窗
export const getPettyPaymentShop_DYPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetPettyPaymentShop_DYPageList', params, config) }
//抖音小额打款数据——打款类型弹窗
export const getPettyPaymentType_DYPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetPettyPaymentType_DYPageList', params, config) }
//抖音小额打款数据——打款类型汇总弹窗
export const getPettyPaymentTypeGroup_DYPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetPettyPaymentTypeGroup_DYPageList', params, config) }
//抖音系列产品汇总数据
export const getPettyPaymentProductSeriesSum_DYPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetPettyPaymentProductSeriesSum_DYPageList', params, config) }
//抖音系列产品汇总数据-导出
export const exportPettyPaymentProductSeriesSum_DYList = (params, config = { responseType: 'blob' }) => { return request.post(GroupPrefix + 'ExportPettyPaymentProductSeriesSum_DYList', params, config)}
//导入抖音小额打款数据
export const importPettyPayment_DYAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportPettyPayment_DYAsync', params, config) }
