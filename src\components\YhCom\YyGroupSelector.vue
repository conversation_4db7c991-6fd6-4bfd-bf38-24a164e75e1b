<template>
    <el-select filterable v-model="innerValue" clearable placeholder="运营组" :style="{width: width}" :multiple="multiple" :collapse-tags="collapse"
     @change="valueChanged($event)" @clear="clear" >
        <el-option key="无运营组" label="无运营组" :value="0"></el-option>
        <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
</template>

<script>

import { getDirectorGroupList } from '@/api/operatemanage/base/shop'

    export default {
        name: 'YyGroupSelector',
        props: {
            width: {
                type: String,
                default() {
                    return '90px';
                }
            },
            multiple: {
                type: Boolean,
                default() { return false; }
            },
            collapse: {
                type: Boolean,
                default() { return false; }
            },
            value: {
                type: String,
                default() {
                    return null;
                }
            },
            text: {
                type: String,
                default() {
                    return ""
                }
            },
            clearable: {
                type: <PERSON><PERSON><PERSON>,
                default() { return true; }
            },
            cststyle: {
                type: Object,
                default() {
                    return {}
                }
            }
        },
        data() {
            return {
                loading: false,
                innerValue:null,
                grouplist: [],
            }
        },
        computed: {
        },
        async mounted() {
            var res2 = await getDirectorGroupList();
            this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
            this.innerValue=this.value;

        },
        methods: {
            clearValue(){
              this.innerValue = null
            },
            clear(){
                this.$emit("change", null);
            },
            valueChanged(newValue) {
                this.$emit("change",newValue);
            },
        }
    }
</script>

