import request from '@/utils/request'
import { exp } from 'mathjs'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/ProcodeProcessPlan/`


// 获取全平台日报数据
export const ProcodeProcessPlanGetProductDayReportPageList = (params, config) => { return request.post(apiPrefix + 'ProcodeProcessPlanGetProductDayReportPageList', params, config) }
//获取全平台日报数据的所有proCode
export const ProcodeProcessPlanGetProductDayReportList = (params, config) => { return request.post(apiPrefix + 'ProcodeProcessPlanGetProductDayReportList', params, config) }

//全平台日报数据导出
export const exportProcodeProcessPlanGetProductDayReportPageList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportProcodeProcessPlanGetProductDayReportPageList', params, config) }

// 导入生成方案
export const ImportCreateProcodeProcessPlan = (params, config) => { return request.post(apiPrefix + 'ImportCreateProcodeProcessPlan', params, config) }


// 获取所有可勾选指标
export const GetProcodeProcessPlanColList = (params, config = {}) => { return request.get(apiPrefix + 'GetProcodeProcessPlanColList', { params, ...config }) }

// 预览明细
export const PreviewProcodeProcessPlanDtl = (params, config) => { return request.post(apiPrefix + 'PreviewProcodeProcessPlanDtl', params, config) }

// 创建产品处理方案
export const CreateProcodeProcessPlan = (params, config) => { return request.post(apiPrefix + 'CreateProcodeProcessPlan', params, config) }

// 分页获取产品处理方案
export const GetProcodeProcessPlanPageList = (params, config) => { return request.post(apiPrefix + 'GetProcodeProcessPlanPageList', params, config) }
//获取所有产品处理方案
export const GetPlanPageList = (params, config) => { return request.post(apiPrefix + 'GetPlanPageList', params, config) }

// 获取单个产品处理方案
export const GetProcodeProcessPlanById = (params, config = {}) => { return request.get(apiPrefix + 'GetProcodeProcessPlanById', { params, ...config }) }

// 删除产品处理方案
export const DeleteProcodeProcessPlan = (params, config = {}) => { return request.get(apiPrefix + 'DeleteProcodeProcessPlan', { params, ...config }) }

// 填写处理方案（不是自己的品不让保存）
export const WriteOperateProcessPlan = (params, config) => { return request.post(apiPrefix + 'WriteOperateProcessPlan', params, config) }



// 预览明细2
export const PreviewProcodeProcessPlanPros = (params, config) => { return request.post(apiPrefix + 'PreviewProcodeProcessPlanPros', params, config) }
// 获取单个产品处理方案
export const GetProcodeProcessPlanById2 = (params, config) => { return request.post(apiPrefix + 'GetProcodeProcessPlanById2', params, config) }
// 删除明细Pro
export const DeleteProcodeProcessPlanProByIds = (params, config) => { return request.post(apiPrefix + 'DeleteProcodeProcessPlanProByIds', params, config) }
//获取单个产品处理方案2-导出明细
export const ExportProcodeProcessPlanById2 = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportProcodeProcessPlanById2', params, config)
}


// 自动生成方案设置-查询
export const GetProcodeProcessPlanProAutoPlanSet = (params, config = {}) => { return request.get(apiPrefix + 'GetProcodeProcessPlanProAutoPlanSet', { params, ...config }) }
// 自动生成方案设置
export const SaveProcodeProcessPlanProAutoPlanSet = (params, config) => { return request.post(apiPrefix + 'SaveProcodeProcessPlanProAutoPlanSet', params, config) }
// 自动生成方案手动点击
export const AutoCreateProcodeProcessPlan = (params, config = {}) => { return request.get(apiPrefix + 'AutoCreateProcodeProcessPlan', { params, ...config }) }


// 新增白名单
export const ImportProcodeProcessPlanBmd = (params, config) => { return request.post(apiPrefix + 'ImportProcodeProcessPlanBmd', params, config) }
// 新增白名单
export const SaveProcodeProcessPlanBmd = (params, config) => { return request.post(apiPrefix + 'SaveProcodeProcessPlanBmd', params, config) }
// 删除白名单
export const DelProcodeProcessPlanBmd = (params, config) => { return request.post(apiPrefix + 'DelProcodeProcessPlanBmd', params, config) }
// 白名单查询
export const GetProcodeProcessPlanBmdList = (params, config) => { return request.post(apiPrefix + 'GetProcodeProcessPlanBmdList', params, config) }
// 白名单导出
export const ExportProcodeProcessPlanBmdList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportProcodeProcessPlanBmdList', params, config) }
// 查看ProCode是不是白名单里的
export const GetProcodeProcessPlanBmdListByProCodes = (params, config) => { return request.post(apiPrefix + 'GetProcodeProcessPlanBmdListByProCodes', params, config) }
// 产品处理方案-更改行政按钮权限状态
export const UpdateProcodeProcessPlanByAnNiu = (params, config = {}) => { return request.get(apiPrefix + 'UpdateProcodeProcessPlanByAnNiu', { params, ...config }) }
// 获取产品处理方案-行政按钮权限状态
export const GetProcodeProcessPlanByAnNiu = (params, config = {}) => { return request.get(apiPrefix + 'GetProcodeProcessPlanByAnNiu', { params, ...config }) }



















