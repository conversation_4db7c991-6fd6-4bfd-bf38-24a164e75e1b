import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/warehouse/`

export const updateWarehouse = (params, config = {}) => {
  return request.post(apiPrefix + 'UpdateWarehouse', params, config)
}
export const getWarehousesigle = (params, config = {}) => { return request.get(apiPrefix + 'GetWarehouse', { params: params, ...config }) }
export const getAllProBrand = (params, config = {}) => { return request.get(apiPrefix + 'GetAllBianMaBrandAsync', { params: params, ...config }) }
export const pageWarehouseRecord = (params, config = {}) => { return request.get(apiPrefix + 'PageWarehouseRecordAsync', { params: params, ...config }) }
export const pageWarehouseRecord2 = (params, config = {}) => { return request.get(apiPrefix + 'PageWarehouseRecord2Async', { params: params, ...config }) }
export const getWarehouse = (params, config = {}) => { return request.get(apiPrefix + `GetWarehouseAsync`, { params: params, ...config }) }
export const getPageWarehouse = (params, config = {}) => { return request.get(apiPrefix + `GetPageWarehouseList`, { params: params, ...config }) }
export const getPageWarehouseSub = (params, config = {}) => { return request.get(apiPrefix + `GetPageWarehouseSubList`, { params: params, ...config }) }

export const warehouseRecordAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'WarehouseRecordAnalysisAsync', { params: params, ...config }) }
export const importWarehouseRecord = (params, config = {}) => { return request.post(apiPrefix + 'ImportWarehouseRecordAsync', params, config) }
export const importWarehouseRecordV2 = (params, config = {}) => { return request.post(apiPrefix + 'ImportWarehouseRecordv2Async', params, config) }
export const exportWarehouseRecord = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportWarehouseRecordAsync', params, config) }

export const pageBianMaBrand = (params, config = {}) => { return request.get(apiPrefix + 'PageBianMaBrandAsync', { params: params, ...config }) }
export const getBianMaBrand = (params, config = {}) => { return request.get(apiPrefix + 'GetBianMaBrandAsync', { params: params, ...config }) }
export const GetBianMaBrandPurDept = (params, config = {}) => { return request.get(apiPrefix + 'GetBianMaBrandPurDept', { params: params, ...config }) }

export const getBianMaBrandTeamTree = (params, config = {}) => { return request.get(apiPrefix + 'GetBianMaBrandTeamTreeAsync', { params: params, ...config }) }
export const checkRepeatNameAddUser = (params, config = {}) => { return request.post(apiPrefix + 'CheckRepeatNameAddUser', params, config) }
export const updateBianMaBrand = (params, config = {}) => { return request.post(apiPrefix + 'UpdateBianMaBrandAsync', params, config) }
export const updateBianMaBrandIsAuto = (params, config = {}) => { return request.post(apiPrefix + 'UpdateBianMaBrandIsAuto', params, config) }

export const pageStockTaking = (params, config = {}) => { return request.get(apiPrefix + 'PageStockTakingAsync', { params: params, ...config }) }
export const getInventoryCheckFee = (params, config = {}) => { return request.get(apiPrefix + 'GetInventoryCheckFeeAsync', { params: params, ...config }) }

export const getAllBianMaBrandByFilter = (params, config = {}) => { return request.post(apiPrefix + 'GetAllBianMaBrandByFilterAsync', params, config) }

export const getCurBianMaBrand = (params, config = {}) => { return request.get(apiPrefix + 'GetCurBianMaBrandAsync', { params: params, ...config }) }
export const getAllWarehouse = (params, config = {}) => { return request.get(apiPrefix + 'GetAllWarehouseAsync', { params: params, ...config }) }

export const getBianManPositionList = (params, config = {}) => { return request.get(apiPrefix + 'GetBianManPositionList', { params: params, ...config }) }

export const GetAllGoodsDetailList = (params, config = {}) => { return request.get(apiPrefix + 'GetAllGoodsDetailList', { params: params, ...config }) }
export const GetPageFuHuaCangList = (params, config = {}) => { return request.post(apiPrefix + 'GetPageFuHuaCangList', params, config) }
export const GetPageFuHuaCangHistoryList = (params, config = {}) => { return request.post(apiPrefix + 'GetPageFuHuaCangHistoryList', params, config) }
export const GetAllSubmitUserList = (params, config = {}) => { return request.get(apiPrefix + 'GetAllSubmitUserList', { params: params, ...config }) }
export const GetLogAllocateDetailList = (params, config = {}) => { return request.get(apiPrefix + 'GetLogAllocateDetailList', { params: params, ...config }) }
export const SaveOpSpecialUser = (params, config = {}) => { return request.get(apiPrefix + 'SaveOpSpecialUser', { params: params, ...config }) }
export const GetCtrlUserLog = (params, config = {}) => { return request.get(apiPrefix + 'GetCtrlUserLog', { params: params, ...config }) }
export const GetSpecialUser = (params, config = {}) => { return request.get(apiPrefix + 'GetSpecialUser', { params: params, ...config }) }

export const getWarehousePositionUsagePageList = (params, config = {}) => { return request.post(apiPrefix + 'GetWarehousePositionUsagePageList', params, config) }
export const getWarehousePositionUsageLogPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetWarehousePositionUsageLogPageList', params, config) }
export const updateWarehousePositionUsage = (params, config = {}) => { return request.post(apiPrefix + 'UpdateWarehousePositionUsage', params, config) }
//获取采购岗位数据
export const getBianManPositionListV2 = (params, config = {}) => { return request.post(apiPrefix + 'GetBianManPositionListV2', params, config) }

//获取仓库收货地址 GetWarehouseReceiveAddressSets
export const getWarehouseReceiveAddressSets = (params, config = {}) => { return request.post(apiPrefix + 'GetWarehouseReceiveAddressSets', params, config ) }

//保存仓库收货地址 SaveWarehouseReceiveAddressSet
export const saveWarehouseReceiveAddressSet = (params, config = {}) => { return request.post(apiPrefix + 'SaveWarehouseReceiveAddressSet', params, config ) }

//删除仓库收货地址 DeleteWarehouseReceiveAddressSetById
export const deleteWarehouseReceiveAddressSetById = (params, config = {}) => { return request.post(apiPrefix + 'DeleteWarehouseReceiveAddressSetById', params, config ) }

//获取所有仓库
export const GetAllWarehouseList = (params, config = {}) => { return request.get(apiPrefix + 'GetAllWarehouseList', { params: params, ...config }) }
//导入库位库容
export const ImportKuWeiKuRongList = (params, config = {}) => { return request.post(apiPrefix + 'ImportKuWeiKuRongList', params, config) }
//库位库容分页查询
export const GetKuWeiKuRongPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetKuWeiKuRongPageList', params, config ) }
//编辑库位库容真实数量
export const editKuWeiKuRongActualQuantity = (params, config = {}) => { return request.post(apiPrefix + 'EditKuWeiKuRongActualQuantity', params, config) }


//采购员离职账号自动禁用设置获取
export const GetBianMaBrandLeaveSet = (params, config = {}) => { return request.get(apiPrefix + 'GetBianMaBrandLeaveSet', { params: params, ...config }) }
//采购员离职账号自动禁用设置
export const SaveBianMaBrandLeaveSet = (params, config = {}) => { return request.post(apiPrefix + 'SaveBianMaBrandLeaveSet', params, config) }
//采购员离职日志
export const GetBianMaBrandLeaveLogList = (params, config = {}) => { return request.post(apiPrefix + 'GetBianMaBrandLeaveLogList', params, config) }
//采购员离职账号自动禁用记录日志
export const SaveBianMaBrandLeaveLog = (params, config = {}) => { return request.post(apiPrefix + 'SaveBianMaBrandLeaveLog', params, config) }
//采购员离职账号自动禁用记录日志
export const AutoBianMaBrandLeaveLog = (params, config = {}) => { return request.get(apiPrefix + 'AutoBianMaBrandLeaveLog', { params: params, ...config }) }
//返回采购组导出
export const ExportBianMaBrandAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportBianMaBrandAsync', params, config) }
//更新标题
export const UpdateTitle = (params, config = {}) => { return request.post(apiPrefix + 'UpdateTitle', params, config) }
//更新标题日志
export const GetTitleHistory = (params, config = {}) => { return request.get(apiPrefix + 'GetTitleHistory', { params: params, ...config }) }