import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/OutWarehouseFee/`

export const GetyOutWarehouseFeePageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetyOutWarehouseFeePageList', params, config)
}

export const ImportOutWarehouseFee = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportOutWarehouseFee', params, config)
}

export const GetyOutWarehouseOrderFeePageList = (params, config) => { return request.post(apiPrefix + 'GetyOutWarehouseOrderFeePageList', params, config) }
export const ExportOutWarehouseOrderFeeList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportOutWarehouseOrderFeeList', params, config) }


export const GetyWarehouseSalaryPageList = (params, config) => { return request.post(apiPrefix + 'GetyWarehouseSalaryPageList', params, config) }

export const ReClacOutWarehouseFee = (params, config) => { return request.post(apiPrefix + 'ReClacOutWarehouseFee', params, config) }
//出仓成本数据源导出
export const exportOutWarehouseFee = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + `ExportOutWarehouseFee`,  params, config) } 
