<template>
    <div style="height:100%;padding-left:10px;overflow: auto;min-height: 100px;" v-loading="loading">
        <div  class="fixBox" v-if="Array.isArray(analysisData.legendGroup)" @click="openlog" >
            <div class="suspend"  >
                <i class="el-icon-data-board"></i>
            </div>
            <span class="font" style="font-size: 20px; cursor: pointer;">筛选</span>
            
        </div>
        
        <div v-if="analysisData && analysisData.series  && analysisData.series !=null && analysisData.series.length>0" :id="'buschar'+randrom" :style="thisStyle" ></div>
        <div v-else>没有可供展示的图表数据！</div>

        <el-dialog
            title="切换展示"
            :visible.sync="showDialog"
            width="50%"
            :append-to-body='true'
            v-dialogDrag
            id="buscharSelect20241010"
            @close="closedLog"
            >
            <el-input v-model="keywords" style="width:200px;" placeholder="请输入关键字进行检索"
                @keyup.enter.native="getKeyWord(keywords)" clearable>
            </el-input>
                
           <template  v-for="(value) in analysisData.legendGroup" >
            <div :key="value.name" class="legendGroup">
                 <el-row :key="value.name" style="margin-top:10px">
                <el-checkbox  v-model="value.checkAll" @change="handleCheckAllChange(value)" >{{value.name}}:</el-checkbox>
                </el-row>
                <el-row class="fontOverflow">
                    <el-checkbox-group v-model="value.selected" @change="handleCheckedChange(value)">
                        <el-col v-for="item in value.legens" :key="item" :span="4">
                            <el-checkbox :label="item" :key="item">
                                <el-tooltip class="item" effect="dark" :content="item" placement="top-start">
                                    <span :style="keywords && item.includes(keywords)  ? 'background-color:yellow' : ''">{{item}}</span>
                                </el-tooltip>
                            </el-checkbox>
                        </el-col>
                    </el-checkbox-group>
                </el-row>
            </div>
           </template>
            <span slot="footer" class="dialog-footer">
                <el-button @click="checkCommonLegend">勾选常用选项</el-button>
                <el-button @click="saveCommonLegend">保存常用选项</el-button>

                <el-button @click="showDialog = false">取 消</el-button>
                <el-button type="primary" @click="submit">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
    import * as echarts from 'echarts';
    import {GetDayReportSumDialogColumnCacheAsync,SetDayReportSumDialogColumnCacheAsync } from '@/api/bookkeeper/reportday.js'

    export default {
        name: 'buschar',
        components: {},
        props: {
            loading:{type:Boolean,default:function(){
                return false;
            }},
            action: { type: Function, default: null },
            parms: { type: Object, default: null },
            analysisData: { type: Object, default: null },
            thisStyle: {
                type: Object,
                default: function () {
                    return {
                        width: '100%', height: '550px', 'box-sizing': 'border-box', 'line-height': '360px'
                    }
                }
            },
            gridStyle: {
                type: Object, default: function () {
                    return {
                        top: '20%',
                        left: '5%',
                        right: '4%',
                        bottom: '5%',
                        containLabel: false
                    }
                }
            },
            legendChanges: { type: Function, default: null },
        },
        data () {
            return {
                that: this,
                randrom: "",
                period: 0,
                pageLoading: false,
                listLoading: false,
                procode: '',
                showDialog:false,
                keywords:'',
                logSelectDataOld:null,
                logSelectDataNew:{
                    flag:false,
                    data:{}
                },
            }
        },
        created () {
            var e = 10;
            var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
                a = t.length,
                n = "";
            for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
            this.randrom = n;
        },
        mounted () {
            this.initcharts();
        },
        methods: {
            initcharts () {
                let that = this;

                if(!(that.analysisData && that.analysisData.series  && that.analysisData.series !=null && that.analysisData.series.length>0))
                    return;

                this.$nextTick(() => {
                    var chartDom = document.getElementById('buschar' + that.randrom);
                    var myChart =  echarts.init(chartDom);
                    myChart.clear();
                    if (that.analysisData.series) {
                        var option = that.Getoptions(that.analysisData);
                        option && myChart.setOption(option);
                    }
                    myChart.on('legendselectchanged', function (params) {
                        if(that.legendChanges!=null){
                            that.legendChanges(params.selected);
                        }
                    });
                });
            },
            randomString () {
                var e = 10;
                var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
                    a = t.length,
                    n = "";
                for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
                return n
            },
            handleCheckAllChange(val) {
                val.selected = val.checkAll ? val.legens: [];
                // console.log(this.analysisData,'gaibian All')
                this.handleCheck()
            },
            //选中后改变选中列  记录闪照
            handleCheck(){
                let checks = []
                this.analysisData.legendGroup.forEach(item=>{
                   if(item.selected.length > 0){
                       checks = [...checks,...item.selected]
                   }
                })

                this.analysisData.selectedLegend = checks
                this.logSelectDataNew.flag = false
                this.logSelectDataNew.data =JSON.parse(JSON.stringify(this.analysisData))
            },
            handleCheckedChange(value) {
                let checkedCount = value.selected.length;
                value.checkAll = checkedCount === value.legens.length;
                this.handleCheck()
                
                // console.log(this.analysisData,'gaibian shuju')
            },
            getKeyWord(v){
                if (!v) return;
                let {legendGroup} = this.analysisData 
                for (let i = 0; i < legendGroup.length; i++) {
                    for(let j = 0;j<legendGroup[i].legens.length;j++){
                        if(legendGroup[i].legens[j].includes(v) && !legendGroup[i].selected.includes(legendGroup[i].legens[j])){
                            legendGroup[i].selected.push(legendGroup[i].legens[j])
                            legendGroup[i].checkAll = legendGroup[i].selected.length == legendGroup[i].legens.length
                        }
                    }
                }
                this.analysisData.legendGroup = legendGroup
            }, 
            closedLog(){
                this.analysisData.selectedLegend = this.logSelectDataOld.selectedLegend
                this.showDialog=false
            },
            openlog(){
                if(this.logSelectDataNew.flag){
                    this.analysisData.selectedLegend = this.logSelectDataNew.data.selectedLegend
                }
                this.logSelectDataOld = JSON.parse(JSON.stringify(this.analysisData))
                this.logSelectDataNew.data =JSON.parse(JSON.stringify(this.analysisData))
                
                this.filterSelect(true)
                this.showDialog=true
                console.log(this.analysisData,'777')
            },
            filterSelect(type){
                if(!type){
                    this.logSelectDataNew.flag = false
                this.logSelectDataNew.data =JSON.parse(JSON.stringify(this.analysisData))

                }
                //selectedLegend 属性 处理选中
                console.log(this.analysisData,221)
                if(this.analysisData.selectedLegend.length >0){
                    this.analysisData.legendGroup.forEach(item=>{
                        item.selected = []

                        if(item.legens.length !== item.selected.length){
                                    item.checkAll = false 
                         }
                        item.legens.forEach(j=>{
                            if(this.analysisData.selectedLegend.includes(j)) {
                                item.selected.push(j)
                                console.log(item,'item')
                                if(item.legens.length == item.selected.length){
                                    item.checkAll = true
                                }
                            }
                        })
                    })
                }
                console.log(this.analysisData,'过滤后')
            },
            submit(){
                this.analysisData.selectedLegend = this.logSelectDataNew.data.selectedLegend
                this.logSelectDataNew.flag = true
                let select  = []
                if(this.analysisData.legendGroup.length>0){
                    let legendGroup = this.analysisData.legendGroup
                    legendGroup.forEach((item,index)=>{
                        select = [...select,...item.selected]
                    })
                }
                var chartDom = document.getElementById('buschar' + this.randrom);
                var myChart  =  echarts.init(chartDom);
                myChart.clear();
                if (this.analysisData.series) {
                    this.analysisData.selectedLegend = select
                    var option = this.Getoptions(this.analysisData);
                    option && myChart.setOption(option);
                }
                 if(this.legendChanges!=null){
                    let selected = {}
                    this.analysisData.legend.forEach(item=>{
                        selected[item]=select.includes(item)
                    })

                     this.legendChanges(selected);
                 }
                this.showDialog=false
            },
            Getoptions (element) {
                var series = []

                element.series.forEach(s => {
                    series.push({ smooth: true, ...s })
                })
                var yAxis = []
                if (Array.isArray(element.yAxis)) {
                    element.yAxis.forEach(s => {
                        yAxis.push({
                            type: 'value', minInterval: 1, offset: s.offset, splitLine: s.splitLine, position: s.position, name: s.name, max: s.max, min: s.min, axisLabel: {
                                formatter: function (value) {
                                    if (value >= 100000000) {
                                        value = (value / 100000000).toFixed(1) + 'Y';
                                    }
                                    if (value >= 10000000) {
                                        value = (value / 10000000).toFixed(1) + 'KW';
                                    }
                                    if (value >= 10000) {
                                        value = (value / 10000).toFixed(1) + 'W';
                                    }
                                    if (value >= 1000) {
                                        value = (value / 1000).toFixed(1) + 'K';
                                    }
                                    if (value <= -100000000) {
                                        value =(value / 100000000).toFixed(1) + 'Y';
                                    }
                                    if (value <= -10000000) {
                                        value = (value / 10000000).toFixed(1) + 'KW';
                                    }
                                    if (value <= -10000) {
                                        value = (value / 10000).toFixed(1) + 'W';
                                    }
                                    if (value <= -1000) {
                                        value = (value / 1000).toFixed(1) + 'K';
                                    }
                                    return value + s.unit;
                                }
                            }
                        })
                    })
                } else {
                    yAxis = { ...element.yAxis };
                }
                var selectedLegend = {};//{};
                if (element.selectedLegend) {
                  const processLegend = (legend) => {
                    legend.forEach(f => {
                      //if(!element.selectedLegend.includes(f)) selectedLegend["'"+f+"'"]=false
                      if (!element.selectedLegend.includes(f)) selectedLegend[f] = false;
                    });
                  };

                  if (element.legend.data) {
                    processLegend(element.legend.data);
                  } else {
                    processLegend(element.legend);
                  }
                }
                if (element && element.legend && element.legend.data) {
                  var legends = element.legend.data;
                } else {
                  var legends = element.legend;
                }
                //有legendGroup 不显示 legend
                let showLegend  = Array.isArray(element.legendGroup)
                var option = {
                    title: { text: element.title },
                    tooltip: { trigger: 'axis' },
                    legend: {
                        show:!showLegend,
                        selected: selectedLegend,
                        data: legends
                    },
                    grid: this.gridStyle,
                    toolbox: {
                        feature: {
                            magicType: { show: true, type: ['line', 'bar'] },
                            //restore: {show: true},
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross'
                        },
                        padding: [5, 10]
                    },
                    xAxis: {
                        type: 'category',
                        data: element.xAxis
                    },
                    yAxis: yAxis,
                    series: series
                };
                //将element.legend中的属性合并到option.legend中
                option.legend = { ...element.legend, ...option.legend };
                return option;
            },
            async checkCommonLegend(){
                //接口返回根据个人选中数据
                   const res = await GetDayReportSumDialogColumnCacheAsync({TableId :'buscharSelect20241010'})
                if(res.success){
                    let data = JSON.parse(res.data)
                    if(data.selectedLegend.length){
                        this.analysisData.selectedLegend = data.selectedLegend
                        this.filterSelect()
                        this.$message.success('选中常用项成功')
                    }else{
                        this.$message.warning('你还没有设置常用选项')
                    }
                }else{
                    this.$message.error('获取常用选项失败')
                }
                 
            },

            //保存常用legend选项
            async saveCommonLegend(){
                // 当前弹窗 里的选中项
                let checks = []
                this.analysisData.legendGroup.forEach(item=>{
                   if(item.selected.length > 0){
                       checks = [...checks,...item.selected]
                   }
                })
                //接口保存所有选中项
                   const res = await SetDayReportSumDialogColumnCacheAsync({selectedLegend:checks,TableId :'buscharSelect20241010'})
                   if(res.success){
                    this.$message.success('保存常用项成功')

                   }else{
                    this.$message.error('保存常用项失败')

                   }
                this.analysisData.selectedLegend = checks
                this.filterSelect()
                    
                console.log('查看当前legend',this.analysisData,checks,res)
              
            },
        }
    }
</script>

<style lang="scss" scoped>
.suspend{
       border-radius: 50%;
    position: relative;
    text-align: center;
    width: 30px;
    font-size: 20px;
    border: 1px solid gray;
    height: 30px;
    
}
.fixBox{
    position: absolute;
    display: flex;
    align-items: center;
    cursor: pointer;
    z-index: 1;
    &:hover{
        color: #73b8ff;
        .suspend{

            border-color: #73b8ff !important;
        }
    }
}

::v-deep .fontOverflow{
    .el-checkbox{
        width: fit-content;
        max-width: 100%;
        display: flex;
        align-items: center;
        .el-checkbox__label{
            width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    white-space: nowrap;
    text-overflow:ellipsis;
        }
    }
}
.legendGroup{
    & + &{
        margin-top: 20px;
    }
    
}
::v-deep .legendGroup > :nth-child(1) .el-checkbox__label{
    font-weight: 900;
    text-decoration: underline;
}

</style>

