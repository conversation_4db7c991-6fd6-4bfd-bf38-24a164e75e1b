<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <!-- elment样式 -->
    <link rel="stylesheet" href="/approvalform/html/api/elment.css">
    <!-- vue -->
    <script src="/approvalform/html/api/vue.min.js"></script>
    <!-- elment脚本 -->
    <script src="/approvalform/html/api/elment.js"></script>
    <!-- jquery -->
    <script src="/approvalform/html/api/jquery.min.js"></script>
    <title></title>
</head>

<body>
    <div id="app" style="margin:0 auto;">
        <el-container v-loading="thisLonding" direction="vertical"
            style="padding: 0px 10px 0px 10px ; border: 1px #ccc solid;">
            <template>
                <el-tabs v-model="buildDocId" @tab-click="handleClick">
                    <el-tab-pane v-for="item in buildDocIdList" :key="item.buildDocId" 
                        :label="item.goodsCompeteName" :name="item.buildDocId">
                    </el-tab-pane>
                </el-tabs>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="20" :xl="20" style="border-right: 1px #ccc solid;">
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24"
                                style="font-size: 16px;font-weight: bold;margin-top: 5px; ">
                                商品： <el-button type="primary" @click="onGetExcel" style="padding: 7px 10px ;"
                                    v-if=false>下载该附件
                                </el-button>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                                <el-table :data="thisDocGoods" height="350px" style="width: 99%;" tooltip-effect="dark"
                                    :show-summary="true" :summary-method="docGoodsHj">
                                    <el-table-column show-overflow-tooltip prop="goodsImageUrl" label="商品图片" width="70">
                                        <template slot-scope="scope">
                                            <el-image style="width: 50px; height: 50px" :src="scope.row.goodsImageUrl"
                                                :preview-src-list="[scope.row.goodsImageUrl]" fit="scale-down">
                                        </template>
                                        </el-image>
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="yhGoodsCode" label="商品编码" width="150">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="yhGoodsName" label="商品名称" width="265">
                                    </el-table-column>
                                    <!-- <el-table-column show-overflow-tooltip prop="yhGoodsUnit" label="品名单位" width="80">
                                    </el-table-column> -->
                                    <el-table-column show-overflow-tooltip prop="costPrice" label="成本价" width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="forNewWarehouseName" label="上新仓库"
                                        width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="goodsProgressType" label="商品类型"
                                        width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="yyGroupName" label="运营组" width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="isMainSaleName" label="是否主卖"
                                        width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="mainSaleAvgCount" label="主卖人均件数"
                                        width="95">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="estimateStockInCount" label="预计进货数量"
                                        width="110">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="estimateStockInAmount" label="预计进货金额"
                                        width="120">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip width="170" label="长宽高（cm）">
                                        <template slot-scope="scope">
                                            {{scope.row.goodsLength == null? '0.00':scope.row.goodsLength.toFixed(2)}} *
                                            {{scope.row.goodsWidth == null? '0.00':scope.row.goodsWidth.toFixed(2)}} *
                                            {{scope.row.goodsHeigdth == null? '0.00':scope.row.goodsHeigdth.toFixed(2)}}
                                        </template>
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip label="重量(kg)" width="70">
                                        <template slot-scope="scope">
                                            {{scope.row.goodsWeight == null? '0.00':scope.row.goodsWeight.toFixed(2)}}
                                        </template>
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="remark" label="备注">
                                    </el-table-column>
                                </el-table>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24"
                                style="font-size: 16px;font-weight: bold;margin-top: 5px; ">
                                竞品：
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                                <el-table :data="thisChooses" height="100px" style="width: 99%;" tooltip-effect="dark">
                                    <el-table-column show-overflow-tooltip prop="platformName" label="竞品平台" width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="goodsCompeteName" label="竞品名称">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="goodsCompeteShortName" label="产品简称"
                                        width="100">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="goodsCompeteId" label="竞品ID"
                                        width="150">
                                        <template slot-scope="scope">
                                            <a :href="scope.row.goodsCompeteLink" target="_blank"
                                                v-if="scope.row.goodsCompeteLink!=''&&scope.row.goodsCompeteLink!=null"
                                                class="buttonText">{{scope.row.goodsCompeteId}}</a>
                                            <span
                                                v-if="scope.row.goodsCompeteLink==''||scope.row.goodsCompeteLink==null"
                                                class="buttonText">{{scope.row.goodsCompeteId}}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="lastMonthSaleCount" label="月销量"
                                        width="70">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="lastMonthSaleAmount" label="月销售额"
                                        width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="lastMonthPayerCount" label="支付人数"
                                        width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="skuTotalCost" label="SKU总成本"
                                        width="100">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="expressPrice" label="快递单价" width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="expressAmount" label="快递费(预估)"
                                        width="120">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="grossProfit" label="毛二利润" width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="grossProfitRatio" label="毛二利润率(%)"
                                        width="105">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="otherFee" label="其他费用(预估比例)"
                                        width="150">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="grossProfit3Ratio" label="毛三利润率(%)"
                                        width="105">
                                    </el-table-column>
                                </el-table>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24"
                                style="font-size: 16px;font-weight: bold;margin-top: 5px; ">
                                SKU：
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                                <el-table :data="thisSkus" height="500px" style="width: 99%;" tooltip-effect="dark">
                                    <el-table-column show-overflow-tooltip prop="skuImgUrl" label="SKU图" width="70">
                                        <template slot-scope="scope">
                                            <el-image style="width: 50px; height: 50px" :src="scope.row.skuImgUrl"
                                                :preview-src-list="[scope.row.skuImgUrl]" fit="scale-down">
                                        </template>
                                        </el-image>
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="skuName" label="规格名称">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="skuImgUrl" label="SKU图链接" width="120">
                                        <template slot-scope="scope">
                                            <el-link :href="scope.row.skuImgUrl" target="_blank"><span
                                                    v-html="scope.row.skuImgUrl"></span></el-link>
                                        </template>
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="salePrice" label="售价" width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="monthSales" label="月销量" width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="monthSaleMoney" label="月销售额"
                                        width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="monthSalesRatio" label="月销占比"
                                        width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="goodsCost" label="成本" width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="goodsCostSum" label="SKU总成本"
                                        width="95">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="grossProfit1Ratio" label="毛一利润率(%)"
                                        width="105">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="packCode" label="包装编码" width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="packCostPrice" label="包装成本" width="80">
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="profit" label="利润" width="80">
                                    </el-table-column>
                                </el-table>
                            </el-col>
                        </el-row>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="4" :xl="4" style="text-align:center;">
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24"
                                style="font-size: 16px;font-weight: bold; margin-top: 5px;margin-bottom: 5px;">
                                商品图片
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                                <el-image style="width: 160px; height: 160px" :src="url1" :preview-src-list="srcList1"
                                    lazy fit="scale-down">
                                </el-image>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24"
                                style="font-size: 16px;font-weight: bold; margin-top: 10px;margin-bottom: 5px;">
                                质检报告
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                                <el-image style="width: 160px; height: 160px" :src="url2" :preview-src-list="srcList2"
                                    lazy fit="scale-down">
                                </el-image>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24"
                                style="font-size: 16px;font-weight: bold; margin-top: 10px;margin-bottom: 5px;">
                                专利资质
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                                <el-image style="width: 160px; height: 160px" :src="url3" :preview-src-list="srcList3"
                                    lazy fit="scale-down">
                                </el-image>
                            </el-col>
                            <el-col :span="24">
                                <p v-for="f in patentQualificationPdfFiles">
                                    <el-link type="primary" :href="f.url" target="_blank">{{ f.name }}</el-link>
                                </p>
                            </el-col>
                        </el-row>


                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24"
                                style="font-size: 16px;font-weight: bold; margin-top: 10px;margin-bottom: 5px;">
                                包装图片
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                                <el-image style="width: 160px; height: 160px" :src="url4" :preview-src-list="srcList4"
                                    lazy fit="scale-down">
                                </el-image>
                            </el-col>
                        </el-row>
                    </el-col>
                </el-row>
            </template>
        </el-container>
    </div>
    <script>
        var vm = new Vue({
            el: '#app',
            data: function () {
                return {
                    thisFormData: {},
                    thisDoc: {},
                    thisDocGoods: [],
                    thisChooses: [],
                    thisSkus: [],
                    url1: "",
                    srcList1: [],
                    url2: "",
                    srcList2: [],
                    url3: "",
                    srcList3: [],
                    url4: "",
                    srcList4: [],
                    patentQualificationPdfFiles: [],
                    thisLonding: false,
                    buildDocId: "",
                    buildDocIdList: []
                }
            },
            created() {
            },
            async mounted() {

                let searchURL = window.location.search;
                searchURL = searchURL.substring(1, searchURL.length);
                let targetPageId = searchURL.split("&")[0].split("=")[1];
                if (targetPageId.length>0){
                    var arrIds = targetPageId.split(",");
                    await this.getGoodsCompeteName(arrIds);
                }
            },
            methods: {
                async handleClick(tab, event) {
                    await this.getStyleSheetInfo();
                },
                docGoodsHj(param) {
                    const { columns, data } = param
                    const sums = []
                    columns.forEach((column, index) => {
                        if (index === 0) {
                            sums[index] = '合计';
                            return;
                        }
                        if (data == null)
                            return;
                        const values = data.map(item => Number(item[column.property]));
                        if (column.property == 'estimateStockInCount' || column.property == 'estimateStockInAmount') {
                            sums[index] = values.reduce((prev, curr) => {
                                const value = Number(curr);
                                if (!isNaN(value)) {
                                    return prev + curr;
                                } else {
                                    return prev;
                                }
                            }, 0).toFixed(2);
                        }
                    });
                    return sums
                },
                async getStyleSheetInfo() {
                    var me = this;
                    let searchURL = window.location.search;
                    searchURL = searchURL.substring(1, searchURL.length);
                    // let targetPageId = searchURL.split("&")[0].split("=")[1];
                    let targetPageId = this.buildDocId;
                    let spHisId = 0;
                    if (searchURL.split("&").length >= 2) {
                        spHisId = searchURL.split("&")[1].split("=")[1];
                    }
                    me.thisLonding = true;
                    if (!spHisId) {
                        $.ajax({
                            type: 'GET',
                            async: false,
                            url: `/api/operatemanage/alllink/GetBuildGoodsDocDataForApprovaling`,
                            data: {
                                docId: targetPageId
                            },
                            success: function (result) {
                                me.thisLonding = false;

                                me.thisFormData = result;
                                //建编码
                                me.thisDoc = result.doc;
                                //建编码明细
                                me.thisDocGoods = result.doc?.goodsDtlEntities;
                                if (me.thisDocGoods && me.thisDocGoods.length > 0) {
                                    me.thisDocGoods.forEach(f => {

                                        f.yyGroupName = result.doc?.yyGroupName;
                                        f.isMainSaleName = f.isMainSale ? "是" : "否";
                                        f.goodsImageUrl = me.convertImgUrl(f.goodsImageUrl);
                                    });
                                }
                                //选品
                                me.thisChooses = result.choose?.skuEditDto;
                                if (result.doc.goodsCompeteShortName != "" && result.doc.goodsCompeteShortName != null) {
                                    me.thisChooses[0].goodsCompeteShortName = result.doc.goodsCompeteShortName;
                                }
                                me.thisChooses[0].goodsCompeteLink = me.getLink(me.thisChooses[0].platform, me.thisChooses[0].goodsCompeteId);
                                //选品SKU利润
                                me.thisSkus = result.choose?.skuLiRunList;
                                if (result.choose?.skuLiRunList && result.choose?.skuLiRunList.length > 0) {
                                    result.choose.skuLiRunList.forEach(f => {
                                        f.skuImgUrl = me.convertImgUrl(f.skuImgUrl)
                                    });
                                }

                                me.url1 = me.convertImgUrl(result.doc?.goodsCompeteImgUrl);
                                me.srcList1.push(me.url1);

                                me.url2 = me.convertImgUrl(result.doc?.inspectionReportImgUrl);
                                me.srcList2.push(me.url2);

                                if (result.doc?.patentQualificationImgUrls && result.doc?.patentQualificationImgUrls.length > 2) {
                                    let patentQualificationImgUrls = JSON.parse(result.doc?.patentQualificationImgUrls);
                                    me.url3 = me.convertImgUrl(patentQualificationImgUrls[0].url);
                                    patentQualificationImgUrls.forEach(f => {
                                        me.srcList3.push(me.convertImgUrl(f.url));
                                    });
                                }
                                else {
                                    me.url3 = me.convertImgUrl(null);
                                    me.srcList3.push(me.url3);
                                }

                                if (result.doc?.patentQualificationPdfUrls && result.doc?.patentQualificationPdfUrls.length > 2) {
                                    me.patentQualificationPdfFiles = JSON.parse(result.doc?.patentQualificationPdfUrls);
                                }


                                if (result.doc?.packingImgUrls && result.doc?.packingImgUrls.length > 2) {
                                    let packingImgUrls = JSON.parse(result.doc?.packingImgUrls);
                                    me.url4 = me.convertImgUrl(packingImgUrls[0].url);
                                    packingImgUrls.forEach(f => {
                                        me.srcList4.push(me.convertImgUrl(f.url));
                                    });
                                }
                                else {
                                    me.url4 = me.convertImgUrl(null);
                                    me.srcList4.push(me.url4);
                                }
                            }
                        })
                    }
                    else {
                        $.ajax({
                            type: 'GET',
                            async: false,
                            url: `/api/operatemanage/alllink/GetBuildGoodsDocDataForApprovaling2`,
                            data: {
                                docId: targetPageId,
                                spHisId: spHisId
                            },
                            success: function (result) {
                                me.thisLonding = false;

                                me.thisFormData = result;
                                //建编码
                                me.thisDoc = result.doc;
                                //建编码明细
                                me.thisDocGoods = result.goodsDtlEntities;
                                if (me.thisDocGoods && me.thisDocGoods.length > 0) {
                                    me.thisDocGoods.forEach(f => {

                                        f.yyGroupName = result.doc?.yyGroupName;
                                        f.isMainSaleName = f.isMainSale ? "是" : "否";
                                        f.goodsImageUrl = me.convertImgUrl(f.goodsImageUrl);
                                    });
                                }
                                //选品
                                me.thisChooses = result.choose?.skuEditDto;
                                if (result.doc.goodsCompeteShortName != "" && result.doc.goodsCompeteShortName != null) {
                                    me.thisChooses[0].goodsCompeteShortName = result.doc.goodsCompeteShortName;
                                }
                                me.thisChooses[0].goodsCompeteLink = me.getLink(me.thisChooses[0].platform, me.thisChooses[0].goodsCompeteId);
                                //选品SKU利润
                                me.thisSkus = result.choose?.skuLiRunList;
                                if (result.choose?.skuLiRunList && result.choose?.skuLiRunList.length > 0) {
                                    result.choose.skuLiRunList.forEach(f => {
                                        f.skuImgUrl = me.convertImgUrl(f.skuImgUrl)
                                    });
                                }

                                me.url1 = me.convertImgUrl(result.doc?.goodsCompeteImgUrl);
                                me.srcList1.push(me.url1);

                                me.url2 = me.convertImgUrl(result.doc?.inspectionReportImgUrl);
                                me.srcList2.push(me.url2);

                                if (result.doc?.patentQualificationImgUrls && result.doc?.patentQualificationImgUrls.length > 2) {
                                    let patentQualificationImgUrls = JSON.parse(result.doc?.patentQualificationImgUrls);
                                    me.url3 = me.convertImgUrl(patentQualificationImgUrls[0].url);
                                    patentQualificationImgUrls.forEach(f => {
                                        me.srcList3.push(me.convertImgUrl(f.url));
                                    });
                                }
                                else {
                                    me.url3 = me.convertImgUrl(null);
                                    me.srcList3.push(me.url3);
                                }

                                if (result.doc?.patentQualificationPdfUrls && result.doc?.patentQualificationPdfUrls.length > 2) {
                                    me.patentQualificationPdfFiles = JSON.parse(result.doc?.patentQualificationPdfUrls);
                                }

                                if (result.doc?.packingImgUrls && result.doc?.packingImgUrls.length > 2) {
                                    let packingImgUrls = JSON.parse(result.doc?.packingImgUrls);
                                    me.url4 = me.convertImgUrl(packingImgUrls[0].url);
                                    packingImgUrls.forEach(f => {
                                        me.srcList4.push(me.convertImgUrl(f.url));
                                    });
                                }
                                else {
                                    me.url4 = me.convertImgUrl(null);
                                    me.srcList4.push(me.url4);
                                }
                            }
                        })
                    }

                    if (me.thisDoc.goodsCompeteShortName != "" && me.thisDoc.goodsCompeteShortName != null)
                        document.title = "审批" + me.thisDoc.goodsCompeteShortName;
                    else
                        document.title = "审批" + me.thisChooses[0].goodsCompeteShortName;
                },
                getLink(platform, proCode) {
                    let proBaseUrl = "";
                    switch (platform) {
                        case 1:
                        case '淘系':
                            proBaseUrl = "https://detail.tmall.com/item.htm?id=" + proCode;
                            break;
                        case 2:
                        case '拼多多':
                            proBaseUrl = "https://mobile.yangkeduo.com/goods2.html?goods_id=" + proCode;
                            break;
                        case 8:
                        case '淘工厂':
                            proBaseUrl = "https://detail.tmall.com/item.htm?id=" + proCode;
                            break;
                        case 9:
                        case '淘宝':
                            proBaseUrl = "https://detail.tmall.com/item.htm?id=" + proCode;
                            break;
                        case 7:
                        case '京东':
                            proBaseUrl = "https://item.jd.com/" + proCode + ".html";
                            break;
                    }
                    return proBaseUrl;
                },
                async onGetExcel() {
                    var me = this;
                    let searchURL = window.location.search;
                    searchURL = searchURL.substring(1, searchURL.length);
                    let targetPageId = searchURL.split("&")[0].split("=")[1];
                    me.thisLonding = true;
                    $.ajax({
                        type: 'GET',
                        async: false,
                        url: `/api/operatemanage/alllink/GetBuildGoodsDocExcelForApprovaling`,
                        data: {
                            id: targetPageId
                        },
                        success: function (result) {
                            me.thisLonding = false;
                            window.open(result);
                        }
                    })
                },
                convertImgUrl(url) {
                    if (!url) return "/approvalform/html/images/nonupload.png";
                    url = url.replace('http://192.168.90.12:8004', 'https://nanc.yunhanmy.com:10020')
                    url = url.replace('http://192.168.90.12:8001', 'https://nanc.yunhanmy.com:10010')
                    return url;
                },
                //获取建编码 竞品标题
                async getGoodsCompeteName(arrIds){
                    var me = this;
                    var param = {
                        docIds: arrIds
                    }
                    $.ajax({
                        type: 'POST',
                        async: false,
                        url: `/api/operatemanage/alllink/GetGoodsCompeteNameByBuildDocIds`,
                        dataType: 'JSON',
                        contentType: 'application/json',
                        data: JSON.stringify(param),
                        success: function(result) {
                            me.buildDocIdList = result?.data??[];
                            if (me.buildDocIdList.length > 0){
                                me.buildDocId = me.buildDocIdList[0].buildDocId;
                                me.getStyleSheetInfo();
                            }
                        }
                    });
                }
            }
        });
    </script>
</body>

</html>