import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/purchaseOrderTrack/`

//GetColumns
export const getColumns = (params, config = {}) => {
  return request.post(apiPrefix + 'GetColumns', params, config)
}

//查询数据 PageGetData
export const pageGetData = (params, config = {}) => {
  return request.post(apiPrefix + 'PageGetData', params, config)
}

//数据导出 ExportData
export const exportData = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'ExportData', params, config)
}

//保存配置 PurchaseOrderTrack
export const purchaseOrderTrack = (params, config = {}) => {
  return request.post(apiPrefix + 'PurchaseOrderTrack', params, config)
}

//获取通知 GetSetting
export const getSetting = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSetting', params, config)
}

//保存通知 Setting
export const setting = (params, config = {}) => {
  return request.post(apiPrefix + 'Setting', params, config)
}

//修改单挑数据数量 UpdateCount
export const updateCount = (params, config = {}) => {
  return request.post(apiPrefix + 'UpdateCount', params, config)
}

//修改单挑数据发货方式 UpdateConsignmentType
export const updateConsignmentType = (params, config = {}) => {
  return request.post(apiPrefix + 'UpdateConsignmentType', params, config)
}

//修改单挑数据状态 UpdateTrackStatus
export const updateTrackStatus = (params, config = {}) => {
  return request.post(apiPrefix + 'UpdateTrackStatus', params, config)
}

//修改单挑数据备注 UpdateRemark
export const updateRemark = (params, config = {}) => {
  return request.post(apiPrefix + 'UpdateRemark', params, config)
}

//修改单挑数据备注图片 UpdateRemarkImages
export const updateRemarkImages = (params, config = {}) => {
  return request.post(apiPrefix + 'UpdateRemarkImages', params, config)
}

//修改单挑数据保存配置 GetPurchaseOrderTrack
export const getPurchaseOrderTrack = (params, config = {}) => {
  return request.post(apiPrefix + 'GetPurchaseOrderTrack', params, config)
}

//UpdateConsignmentInfos 修改单条数据 发货方式 电话 单号 图片 备注
export const updateConsignmentInfos = (params, config = {}) => {
  return request.post(apiPrefix + 'UpdateConsignmentInfos', params, config)
}

//MERGE 修改或新增 PurchaseOrderTrack 数据 //MergePurchaseOrderTrack
export const mergePurchaseOrderTrack = (params, config = {}) => {
  return request.post(apiPrefix + 'MergePurchaseOrderTrack', params, config)
}

//分页获取采购单跟进更改日志 PageGetPurchaseOrderTrackChangeLog
export const pageGetPurchaseOrderTrackChangeLog = (params, config = {}) => {
  return request.post(apiPrefix + 'PageGetPurchaseOrderTrackChangeLog', params, config)
}

// QueryPurchaseOrderDetailAsync
export const queryPurchaseOrderDetailAsync = (params, config = {}) => {
  return request.get(apiPrefix + `QueryPurchaseOrderDetailAsync?buyno=${params}`)
}

//采购单跟进同步到聚水潭 PurchaseOrderTrackSyncToJst
export const purchaseOrderTrackSyncToJst = (params, config = {}) => {
  return request.post(apiPrefix + 'PurchaseOrderTrackSyncToJst', params,config)
}

//获取获取物流轨迹
export const getPurchaseOrderLogisticsTrace = (params, config = {}) => {
  return request.get(apiPrefix + `GetPurchaseOrderLogisticsTrace?indexNo=${params}`)
}
