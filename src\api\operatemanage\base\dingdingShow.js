import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API}/deptmentuserinfo/`


///查获取部门
export const getDeptTreeInfo = (params, config = {}) => {
  return request.post(apiPrefix + 'GetDeptTreeInfo', params, config)
}
//查询分页部门用户

export const getPageDeptUser = (params, config = {}) => {
  return request.get(apiPrefix + 'GetPageDeptUser', { params: params, ...config })
}


//查询分页部门考勤
export const getAttendanceOrderNodesList = (params, config = {}) => {
  return request.get(apiPrefix + 'GetAttendanceOrderNodesList', { params: params, ...config })
}

///获取运营部门数据
export const getDeptTypeTreeInfo = (params, config = {}) => {
  return request.get(apiPrefix + 'GetDeptTypeTreeInfo', { params: params, ...config })
}



export const updateDDuserdingId = (params, config = {}) => {
  return request.get(apiPrefix + 'UpdateDDuserdingId', { params: params, ...config })
}

//获取当前用户部门名称
export const getCurUserDepartmentName = (params, config = {}) => {
  return request.get(apiPrefix + 'GetCurUserDepartmentName', { params: params, ...config })
}

//导出
export const exportUserAttendanceList =  (params, config = {responseType: 'blob'})=> {
  return request.post(apiPrefix + 'ExportUserAttendanceList', params, config)
}

