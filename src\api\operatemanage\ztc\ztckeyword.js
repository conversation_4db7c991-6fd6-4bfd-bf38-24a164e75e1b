import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/ztckeyword/`

export const getPageList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPageList', { params, ...config })
  }
  
export const exportZtcKeyWord = (params, config = {responseType: 'blob'}) => {
  return request.get(apiPrefix + `ExportZtcKeyWord`,  {params, ...config})
}

export const importZTCKeyWordAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportZTCKeyWordAsync', params, config)
}

export const getKeyWordListByGroupId = (id, config = {}) => {
  return request.get(apiPrefix + `GetKeyWordListByGroupId?id=${id}`, {}, config)
}

export const getKeyWordListByOperateSpecialId = (id, config = {}) => {
  return request.get(apiPrefix + `GetKeyWordListByOperateSpecialId?id=${id}`, {}, config)
}

export const getKeyWordListByUser2Id = (id, config = {}) => {
  return request.get(apiPrefix + `GetKeyWordListByUser2Id?id=${id}`, {}, config)
}

export const ztcKeyWordAnalysisTableAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'ZtcKeyWordAnalysisTableAsync', params, config)
}

export const getAllProjectList = (params, config = {}) => {
  return request.get(apiPrefix + 'GetAllProjectList', { params, ...config })
}

export const getKeyWordListByProjectId = (id, config = {}) => {
  return request.get(apiPrefix + `GetKeyWordListByProjectId?id=${id}`, {}, config)
}

export const getKeyWordListByProducttId = (id, config = {}) => {
  return request.get(apiPrefix + `GetKeyWordListByProducttId?id=${id}`, {}, config)
}

export const filterKeyWordListAsync = (id, config = {}) => {
  return request.get(apiPrefix + `FilterKeyWordListAsync?keyword=${id}`, {}, config)
}

export const getAllKeyWordListAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'GetAllKeyWordListAsync', { params, ...config })
}


export const getOperateSpcialListByKeyWordAsync = (id, config = {}) => {
  return request.get(apiPrefix + `GetOperateSpcialListByKeyWordAsync?keyword=${id}`, {}, config)
}

export const getUser2ListByKeyWordAsync = (id, config = {}) => {
  return request.get(apiPrefix + `GetUser2ListByKeyWordAsync?keyword=${id}`, {}, config)
}

export const getProjectListByKeyWordAsync = (id, config = {}) => {
  return request.get(apiPrefix + `GetProjectListByKeyWordAsync?keyword=${id}`, {}, config)
}

export const getProductListByKeyWordAsync = (id, config = {}) => {
  return request.get(apiPrefix + `GetProductListByKeyWordAsync?keyword=${id}`, {}, config)
}

export const ztcKeyWordAnalysisWithCost = (params, config = {}) => {
  return request.post(apiPrefix + 'ZtcKeyWordAnalysisWithCost', {...params, ...config})
}

export const exportZtcKeyWordAnalysis = (strJson, config = {responseType: 'blob'}) => {
  return request.get(apiPrefix + `ExportZtcKeyWordAnalysis?jsonParam=${strJson}`,  {...config})
}