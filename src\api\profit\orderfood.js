import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Profit}/orderfood/`

//导出订单
export const exportOrderMenuManageAsync =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportOrderMenuManageAsync',{params: params, ...config})
}

export const exportOrderMenuGroupAsync =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportOrderMenuGroupAsync',{params: params, ...config})
}

export const exportOrderMenuAsync =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportOrderMenuAsync',{params: params, ...config})
}

//导入菜单
export const importBaseMenuAsync = (params,config ={}) =>{
    return request.post(apiPrefix + 'ImportBaseMenuAsync', params, config)
}

export const importBaseSupermarketGoodAsync = (params,config ={}) =>{
    return request.post(apiPrefix + 'ImportBaseSupermarketGoodAsync', params, config)
}

export const getIntegralLogAsync = (params,config ={}) =>{
    return request.get(apiPrefix+'GetIntegralLogAsync', {params: params, ...config})
}

export const getOrderMenuManageAsync = (params,config ={}) =>{
    return request.get(apiPrefix+'GetOrderMenuManageAsync', {params: params, ...config})
}

export const getGwInfo = (params,config ={}) =>{
    return request.get(apiPrefix+'GetGwInfo', {params: params, ...config})
}

export const getIntegralRuleAsync = (params,config ={}) =>{
    return request.get(apiPrefix+'GetIntegralRuleAsync', {params: params, ...config})
}

export const saveIntegralRuleAsync = (params,config ={}) =>{
    return request.post(apiPrefix + 'SaveIntegralRuleAsync', params, config)
}

export const getPageBaseMenuAsync = (params,config ={}) =>{
    return request.get(apiPrefix+'GetPageBaseMenuAsync', {params: params, ...config})
}

export const updateBaseMenuAsync = (params,config ={}) =>{
    return request.post(apiPrefix + 'UpdateBaseMenuAsync', params, config)
}

export const delBaseMenuAsync = (params,config ={}) =>{
    return request.post(apiPrefix + 'DelBaseMenuAsync', params, config)
}

export const getOrderMenuTypeAsync = (params,config ={}) =>{
    return request.get(apiPrefix+'GetOrderMenuTypeAsync', {params: params, ...config})
}
export const saveMenuTypeAsync = (params,config ={}) =>{
    return request.post(apiPrefix + 'SaveMenuTypeAsync', params, config)
}
export const updateIntegralErp = (params,config ={}) =>{
    return request.get(apiPrefix+'UpdateIntegralErp', {params: params, ...config})
}

export const getDeptTreeInfo = (params,config ={}) =>{
    return request.get(apiPrefix+'GetDeptTreeInfo', {params: params, ...config})
}

export const getPageDeptUser = (params,config ={}) =>{
    return request.get(apiPrefix+'GetPageDeptUser', {params: params, ...config})
}

export const getPageOrderSupermaketGoodManageAsync = (params,config ={}) =>{
    return request.get(apiPrefix+'GetPageOrderSupermaketGoodManageAsync', {params: params, ...config})
}

export const getLoginOrderSupermaketGoodAsync = (params,config ={}) =>{
    return request.get(apiPrefix+'GetLoginOrderSupermaketGoodAsync', {params: params, ...config})
}

export const getOrderSupermaketGoodCountAsync = (params,config ={}) =>{
    return request.get(apiPrefix+'GetOrderSupermaketGoodCountAsync', {params: params, ...config})
}

export const updateOrderSupermaketGoodStatusAsync = (params,config ={}) =>{
    return request.get(apiPrefix+'UpdateOrderSupermaketGoodStatusAsync', {params: params, ...config})
}

export const exportOrderSupermaketGoodManageAsync =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportOrderSupermaketGoodManageAsync',{params: params, ...config})
}

export const getPageBaseSupermaketGoodAsync = (params,config ={}) =>{
    return request.get(apiPrefix+'GetPageBaseSupermaketGoodAsync', {params: params, ...config})
}

export const updateBaseSupermaketGoodAsync = (params,config ={}) =>{
    return request.post(apiPrefix + 'UpdateBaseSupermaketGoodAsync', params, config)
}

export const updateBaseSupermaketGoodStockAsync = (params,config ={}) =>{
    return request.post(apiPrefix + 'UpdateBaseSupermaketGoodStockAsync', params, config)
}


export const delBaseSupermaketGoodAsync = (params,config ={}) =>{
    return request.post(apiPrefix + 'DelBaseSupermaketGoodAsync', params, config)
}

export const getGoodTypeAsync = (params,config ={}) =>{
    return request.get(apiPrefix+'GetGoodTypeAsync', {params: params, ...config})
}

export const saveGoodTypeAsync = (params,config ={}) =>{
    return request.post(apiPrefix + 'SaveGoodTypeAsync', params, config)
}
//第二个页面列表
// export const getOrderFoodMenuList = (params,config ={}) =>{
//     return request.get(apiPrefix+'GetOrderFoodMenuList', {params: params, ...config})
// }
export const getOrderFoodMenuList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetOrderFoodMenuList', params, config)
}

//获取基础数据明细
export const getBaseManagerDetailAsync = (params,config ={}) =>{
    return request.get(apiPrefix+'GetBaseManagerDetailAsync?menuId='+params, {...config})
}

//查询 区域、楼层、部门、组 信息
export const getBaseMenuAreaSetInfos = (params,config ={}) =>{
  return request.post(apiPrefix + 'GetBaseMenuAreaSetInfos', params, config)
}

//删除 区域、楼层、部门、组 信息
export const delBaseMenuAreaSetInfo = (params,config ={}) =>{
  return request.post(apiPrefix + 'DelBaseMenuAreaSetInfo', params, config)
}

//新增 区域、楼层、部门、组 信息
export const addOrUpdateBaseMenuAreaSetInfo = (params,config ={}) =>{
  return request.post(apiPrefix + 'AddOrUpdateBaseMenuAreaSetInfo', params, config)
}

//获取供应商
export const getOrderFoodMenuProvier = (params,config ={}) =>{
    return request.get(apiPrefix + 'GetOrderFoodMenuProvier', params, config)
  }

  //获取汇总列表
// export const getOrderFoodMenuStatisList = (params,config ={}) =>{
//     return request.get(apiPrefix + 'GetOrderFoodMenuStatisList?startTime='+params.startTime+'&endTime='+params.endTime+'&gysName='+params.gysName, config)
//   }
  export const getOrderFoodMenuStatisList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetOrderFoodMenuStatisList', params, config)
  }

  //获取区域
  export const getAreaSetList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetAreaSetList', params, config)
  }
  
 //获取汇总列表
// export const getAreaSetList = (params, config = {}) => { return request.get(apiPrefix + 'GetAreaSetList', { params: params, ...config })}

//明细
export const getUserOrderDetailList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetUserOrderDetailList', params, config)
  }
  
//查询菜单公告
export const GetPageBaseMenuNoticeAsync = (params,config ={}) =>{
    return request.get(apiPrefix + 'GetPageBaseMenuNoticeAsync', {params: params, ...config})
}

//获取区域公告信息
export const GetBaseMenuNoticeInfos = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetBaseMenuNoticeInfos', params, config)
}

//新增或编辑区域公告信息
export const AddOrUpdateBaseMenuAreaNoticeSetInfo = (params,config ={}) =>{
    return request.post(apiPrefix + 'AddOrUpdateBaseMenuAreaNoticeSetInfo', params, config)
}

//删除区域公告信息
export const DelBaseMenuAreaNoticeSetInfo = (params,config ={}) =>{
    return request.post(apiPrefix + 'DelBaseMenuAreaNoticeSetInfo', params, config)
}

//获取查看明细人数
export const GetBaseMenuNoticeViewCount = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetBaseMenuNoticeViewCount', params, config)
}