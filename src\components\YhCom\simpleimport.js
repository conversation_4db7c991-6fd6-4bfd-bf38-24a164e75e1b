import Vue from "vue"; // 引入 Vue 是因为要用到 Vue.extend() 这个方法
import simpleimport from "./simpleimport.vue"; // 引入组件

let simpleimportformConstructor = Vue.extend(simpleimport);  //创建构造器
let instance;


const showSimpleimport = function (options = {  
    action,
    accept:'',   
    title:'导入',  
    tip:'只能上传xlxs',
    autoClose:true,
    callOk,
    callCancel            
}) {

    let opt={
        ...{title:'导入',autoClose:true,tip:'' },  
        ...options
    };      

    let data={
        dialogVisibleSyj:true,     
        title:opt.title,
        accept:opt.accept,
        autoClose:opt.autoClose,
        tip:opt.tip, 
        uploading:false   
    }

    let methods={        };

    if(options.callOk && typeof options.callOk ==="function"){
        methods.callOk=options.callOk;
    }

    if(options.callCancel && typeof options.callCancel ==="function"){
        methods.callCancel=options.callCancel;
    }

    if(options.action && typeof options.action ==="function"){
        methods.upAction=options.action;
    }else{
        alert('必须输入上传action');
        return;
    }

    instance = new simpleimportformConstructor ({      
       data() {     
            return {...data};
        },
        methods:methods,
    }).$mount(); // 创建实例
    //debugger;
   // document.body.appendChild(instance.$el); // 将元素挂载到 body 下
};


export default showSimpleimport;