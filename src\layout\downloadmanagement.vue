<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-button type="primary" @click="onSearch">刷新</el-button>
    </template>
    <template>
      <ces-table :id="'downloadmanagement202408041348'" ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
        style="height: 300px;" :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
        :isSelectColumn="false" :loading="listLoading">
        <template slot="right">
          <vxe-column>
            <template #header>
              <el-tooltip class="item" effect="dark" content="可在浏览器下载栏查看到下载进度" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
              <span>无文件名下载</span>
            </template>
            <template #default="{ row, $index }">
              <div style="display: flex; justify-content: center; align-items: center;">
                <el-button type="text" v-show="row.fileUrl" @click="onNoFilenameDownload(row.fileUrl, row.title)">下载</el-button>
              </div>
            </template>
          </vxe-column>
        </template>
      </ces-table>
    </template>
    <template #footer>
      <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="viewtotal"
        @page-change="detailPagechange" @size-change="detailSizechange" style="margin-top: 10px;" />
    </template>
  </my-container>
</template>

<script>
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import { queryPageTaskLog } from '@/api/admin/exportLog';
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { downloadLink } from "@/utils/tools";

const tableCols = [
  { istrue: true, prop: 'title', label: '标题', width: '230', sortable: 'custom' },
  { istrue: true, prop: 'message', label: '状态', width: '230', sortable: 'custom' },
  { istrue: true, prop: 'createdTime', label: '开始时间', width: '230', sortable: 'custom', formatter: (row) => row.createdTime === null ? '' : formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss') },
  { istrue: true, prop: 'modifiedTime', label: '结束时间', width: '230', sortable: 'custom', formatter: (row) => row.modifiedTime === null ? '' : formatTime(row.modifiedTime, 'YYYY-MM-DD HH:mm:ss') },
  {
    istrue: true, prop: 'fileUrl', label: '下载地址', type: 'button', btnList: [
      {
        label: '下载',
        handle: (that, row) => {
          that.downloadLink(row.fileUrl, row.title);
        },
        ishide: (that, row) => !row.fileUrl
      },
    ]
  },
];

export default {
  name: 'downloadmanagement',
  components: { cesTable, MyContainer },
  data() {
    return {
      that: this,
      summaryarry: {},
      list: [],
      listLoading: false,
      tableCols: tableCols,
      viewtotal: 0,
      pageLoading: false,
      filter: {
        title: '',
        currentPage: 1,//当前页
        pageSize: 50,//每页条数
        orderBy: ' ',//排序字段
        isAsc: false,//是否降序
      },
    };
  },

  async mounted() {
    // await this.getlist();
  },

  methods: {
    //下载
    // 使用window.open()方法进行下载
    onNoFilenameDownload(url, name) {
      window.open(url);
    },
    // 使用downloadLink方法进行下载
    downloadLink: downloadLink,
    onSearch(){
      this.$refs.pager.setPage(1)
      this.getlist();
    },
    //页面数量改变
    detailSizechange(val) {
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.getlist();
    },
    //当前页改变
    detailPagechange(val) {
      this.filter.currentPage = val;
      this.getlist();
    },
    //查询
    async getlist(userid) {
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      const params = { ...this.filter, userid: userid };
      this.listLoading = true;
      const { data } = await queryPageTaskLog(params)
      this.listLoading = false;
      this.list = data.data.list
      this.viewtotal = data.data.total
    },
    //排序查询
    async sortchange(column) {
      if (!column.order) {
        this.filter.orderBy = 'createdTime';
        this.filter.isAsc = false;
      } else {
        this.filter.orderBy = column.prop
        this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false
        this.getlist();
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
