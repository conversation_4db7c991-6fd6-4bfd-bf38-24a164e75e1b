<!DOCTYPE html>
<html>
 <head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="referrer" content="no-referrer" />
  <!-- elment样式 -->
  <link rel="stylesheet" href="/approvalform/html/api/elment.css">
  <!-- vue -->
  <script src="/approvalform/html/api/vue.min.js"></script>
  <!-- elment脚本 -->
  <script src="/approvalform/html/api/elment.js"></script>
  <!-- jquery -->
  <script src="/approvalform/html/api/jquery.min.js"></script>

  <script src="/approvalform/html/api/html2canvas.js"></script>
  
  <title>登录</title>
  <style type="text/css"> 
      .linebreak{
      overflow: hidden;/*超出部分隐藏*/
      
      text-overflow:ellipsis;/* 超出部分显示省略号 */
      white-space: normal;/*规定段落中的文本不进行换行 */
      width: 100%;
  }
  </style>
</head>
<body>
<div id="login_container" style="color: red;"></div>
</body>
<script>
    init();
    async function init() {
        let appid = "";
        var REDIRECT_URI="";
        // var res=await window.parent.getdingdingurl();
        // var info=res.split(",");

        $.ajax({
            type: 'GET',
            async: false,
            url: `/api/admin/auth/GetCompany`,
            data: {},
            success: function (result) {
                if (result.success) {
                    appid = result.data[0].appId;
                }
            }
        })

        $.ajax({
            type: 'GET',
            async: false,
            url: `/api/admin/auth/GetDDRedirectUrl`,
            data: {},
            success: function (result) {
                if (result.success) {
                    var info=result.data.split(",");
                    if(info[0]=='http://**************:8000'){
                        REDIRECT_URI="https://www.yunhanmy.com:10002"+"/approvalform/html/dddirect.html";
                    }else if(info[0]=='http://**************:8001'){
                        REDIRECT_URI="https://www.yunhanmy.com:10001"+"/approvalform/html/dddirect.html";
                    }
                    // REDIRECT_URI=info[0]+"/approvalform/html/dddirect.html";
                }
            }
        })
        if(REDIRECT_URI==""||appid==""){
            return
        }

        let url = encodeURIComponent(REDIRECT_URI);
        let goto = encodeURIComponent(`https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=${appid}&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=${url}`)


        let a = {
            id:"login_container",//这里需要你在自己的页面定义一个HTML标签并设置id，例如<div id="login_container"></div>或<span id="login_container"></span>
            goto: goto,
            style: "border:none;background-color:#FFFFFF;",
            width : "365",//官方参数 365
            height: "400"//官方参数 400
        };
        var e, c = document.createElement("iframe"),
            d = "https://login.dingtalk.com/login/qrcode.htm?goto=" + a.goto ;
        d += a.style ? "&style=" + encodeURIComponent(a.style) : "",
            d += a.href ? "&href=" + a.href : "",
            c.src = d,
            c.frameBorder = "0",
            c.allowTransparency = "true",
            c.scrolling = "no",
            c.width =  a.width ? a.width + 'px' : "50px",
            c.height = a.height ? a.height + 'px' : "50px",
            e = document.getElementById(a.id),
            e.innerHTML = "",
            e.appendChild(c)

        let handleMessage = (event) =>{
            
            let origin = event.origin;
            if(origin == "https://login.dingtalk.com" ) {
              let loginTmpCode = event.data; 
              window.location.href = `https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=${appid}&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=${url}&loginTmpCode=${loginTmpCode}`
             }
        };
        if (typeof window.addEventListener != 'undefined') {
            window.addEventListener('message', handleMessage, false);
        } else if (typeof window.attachEvent != 'undefined') {
            window.attachEvent('onmessage', handleMessage);
        }
        setTimeout(()=>{
            var demo = document.getElementById('login_container').children[0]
                .contentWindow
                .document
            // .getElementById('xoc')
            // .style
            // .color = 'green'
        },1000)

    }

</script>
</html>
