import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/pddSafeInventorys/`

//获取未审核数据
export const pagePddSafeInventoryAsync  = (params, config = {}) => {  return request.post(apiPrefix + 'PagePddSafeInventoryAsync', params, config) } 

//获取通过的数据
export const pagePddSafeInventoryPassLogAsync  = (params, config = {}) => {  return request.post(apiPrefix + 'PagePddSafeInventoryPassLogAsync', params,config ) } 

//获取不通过的数据
export const pagePddSafeInventoryNoPassLogAsync  = (params, config = {}) => {  return request.post(apiPrefix + 'PagePddSafeInventoryNoPassLogAsync', params,config ) } 

//拼多多预售审核前校验
export const validateGoodsCodesHasCombinedCode = (params, config = {}) => {  return request.post(apiPrefix + 'ValidateGoodsCodesHasCombinedCode', params, config)}

//拼多多预售单个审核
export const pddSafeInventoryAudit = (params, config = {}) => {  return request.post(apiPrefix + 'PddSafeInventoryAudit', params, config)}

//拼多多预售批量审核
export const pddSafeInventoryBatchAudit = (params, config = {}) => {  return request.post(apiPrefix + 'PddSafeInventoryBatchAudit', params, config)}

//获取异常明细
export const pagePddSafeInventoryPurchaseDetailAsync  = (params, config = {}) => {  return request.get(apiPrefix + 'PagePddSafeInventoryPurchaseDetailAsync', { params: params, ...config }) } 
                                          