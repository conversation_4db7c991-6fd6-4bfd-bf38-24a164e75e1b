<template>
    <div>
    <quill-editor v-model="txt" ref="myQuillEditor" :options="editorOption" @change="onEditorChange($event)"
        :disabled="disabled">
    </quill-editor>
        <input type="file" accept="image/*" @change="change" id="imageUpload" style="display:none;" />
    </div>
</template>

<script>

import { quillEditor, Quill } from 'vue-quill-editor'
import { container, ImageExtend, QuillWatch } from 'quill-image-extend-module'
import Compressor from 'compressorjs';
Quill.register('modules/ImageExtend', ImageExtend);

let  fileServer = process.env.VUE_APP_DOMAIN_NAME


export default {
    name: 'YhQuillEditor',
    components: { quillEditor },
    props: {
        value: {
            type: String,
            default() {
                return ""
            }
        },
        maxCharacters: {
            type: Number,
            default() {
                return 300
            }
        }
    },
    watch: {
        value(newVal, oldVal) {
            if (this.txt != newVal) {
                this.txt = newVal;
            }
        }
    },
    data() {

        return {
            txt: '',
            disabled: false,
            editorOption: {
                modules: {
                    ImageExtend: {
                        loading: true,
                        name: 'file',
                        action:  fileServer+ '/api/uploadnew/file/UploadCommonFileAsync',
                        response: (res) => {
                            console.log(res);
                            return res.data.url
                        }
                    },
                    toolbar: {
                        container: container,
                        handlers: {
                            'image': function (v) {
                                if(v){
                                     document.querySelector("#imageUpload").click();
                                }else{
                                    QuillWatch.emit(this.quill.id)
                                }
                            }
                        }
                    }
                }
            },           
        }
    },
    computed: {

    },
    mounted() { 
        
        //  自定义粘贴图片功能
        let quill = this.$refs.myQuillEditor.quill
        if (!this.disableFlag) {
            container[0] = ['image']
        }
        this.$forceUpdate()
        quill.root.addEventListener('paste', evt => {         
            if (evt.clipboardData && evt.clipboardData.files && evt.clipboardData.files.length) {
             
                evt.preventDefault();

                [].forEach.call(evt.clipboardData.files,async file => {
                    if (!file.type.match(/^image*/)) {
                        return
                    }
                    var quality = 10
                    if(file.type.startsWith('image/')&&file.size > 200*1024){
                        for(quality = 10;quality>=0;quality--){
                            file =await  this.compressImage(file,quality/10)
                            if(file.size<=200*1024){
                                break;
                            }
                        }
                        if(file.size > 200 * 1024){
                            this.$message.info('上传的图片过大，请压缩后上传')
                            return;
                        }
                    }
                    this.uploadToServer(file, (res) => {                      
                        
                        this.uploadAttachment(res, file, null);

                        this.$nextTick(() => {
                            setTimeout(() => {
                                quill.setSelection(quill.selection.savedRange.index + 1);
                            }, 300);
                        });


                    })
                })
            }
        }, false)


    },
    methods: {
        async change(e){
            var file = e.target.files[0]
           
           var quality = 10
            if(file.type.startsWith('image/')&&file.size > 200*1024){
                for(quality = 10;quality>=0;quality--){
                    file =await  this.compressImage(file,quality/10)
                    if(file.size<=200*1024){
                        break;
                    }
                }
                if(file.size > 200 * 1024){
                    this.$message.info('上传的图片过大，请压缩后上传')
                    return;
                }
            }
           this.uploadToServer(file, (res) => {                      
                    let quill = this.$refs.myQuillEditor.quill 
                    //光标位置
                    let length = quill.getSelection().index; 
                    // 插入图片 图片地址是拼接的 
                    quill.insertEmbed(length, "image", res.data.url);  
                    // 调整光标内容后面
                    quill.setSelection(length + 1); 
            })
        },
        // 将图像进行压缩
        compressImage(file,quality) {
            return new Promise((resolve, reject) => {
                new Compressor(file, {
                    quality: quality,
                    convertTypes:['image/png','image/webp','image/svg+xml','image/apng','image/avif','image/gif'],
                    convertSize:204800,
                    success(result) {
                        resolve(result);
                    },
                    error(err) {
                        reject(err)
                    },
                })
            })
        },
        onEditorChange({ quill, html, text }) {
            const regex = /<p>(.*?)<\/p>/;
            const match = regex.exec(html);
            const content = match ? match[1] : '';
            if (text.length - 1 > this.maxCharacters) {
                const truncatedText = text.slice(0, this.maxCharacters);
                this.txt = truncatedText;
                this.disabled = true;
                quill.disable();
                setTimeout(() => {
                    this.disabled = false;
                    quill.enable();
                }, 500)
                // quill.setText(truncatedText);
                this.$message.error('输入长度达到最大限制');
                return;
            } else {
                //this.txt = text;
            }

            this.$emit("update:value", html);
        },
        uploadToServer(file, callback) {           

            var xhr = new XMLHttpRequest()
            var formData = new FormData()
            formData.append('file', file,file.name)
            xhr.open('post', fileServer+'/api/uploadnew/file/UploadCommonFileAsync')
            xhr.withCredentials = true
            xhr.responseType = 'json'
            xhr.send(formData)
            xhr.onreadystatechange = () => {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    //debugger;
                    console.log(xhr.response)
                    this.$emit("imgget", xhr.response);
                    callback(xhr.response)
                }
            }
        },
        uploadAttachment(response, file, fileList) {
            // 保存文件信息
            if (response.success) {
                // 获取富文本组件实例
                let quill = this.$refs.myQuillEditor.quill

                // 获取光标所在位置
                let slct = quill.getSelection();
                console.log('slct:' + (slct ? slct : 'null'));
                let length = slct?.index ?? quill.selection.savedRange?.index ?? quill.getLength();

                // 插入图片  res.info为服务器返回的图片地址
                quill.insertEmbed(length, 'image', response.data.url)


                let fileType = null
                if (file.raw && file.raw.type) {
                    fileType = file.raw.type
                } else {
                    fileType = file.type
                }

            } else if (response.msg) {
                this.$message.error(response.msg)
            }
            // 清空文件列表
            this.uploadList = []
        },
    }
}
</script>
