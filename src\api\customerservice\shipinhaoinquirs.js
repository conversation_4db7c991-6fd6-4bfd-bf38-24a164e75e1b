import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/SPHInquirs/`

// 获取分组列表
export const getGroupNameList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGroupNameList',  params, config )
}

// 获取分组列表
export const getGroupSnameList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGroupSnameList',  params, config )
}

/*
分组管理
*/

// 查询分组管理列表
export const getGroupList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGroupList',  params, config )
}

// 添加分组数据
export const addGroup = (params, config = {}) => {
    return request.post(apiPrefix + 'AddGroup',  params, config )
}

// 导入视频号分组数据
export const importSPHGroupAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportSPHGroupAsync',  params, config )
}

// 批量离组
export const batchUpdateLeaveDate = (params, config = {}) => {
    return request.post(apiPrefix + 'BatchUpdateLeaveDate',  params, config )
}

// 查询分组修改日志
export const getGroupLogList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGroupLogList',  params, config )
}

// 编辑分组数据
export const updateGroup = (params, config = {}) => {
    return request.post(apiPrefix + 'UpdateGroup',  params, config )
}

// 删除分组数据
export const deleteGroup = (params, config = {}) => {
    return request.post(apiPrefix + 'DeleteGroup',  params, config )
}

// 批次删除分组数据
export const batchDeleteGroup = (params, config = {}) => {
    return request.post(apiPrefix + 'BatchDeleteGroup',  params, config )
}


/*
咨询数据导入
*/

// 导入视频号咨询数据-售前
export const importSPHInquirsAsyncSQ = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportSPHInquirsAsyncSQ',  params, config )
}

// 导入视频号咨询数据-售后
export const importSPHInquirsAsyncSH = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportSPHInquirsAsyncSH',  params, config )
}

// 查询分组修改日志
export const getInquirsList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetInquirsList',  params, config )
}

// 删除分组数据
export const deleteInquireAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'DeleteInquireAsync',  params, config )
}

// 批次删除分组数据
export const batchDeleteInquirsAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'BatchDeleteInquirsAsync',  params, config )
}


// 查询客服代班数据
export const getSPHSubstituteAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSPHSubstituteAsync',  params, config )
}

// 导出客服代班数据
export const exportSPHSubstituteAsync = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportSPHSubstituteAsync',  params, config )
}

// 新增客服代班数据
export const addSPHSubstitute = (params, config = {}) => {
    return request.post(apiPrefix + 'AddSPHSubstitute',  params, config )
}

// 删除客服代班数据
export const deleteSPHSubstitute = (params, config = {}) => {
    return request.post(apiPrefix + 'DeleteSPHSubstitute',  params, config )
}



/*
店效率统计
*/
// 查询店效率统计
export const getSPHShopInquirsAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSPHShopInquirsAsync',  params, config )
}

// 导出店效率统计
export const exportSPHShopInquirsAsync = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportSPHShopInquirsAsync',  params, config )
}

// 店效率统计趋势图取数
export const getSPHShopInquirsMap = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSPHShopInquirsMap',  params, config )
}

/*
未匹配咨询数据
*/

// 查询未匹配咨询数据
export const getSPHInquirsNoExistsList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSPHInquirsNoExistsList',  params, config )
}

// 查询组效率统计
export const getSPHGroupInquirsAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSPHGroupInquirsAsync',  params, config )
}

// 导出组效率统计
export const exportSPHGroupInquirsAsync = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportSPHGroupInquirsAsync',  params, config )
}

// 组效率统计趋势图取数
export const getSPHGroupInquirsMap = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSPHGroupInquirsMap',  params, config )
}

// 查询个人效率统计
export const getSPHPersonalInquirsAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSPHPersonalInquirsAsync',  params, config )
}

// 导出个人效率统计
export const exportSPHPersonalInquirsAsync = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportSPHPersonalInquirsAsync',  params, config )
}

// 个人效率统计趋势图取数
export const getSPHPersonalInquirsMap = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSPHPersonalInquirsMap',  params, config )
}

// 查询指定个人咨询数据
export const getSPHPersonalInquirsList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSPHPersonalInquirsList',  params, config )
}
