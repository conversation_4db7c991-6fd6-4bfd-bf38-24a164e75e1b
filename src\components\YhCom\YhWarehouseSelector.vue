<template>
    <div>
        <el-popover placement="bottom-start" width="200" trigger="hover">
            <div v-if="checkedWarehouses.length > 0">
                <el-scrollbar>
                    <div v-for="item in checkedWarehouses" :key="item" style="line-height: 25px;"> {{ item }}</div>
                </el-scrollbar>
            </div>
            <div v-else>暂未选择仓库</div>

            <el-button slot="reference" @click="onSelect()" height="30" style="width: 110px;">
                {{ checkedWarehouses.length == 0 ? "请选择仓库" : checkedWarehouses[0].substr(0, 5) + "+" +
                    checkedWarehouses.length }}
            </el-button>
        </el-popover>

        <el-dialog v-if="showCheckbox" title="仓库选择列表(多选)" :visible.sync="showWarehouseList" @close="closeDialog"
            v-dialogDrag :append-to-body="true" style="height:800px;">
            <el-checkbox v-model="checkAll" @change="handleCheckAllChange" style="margin-bottom: 10px;">全选</el-checkbox>
            <el-checkbox v-model="checkInvert" @change="handleCheckInvertChange"
                style="margin-bottom: 10px;">反选</el-checkbox>
            <el-checkbox v-model="checkForwarding" @change="handleCheckForwardingWarehouseChange"
                style="margin-bottom: 10px;">所有代发仓</el-checkbox>
            <el-checkbox v-model="checkNonIssuing" @change="hadnleCheckNonIssuingWarehousechange"
                style="margin-bottom: 10px;">所有非代发仓</el-checkbox>
            <el-checkbox v-model="excludeValue" @change="hadnleCheckNonIssuingWarehousechange"
                style="margin-bottom: 10px;">所有非代发仓(排除云仓)</el-checkbox>
            <el-input v-model="keywords" style="margin-left:30px;width:200px;" placeholder="请输入关键字进行检索"
                @keyup.enter.native="getWarehouse(keywords)" clearable>
            </el-input>
            <el-button @click="btnOn('义乌')" style="margin-left: 10px;">义乌</el-button>
            <el-button @click="btnOn('圆通')">圆通</el-button>
            <el-button @click="btnOn('水果')">水果</el-button>
            <el-button @click="btnOn('邮政')">邮政</el-button>

            <div style="height:450px;">
                <el-checkbox-group v-model="checkedWarehouses" @change="handleCheckWarehouseChange">
                    <el-row><!-- :justify="'start'" -->
                        <el-col v-for="item in listObject" :key="item.value" :span="6">
                            <el-checkbox :key="item.value" :label="item.label" :value="item.value">
                                <span
                                    :style="keywords && item.label.indexOf(keywords) >= 0 ? 'background-color:yellow' : ''">
                                    {{ item.label }}
                                </span>
                            </el-checkbox>
                        </el-col>
                    </el-row>
                </el-checkbox-group>
            </div>

            <span style="position: absolute; bottom: 30px; right: 30px;">
                <el-button @click="GetCommonWarehouseAsync">选取常用仓库</el-button>
                <el-button @click="SetCommonWarehouseAsync">设置为常用仓库</el-button>
                <el-button @click="clearValue">清空</el-button>
                <el-button @click="cancle">取消</el-button>
                <el-button type="primary" @click="confirm">确定</el-button>
            </span>
        </el-dialog>

        <el-dialog v-else title="仓库选择列表(单选)" :visible.sync="showWarehouseList" @close="closeDialog" v-dialogDrag
            :append-to-body="true" style="height:800px;">
            <div style="height:450px;">
                <el-radio-group v-model="radioCheckedWarehouse">
                    <el-row><!-- :justify="'start'" -->
                        <el-col v-for="item in listObject" :key="item.value" :span="6">
                            <el-radio :key="item.id" :label="item.label" @change="handleRadioChange(item.label)"
                                style="margin-top: 5px;">{{ item.label }}
                            </el-radio>
                        </el-col>
                    </el-row>
                </el-radio-group>
            </div>

            <span style="position: absolute; bottom: 30px; right: 30px;">
                <el-button @click="clearValue">清空</el-button>
                <el-button @click="cancle">取消</el-button>
                <el-button type="primary" @click="confirm">确定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { getAllWarehouse } from "@/api/inventory/warehouse";
import { GetVxeTableColumnCacheAsync, SetVxeTableColumnCacheAsync } from '@/api/admin/business'

export default {
    name: 'YhWarehouseSelector',
    props: {
        values: {
            type: Array,
            default() {
                return [];//默认选择仓库
            }
        },
        names: {
            type: Array,
            default() {
                return [];//默认选择仓库ID
            }
        },
        checkboxOrRadio: {
            type: String,
            default() {
                return 'checkbox';//单选框或多选框
            }
        },
        addAllWarehouseConcept: {
            type: Boolean,
            default() {
                return false;//默认不添加全仓概念
            }
        },
    },
    watch: {
        checkboxOrRadio: {
            handler(newval, oldval) {
                this.showCheckbox = newval.includes("checkbox");
            },
            immediate: true, // 立即执行一次
        },
    },
    data() {
        return {
            keyId: 'warehouseCurrnetIdCache2407281833',
            keywords: "",//检索关键字
            warehouselist: [],//仓库列表:value-label-所有仓库对象(未去重)
            listName: [],//仓库去重需要:label-所有仓库名
            listID: [],//仓库去重需要:value-所有仓库ID
            listObject: [],//仓库去重需要:value-label-所有仓库对象
            listForwardingWarehouse: [],//代发仓
            listNonIssuingWarehouse: [],//非代发仓
            excludeYcWarehouse: [],//非代发仓排除云仓仓库
            checkedWarehouses: [],//已选仓库名(多选)
            checkedWarehouseIDs: [],//已选仓库ID(多选)
            radioCheckedWarehouse: '',//已选仓库名(单选)

            showWarehouseList: false,//仓库列表弹窗出现状态
            checkAll: false,//全选状态
            // isIndeterminate: false,//不确定状态
            checkInvert: false,//反选状态
            checkForwarding: false,//代发仓
            checkNonIssuing: false,//非代发仓
            excludeValue: false,//非代发仓排除云仓仓库
            // // placeholder: "",
            // props: ['names', 'values'],
            props: { names: 'names', values: 'values', checkboxOrRadio: 'chexboxOrRadio', addAllWarehouseConcept: 'addAllWarehouseConcept' },
            showCheckbox: true,//多选框或单选框

            remoteCfg: [],//常用仓库缓存
        };
    },
    mounted() {
        this.init();
    },
    methods: {
        //初始化
        async init() {
            const cfg = await GetVxeTableColumnCacheAsync({ tableId: this.keyId });
            if (cfg && cfg.success && cfg.data) {
                this.remoteCfg = JSON.parse(cfg.data);
            }
            // if (cfg.data === "") {
            //     this.$message.success("未设置常用仓库!");
            // } else if(cfg && cfg.success && cfg.data){
            //     this.$message.success("获取常用仓库成功!");
            //     this.remoteCfg = JSON.parse(cfg.data);
            // } else if (!cfg.success) {
            //     this.$message.error("获取常用仓库失败!");
            // }

            const { data } = await getAllWarehouse();
            this.warehouselist = data.map(item => { return { value: item.wms_co_id, label: item.name }; });

            //新增仓库概念---label:全仓, value:-1
            if (this.addAllWarehouseConcept && !this.listName.includes("全仓")) {
                this.listObject.push({ label: "全仓", value: -1 });
                this.listName.push("全仓");
                this.listID.push(-1);
            }

            //仓库去重
            for (let i = 0; i < this.warehouselist.length; i++) {
                if (!this.listName.includes(this.warehouselist[i].label)) {
                    this.listName.push(this.warehouselist[i].label);
                    this.listID.push(this.warehouselist[i].value);
                    this.listObject.push({ label: this.warehouselist[i].label, value: this.warehouselist[i].value });
                }
            }
            //只有先去重后，再查找代发仓、非代发仓
            for (let i = 0; i < this.listName.length; i++) {
                if (this.listName[i].includes("代发")) {
                    this.listForwardingWarehouse.push(this.listName[i]);
                } else {
                    this.listNonIssuingWarehouse.push(this.listName[i]);
                }
            }

            //初始化选择仓库
            if (this.listObject.filter(item => this.names.includes(item.label)).length > 0 && this.names.length > 0) {
                if (this.showCheckbox) {//多选
                    this.checkedWarehouses = this.names;//初始设定仓库
                    this.checkedWarehouseIDs = this.values;//初始设定仓库ID
                    this.$emit('onChange', [this.checkedWarehouseIDs, this.checkedWarehouses, this.listObject.filter(item => this.checkedWarehouses.includes(item.label))]);//传递组件数据
                    this.checkAll = this.checkedWarehouses.length === this.listObject.length;//全选状态
                    // this.isIndeterminate = this.checkedWarehouses.length > 0 && this.checkedWarehouses.length < this.listObject.length;//部分选中状态
                    this.checkForwarding = this.listForwardingWarehouse.length === this.checkedWarehouses.filter(item => this.listForwardingWarehouse.includes(item)).length;//代发仓全选状态
                    this.checkNonIssuing = this.listNonIssuingWarehouse.length === this.checkedWarehouses.filter(item => this.listNonIssuingWarehouse.includes(item)).length;//非代发仓全选状态
                } else {//单选
                    this.radioCheckedWarehouse = this.listObject[0].label;//初始设定仓库
                    this.checkedWarehouses.push(this.listObject[0].label);//初始设定仓库名
                    this.checkedWarehouseIDs.push(this.listObject[0].value);//初始设定仓ID
                    this.$emit('onChange', [this.checkedWarehouseIDs, this.checkedWarehouses, this.listObject.filter(item => this.checkedWarehouses.includes(item.label))]);//传递组件数据
                }
            } else if (this.names.length != 0) {
                this.$message.error("默认初始选择的仓库不存在！");
                console.log("默认初始选择的仓库不存在！");
            }
            const regex = /【昀晗-[a-zA-Z]+】/;
            this.excludeYcWarehouse = this.listNonIssuingWarehouse.filter(item => !regex.test(item));
        },
        //选择仓库列表
        onSelect() {
            this.showWarehouseList = true;

            if (this.showCheckbox) {
                let checkedCount = this.checkedWarehouses.length;
                this.checkAll = checkedCount === this.listObject.length ? true : false;//全选状态
                // this.isIndeterminate = checkedCount > 0 && checkedCount < this.listObject.length;//获取长度方式---部分选中状态
                this.checkForwarding = (this.listForwardingWarehouse.length === this.checkedWarehouses.filter(item => this.listForwardingWarehouse.includes(item)).length);//代发仓全选状态
                this.checkNonIssuing = (this.listNonIssuingWarehouse.length === this.checkedWarehouses.filter(item => this.listNonIssuingWarehouse.includes(item)).length);//非代发仓全选状态
            }
        },
        //关闭弹窗
        closeDialog() {
            this.keywords = '';
            this.showWarehouseList = false;
            this.cancle();
        },
        //清空
        clearValue() {
            // this.showWarehouseList = false;//弹窗关闭状态
            this.keywords = '';
            this.checkedWarehouses = [];
            this.checkedWarehouseIDs = [];
            this.radioCheckedWarehouse = '';
            this.checkAll = false;//全选状态
            this.isIndeterminate = false;//不确定状态
            this.checkInvert = false;//反选状态
            this.checkForwarding = false;//代发仓状态
            this.checkNonIssuing = false;//非代发仓状态
            this.excludeValue = false;//非代发仓排除云仓仓库全选状态
            this.$emit('onChange', [this.checkedWarehouseIDs, this.checkedWarehouses, this.listObject.filter(item => this.checkedWarehouses.includes(item.label))]);//传递组件数据
        },
        //取消---返回上次选择的数据
        cancle() {
            this.keywords = '';
            this.showWarehouseList = false;//弹窗关闭状态
            this.checkedWarehouses = [];
            this.checkAll = false;//全选状态
            // this.isIndeterminate = false;//不确定状态
            this.checkInvert = false;//反选状态
            this.checkForwarding = false;//代发仓状态
            this.checkNonIssuing = false;//非代发仓状态

            if (this.showCheckbox) {//多选
                for (var i = 0; i < this.listID.length; i++) {
                    if (this.checkedWarehouseIDs.includes(this.listID[i])) {
                        this.checkedWarehouses.push(this.listName[i]);
                    }
                }
            }
            else {//单选
                for (var i = 0; i < this.listID.length; i++) {
                    if (this.checkedWarehouseIDs.includes(this.listID[i])) {
                        this.checkedWarehouses.push(this.listName[i]);
                        this.radioCheckedWarehouse = this.listName[i];
                        break;
                    }
                }
            }
            this.$emit('onChange', [this.checkedWarehouseIDs, this.checkedWarehouses, this.listObject.filter(item => this.checkedWarehouses.includes(item.label))]);//传递组件数据
        },
        //确定
        confirm() {
            this.keywords = '';
            this.showWarehouseList = false;//弹窗关闭状态
            this.checkedWarehouseIDs = [];//点击提交时才清空上次复选框选择状态
            for (var i = 0; i < this.listName.length; i++) {
                if (this.checkedWarehouses.includes(this.listName[i])) {
                    this.checkedWarehouseIDs.push(this.listID[i]);
                }
            }
            this.$emit('onChange', [this.checkedWarehouseIDs, this.checkedWarehouses, this.listObject.filter(item => this.checkedWarehouses.includes(item.label))]);//传递组件数据
        },
        //全选
        handleCheckAllChange(value) {
            this.checkedWarehouses = value ? this.listName : [];
            // this.isIndeterminate = false;//部分选中状态
            this.checkInvert = false;//反选状态
            this.checkForwarding = this.listForwardingWarehouse.length === this.checkedWarehouses.filter(item => this.listForwardingWarehouse.includes(item)).length;//代发仓全选状态
            this.checkNonIssuing = this.listNonIssuingWarehouse.length === this.checkedWarehouses.filter(item => this.listNonIssuingWarehouse.includes(item)).length;//非代发仓全选状态
        },
        //反选
        handleCheckInvertChange(value) {
            let warehouses = this.listName;
            let checkedWarehouses = this.checkedWarehouses;
            if (0 === checkedWarehouses.length) {
                this.checkedWarehouses = value ? warehouses : [];//仓库全选
            } else if (checkedWarehouses.length === warehouses.length) {
                this.checkedWarehouses = [];//仓库非全选
                this.checkAll = false;
            } else {
                let list = warehouses.concat(checkedWarehouses).filter((v, i, array) => {
                    return array.indexOf(v) === array.lastIndexOf(v);//返回只出现一次索引的元素
                });
                this.checkedWarehouses = list;//仓库反选
            }
            this.checkAll = this.checkedWarehouses.length === this.listObject.length;//全选状态
            // this.isIndeterminate = checkedWarehouses.length > 0 && checkedWarehouses.length < this.listObject.length;//部分选中状态
            this.checkForwarding = this.listForwardingWarehouse.length === this.checkedWarehouses.filter(item => this.listForwardingWarehouse.includes(item)).length;//代发仓全选状态
            this.checkNonIssuing = this.listNonIssuingWarehouse.length === this.checkedWarehouses.filter(item => this.listNonIssuingWarehouse.includes(item)).length;//非代发仓全选状态
        },
        //代发仓全选
        handleCheckForwardingWarehouseChange(value) {
            this.checkedWarehouses = this.checkedWarehouses.filter(item => !this.listForwardingWarehouse.includes(item));//先去除"代发"，然后再添加，避免重复
            this.checkedWarehouses = value ? this.checkedWarehouses.concat(this.listForwardingWarehouse) : this.checkedWarehouses;//添加全选代发仓
            this.checkAll = this.checkedWarehouses.length === this.listObject.length;//代发仓全选状态
            this.isIndeterminate = this.checkedWarehouses.length > 0 && this.checkedWarehouses.length < this.listObject.length;//部分选中状态
        },
        //非代发仓全选
        hadnleCheckNonIssuingWarehousechange(value,type) {
              if (this.checkNonIssuing) {
                this.checkedWarehouses = this.checkedWarehouses.filter(item => !this.listNonIssuingWarehouse.includes(item));//先去除"非代发"，然后再添加，避免重复
                this.checkedWarehouses = this.checkNonIssuing ? this.checkedWarehouses.concat(this.listNonIssuingWarehouse) : this.checkedWarehouses;//添加全选非代发仓
                this.checkAll = this.checkedWarehouses.length === this.listObject.length;//非代发仓全选状态
              }else if(!this.checkNonIssuing && this.excludeValue){//如果没有勾选所有非代发仓,那么就勾选所有非代发仓(排除云仓)加上之前勾选的
                this.checkedWarehouses = this.checkedWarehouses.filter(item => !this.excludeYcWarehouse.includes(item));//先去除"非代发"，然后再添加，避免重复
                this.checkedWarehouses = this.excludeValue ? this.checkedWarehouses.concat(this.excludeYcWarehouse) : this.checkedWarehouses;//添加全选非代发仓
                this.checkAll = this.checkedWarehouses.length === this.listObject.length;//非代发仓全选状态
              }else if(!this.checkNonIssuing && !this.excludeValue){//如果两个都没有,就去掉所有非代发仓
                this.checkedWarehouses = this.checkedWarehouses.filter(item => !this.listNonIssuingWarehouse.includes(item));//先去除"非代发"，然后再添加，避免重复
                this.checkedWarehouses = this.checkNonIssuing ? this.checkedWarehouses.concat(this.listNonIssuingWarehouse) : this.checkedWarehouses;//添加全选非代发仓
                this.checkAll = this.checkedWarehouses.length === this.listObject.length;//非代发仓全选状态
              }
        },
        //点击后：勾选检查(多选)
        handleCheckWarehouseChange() {
            this.checkAll = this.checkedWarehouses.length === this.listObject.length ? true : false;//全选状态
            // this.isIndeterminate = checkedCount > 0 && checkedCount < this.listObject.length;//获取长度方式---部分选中状态
            this.checkForwarding = this.listForwardingWarehouse.length === this.checkedWarehouses.filter(item => this.listForwardingWarehouse.includes(item)).length;//代发仓全选状态
            this.checkNonIssuing = this.listNonIssuingWarehouse.length === this.checkedWarehouses.filter(item => this.listNonIssuingWarehouse.includes(item)).length;//非代发仓全选状态
            this.excludeValue = this.excludeYcWarehouse.length === this.checkedWarehouses.filter(item => this.excludeYcWarehouse.includes(item)).length;//非代发仓排除云仓仓库全选状态
        },
        //点击后，勾选检测(单选)
        handleRadioChange(value) {
            this.checkedWarehouses = [value];
        },
        //输入框Enter事件监听
        getWarehouse(keywords) {
            if (!keywords) return;
            for (let i = 0; i < this.listName.length; i++) {
                if (this.listName[i].includes(keywords) && !this.checkedWarehouses.includes(this.listName[i])) {
                    this.checkedWarehouses.push(this.listName[i]);
                    // this.checkedWarehouseIDs.push(this.listID[i]);
                    // if (!this.showCheckbox) {
                    //     this.radioCheckedWarehouse = this.listName[i];
                    //     break;
                    // }
                }
            }
            this.handleCheckWarehouseChange();
        },
        //固定仓库按钮点击事件监听
        btnOn(keywords) {
            this.keywords = keywords;
            this.getWarehouse(this.keywords);
        },
        //设置常用仓库
        async SetCommonWarehouseAsync() {
            this.checkedWarehouseIDs = [];//去除初始化时添加的仓库ID
            for (var i = 0; i < this.listName.length; i++) {
                if (this.checkedWarehouses.includes(this.listName[i])) {
                    this.checkedWarehouseIDs.push(this.listID[i]);
                }
            }
            this.remoteCfg = [this.checkedWarehouseIDs, this.checkedWarehouses, this.listObject.filter(item => this.checkedWarehouses.includes(item.label))];
            const cfg = await SetVxeTableColumnCacheAsync({ tableId: this.keyId, ColumnConfig: JSON.stringify(this.remoteCfg) });//设置常用仓库缓存
            if (this.checkedWarehouses.length === 0) {
                this.$message.success("请注意，您未设置常用仓库!");
            } else if (cfg.success && cfg.data) {
                this.$message.success("设置常用仓库成功!");
            } else if (!cfg.success) {
                this.$message.error("设置常用仓库失败!");
            }
        },
        //选取常用仓库
        async GetCommonWarehouseAsync() {
            for (let i = 0; i < this.remoteCfg[1].length; i++) {
                if (!this.checkedWarehouses.includes(this.remoteCfg[1][i])) {//避免一直点击设置常用仓库后，checkedWarehouses与checkedWarehouseIDs存在重复数据
                    this.checkedWarehouses.push(this.remoteCfg[1][i]);
                }
            }
            if (this.remoteCfg[1].length === 0) {
                this.$message.success("请注意，您未设置常用仓库!");
            } else if (this.remoteCfg[1].length > 0) {
                this.$message.success("选取常用仓库成功!");
            }
            this.handleCheckWarehouseChange();
        }
    },
};
</script>

<style scoped>
::v-deep .el-scrollbar__wrap {
    max-height: 300px;
    /* 控制 el-scrollbar 最大高度 */
}
</style>