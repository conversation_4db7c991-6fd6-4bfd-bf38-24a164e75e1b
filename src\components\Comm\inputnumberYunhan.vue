<template>
    <div>
        <el-input-number v-model="num" controls-position="right" :precision="0"  @blur="handleChange(num)" style="width: 50px;" :controls="false" :disabled="isdisabled" :min="0" :max="999999999" size="mini"></el-input-number>
    </div>
</template>

<script>
export default {
    name: 'YunHanAdminInputnumberYunhan',
    props: {
        scoperow:{},
        disabled:{ type: Boolean, default: () => { return false } },
    },
    watch: {
        scoperow: {
            deep: true,
            handler(val) {
                this.showrow(val)
            }
        } 
    },

    data() {
        return {
            num: 0,
            rowstring: {},
            isdisabled: false
        };
    },

    async mounted() {
        var _this = this;
        _this.rowstring = _this.scoperow;
        _this.num = _this.rowstring.purchasePlanCount3

        _this.isdisabled = _this.disabled;
        //console.log('数据输出',_this.rowstring.safeDayDown)
    },

    methods: {
        async handleChange(value){
            var _this = this;
            value = value ?? 0;
            _this.num = value;
            _this.isdisabled = true
            console.log(value);
            if (!/^[0-9]*$/.test(value)) {
                this.$message.error("请输入整数！！！！")
                _this.isdisabled = false
                return false
            }
            _this.$emit('changesafeDay',value, this.scoperow);
            //禁止用户连续点击
            setTimeout(() => {
                _this.isdisabled = false
            },500)
        },
        async showrow(){
            var _this = this;
            _this.rowstring = _this.scoperow;
            _this.num = _this.rowstring.purchasePlanCount3
            //console.log('数据输出1',_this.rowstring.safeDayDown)          
        },
        showrowMethod(row){
            this.rowstring = row;
            this.num = this.rowstring.purchasePlanCount3;
            // this.$set()
            // this.$set(this.num, 'num', this.rowstring.purchasePlanCount3)
            // this.$set(this.data.summary, 'totalPreAllotQty_sum', res1.toFixed(0))
            this.$forceUpdate()
        },
    },
};
</script>

<style lang="scss" scoped>

</style>