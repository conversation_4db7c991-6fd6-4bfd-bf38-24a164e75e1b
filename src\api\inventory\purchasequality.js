import request from "@/utils/request";
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/purchasequality/`;

//添加自动开单对应信息
export const getPurchaseQualityGoodsRecordImgs = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetPurchaseQualityGoodsRecordImgs",
    params,
    config
  );
};

//入库重量
//获取配置
export const getWarehousingOrderVideoWeightCompareConfig = (
  params,
  config = {}
) => {
  return request.post(
    apiPrefix + "GetWarehousingOrderVideoWeightCompareConfig",
    params,
    config
  );
};

//保存配置
export const saveWarehousingOrderVideoWeightCompareConfig = (
  params,
  config = {}
) => {
  return request.post(
    apiPrefix + "SaveWarehousingOrderVideoWeightCompareConfig",
    params,
    config
  );
};

//页面数据
export const getWarehousingOrderVideoWeighingPage = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetWarehousingOrderVideoWeighingPage",
    params,
    config
  );
};

//单个操作
export const setOrderVideoWeighingStatus = (params, config = {}) => {
  return request.post(
    apiPrefix + "SetOrderVideoWeighingStatus",
    params,
    config
  );
};

//批量操作
export const batchSetOrderVideoWeighingStatus = (params, config = {}) => {
  return request.post(
    apiPrefix + "BatchSetOrderVideoWeighingStatus",
    params,
    config
  );
};

//获取采购跟单列表 GetMerchandiserList
export const getMerchandiserList = (params, config = {}) => {
  return request.post(apiPrefix + "GetMerchandiserList", params, config);
};

//获取日志 GetPurchaseQualityMetageWeightLog
export const getPurchaseQualityMetageWeightLog = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetPurchaseQualityMetageWeightLog",
    params,
    config
  );
};

//仓库工作量统计
export const getWarehouseWorkloadPage = (params, config = {}) => {
    return request.post(
      apiPrefix + "GetWarehouseWorkloadPage",
      params,
      config
    );
};

//仓库工作量统计明细
export const GetWarehouseWorkloadDtl = (params, config = {}) => {
    return request.post(
      apiPrefix + "GetWarehouseWorkloadDtl",
      params,
      config
    );
};
  
//  导出数据
export const exportWarehouseWorkload = (params, config = {responseType: 'blob'}) => {
    return request.post(apiPrefix + 'ExportWarehouseWorkload', params, config) }