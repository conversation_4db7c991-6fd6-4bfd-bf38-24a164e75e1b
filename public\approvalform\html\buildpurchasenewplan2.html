<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <!-- elment样式 -->
    <link rel="stylesheet" href="/approvalform/html/api/elment.css">
    <!-- vue -->
    <script src="/approvalform/html/api/vue.min.js"></script>
    <!-- elment脚本 -->
    <script src="/approvalform/html/api/elment.js"></script>
    <!-- jquery -->
    <script src="/approvalform/html/api/jquery.min.js"></script>

    <script src="/approvalform/html/api/html2canvas.js"></script>

    <title>计划采购建议</title>
    <style type="text/css" scoped>
        .linebreak {
            overflow: hidden;
            /*超出部分隐藏*/

            text-overflow: ellipsis;
            /* 超出部分显示省略号 */
            white-space: normal;
            /*规定段落中的文本不进行换行 */
            width: 100%;
        }

        .linebreak1 {
            /* 超出隐藏,给省略号,换三行 */
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            width: 100%;
        }

        .imgcss img {
            min-width: 55px !important;
            min-height: 55px !important;
            width: 55px !important;
            height: 55px !important;
        }
        
        .el-table thead{
            color:black;
        }
    </style>
</head>

<body>
    <div id="app" style="margin:0 auto;">
        <el-container direction="vertical" style="padding: 0px 10px 0px 10px ; border: 1px #ccc solid;">
            <template>
                <!-- <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="2" :xl="2">
                        <el-button type="primary" size="default" @click="tocreateimg()">下载图片</el-button>

                    </el-col>
                </el-row> -->
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <div ref="oneboxx">
                            <el-table ref="table" :data="list" row-key="id" border style="width: 100%;font-size: 13px;color: #757575;" height="98vh"
                                show-overflow-tooltip v-loading="thisLonding">
                                <el-table-column type="index" label="#" align="center" width="30"></el-table-column>
                                <el-table-column label="图片" :show-overflow-tooltip="true" width="100" align="left">
                                    <teleport slot-scope="scope">
                                        <el-image :src="scope.row.images" :preview-src-list="scope.row.imageList"
                                            class="imgcss" style="margin-left: -10px;" fit="fill"
                                            :lazy="true"></el-image>
                                    </teleport>
                                </el-table-column>
                                <el-table-column label="商品信息" prop="styleCode" width="130"
                                    :show-overflow-tooltip="true">
                                    <template #header>
                                        <span class="grid-header">
                                            <span>
                                                <el-tooltip class="item" effect="dark" content="商品信息，款式编码，分仓"
                                                    placement="top-end">
                                                    <span><i class="el-icon-question"></i></span>
                                                </el-tooltip>
                                            </span>
                                            <span>商品信息</span>
                                        </span>
                                    </template>
                                    <template slot-scope="scope">
                                      <div style="height: 100px; overflow: hidden; display: flex; flex-direction: column; justify-content: center;">
                                        <div class="divline">
                                          <span style="color: #757575; display: block; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                            {{ scope.row.goodsName }}
                                          </span>
                                        </div>
                                        <div>
                                          <span style="font-size: 15px;color: black; display: block; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                            {{ scope.row.styleCode }}
                                          </span>
                                        </div>
                                        <div>
                                          <span style="color: #757575; display: block; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                            {{ scope.row.warehouseName }}
                                          </span>
                                        </div>
                                      </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="商品编码" prop="goodsCode" width="80" :show-overflow-tooltip="true">
                                    <template slot-scope="scope">
                                        <span class="linebreak">
                                            {{ scope.row.goodsCode }}
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="商品标签" prop="goodsLable" width="70"
                                    :show-overflow-tooltip="true">
                                    <template slot-scope="scope">
                                        <div>
                                            <div style="color: #757575;" class="linebreak1">{{ scope.row.goodsLable }}
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="商品分类" prop="groupId" width="70" :show-overflow-tooltip="true">
                                    <template slot-scope="scope">
                                        <div>
                                            {{ scope.row.groupName == null ? '' : scope.row.groupName }}
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="品牌" prop="brandId" width="70" :show-overflow-tooltip="true">
                                    <template slot-scope="scope">
                                        <div>
                                            {{ scope.row.brandName == null ? '' : scope.row.brandName }}
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="商品备注" prop="goodsRemark" width="80"></el-table-column>
                                <el-table-column label="云仓缺货数" prop="wmsLackQty2" width="80"></el-table-column>
                                <el-table-column label="建议采购信息" width="180">
                                    <template slot-scope="scope">
                                        <div>
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            <span style="color: red;">{{ scope.row.purchasePlanCount }}({{
                                                scope.row.packCount }})</span>
                                        </div>
                                        <div style="display: flex; flex-direction: row;">
                                            <div>
                                                ￥{{ scope.row.cost }} *
                                            </div>
                                            <el-tooltip class="item" effect="dark" :content="scope.row.applyMsg"
                                                placement="top">
                                                <el-input-number v-model="scope.row.purchasePlanCount"
                                                    style="width: 80px;" :controls="false" size="small"
                                                    :disabled="true"></el-input-number>
                                            </el-tooltip>
                                            <div>
                                                ={{ (scope.row.purchasePlanCount * scope.row.cost).toFixed(1) }}
                                            </div>

                                        </div>
                                        <div>
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            <!-- {{scope.row.packNum}} -->
                                            {{ scope.row.packCount == 0 ? scope.row.purchasePlanCount + '件' :
                                            Math.floor(scope.row.purchasePlanCount / scope.row.packCount) == 0 ?
                                            scope.row.purchasePlanCount + '件' : Math.floor(scope.row.purchasePlanCount /
                                            scope.row.packCount) + '箱' +
                                            ((scope.row.purchasePlanCount % scope.row.packCount) == 0 ? '' :
                                            (scope.row.purchasePlanCount % scope.row.packCount) + '件')
                                            }}
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="库存周转天数" prop="inventoryDay" column-key="inventoryDay"
                                    width="110" :show-overflow-tooltip="true">
                                    <template #header>
                                        <span class="grid-header">
                                            <span>库存周转天数<br /> 采购周转天数</span>
                                        </span>
                                    </template>
                                    <template slot-scope="scope">
                                        <div>
                                            {{ scope.row.inventoryDay }}
                                        </div>
                                        <br />
                                        <div style="color: blue;">
                                            {{ scope.row.purchaseDay }}
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="主仓库存" prop="masterStock" column-key="masterStock" width="95"
                                    :show-overflow-tooltip="true">
                                    <template #header>
                                        <span class="grid-header">
                                            <span>主仓库存<br /> 仓库库存数</span>
                                        </span>
                                    </template>
                                    <template slot-scope="scope">
                                        <div>
                                            {{ scope.row.masterStock }}
                                        </div>
                                        <br />
                                        <div>
                                            {{ scope.row.warehouseStock }}
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="进货仓库存" prop="purchaseStock" column-key="purchaseStock"
                                    width="100" :show-overflow-tooltip="true">
                                    <template #header>
                                        <span class="grid-header">
                                            <span>采购在途数<br /> 进货仓库存 <br /> 待审核</span>
                                        </span>
                                    </template>
                                    <teleport slot-scope="scope">
                                        <div style="color: blue;">
                                            在 途：{{ scope.row.inTransitNum }}
                                        </div>
                                        <div>
                                            进货仓：{{ scope.row.purchaseStock }}
                                        </div>
                                        <div>
                                            待审核：{{ scope.row.checkNum }}
                                        </div>
                                    </teleport>
                                </el-table-column>
                                <!-- <el-table-column label="采购在途数" prop="inTransitNum" width="70"  :show-overflow-tooltip="true">
                        </el-table-column> -->
                                <el-table-column label="订单待发" prop="orderWaitSend" width="80"
                                    :show-overflow-tooltip="true">
                                </el-table-column>
                                <el-table-column label="可售库存" prop="sellStock" width="80" :show-overflow-tooltip="true">
                                    <template #header>
                                        <span class="grid-header">
                                            <span>可售库存</span>
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="安全天数上~下 安全库存上~下" width="120">
                                    <template #header>
                                        <span class="grid-header">
                                            <!-- <span>
                                        <el-tooltip class="item" effect="dark" content="商品信息，款式编码，分仓" placement="top-end">
                                            <span><i class="el-icon-question"></i></span>
                                        </el-tooltip>
                                    </span> -->
                                            <span>安全天数上~下<br /> 安全库存上~下</span>
                                        </span>
                                    </template>
                                    <teleport slot-scope="scope">
                                        <div>
                                            {{ scope.row.safeDayUp2 }} / {{ scope.row.safeDayDown2 }}
                                        </div>
                                        <br />
                                        <div>
                                            {{ scope.row.safeStockUp2 }} / {{ scope.row.safeStockDown2 }}
                                        </div>
                                    </teleport>
                                </el-table-column>
                                <el-table-column label="最近在途时长 历史平均在途" width="110">
                                    <template #header>
                                        <span class="grid-header">
                                            <span>最近在途时长<br />历史平均在途</span>
                                        </span>
                                    </template>
                                    <teleport slot-scope="scope">
                                        <div>
                                            {{ scope.row.lastInTransitTime }}
                                        </div>
                                        <br />
                                        <div>
                                            {{ scope.row.avgInTransitTime }}
                                        </div>
                                    </teleport>
                                </el-table-column>
                                <el-table-column label="总销量/总退货" width="130">
                                    <teleport slot-scope="scope">
                                        <div>
                                            3日： {{ scope.row.salesDay3 }} / {{ scope.row.refundDay3 }}
                                        </div>
                                        <div>
                                            7日： {{ scope.row.salesDay7 }} / {{ scope.row.refundDay7 }}
                                        </div>
                                        <div>
                                            15日： {{ scope.row.salesDay15 }} / {{ scope.row.refundDay15 }}
                                        </div>
                                        <div>
                                            30日： {{ scope.row.salesDay30 }} / {{ scope.row.refundDay30 }}
                                        </div>
                                    </teleport>
                                </el-table-column>
                                <el-table-column label="昨日销量" prop="salesYesterday" width="80" >
                                </el-table-column>
                                <el-table-column label="日均销量/日均退货" width="140">
                                    <teleport slot-scope="scope">
                                        <div>
                                            3日： {{ scope.row.avgDay3 }} / {{ scope.row.avgRefundDay3 }}
                                        </div>
                                        <div>
                                            7日： {{ scope.row.avgDay7 }} / {{ scope.row.avgRefundDay7 }}
                                        </div>
                                        <div>
                                            15日： {{ scope.row.avgDay15 }} / {{ scope.row.avgRefundDay15 }}
                                        </div>
                                        <div>
                                            30日： {{ scope.row.avgDay30 }} / {{ scope.row.avgRefundDay30 }}
                                        </div>
                                    </teleport>
                                </el-table-column>
                                <el-table-column label="日均修正量" prop="amendDay" width="auto"
                                    :show-overflow-tooltip="true">
                                </el-table-column>
                                <el-table-column label="总销量(排除下架)" width="140">
                                    <teleport slot-scope="scope">
                                        <div>
                                            3日： {{ scope.row.salesDay3_2 }}
                                        </div>
                                        <div>
                                            7日： {{ scope.row.salesDay7_2 }}
                                        </div>
                                        <div>
                                            15日： {{ scope.row.salesDay15_2 }}
                                        </div>
                                        <div>
                                            30日： {{ scope.row.salesDay30_2 }}
                                        </div>
                                    </teleport>
                                </el-table-column>
                                <el-table-column label="新日均修正量" prop="amendDayNew" width="100" :show-overflow-tooltip="true">
                                    <template #header>
                                        <span class="grid-header">
                                            <span>
                                                <el-tooltip class="item" effect="dark" content="总销量(排除下架)3日/3" placement="top-end">
                                                    <span><i class="el-icon-question"></i></span>
                                                </el-tooltip>
                                            </span>
                                            <span>新日均修正量</span>
                                        </span>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </el-col>
                </el-row>
            </template>
        </el-container>
    </div>
    <script>
        var vm = new Vue({
            el: '#app',
            data: function () {
                return {
                    //正式上线时，请切换为正式地址，末尾不许带 /
                    //测试：http://**************
                    //正式：http://*************
                    thisInterfaceUrl: "http://**************",
                    thisFormData: {},
                    thisDoc: {},
                    thisDocGoods: [],
                    thisChooses: [],
                    thisSkus: [],
                    url1: "",
                    srcList1: [],
                    url2: "",
                    srcList2: [],
                    url3: "",
                    srcList3: [],
                    thisLonding: false,
                    list: []
                }
            },
            created() {

            },
            async mounted() {
                this.getStyleSheetInfo();
            },
            methods: {
                async getStyleSheetInfo() {
                    var me = this;
                    let searchURL = window.location.search;
                    searchURL = searchURL.substring(1, searchURL.length);
                    let targetPageId = searchURL.split("&")[0].split("=")[1];
                    me.thisLonding = true;
                    let params = {
                        ids: targetPageId
                        //'1595265205358846054,1592076423492715074,1595265005085024256,1592076423262028468,1592076423572407174,1592076423375274353,1592076423022953042'
                    };
                    $.ajax({
                        type: 'POST',
                        async: false,
                        url: `/api/Inventory/purchaseordernew/PagePurchaseNewPlan2HtmlAsync`,
                        data: JSON.stringify(params),
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        success: function (result) {
                            me.thisLonding = false;
                            me.list = result.data
                            me.list.forEach(f => {
                                f.lastInTransitTime = me.formatSecondNewToHour(f.lastInTransitTime);
                                f.avgInTransitTime = me.formatSecondNewToHour(f.avgInTransitTime);
                                f.children.forEach(a => {
                                    a.lastInTransitTime = me.formatSecondNewToHour(a.lastInTransitTime);
                                    a.avgInTransitTime = me.formatSecondNewToHour(a.avgInTransitTime);
                                });
                            })

                        },
                        error: function (err) {
                            console.log(err);
                        }
                    })
                },
                tocreateimg() {
                    html2canvas(this.$refs.oneboxx).then((canvas) => {
                        let dataURL = canvas.toDataURL('image/png')
                        this.imgUrl = dataURL
                        console.log("生成图片", this.imgUrl);
                        //this.dialogTableVisible = true;
                    })
                },
                //转换成小数
                formatSecondNewToHour(minutes) {
                    if (!minutes || minutes == 0)
                        return ''
                    var day = Math.floor(minutes / (60 * 24));
                    return (((day * 24 + Math.floor((minutes % (60 * 24)) / 60)) / 24).toFixed(1));
                }
            }
        });
    </script>
</body>

</html>
