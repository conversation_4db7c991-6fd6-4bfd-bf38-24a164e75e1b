import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/DistributionGoodsOnline/`

export const distributionGoodsOnlineAsync = (params, config = {}) => { return request.post(apiPrefix + 'DistributionGoodsOnlineAsync', params, config)}

export const getDistributionGoodsOnlineRecordAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetDistributionGoodsOnlineRecordAsync', params, config)}

export const getDistributionGoodsOnlineSkuRecordAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetDistributionGoodsOnlineSkuRecordAsync', params, config)}

export const distributionGoodsOnlineAgainAsync =(params, config = {}) => { return request.post(apiPrefix + 'DistributionGoodsOnlineAgainAsync', params, config)}

export const getProCodeSkuInfos=(params, config = {}) => {  return request.get(apiPrefix + 'GetProCodeSkuInfos', { params: params, ...config })} 

export const getDistributionGoodsOnlineDetailReportAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetDistributionGoodsOnlineDetailReportAsync', params, config)}

export const distributionGoodsDetailReportAnalysis = (params, config = {}) => { return request.post(apiPrefix + 'DistributionGoodsDetailReportAnalysis', params, config)}

export const distributionGoodsDetailOneTypeReportAnalysis = (params, config = {}) => { return request.post(apiPrefix + 'DistributionGoodsDetailOneTypeReportAnalysis', params, config)}

export const getDistributionGoodsOnlineRecordGroupAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetDistributionGoodsOnlineRecordGroupAsync', params, config)}

export const getDistributionGoodsOperateLogAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetDistributionGoodsOperateLogAsync', params, config)} 

// 3.0
export const addDistributionOnlineProduct = (params, config = {}) => { return request.post(apiPrefix + 'AddDistributionOnlineProduct', params, config)} 

export const getDistributionOnlineProductList = (params, config = {}) => { return request.post(apiPrefix + 'GetDistributionOnlineProductList', params, config) } 

//上架
export const onlineDistributionProduct = (params, config = {}) => { return request.post(apiPrefix + 'OnlineDistributionProduct', params, config) } 

//根据id获取铺货上新产品信息
export const getDistributionOnlineProductById = (params, config = {}) => { return request.post(apiPrefix + 'GetDistributionOnlineProductById', params, config) } 
//保存铺货上新的产品信息
export const saveDistributionOnlineProduct = (params, config = {}) => { return request.post(apiPrefix + 'SaveDistributionOnlineProduct', params, config) } 

//分销金额最高查询 GetMaximumDistributionList   
export const getMaximumDistributionList = (params, config = {}) => { return request.post(apiPrefix + 'GetMaximumDistributionList', params, config) }

//分销金额增量最多查询 GetDistributionAmountLargestList
export const getDistributionAmountLargestList = (params, config = {}) => { return request.post(apiPrefix + 'GetDistributionAmountLargestList', params, config) }

// 铺货最多查询 GetMostStockedList   
export const getMostStockedList = (params, config = {}) => { return request.post(apiPrefix + 'GetMostStockedList', params, config) }

//分销金额最高导出 ExportMaximumDistributionList   
export const exportMaximumDistributionList = (params,config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportMaximumDistributionList', params, config) }

//分销金额增量最多导出 ExportDistributionAmountLargestList  
export const exportDistributionAmountLargestList = (params, config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportDistributionAmountLargestList', params, config) }

//铺货最多导出 ExportMostStockedList
export const exportMostStockedList = (params, config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportMostStockedList', params, config) }