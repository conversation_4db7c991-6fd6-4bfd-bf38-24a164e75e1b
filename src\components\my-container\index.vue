<template>
  <section style="height:100%;">
    <el-container style="height:100%;position:relative;">
      <el-header height="auto" class="myheader">
        <slot name="header" />
      </el-header>
      <el-main class="mycontainer">
        <slot />
      </el-main>
      <!-- <el-footer height style="padding:0px 0px 5px 5px;">
        <slot name="footer" />
      </el-footer> -->
      <el-footer height style="padding:0; text-align: end;">
        <slot name="footer" />
      </el-footer>
    </el-container>
  </section>
</template>

<script>
/**
 * 容器组件
 * 使用说明
<my-container>
  <template #header>
    <el-form />
  </template>
  <el-table />
  <template #footer>
    <my-pagination />
  </template>
</my-container>
 */

export default {
  name: 'My<PERSON>ontainer'
}
</script>
<style lang="scss" scoped>
.mycontainer{
  padding:0px 5px;
  height:100%;
}
.myheader{
  padding:10px 0px 0px 10px;
}
</style>
