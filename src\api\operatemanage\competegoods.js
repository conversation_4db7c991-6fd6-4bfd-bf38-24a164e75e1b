import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/competegoods/`

//查询工厂店竞品数据

export const getGoodsMonitoring = (params, config = {}) => { return request.get(apiPrefix + 'GetGoodsMonitoringAsync', { params: params, ...config }) }

//添加or编辑工厂店竞品数据
export const editGoodsMonitoring = (params, config) => { return request.post(apiPrefix + 'EditGoodsMonitoringAsync', params, config) }

//更改监控状态
export const changeGoodsMonitoringStatus=(params,config={})=>{return request.get(apiPrefix+'ChangeGoodsMonitoringStatusAsync',{params:params,...config})}
//竞品监控趋势图
export const getGoodsMonitoringChart = (params, config = {}) => {return request.get(apiPrefix + 'GetGoodsMonitoringChart', { params: params, ...config })}

//查询竞品数据明细
export const getGongCompeteGoodsDetail = (params, config = {}) => { return request.get(apiPrefix + 'GetGongCompeteGoodsDetailAsync', { params: params, ...config }) }

export const exportGongCompeteGoodsDetail = (params, config = {responseType: 'blob'}) => { return request.get(apiPrefix + 'ExportGongCompeteGoodsDetailAsync',  { params: params, ...config })}



export const deleteGongCompeteGoods = (params, config = {}) => { return request.delete(apiPrefix + 'DeleteGongCompeteGoodsAsync', { params: params, ...config }) }


