<template>
  <div>
     <el-input :placeholder="placeholder" v-model="value" clearable @keyup.enter.native="onEnter"></el-input>
     <el-dialog :visible.sync="dialogVisible" :show-close="false" :lock-scroll="true" :close-on-press-escape="true" :center="true"
        :modal-append-to-body="true" :append-to-body="true" v-dialogDrag>
        <el-input type="textarea" :placeholder="placeholder+placeholderarea" v-model="value" 
              :autosize="{ minRows: 8, maxRows: 50}"></el-input>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">关 闭</el-button>
            <el-button type="primary" @click="onQueren()">确 定</el-button>
          </span>
       </template>
     </el-dialog>
    </div>
</template>
<script>
  export default {
    props:{
       value:null,
       placeholder:"",
    },
    data() {
      return {
        valuenew:null,
        dialogVisible:false,
        placeholderarea:" 多个请换行"
      }
    },
    methods: {
     async onEnter(){
         console.log("onEnter")
        this.dialogVisible=true
      },
     async onQueren(){
        this.dialogVisible=false
     }
    }
  }
</script>