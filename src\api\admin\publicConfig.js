import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API}/publicConfig/`

//组合装商品-维护自定义标签-获取自定义标签列表
export const getConfigByParentTitle = (params, config = {}) => {
  return request.post(apiPrefix + 'GetConfigByParentTitle?title=' + params.title, params, config)
}

//组合装商品-维护自定义标签-添加自定义标签
export const addChildrenConfig = (params, config = {}) => {
  return request.get(apiPrefix + 'AddChildrenConfig', { params: params, ...config })
}

//组合装商品-维护自定义标签-删除单个子级标签
export const delChildrenConfig = (params, config = {}) => {
  return request.get(apiPrefix + 'DelChildrenConfig', { params: params, ...config })
}

//运营新品-建编码-仓库设置-根据父级Key获取配置数据
export const getConfigByParentKey = (params, config = {}) => {
  return request.post(apiPrefix + 'GetConfigByParentKey?key=' + params.key, params, config)
}