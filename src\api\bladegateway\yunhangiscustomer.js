import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_JAVA_API_BLADEGATEWAY}/yunhan-gis-customer/`
// const apiPrefix = `/yunhan-gis-customer/`

//获取字典
export const dictionary = (params, config = {}) => { return request.post(apiPrefix + 'dictionary?code=' + params.code, params, config) }

//获取客服成本页面
export const customerCostPage = (params, config = {}) => { return request.post(apiPrefix + 'customerCostPage', params, config) }

//获取客服成本详情
export const customerPersonnelDetail = (params, config = {}) => { return request.post(apiPrefix + 'customerPersonnelDetail', params, config) }

//客服成本导出
export const customerCostExport = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'customerCostExport', params, config)
}

//组成本统计页面
export const groupCostsPage = (params, config = {}) => { return request.post(apiPrefix + 'groupCostsPage', params, config) }

//个人成本统计页面
export const personalCostsPage = (params, config = {}) => { return request.post(apiPrefix + 'personalCostsPage', params, config) }

//成本成本统计页面
export const customerCostImportPage = (params, config = {}) => { return request.post(apiPrefix + 'customerCostImportPage', params, config) }

//客服人员新增页面
export const customerPersonnelSave = (params, config = {}) => { return request.post(apiPrefix + 'customerPersonnelSave', params, config) }

//编辑保存
export const customerPersonnelUpdate = (params, config = {}) => { return request.post(apiPrefix + 'customerPersonnelUpdate', params, config) }

//客服人员导入
export const customerCostImport = (params, config = {}) => { return request.post(apiPrefix + 'customerCostImport?costDate=' + params[1], params[0], config) }

//客服人员删除
export const customerCostRemove = (params, config = {}) => { return request.post(apiPrefix + 'customerCostRemove', params, config) }


//成本数据删除
export const customerCostImportRemove = (params, config = {}) => { return request.post(apiPrefix + 'customerCostImportRemove', params, config) }

// 第一个页面操作记录
export const operationLogPage = (params, config = {}) => { return request.post(apiPrefix + 'operationLogPage', params, config) }

//成本导入
// export const customerCostDataImport= (params, config = {}) => { return request.post(apiPrefix + 'customerCostDataImport?costDate='+params[1]+'&file='+params[0]+'&importType='+params[2], params, config)}
export const customerCostDataImport = (params, config = {}) => { return request.post(apiPrefix + 'customerCostDataImport', params, config) }

//历史记录分页
// export const  customerCostHistoryRecordPage= (params, config = {}) => { return request.post(apiPrefix + ' customerCostHistoryRecordPage', params, config)}
export const customerCostHistoryRecordPage = (params, config = {}) => { return request.post(apiPrefix + 'customerCostHistoryRecordPage', params, config) }

//个人趋势图
export const personalCostsTrendChart = (params, config = {}) => { return request.post(apiPrefix + 'personalCostsTrendChart', params, config) }
//获取等级设置页面-新增批量保存
export const customerLevelBatchSave = (params, config = {}) => { return request.post(apiPrefix + 'customerLevelBatchSave', params, config) }

//获取等级设置页面删除
export const customerLevelConfigRemove = (params, config = {}) => { return request.post(apiPrefix + 'customerLevelConfigRemove?ids=' + params.ids, params, config) }

//获取等级设置页面
export const customerLevelConfigPage = (params, config = {}) => { return request.post(apiPrefix + 'customerLevelConfigPage', params, config) }

//获取提成系数设置页面
export const customerCommissionConfigPage = (params, config = {}) => { return request.post(apiPrefix + 'customerCommissionConfigPage', params, config) }

//提成系数设置-新增批量保存
export const customerCommissionBatchSave = (params, config = {}) => { return request.post(apiPrefix + 'customerCommissionBatchSave', params, config) }

//组趋势图
export const groupCostsTrendChart = (params, config = {}) => { return request.post(apiPrefix + 'groupCostsTrendChart', params, config) }
//提成系数设置页面删除
export const customerCommissionConfigRemove = (params, config = {}) => { return request.post(apiPrefix + 'customerCommissionConfigRemove?ids=' + params.ids, params, config) }

//获取客服等级设置列表编辑弹窗详情页面
export const customerLevelConfigDetail = (params, config = {}) => { return request.post(apiPrefix + 'customerLevelConfigDetail', params, config) }

//获取提成系数设置列表编辑弹窗详情页面
export const customerCommissionConfigDetail = (params, config = {}) => { return request.post(apiPrefix + 'customerCommissionConfigDetail', params, config) }

//客服人员管理
export const customerPersonnelPage = (params, config = {}) => { return request.post(apiPrefix + 'customerPersonnelPage', params, config) }

//客服人员管理删除
export const customerPersonnelRemove = (params, config = {}) => { return request.post(apiPrefix + 'customerPersonnelRemove', params, config) }

//客服人员导入
export const customerPersonnelImport = (params, config = {}) => { return request.post(apiPrefix + 'customerPersonnelImport?costDate=' + params[1], params[0], config) }

//客服成本导出
export const customerPersonnelExport = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'customerPersonnelExport', params, config)
}
