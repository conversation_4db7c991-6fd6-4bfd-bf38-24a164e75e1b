import request from '@/utils/request'
const GroupPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/ExpressIntercept/`

//快递拦截分页查询  GetExpressCompanyNameList
export const GetExpressCompanyNameList = (params, config = {}) => request.get(`${GroupPrefix}GetExpressCompanyNameList`, { params, ...config })

//导入快递拦截 ImporExpressInterceptAsync
export const ImporExpressInterceptAsync = (params, config = {}) => request.post(`${GroupPrefix}ImporExpressInterceptAsync`, params, config)

//快递拦截分页查询明细页面 GetExpressInterceptPageList
export const GetExpressInterceptPageList = (params, config = {}) => request.post(`${GroupPrefix}GetExpressInterceptPageList`, params, config)

//快递拦截导出 ExportExpressInterceptList
export const ExportExpressInterceptList = (params, config = { responseType: 'blob' }) => request.post(`${GroupPrefix}ExportExpressInterceptList`, params, config)

//设置通知时间列表查询 GetExpressInterceptSetNoticeList
export const GetExpressInterceptSetNoticeList = (params, config = {}) => request.post(`${GroupPrefix}GetExpressInterceptSetNoticeList`, params, config)

//保存快递拦截设置通知时间  SaveExpressInterceptSetNotice
export const SaveExpressInterceptSetNotice = (params, config = {}) => request.post(`${GroupPrefix}SaveExpressInterceptSetNotice`, params, config)
export const DeleteExpressInterceptSetNotice = (params, config = {}) => request.get(`${GroupPrefix}DeleteExpressInterceptSetNotice`, { params, ...config })

//快递拦截通知记录分页查询 GetExpressInterceptNoticeLogPageList
export const GetExpressInterceptNoticeLogPageList = (params, config = {}) => request.post(`${GroupPrefix}GetExpressInterceptNoticeLogPageList`, params, config)

//获取快递拦截汇总统计表分页 GetExpressInterceptStatisticsPageList
export const GetExpressInterceptStatisticsPageList = (params, config = {}) => request.post(`${GroupPrefix}GetExpressInterceptStatisticsPageList`, params, config)

//获取快递拦截汇总统计表趋势图 GetExpressInterceptStatisticsChat
export const GetExpressInterceptStatisticsChat = (params, config = {}) => request.post(`${GroupPrefix}GetExpressInterceptStatisticsChat`, params, config)

//取消拦截
export const CancelExpressIntercept = (params, config = {}) => request.get(`${GroupPrefix}CancelExpressIntercept`, { params, ...config })
//快递拦截单条操作记录分页查询
export const GetExpressInterceptLogPageList = (params, config = {}) => request.post(`${GroupPrefix}GetExpressInterceptLogPageList`, params, config)


