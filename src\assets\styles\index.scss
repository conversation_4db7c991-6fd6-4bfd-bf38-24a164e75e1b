@import './element-ui.scss';

body {
  margin: 0px;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}
a, a:focus, a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

#app,.container {
  height: 99%;
}

.container {
  position: absolute;
  top: 0px;
  bottom: 0px;
  width: 100%;
}

.container .aside{
  overflow:hidden;
}

.container .aside .el-menu a {
  display: inline-block;
  width: 100%;
  overflow: hidden;
}

.aside .el-scrollbar{
  height: calc(100% - 50px);
}

.page-component__scroll .el-scrollbar__wrap{
  overflow-x: auto !important;
}

.container .el-header {
  padding: 0px;
}
.container .el-header .right-menu {
  text-align: right;
  padding-right: 5px;
  float: right;
  width: auto;
  overflow: hidden;
  height: 100%;
  line-height: 100%;
}
.container .el-header .right-menu .right-menu-item{
  padding: 0px 12px;
  cursor: pointer;
  transition: all .3s;
  line-height: 50px;
}
.container .el-header .right-menu .right-menu-item:hover{
  /* background: rgba(0,0,0,.025); */
  background: #66b1ff;
}
.container .el-header .right-menu .user-info{
  margin: 7px 8px 7px 0px;
  vertical-align: top;
  /* background-color: #409EFF; */
  background-color:transparent;
  color:#fff;
  border: 2px solid #fff;
  font-size: 12px;
}

.container .aside .logo {
  height: 50px;
  line-height: 50px;
  font-size: 22px;
  background: #409EFF;
  color:#fff;
  width: 100%;
  overflow: hidden;
  cursor: pointer;
}
.container .el-header .logo img {
  width: 40px;
  float: left;
  margin: 10px 10px 10px 18px;
}
.container .el-header .logo .txt {
  color: #fff;
}

.navbar{
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #409EFF;
  -webkit-box-shadow: 0 2px 4px rgba(0,21,41,.08);
  box-shadow: 0 2px 4px rgba(0,21,41,.08);
}
.navbar .breadcrumb-container{
  float: left;
  margin-left: 8px;
}

.aside .logo-text{
  margin-left:20px;
}
.container .aside .logo-collapse .logo-text{
  margin-left:unset;
  text-align: center;
}

.container .el-header .fold-btn {
  padding: 0px 15px;
  width: 14px;
  height: 50px;
  line-height: 50px;
  cursor: pointer;
  float:left;
  cursor: pointer;
  -webkit-transition: background .3s;
  transition: background .3s;
  -webkit-tap-highlight-color: transparent;
  & i{
    font-size: 18px;
    color:#fff;
  }
}
.container .el-header .fold-btn:hover {
  background: #66b1ff;
}

.container .el-main{
  padding: 0px;
  height:100%;
}

.container .aside .el-menu {
  height: 100%;
  border: none;
}
.container .aside .el-menu:not(.el-menu--collapse) {
  width: 200px;
}
.container .main aside .el-menu--collapse{
  width:65px;
}

.main .el-form--label-top .el-form-item__label {
  line-height: 1.5;
  padding-bottom: 8px;
}

input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px #fff inset;
}

.drawer-footer{
 // position: absolute;
  position: relative; 
  left: 0px;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(233, 233, 233);
  padding: 16px;
  background: rgb(255, 255, 255);
  text-align: right;
  z-index: 1;
}

.pagination{
  margin-top: 15px;
  margin-bottom: 5px;
}

.ad-pagination__selection{
  float: left;
  font-weight: 400;
  color: #606266;
  margin-right:10px;
}
.ad-form-query{
  & .el-form-item--mini, & .el-form-item--small{
    &.el-form-item{
      margin-bottom: 10px;
    }
  }
}
