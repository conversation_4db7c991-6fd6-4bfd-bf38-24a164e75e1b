import request from "@/utils/request";
const apiPrefix = `${process.env.VUE_APP_BASE_API_CwManage}/costMaintenanceManager`;

//成品计算 CalPurchaseGoodsCostDiffs
export const calPurchaseGoodsCostDiffs = (params, config = {}) => {
  return request.post(apiPrefix + "/CalPurchaseGoodsCostDiffs", params, config);
};

//查询成品列表
export const GetPurchaseGoodsCostDiffsList = (params, config = {}) => {
  return request.post(
    apiPrefix + "/GetPurchaseGoodsCostDiffsList",
    params,
    config
  );
};

//成品导出历史 ExportPurchaseGoodsCostDiffsList
export const ExportPurchaseGoodsCostDiffsList = (
  params,
  config = { responseType: "blob" }
) => {
  return request.post(
    apiPrefix + "/ExportPurchaseGoodsCostDiffsList",
    params,
    config
  );
};

//成品审批 ApprovePurchaseGoodsCostDiffsList
export const ApprovePurchaseGoodsCostDiffsList = (params, config = {}) => {
  return request.post(
    apiPrefix + "/ApprovePurchaseGoodsCostDiffsList",
    params,
    config
  );
};

//查询钉钉核价列表 GetDingApproveGoodsCostDiffsList
export const GetDingApproveGoodsCostDiffsList = (params, config = {}) => {
  return request.post(
    apiPrefix + "/GetDingApproveGoodsCostDiffsList",
    params,
    config
  );
};

//钉钉核价导出历史 ExportDingApproveGoodsCostDiffsList
export const ExportDingApproveGoodsCostDiffsList = (
  params,
  config = { responseType: "blob" }
) => {
  return request.post(
    apiPrefix + "/ExportDingApproveGoodsCostDiffsList",
    params,
    config
  );
};

//钉钉核价审批 ApproveDingApproveGoodsCostDiffsList
export const ApproveDingApproveGoodsCostDiffsList = (params, config = {}) => {
  return request.post(
    apiPrefix + "/ApproveDingApproveGoodsCostDiffsList",
    params,
    config
  );
};

//钉钉核价计算 CalDingApproveGoodsCostDiffs
export const CalDingApproveGoodsCostDiffs = (params, config = {}) => {
  return request.post(
    apiPrefix + "/CalDingApproveGoodsCostDiffs",
    params,
    config
  );
};

//成本维护列表  GetCostMaintenanceManageList
export const GetCostMaintenanceManageList = (params, config = {}) => {
  return request.post(
    apiPrefix + "/GetCostMaintenanceManageList",
    params,
    config
  );
};

//成本维护导出  ExportCostMaintenanceManageList
export const ExportCostMaintenanceManageList = (
  params,
  config = { responseType: "blob" }
) => {
  return request.post(
    apiPrefix + "/ExportCostMaintenanceManageList",
    params,
    config
  );
};

//半成品计算 CalFinishedPartGoodsCostDiffs
export const CalFinishedPartGoodsCostDiffs = (params, config = {}) => {
  return request.post(
    apiPrefix + "/CalFinishedPartGoodsCostDiffs",
    params,
    config
  );
};

//获取半成品列表 GetFinishedPartGoodsCostDiffsList
export const GetFinishedPartGoodsCostDiffsList = (params, config = {}) => {
  return request.post(
    apiPrefix + "/GetFinishedPartGoodsCostDiffsList",
    params,
    config
  );
};

//半成品导出历史 ExportFinishedPartGoodsCostDiffsList
export const ExportFinishedPartGoodsCostDiffsList = (
  params,
  config = { responseType: "blob" }
) => {
  return request.post(
    apiPrefix + "/ExportFinishedPartGoodsCostDiffsList",
    params,
    config
  );
};

//半成品审批  ApproveFinishedPartGoodsCostDiffsList
export const ApproveFinishedPartGoodsCostDiffsList = (params, config = {}) => {
  return request.post(
    apiPrefix + "/ApproveFinishedPartGoodsCostDiffsList",
    params,
    config
  );
};

//半成品所需数量维护列表 GetPackagesProcesingFinishDatasList
export const GetPackagesProcesingFinishDatasList = (params, config = {}) => {
  return request.post(
    apiPrefix + "/GetPackagesProcesingFinishDatasList",
    params,
    config
  );
};

//更新半成品成本,数量 UpdatePackagesProcesingFinishDatasCountSet
export const UpdatePackagesProcesingFinishDatasCountSet = (
  params,
  config = {}
) => {
  return request.post(
    apiPrefix + "/UpdatePackagesProcesingFinishDatasCountSet",
    params,
    config
  );
};

//成本维护编辑接口
export const ModifyDingApproveGoodsCostDiffs = (
  params,
  config = {}
) => {
  return request.post(
    apiPrefix + "/ModifyDingApproveGoodsCostDiffs",
    params,
    config
  );
};

//获取成品半成品维护列表
export const GetPackagesProcesingCostMaintenanceList = (
  params,
  config = {}
) => {
  return request.post(
    apiPrefix + "/GetPackagesProcesingCostMaintenanceList",
    params,
    config
  );
};

//获取成品半成品维护列表-编辑
export const ModifyPackagesProcesingCostMaintenance = (
  params,
  config = {}
) => {
  return request.post(
    apiPrefix + "/ModifyPackagesProcesingCostMaintenance",
    params,
    config
  );
};

//拉取加工编码成品半成品信息
export const PullPackagesProcesingHalfProductInfo = (
  params,
  config = {}
) => {
  return request.post(
    apiPrefix + "/PullPackagesProcesingHalfProductInfo",
    params,
    config
  );
};

//成品半成品维护列表导出
export const ExportPackagesProcesingCostMaintenanceList = (
  params,
  config = { responseType: "blob" }
) => {
  return request.post(
    apiPrefix + "/ExportPackagesProcesingCostMaintenanceList",
    params,
    config
  );
};

//获取成品半成品维护日志列表
export const GetPackagesProcesingCostMaintenanceLogList = (
  params,
  config = {}
) => {
  return request.post(
    apiPrefix + "/GetPackagesProcesingCostMaintenanceLogList",
    params,
    config
  );
};

//获取成品半成品维护列表导出
export const ExportPackagesProcesingCostMaintenanceLogList = (
  params,
  config = { responseType: "blob" }
) => {
  return request.post(
    apiPrefix + "/ExportPackagesProcesingCostMaintenanceLogList",
    params,
    config
  );
};
