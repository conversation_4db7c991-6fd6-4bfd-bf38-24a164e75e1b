import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_WmsOperation}/lable/`

//获取设备主控列表数据
export const QueryLabelMastList = (params, config = {}) => {return request.get(apiPrefix + 'QueryLabelMastList', {params:params,...config} )}

//设备主控编辑
export const EditLabelMast = (params, config = {}) => {return request.post(apiPrefix + 'EditLabelMast', params,config )}

//获取标签设备列表数据
export const QueryLabelList = (params, config = {}) => {return request.get(apiPrefix + 'QueryLabelList', {params:params,...config} )}

//标签设备编辑
export const EditLabel = (params, config = {}) => {return request.post(apiPrefix + 'EditLabel', params,config )}
