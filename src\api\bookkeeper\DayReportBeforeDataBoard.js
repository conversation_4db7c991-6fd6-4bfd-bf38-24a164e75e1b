import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/dayReport/`

//获取日报前置数据看板
export const getDayReportBeforeDataBoardWorkProgress = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDayReportBeforeDataBoardWorkProgress', params, config ) 
}

//获取任务设置
export const getDayReportBeforeDataBoardBasicSetting = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDayReportBeforeDataBoardBasicSetting', params, config ) 
}

//获取数据类型
export const getDayReportBeforeDataBoardDataType = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDayReportBeforeDataBoardDataType', params, config ) 
}

//新增/编辑RPA任务
export const addDayReportBeforeDataBoardBasicSetting = (params, config = {}) => {
    return request.post(apiPrefix + 'AddDayReportBeforeDataBoardBasicSetting', params, config ) 
}

//新增/编辑RPA任务
export const deleteDayReportBeforeDataBoardBasicSetting = (params, config = {}) => {
    return request.post(apiPrefix + 'DeleteDayReportBeforeDataBoardBasicSetting', params, config ) 
}

//7日趋势图取数
export const getDayReportBeforeDataBoardWorkProgressMap = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'GetDayReportBeforeDataBoardWorkProgressMap', params, config)
}

//编辑异常反馈
export const editErrorReason = (params, config = {}) => {
    return request.post(apiPrefix + 'EditErrorReason', params, config ) 
}

