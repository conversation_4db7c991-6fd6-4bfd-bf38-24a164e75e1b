import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/ordernegativeprofit/`

//分页查询 订单
export const getNegativeProfitOrder = (params,config ={}) =>{
    return request.post(apiPrefix+'GetNegativeProfitOrderAsync', params, config)
}

//导出 订单
export const exportNegativeProfitOrder =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportNegativeProfitOrderAsync',{params: params, ...config})
}

//分页查询 产品
export const getNegativeProfitProduct = (params,config ={}) =>{
    return request.post(apiPrefix+'GetNegativeProfitProductAsync', params, config)
}

//导出 产品
export const exportNegativeProfitProduct =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportNegativeProfitProductAsync',{params: params, ...config})
}

//分页查询 商品
export const getNegativeProfitGoods = (params,config ={}) =>{
    return request.post(apiPrefix+'GetNegativeProfitGoodsAsync', params, config)
}

//导出 商品
export const exportNegativeProfitGoods =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportNegativeProfitGoodsAsync',{params: params, ...config})
}

//分页查询 产品明细
export const getOrderDetail = (params,config ={}) =>{
    return request.post(apiPrefix+'GetOrderDetailAsync', params, config)
}

//分页查询 店铺分析图表
export const getNegativeProfitShopCharts = (params,config ={}) =>{
    return request.post(apiPrefix+'GetNegativeProfitShopChartsAsync', params, config)
}

//分页查询 运营组分析图表
export const getNegativeProfitGroupCharts = (params,config ={}) =>{
    return request.post(apiPrefix+'GetNegativeProfitGroupChartsAsync', params, config)
}
