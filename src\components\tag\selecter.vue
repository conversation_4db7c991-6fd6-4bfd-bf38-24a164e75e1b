<template>
  <popover @init="init">
    <div class="selecter">
      <el-select v-model="tagValue" disabled filterable placeholder="请选择标签" clearable collapse-tags multiple
        class="selectItem" />
    </div>
  </popover>
</template>
<script>
import popover from './popover'
export default {
  components: {
    popover
  },
  model: {
    prop: 'value',
    event: 'update'
  },
  props: {
    scene: {
      type: String,
      require: true,
      default: null
    },

    value: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      tags: [],
      tagValue: []
    }
  },
  mounted() { },
  methods: {
    close() { },
    init(filter) {
      if (filter?.filters) {
        this.tags = filter.filters
        this.tagValue = filter.filters.map(item => item.name)
      } else {
        this.tags = []
        this.tagValue = []
      }
      this.$emit('update', filter)
    }
  }
}
</script>
<style scoped lang="scss">
.selecter {
  color: #ccc;
  border-radius: 4px;
  font-size: 12px;
  transition: border-color 0.3s;
  min-width: 200px;

  .selectItem {
    width: 100%;
  }

  &.focus {
    border-color: #409eff;
  }
}

::v-deep .el-input.is-disabled .el-input__inner {
  background-color: #fff;
}
</style>
