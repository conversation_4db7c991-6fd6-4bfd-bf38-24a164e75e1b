
   import request from '@/utils/request'
   const apiPrefix = `${process.env.VUE_APP_BASE_API_Financial}/ProductCost/`
   export const pagePurchasePickUp = (params, config = {}) => {return request.get(apiPrefix + 'PagePurchasePickUpAsync', { params: params, ...config })}
   export const exportPurchasePickUp = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + `ExportPurchasePickUp`, params, config ) }

   export const pageFreightfeeUp = (params, config = {}) => {return request.get(apiPrefix + 'PageFreightfeeUpAsync', { params: params, ...config })}
   export const exportFreightfeeUp = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + `ExportFreightfeeUp`, params, config ) }

   export const pageOffLinefee = (params, config = {}) => {return request.get(apiPrefix + 'PageOffLinefeeAsync', { params: params, ...config })}
   export const pageStoreLossfee = (params, config = {}) => {return request.get(apiPrefix + 'PageStoreLossfeeAsync', { params: params, ...config })}

   export const pageShootFee = (params, config = {}) => {return request.get(apiPrefix + 'PageShootFeeAsync', { params: params, ...config })}
   export const exportShootFee = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + `ExportShootFee`, params, config ) }

   export const pageSampleFeeBx = (params, config = {}) => {return request.get(apiPrefix + 'PageSampleFeeBXAsync', { params: params, ...config })}
   export const exportSampleFeeBX = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + `ExportSampleFeeBX`, params, config ) }

   export const pageSampleFee = (params, config = {}) => {return request.get(apiPrefix + 'PageSampleFeeAsync', { params: params, ...config })}

   export const PageSampleFeeYY = (params, config = {}) => {return request.get(apiPrefix + 'PageSampleFeeYYAsync', { params: params, ...config })}
   export const exportSampleFeeYY = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + `ExportSampleFeeYY`, params, config ) }

   export const pageSampleFeeMG = (params, config = {}) => {return request.get(apiPrefix + 'PageSampleFeeMGAsync', { params: params, ...config })}
   export const exportSampleFeeMG = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + `ExportSampleFeeMG`, params, config ) }

   export const pageCompute = (params, config = {}) => {return request.get(apiPrefix + 'PageComputeAsync', { params: params, ...config })}
   export const pageProductMonthReport = (params, config = {}) => {return request.get(apiPrefix + 'PageProductMonthReportAsync', { params: params, ...config })}
   export const deleteProductCost = (params, config = {}) => {return request.post(apiPrefix + 'DeleteProductCostAsync',  params, config)}
   export const computProductCost = (params, config = {}) => {return request.post(apiPrefix + 'ComputProductCostAsync',  params, config)}

   export const importPurchasePickUp = (params, config = {}) => {return request.post(apiPrefix + 'ImportPurchasePickUpAsync', params, config)}
   export const importDingDingExamineFee = (params, config = {}) => {return request.post(apiPrefix + 'ImportDingDingExamineFeeAsync', params, config)}
   export const importShootFeeEntity = (params, config = {}) => {return request.post(apiPrefix + 'ImportShootFeeEntityAsync', params, config)}
   export const importSampleFeeBX = (params, config = {}) => {return request.post(apiPrefix + 'ImportSampleFeeBXAsync', params, config)}
   export const importSampleFeeMG = (params, config = {}) => {return request.post(apiPrefix + 'ImportSampleFeeMGAsync', params, config)}
   export const importSampleFeeYY = (params, config = {}) => {return request.post(apiPrefix + 'ImportSampleFeeYYAsync', params, config)}
   export const importFreightfee = (params, config = {}) => {return request.post(apiPrefix + 'ImportFreightfeeAsync', params, config)}
   export const importOffLinefee = (params, config = {}) => {return request.post(apiPrefix + 'ImportOffLinefeeAsync', params, config)}
   export const importStoreLossfee = (params, config = {}) => {return request.post(apiPrefix + 'ImportStoreLossfeeAsync', params, config)}


   //云仓快递费-分页查询
   export const pageExpressCompanyFeeAsync = (params, config = {}) => { return request.get(apiPrefix + 'PageExpressCompanyFeeAsync', { params: params, ...config }) }
   //云仓快递费-导出
   export const exportExpressCompanyFeeAvgAsync = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'exportExpressCompanyFeeAvgAsync', { params: params, ...config }) }
   //云仓快递费-导入
   export const importExpressCompanyFeeAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportExpressCompanyFeeAsync', params, config) }
   //云仓快递费-删除
   export const deleteExpressCompanyFeeAvgAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteExpressCompanyFeeAvgAsync', params, config) }
   //云仓快递费-新增或修改
   export const AddOrUpdateExpressCompanyFeeAvgAsync = (params, config = {}) => { return request.post(apiPrefix + 'AddOrUpdateExpressCompanyFeeAvgAsync', params, config) }

   //产品费用-采购单管理-导入
   export const importPurchaseOrderMonthAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchaseOrderMonthAsync', params, config) }
    //产品费用-采购单管理-分页查询
   export const pagePurchaseOrderMonthAsync = (params, config = {}) => { return request.get(apiPrefix + 'PagePurchaseOrderMonthAsync', { params: params, ...config }) }
   //产品费用-采购退货-导入
   export const importPurchaseOrderMonthBackAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchaseOrderMonthBackAsync', params, config) }
    //产品费用-采购退货-分页查询
   export const pagePurchaseOrderMonthBackAsync = (params, config = {}) => { return request.get(apiPrefix + 'PagePurchaseOrderMonthBackAsync', { params: params, ...config }) }
