import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OldErp}/profit/`

export const getPageList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPageList', { params, ...config })
}

export const getChatList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetChatList', params, config)
  }

export const saleAmountAnalysisChartAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'SaleAmountAnalysisChartAsync', { ...params, ...config })
}