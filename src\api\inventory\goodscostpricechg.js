import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/GoodsCostChg/`

//组合编码api

//导入
export const GetCostPriceChgDetialInfoAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetCostPriceChgDetialInfoAsync', params, config)
}


// 价格变更分页
export const getGoodsChangeListAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGoodsChangeListAsync', params, config)
}

// 价格变更分页
export const getGoodsChangeApproveLogAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGoodsChangeApproveLogAsync', params, config)
}

// 查询核价审批记录明细
export const getGoodsChangeApproveLogDetailAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGoodsChangeApproveLogDetailAsync', params, config)
}

// 查询系列编码价格变更分页
export const getStyleGoodsChangeListAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetStyleGoodsChangeListAsync', params, config)
}

//分页获取汇总数据
export const pageGetStatData = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetStatData', params, config)
}

//导出汇总数据 ExportStatData
export const exportStatData = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportStatData', params, config)
}

//导入汇总数据
export const importStatAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportStatAsync', params, config)
}

//删除汇总数据
export const deleteGoodsCostStat = (params, config = {}) => {
    return request.post(apiPrefix + 'DeleteGoodsCostStat', params, config)
}

//分页获取审批数据 PageGetApplyData
export const pageGetApplyData = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetApplyData', params, config)
}

//操作数据导出 ExportApplyData
export const exportApplyData = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportApplyData', params, config)
}

//新增、编辑 审批数据
export const mergeGoodsCostApply = (params, config = {}) => {
    return request.post(apiPrefix + 'MergeGoodsCostApply', params, config)
}

//设置核价日志失败
export const setGoodsCostApplyFailedMarker = (params, config = {}) => {
    return request.post(apiPrefix + 'SetGoodsCostApplyFailedMarker', params, config)
}

//批量设置核价日志失败
export const batchSetGoodsCostApplyFailedMarker = (params, config = {}) => {
    return request.post(apiPrefix + 'BatchSetGoodsCostApplyFailedMarker', params, config)
}

//SendApply  发起审批
export const sendApply = (params, config = {}) => {
    return request.post(apiPrefix + 'SendApply', params, config)
}

//DeleteGoodsCostApply 删除数据
export const deleteGoodsCostApply = (params, config = {}) => {
    return request.post(apiPrefix + 'DeleteGoodsCostApply', params, config)
}

//GetApplyData 获取审批数据
export const getApplyData = (params, config = {}) => {
    return request.post(apiPrefix + 'GetApplyData', params, config)
}

//GetApproveLogDetailAsync 获取审批记录明细
export const getApproveLogDetailAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetApproveLogDetailAsync', params, config)
}

//导出核价审批记录明细
export const exportGoodsChangeApproveLog = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportGoodsChangeApproveLog', params, config) }


//分页获取摘品任务 GetPickingTaskPage
export const getPickingTaskPage = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPickingTaskPage', params, config)
}

//分页获取摘品奖励 GetGoodsCostPackingTaskRewardPage
export const getGoodsCostPackingTaskRewardPage = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGoodsCostPackingTaskRewardPage', params, config)
}

//驳回任务 SetGoodsCostApplyReject
export const setGoodsCostApplyReject = (params, config = {}) => {
    return request.post(apiPrefix + 'SetGoodsCostApplyReject', params, config)
}

//获取摘品操作日志 GetGoodsCostApplyPackLogList
export const getGoodsCostApplyPackLogList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGoodsCostApplyPackLogList', params, config)
}

//获取提成配置  GetGoodsCostApplyPickingCommissionConfig
export const getGoodsCostApplyPickingCommissionConfig = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGoodsCostApplyPickingCommissionConfig', params, config)
}

//保存提成配置 SaveGoodsCostApplyPackCommissionConfig
export const saveGoodsCostApplyPackCommissionConfig = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveGoodsCostApplyPackCommissionConfig', params, config)
}

//获取有效采购 GetBrandList
export const getBrandList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetBrandList', params, config)
}
//摘品奖励导出 ExportGoodsCostPackingTaskReward
export const exportGoodsCostPackingTaskReward = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportGoodsCostPackingTaskReward', params, config)
}
//获取采购组 GetPurchaseDeptList
export const getPurchaseDeptList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseDeptList', params, config)
}

//无效 SetGoodsCostApplyInvalid
export const setGoodsCostApplyInvalid = (params, config = {}) => {
    return request.post(apiPrefix + 'SetGoodsCostApplyInvalid', params, config)
}

//无效 SetGoodsCostTaskInvalid
export const setGoodsCostTaskInvalid = (params, config = {}) => {
  return request.post(apiPrefix + 'SetGoodsCostTaskInvalid', params, config)
}

//导出摘品任务 ExportGoodsCostPickingTask
export const exportGoodsCostPickingTask = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportGoodsCostPickingTask', params, config)
}
