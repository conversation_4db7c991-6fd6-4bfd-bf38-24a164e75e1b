import request from '@/utils/request'
// import { config } from 'vue/types/umd'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/Warehouse/`

//查询供应商商品编码列表
export const getSupplierProductCodePageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSupplierProductCodePageList', params, config)
}

//新增供应商商品编码
export const addSupplierProductCodePageList = (params, config = {}) => {
    return request.post(apiPrefix + 'AddSupplierProductCodePageList', params, config)
}

//编辑单条供销商信息
export const updateSupplierProductCode = (params, config = {}) => {
    return request.post(apiPrefix + 'UpdateSupplierProductCode', params, config)
}

//删除单条供销商信息
export const deleteSupplierProductCode = (params, config = {}) => {
    return request.post(apiPrefix + 'DeleteSupplierProductCode', params, config)
}

//导入供销商-供应商关系
export const importSupplierProductCode = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportSupplierProductCode', params, config)
}

//导出供销商-供应商关系
export const exportSupplierProductCode = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportSupplierProductCode', params, config)
}