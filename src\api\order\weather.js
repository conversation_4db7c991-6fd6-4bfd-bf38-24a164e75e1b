import request from '@/utils/request'

//const apiPrefix = `http://**************:9000/yunhan-gis-click/weather/`
const apiPrefix = `${process.env.VUE_APP_JAVA_API_BLADEGATEWAY}/yunhan-gis-click/weather/`


//查询热门城市
export const getPopularCityList = (params,config ={}) =>{
    return request.post(apiPrefix+'popularCityList', params, config)
}

//查询所有省份
export const getProvinceList = (params,config ={}) =>{
    return request.post(apiPrefix+'provinceList', params, config)
}

//查询所有省份以及城市
export const getProvinceCityList = (params,config ={}) =>{
    return request.post(apiPrefix+'provinceCityList', params, config)
}

//根据省份编码查询市
export const getCityListList = (params,config ={}) =>{
    return request.post(apiPrefix+'cityList', params, config)
}

//查询天气预报数据
export const queryWeatherData = (params,config ={}) =>{
    return request.post(apiPrefix+'weatherForecastList', params, config)
}

//天气预报数据导出
export const weatherForecastExport = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'weatherForecastExport', params, config)
}

//商品波动分析-获取天气类型
export const future7DayList = (params, config = {}) => {
  return request.post(apiPrefix + 'future7DayList', params, config)
}
