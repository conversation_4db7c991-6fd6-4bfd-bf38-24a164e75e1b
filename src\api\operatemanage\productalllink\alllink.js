import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/AllLink/`
export const pageHotSaleGoodsAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsAsync', params, config) }
export const isDoHotSaleGoodsAsync = (params, config = {}) => { return request.post(apiPrefix + 'IsDoHotSaleGoodsAsync', params, config) }
export const importHotSaleGoodsAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportHotSaleGoodsAsync', params, config) }

//获取用户信息
export const getUserInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetUserInfo', { params: params, ...config }) }
//获取一个雪花ID
export const GetSnowflakeId = (params, config = {}) => { return request.get(apiPrefix + 'GetSnowflakeId', { params: params, ...config }) }
//获取 商品 GetProductByKeywords
export const GetProductByKeywords = (params, config = {}) => { return request.get(apiPrefix + 'GetProductByKeywords', { params: params, ...config }) }

export const getAllLinkPlantformsAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetAllLinkPlantformsAsync', { params: params, ...config }) }
export const getAllLinkCategoryNamesAsyncByParent = (params, config = {}) => { return request.get(apiPrefix + 'GetAllLinkCategoryNamesAsyncByParent', { params: params, ...config }) }

//按时间范围查看商品趋势图
export const getHotSaleGoodsEchartByDateAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleGoodsEchartByDateAsync', { params: params, ...config }) }


//分页查询已选品
export const pageHotSaleGoodsChooseAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsChooseAsync', params, config) }

//获取产品信息
export const getProductInfo = (id, config = {}) => {
    return request.get(apiPrefix + `GetProductInfo?productId=${id}`, { params: {}, ...config })
}
//获取时间范围内的趋势图
export const getProductSalesTrendByDate = (params, config = {}) => { return request.get(apiPrefix + 'GetProductSalesTrendByDate', { params: params, ...config }) }
//申请查询商品详情
export const goodsInfoQueryReqAsync = (params, config = {}) => { return request.get(apiPrefix + 'GoodsInfoQueryReqAsync', { params: params, ...config }) }


//获取所有 待查询商品记录
export const getAllHotGoodsWaitQueryAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetAllHotGoodsWaitQueryAsync', { params: params, ...config }) }

//申请查询参谋数据
export const reqGoodsCmRefInfoQueryAsync = (params, config = {}) => { return request.get(apiPrefix + 'ReqGoodsCmRefInfoQueryAsync', { params: params, ...config }) }

//查看具体竞品参谋数据
export const getHotGoodsCmRefInfoAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetHotGoodsCmRefInfoAsync', { params: params, ...config }) }



//申请竞品SKU
export const applyCompeteGoodSKU = (params, config = {}) => { return request.get(apiPrefix + 'ApplyCompeteGoodSKU', { params: params, ...config }) }
//手动添加SKU数据
export const addSkuListByUser = (params, config = {}) => { return request.post(apiPrefix + 'AddSkuListByUser', params, config) }

//获取已选品的竞品SKU数据
export const getHotSaleGoodsChooseSkuData = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleGoodsChooseSkuData', { params: params, ...config }) }
//保存已选品的竞品SKU数据
export const saveHotSaleGoodsChooseSkuData = (params, config = {}) => { return request.post(apiPrefix + 'SaveHotSaleGoodsChooseSkuData', params, config) }
//竞品参谋数据-拼多多
export const getHotGoodsCmRefInfoPddData = (params, config = {}) => { return request.get(apiPrefix + 'GetHotGoodsCmRefInfoPddData', { params: params, ...config }) }

//获取竞品操作日志
export const getHotSaleGoodsChooseLog = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleGoodsChooseLog', { params: params, ...config }) }

//根据id或标题获取竞品
export const getHotSaleGoodsByFilter = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleGoodsByFilter', { params: params, ...config }) }

//竞品归档
export const SealCloseChooseAsync = (params, config = {}) => { return request.post(apiPrefix + 'SealCloseChooseAsync', params, config) }

//竞品-转让运营
export const tranOfHotSaleGoodsChoose = (params, config = {}) => { return request.get(apiPrefix + 'TranOfHotSaleGoodsChoose', { params: params, ...config }) }

//设置竞品利润状态
export const DingDingApproval_BuildGoodsDocBeforConfirm = (params, config = {}) => { return request.get(apiPrefix + 'DingDingApproval_BuildGoodsDocBeforConfirm', { params: params, ...config }) }

//获取SKU采样明细数据
export const getSkuSamplingListBySkuId = (params, config = {}) => { return request.get(apiPrefix + 'GetSkuSamplingListBySkuId', { params: params, ...config }) }

//竞品登记采样
export const registerSkuOrderAsync = (params, config = {}) => { return request.get(apiPrefix + 'RegisterSkuOrderAsync', { params: params, ...config }) }

//分页查询SKU采样数据
export const pageHotSaleGoodsChooseSkuOrderAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsChooseSkuOrderAsync', params, config) }

//获取SKU采样数据
export const getSkuOrderAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetSkuOrderAsync', { params: params, ...config }) }

//保存SKU采样数据
export const saveSkuOrderAsync = (params, config = {}) => { return request.post(apiPrefix + 'SaveSkuOrderAsync', params, config) }

//设置SKU禁用状态 SetSkuDisableState
export const setSkuDisableState = (params, config = {}) => { return request.get(apiPrefix + 'SetSkuDisableState', { params: params, ...config }) }
//设置所有SKU禁用状态 SetAllSkuDisableState
export const SetAllSkuDisableState = (params, config = {}) => { return request.get(apiPrefix + 'SetAllSkuDisableState', { params: params, ...config }) }

//竞品SKU单独登记采样 RegisterSkuOrderBySkuAsync
export const registerSkuOrderBySkuAsync = (params, config = {}) => { return request.post(apiPrefix + 'RegisterSkuOrderBySkuAsync', params, config) }

//获取选品建档数据
export const getBuildGoodsDocAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetBuildGoodsDocAsync', { params: params, ...config }) }

//获取选品建档数据
export const getBuildGoodsDocOtherAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetBuildGoodsDocOtherAsync', { params: params, ...config }) }

//保存选品建档
export const buildGoodsDocAsync = (params, config = {}) => { return request.post(apiPrefix + 'BuildGoodsDocAsync', params, config) }

//保存选品建档
export const buildGoodsDocUpdateCodeAsync = (params, config = {}) => { return request.post(apiPrefix + 'BuildGoodsDocUpdateCodeAsync', params, config) }

//选品建档-审批通过前校验
export const validateGoodsCodesAsync = (params, config = {}) => { return request.post(apiPrefix + 'ValidateGoodsCodesAsync', params, config) }

//保存选品建档-并审批通过
export const buildGoodsDocSyncJstAndDingDingPassAsync = (params, config = {}) => { return request.post(apiPrefix + 'BuildGoodsDocSyncJstAndDingDingPassAsync', params, config) }

//采购建编码钉钉审批不通过
export const buildGoodsDocDingDingNoPassAsync = (params, config = {}) => { return request.get(apiPrefix + 'BuildGoodsDocDingDingNoPassAsync', { params: params, ...config }) }

///更新建编码当前审批流程信息
export const syncBuildGoodsDocDingDingCurAuditNode = (params, config = {}) => { return request.get(apiPrefix + 'SyncBuildGoodsDocDingDingCurAuditNode', { params: params, ...config }) }

//导出建编码拒绝记录
export const exportHotSaleGoodsBuildGoodsRejectRecords = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportHotSaleGoodsBuildGoodsRejectRecords', params, config)
}

// //申请竞品档案编码
// export const applyBuildGoodsDocCodeAsync = (params, config = {}) => { return request.get(apiPrefix + 'ApplyBuildGoodsDocCodeAsync', { params: params, ...config }) }

//分页查询选品建档
export const pageHotSaleGoodsBuildGoodsAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsBuildGoodsAsync', params, config) }

//分页查询选品建档
export const pageHotSaleGoodsBuildGoodsTreeAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsBuildGoodsTreeAsync', params, config) }

//建编码驳回记录
export const GetHotSaleGoodsBuildGoodsRejectRecords = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleGoodsBuildGoodsRejectRecords', { params: params, ...config }) }


//【旧】分页查询选品建档
export const pageHotSaleGoodsBuildGoodsCodeAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsBuildGoodsCodeAsync', params, config) }
//【新】分页查询选品建档
export const pageHotSaleGoodsBuildGoodsCodeNewAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsBuildGoodsCodeNewAsync', params, config) }

//选品建档拍摄前获取数据
export const GetDataBeforeBuildGoodsMediaTask = (params, config = {}) => { return request.get(apiPrefix + 'GetDataBeforeBuildGoodsMediaTask', { params: params, ...config }) }


//选品建档发起申请
export const dingDingApproval_BuildGoodsDoc = (params, config = {}) => { return request.get(apiPrefix + 'DingDingApproval_BuildGoodsDoc', { params: params, ...config }) }


//发起SKU询价
export const EnquiryLaunchAsync = (params, config = {}) => { return request.post(apiPrefix + 'EnquiryLaunchAsync', params, config) }

//获取询价报价信息，运营
export const GetEnquiryFbInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetEnquiryFbInfo', { params: params, ...config }) }

//选品上架
export const SetOnSell = (params, config = {}) => { return request.get(apiPrefix + 'SetOnSell', { params: params, ...config }) }

//运营询价列表 PageChooseEnquiryAsync
export const pageChooseEnquiryAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageChooseEnquiryAsync', params, config) }
//运营询价统计列表 PageChooseEnquiryAsync
export const PageChooseEnquirySummaryAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageChooseEnquirySummaryAsync', params, config) }


//获取询价报价信息，采购
export const GetEnquiryInfoAndSelfFb = (params, config = {}) => { return request.get(apiPrefix + 'GetEnquiryInfoAndSelfFb', { params: params, ...config }) }
//选品报价
export const EnquiryFeedbackAsync = (params, config = {}) => { return request.post(apiPrefix + 'EnquiryFeedbackAsync', params, config) }
//采纳报价前校验 SetEnquiryFeedbackWinnerBefore
export const SetEnquiryFeedbackWinnerBefore = (params, config = {}) => { return request.get(apiPrefix + 'SetEnquiryFeedbackWinnerBefore', { params: params, ...config }) }

//询价报价统计报表
export const GetEnquiryFeedbakSummaryCharts = (params, config = {}) => { return request.post(apiPrefix + 'GetEnquiryFeedbakSummaryCharts', params, config) }

//分页查询SKU采样数据--采购模块
export const pageHotSaleGoodsChooseForSkuOrderAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsChooseForSkuOrderAsync', params, config) }

//评价任务-分页查询
export const pageCommentTaskAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageCommentTaskAsync', params, config) }
//获取单条评价
export const getCommentTaskFirstDetailByDtlId = (params, config = {}) => { return request.get(apiPrefix + 'GetCommentTaskFirstDetailByDtlId', { params: params, ...config }) }
//评价任务订单-分页查询
export const pageCommentTaskOrderAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageCommentTaskOrderAsync', params, config) }
// 获取产品
export const getCommentProductListByCode = (params, config = {}) => { return request.get(apiPrefix + 'GetCommentProductListByCode', { params: params, ...config }) }
//保存评价任务
export const saveCommentTaskAsync = (params, config = {}) => { return request.post(apiPrefix + 'SaveCommentTaskAsync', params, config) }
// 删除评价任务
export const deleteCommentTaskAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteCommentTaskAsync', { params: params, ...config }) }
// 删除评价任务-明细
export const deleteCommentTaskDtlAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteCommentTaskDtlAsync', { params: params, ...config }) }
// 发布/取消发布-评价任务
export const releaseCommentTaskAsync = (params, config = {}) => { return request.get(apiPrefix + 'ReleaseCommentTaskAsync', { params: params, ...config }) }
// 发布/取消发布-评价任务
export const sendJiuTanZi = (params, config = {}) => { return request.get(apiPrefix + 'SendJiuTanZi', { params: params, ...config }) }
// 认领-评价任务
export const receiveCommentTaskAsync = (params, config = {}) => { return request.get(apiPrefix + 'receiveCommentTaskAsync', { params: params, ...config }) }
//完成-评价任务
export const finishCommentTaskAsync = (params, config = {}) => { return request.post(apiPrefix + 'FinishCommentTaskAsync', params, config) }
//完成-评价任务-导入订单
export const importCommentTaskOrderAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportCommentTaskOrderAsync', params, config) }
//完成-评价任务-导入订单
export const deleteCommentTaskOrderAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteCommentTaskOrderAsync', { params: params, ...config }) }
//完成-评价任务-下载
export const exportCommentTaskAsync = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportCommentTaskAsync', { params: params, ...config }) }
// 开启或关闭同步
export const openCloseCommentTaskDtlSync = (params, config = {}) => { return request.get(apiPrefix + 'OpenCloseCommentTaskDtlSync', { params: params, ...config }) }
//获取天猫淘宝主图
export const getTaoBaoOrTmallMainImage = (params, config = {}) => { return request.get(apiPrefix + 'GetTaoBaoOrTmallMainImage', { params: params, ...config }) }
//采纳评价
export const adoptCommentTaskDetail = (params, config = {}) => { return request.get(apiPrefix + 'AdoptCommentTaskDetail', { params: params, ...config }) }
//备用评价
export const spareCommentTaskDetail = (params, config = {}) => { return request.get(apiPrefix + 'SpareCommentTaskDetail', { params: params, ...config }) }
//获取下载进度
export const getCommentTaskDownloadProgressAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetCommentTaskDownloadProgressAsync', { params: params, ...config }) }
//获取下载进度
export const getCommentTaskDownloadProgress2Async = (params, config = {}) => { return request.post(apiPrefix + 'GetCommentTaskDownloadProgress2Async', params, config) }
//只修改采纳评价
export const updateCommentTaskDetail = (params, config = {}) => { return request.post(apiPrefix + 'UpdateCommentTaskDetail', params, config) }


//选品统计
export const GetChooseSummaryCharts = (params, config = {}) => { return request.post(apiPrefix + 'GetChooseSummaryCharts', params, config) }
//选品统计报表 明细导出
export const exportChooseSummaryChartsData = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'exportChooseSummaryChartsData', params, config) }




export const getShowJstSkuCode = (params, config = {}) => { return request.get(apiPrefix + 'GetShowJstSkuCode', { params: params, ...config }) }


// 生成采购计划
export const GeneratePurchasePlanByBuildDoc = (params, config = {}) => { return request.get(apiPrefix + 'GeneratePurchasePlanByBuildDoc', { params: params, ...config }) }
// 热销-采购计划列表
export const PageHotPurchasePlanAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotPurchasePlanAsync', params, config) }
// 热销-采购计划 分配人操作日志
export const getHotPurchasePlanDistributeLogAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetHotPurchasePlanDistributeLogAsync', params, config) }

// 自动分配采购设置
export const getPurchasePlanAutoSetList = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchasePlanAutoSetList', { params: params, ...config }) }
// 自动分配采购设置-其他指标
export const getPurchasePlanAutoSetOtherList = (params, config = {}) => { return request.get(apiPrefix + 'GetPurchasePlanAutoSetOtherList', { params: params, ...config }) }
// 热销-自动分配采购设置
export const savePurchasePlanAutoSetList = (params, config = {}) => { return request.post(apiPrefix + 'SavePurchasePlanAutoSetList', params, config) }
// 热销-自动分配采购设置
export const onekeyPurchasePlanAutoSetList = (params, config = {}) => { return request.post(apiPrefix + 'OnekeyPurchasePlanAutoSetList', params, config) }

// 热销-采购计划明细
export const GetHotPurchasePlanDtlAsnyc = (params, config = {}) => { return request.get(apiPrefix + 'GetHotPurchasePlanDtlAsnyc', { params: params, ...config }) }
// 热销-保存建编码采购计划
export const SaveHotPurchasePlanDtlAsnyc = (params, config = {}) => { return request.post(apiPrefix + 'SaveHotPurchasePlanDtlAsnyc', params, config) }
// 热销-分配采购
export const SetHotPurchasePlanBrand = (params, config = {}) => { return request.post(apiPrefix + 'SetHotPurchasePlanBrand', params, config ) }
// 热销-生成采购单
export const GeneratePurchaseOrder = (params, config = {}) => { return request.get(apiPrefix + 'GeneratePurchaseOrder', { params: params, ...config }) }
// 热销-采购计划进度列表
export const GetPurchasePlanProcessRateList = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchasePlanProcessRateList', params, config) }

//获取进货单数量
export const validateStockInApplyCount = (params, config = {}) => { return request.get(apiPrefix + 'ValidateStockInApplyCount', { params: params, ...config }) }

//获取进货单数据
export const getStockInApplyInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetStockInApplyInfo', { params: params, ...config }) }

//保存进货单数据
export const saveStockInApply = (params, config = {}) => { return request.post(apiPrefix + 'SaveStockInApply', params, config) }

//获取进货单列表
export const getStockInApplyList = (params, config = {}) => { return request.get(apiPrefix + 'GetStockInApplyList', { params: params, ...config }) }

//删除进货单
export const delStockInApply = (params, config = {}) => { return request.get(apiPrefix + 'DelStockInApply', { params: params, ...config }) }

//采购人员驳回运营生成采购计划让运营重新生成采购计划
export const rejectPurchasePlanGoNewPlan = (params, config = {}) => { return request.get(apiPrefix + 'RejectPurchasePlanGoNewPlan', { params: params, ...config }) }




export const hotSaleBranPushStatusKeyValue = (params, config = {}) => { return request.get(apiPrefix + 'HotSaleBranPushStatusKeyValue', { params: params, ...config }) }
//获取采购推新
export const getHotSaleBrandPushById = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleBrandPushById', { params: params, ...config }) }
//获取采购推新分页查询
export const getHotSaleBrandPushPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushPageList', params, config) }
//获取采购推新毛三趋势图
export const getHotSaleBrandPushProfit3Map = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleBrandPushProfit3Map', { params: params, ...config }) }
//导入采购推新
export const importHotSaleBrandPushAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportHotSaleBrandPushAsync', params, config) }
//编辑采购推新
export const updateHotSaleBrandPush = (params, config = {}) => { return request.post(apiPrefix + 'UpdateHotSaleBrandPush', params, config) }
//删除并记录日志
export const deleteHotSaleBrandPush = (params, config = {}) => { return request.get(apiPrefix + 'DeleteHotSaleBrandPush', { params: params, ...config }) }
//获取采购推新编辑日志分页查询
export const getHotSaleBrandPushEditLogPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushEditLogPageList', params, config) }
//添加到已选品
export const addChoose = (params, config = {}) => { return request.post(apiPrefix + 'AddChoose', params, config) }
//获取采购推新编辑日志分页查询
export const getHotSaleBrandPushAddChooseLogPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushAddChooseLogPageList', params, config) }

//比样列表
export const getContrastSampleList = (params, config = {}) => { return request.post(apiPrefix + 'GetContrastSampleList', params, config) }

//比样明细
export const getContrastSampleDetail = (params, config = {}) => { return request.get(apiPrefix + 'GetContrastSampleDetail', { params: params, ...config }) }

//新增比样
export const addContrastSample = (params, config = {}) => { return request.post(apiPrefix + 'AddContrastSample', params, config) }

//编辑比样
export const editContrastSample = (params, config = {}) => { return request.post(apiPrefix + 'EditContrastSample', params, config) }

//完成比样
export const completeContrastSample = (params, config = {}) => { return request.get(apiPrefix + 'CompleteContrastSample', { params: params, ...config }) }

//通知比样运营/采购/自定义
export const contrastSampleSendBrandOrGroup = (params, config = {}) => { return request.post(apiPrefix + 'ContrastSampleSendBrandOrGroup', params, config) }

//比样沟通日志信息
export const contrastSampleCommunicateList = (params, config = {}) => { return request.get(apiPrefix + 'ContrastSampleCommunicateList', { params: params, ...config }) }

//比样新增沟通日志
export const addContrastSampleCommunicate = (params, config = {}) => { return request.post(apiPrefix + 'AddContrastSampleCommunicate', params, config) }

//获取当前用户
export const getCurUser = (params, config = {}) => { return request.get(apiPrefix + 'GetCurUser', { params: params, ...config }) }

//获取可沟通通知人
export const getSendUserList = (params, config = {}) => { return request.get(apiPrefix + 'GetSendUserList', { params: params, ...config }) }

//获取采购专员
export const getBrandUserList = (params, config = {}) => { return request.get(apiPrefix + 'GetBrandUserList', { params: params, ...config }) }

//获取采购专员
export const getDirectorList = (params, config = {}) => { return request.get(apiPrefix + 'GetDirectorList', { params: params, ...config }) }

//比样删除
export const delContrastSample = (params, config = {}) => { return request.delete(apiPrefix + 'DelContrastSample', { params: params, ...config }) }

//分页查询新品出审
export const pageHotSaleGoodsFirstApproveAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsFirstApproveAsync', params, config) }

//导出查询新品初审
export const exportHotSaleGoodsFirstApproveAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportHotSaleGoodsFirstApproveAsync', params, config) }

//设置
export const setHotSaleGoodsFirstApproveAutoTime = (params, config = {}) => { return request.post(apiPrefix + `SetHotSaleGoodsFirstApproveAutoTime?hourInt=${params}`) }

//设置已核
export const setHotSaleGoodsFirstApproveYH = (params, config = {}) => { return request.post(apiPrefix + `SetHotSaleGoodsFirstApproveYH?id=${params.id}&supplierId=${params.supplierId}`) }

//获取修改价格信息
export const getHotSaleGoodsFirstApproveById = (params, config = {}) => { return request.post(apiPrefix + `GetHotSaleGoodsFirstApproveById?id=${params.id}&supplierId=${params.supplierId}`) }

//修改价格信息,发起钉钉审批
export const saveHotSaleGoodsFirstApprove = (params, config = {}) => { return request.post(apiPrefix + 'SaveHotSaleGoodsFirstApprove', params, config) }

//获取第一次设置的时间
export const getSetHotSaleGoodsFirstApproveAutoTime = (params, config = {}) => { return request.post(apiPrefix + 'GetSetHotSaleGoodsFirstApproveAutoTime', params, config) }

//选品导出
export const exportHotSaleGoodsChooseAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportHotSaleGoodsChooseAsync', params, config) }

//运营新品-新品进货-操作列新增返回上一步
export const rollBackHotPurchasePlan = (params, config = {}) => { return request.post(apiPrefix + 'RollBackHotPurchasePlan',  params, config ) }





// 新品初核 自动分配核价-人员列表
export const getGenerateFirstAutoSetList = (params, config = {}) => { return request.get(apiPrefix + 'GetGenerateFirstAutoSetList', { params: params, ...config }) }
// 新品初核 自动分配核价-保存
export const saveGenerateFirstAutoSetList = (params, config = {}) => { return request.post(apiPrefix + 'SaveGenerateFirstAutoSetList', params, config) }
// 新品初核 自动分配核价-一键分配
export const onekeyGenerateFirstAutoSetList = (params, config = {}) => { return request.post(apiPrefix + 'OnekeyGenerateFirstAutoSetList', params, config) }
// 自动分配核价-单个
export const setHotyGenerateFirstAssignedSingle = (params, config = {}) => { return request.post(apiPrefix + 'SetHotyGenerateFirstAssignedSingle', params, config) }

//多新品采购计划生成采购单 BatchPlanCreatePurchaseOrder
export const batchPlanCreatePurchaseOrder = (params, config = {}) => { return request.post(apiPrefix + 'BatchPlanCreatePurchaseOrder', params, config) }

//导出商品明细-新品初审
export const exportGoodsDetail_GoodsFirstApproveAsync = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'ExportGoodsDetail_GoodsFirstApproveAsync', params, config) }

export const  rejectHotSaleGoodsFirstApprove = (params, config = {}) => { return request.post(apiPrefix + 'RejectHotSaleGoodsFirstApprove', params, config) }

//采购推新(新)-列表趋势图-获取列表页数据
export const getHotSaleBrandPushNewGoodsPage = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewGoodsPage', params, config) }

//采购推新(新)-列表趋势图-列表页删除
export const setHotSaleBrandPushNewGoodsDel = (params, config = {}) => { return request.post(apiPrefix + 'SetHotSaleBrandPushNewGoodsDel', params, config) }

//批量导入选品建档明细商品
export const importHotSaleGoodsBuildGoodsDtl = (params, config = {}) => { return request.post(apiPrefix + 'ImportHotSaleGoodsBuildGoodsDtl', params, config) }

//采购推新(新)-列表趋势图-获取商品编码
export const getDelGoodsCode = (params, config = {}) => { return request.post(apiPrefix + 'GetDelGoodsCode', params, config) }

//采购推新(新)-列表趋势图-新增保存
export const addGoodsCodeToBrandPushNew = (params, config = {}) => { return request.post(apiPrefix + 'AddGoodsCodeToBrandPushNew', params, config) }



//购推新报表获取下拉
export const GetHotSaleBrandPushNewReportUsers = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleBrandPushNewReportUsers', { params: params, ...config }) }
//获取采购推新报表分页查询
export const GetHotSaleBrandPushNewReport = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewReport', params, config) }
//获取采购推新报表分页查询
export const GetHotSaleBrandPushNewReportChartData = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewReportChartData', params, config) }
//获取采购推新报表分页查询-推品弹窗
export const GetHotSaleBrandPushNewReport1 = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewReport1', params, config) }
//获取采购推新报表分页查询-选中弹窗
export const GetHotSaleBrandPushNewReport2 = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewReport2', params, config) }
//获取采购推新报表分页查询-建编码弹窗
export const GetHotSaleBrandPushNewReport3 = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewReport3', params, config) }
//获取采购推新报表分页查询-被归档弹窗
export const GetHotSaleBrandPushNewReport4 = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewReport4', params, config) }
//获取采购推新报表分页查询-销售/进货弹窗
export const GetHotSaleBrandPushNewReport5 = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewReport5', params, config) }
//建编码作废采购单重新匹配采购单
export const GetHotSaleBrandPushNewReport6 = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewReport6', params, config) }


//获取采购推新报表分页查询
export const GetHotSaleBrandPushNewReport_Sj = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewReport_Sj', params, config) }








//获取特殊采购分配数据 GetSpecialBrandAllot
export const getSpecialBrandAllot = (params, config = {}) => { return request.post(apiPrefix + 'GetSpecialBrandAllot', params, config) }

//保存特殊采购分配数据 SaveSpecialBrandAllout
export const saveSpecialBrandAllout = (params, config = {}) => { return request.post(apiPrefix + 'SaveSpecialBrandAllout', params, config) }

//获取特殊计划列表 GetAutoSetSpecialPlan
export const getAutoSetSpecialPlan = (params, config = {}) => { return request.post(apiPrefix + 'GetAutoSetSpecialPlan', params, config) }

//保存特殊采购分配计划 SaveAutoSetSpecialPlan
export const saveAutoSetSpecialPlan = (params, config = {}) => { return request.post(apiPrefix + 'SaveAutoSetSpecialPlan', params, config) }

//删除分配计划 DelAutoSetSpecialPlan
export const delAutoSetSpecialPlan = (params, config = {}) => { return request.post(apiPrefix + 'DelAutoSetSpecialPlan', params, config) }



//获取采购推新运营业绩报表
export const GetHotSaleBrandPushNewChoosePageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewChoosePageList', params, config) }
//获取采购推新运营业绩报表-趋势图
export const GetHotSaleBrandPushNewChooseChartData = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewChooseChartData', params, config) }
//获取采购推新运营业绩报表-选品次数趋势图-平台占比图
export const GetHotSaleBrandPushNewChooseChartData2 = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewChooseChartData2', params, config) }



//获取采购推新运营业绩报表弹窗明细
export const GetHotSaleBrandPushNewChooseDtlPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewChooseDtlPageList', params, config) }
//购推新报表运营业绩获取下拉
export const GetHotSaleBrandPushNewChooseSearch = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleBrandPushNewChooseSearch', { params: params, ...config }) }
//获取采购推新运营业绩报表弹窗明细
export const GetHotSaleBrandPushNewChoose2PageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewChoose2PageList', params, config) }
export const GetHotSaleBrandPushNewChoose2ChartData = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewChoose2ChartData', params, config) }
export const GetHotSaleBrandPushNewChoose2Dtl1PageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewChoose2Dtl1PageList', params, config) }


//采购推新销量报表
export const GetHotSaleBrandPushNewSalePageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewSalePageList', params, config) }
export const GetHotSaleBrandPushNewSaleFsPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewSaleFsPageList', params, config) }
export const GetHotSaleBrandPushNewSaleFsDtlPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewSaleFsDtlPageList', params, config) }
//采购推新销量报表-款式销量
export const GetHotSaleBrandPushNewSaleStylePageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewSaleStylePageList', params, config) }
//采购推新销量报表-款式销量-万单趋势图
export const GetHotSaleBrandPushNewSaleStyleChartData = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewSaleStyleChartData', params, config) }
//采购推新销量报表-款式单量
export const GetHotSaleBrandPushNewSaleStylePageList2= (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewSaleStylePageList2', params, config) }
//采购推新销量报表-款式销量-单量趋势图
export const GetHotSaleBrandPushNewSaleStyleChartData2 = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewSaleStyleChartData2', params, config) }
//采购推新销量报表-明细销量
export const GetHotSaleBrandPushNewSaleDtlPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewSaleDtlPageList', params, config) }
//采购推新销量报表-明细销量-导出
export const ExportHotSaleBrandPushNewSaleDtlList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportHotSaleBrandPushNewSaleDtlList', params, config) }
//采购推新销量报表-明细销量趋势图-月
export const GetHotSaleBrandPushNewSaleDtlChartData = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewSaleDtlChartData', params, config) }
//采购推新销量报表-明细销量趋势图-日
export const GetHotSaleBrandPushNewSaleDtlChartData2 = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewSaleDtlChartData2', params, config) }
//采购推新销量报表-明细销量
export const SetHotSaleBrandPushNewSaleEnable = (params, config = {}) => { return request.post(apiPrefix + 'SetHotSaleBrandPushNewSaleEnable', params, config) }
//采购推新销量报表-获取被删除的款式编码
export const GetHotSaleBrandPushNewSaleDelEnable = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleBrandPushNewSaleDelEnable', { params: params, ...config }) }
//采购推新销量报表-明细销量-平台占比
export const GetHotSaleBrandPushNewSaleDtlPlarformList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewSaleDtlPlarformList', params, config) }
//采购推新销量报表-明细销量-平台占比
export const GetHotSaleBrandPushNewSaleDtlPlarformList2 = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewSaleDtlPlarformList2', params, config) }
//万单存档，存档后下个月开始就不会统计了万单了
export const HotSaleBrandPushNewSaleStyleCunDang = (params, config = {}) => { return request.post(apiPrefix + 'HotSaleBrandPushNewSaleStyleCunDang', params, config) }
//单量存档，存档后下个月开始就不会统计了此款式
export const HotSaleBrandPushNewSaleStyleCunDang4 = (params, config = {}) => { return request.post(apiPrefix + 'HotSaleBrandPushNewSaleStyleCunDang4', params, config) }

//采购推新-运营建编码报表
export const GetHotSaleBuildDocReportDataPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBuildDocReportDataPageList', params, config) }
//获取采购推新运营建编码界面下拉框数据源
export const GetHotSaleBuildDocReportSearch = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleBuildDocReportSearch', { params: params, ...config }) }
//采购推新-运营建编码报表-已创建系列编码数量弹窗
export const GetHotSaleBuildDocReportDataDtl1PageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBuildDocReportDataDtl1PageList', params, config) }
//采购推新-运营建编码报表-有销量已进货弹窗
export const GetHotSaleBuildDocReportDataDtl2PageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBuildDocReportDataDtl2PageList', params, config) }




//获取Tume组织下的所有人
export const GetHotSaleBrandPushNewKJTemuUsers = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleBrandPushNewKJTemuUsers', { params: params, ...config }) }

//跨境推品人
export const GetHotSaleBrandPushNewKJPushUsers = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleBrandPushNewKJPushUsers', { params: params, ...config }) }

//跨境运营选品-分页查询
export const GetHotSaleBrandPushNewKJSelPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewKJSelPageList', params, config) }

//跨境运营选品ById
export const GetHotSaleBrandPushNewKJSelById = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleBrandPushNewKJSelById', { params: params, ...config }) }
//跨境运营选品ById
export const GetHotSaleBrandPushNewKJSelDtlById = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleBrandPushNewKJSelDtlById', { params: params, ...config }) }

//跨境运营选品-认领/取消认领
export const SetHotSaleBrandPushNewKJSelPushNewUser = (params, config = {}) => { return request.get(apiPrefix + 'SetHotSaleBrandPushNewKJSelPushNewUser', { params: params, ...config }) }

//推新前校验
export const SaveHotSaleBrandPushNewCheck = (params, config = {}) => { return request.get(apiPrefix + 'SaveHotSaleBrandPushNewCheck', { params: params, ...config }) }

//跨境运营选品-保存到采购推新模块去
export const SaveHotSaleBrandPushNewByKJSel = (params, config = {}) => { return request.post(apiPrefix + 'SaveHotSaleBrandPushNewByKJSel', params, config) }

//跨境运营选品日志-分页查询
export const GetHotSaleBrandPushNewKJSelLogPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewKJSelLogPageList', params, config) }




//采购推新销量报表-明细销量
export const GetHotSaleBrandPushNewSaleDtlPageList2 = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewSaleDtlPageList2', params, config) }
export const ExportHotSaleBrandPushNewSaleDtlList2 = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportHotSaleBrandPushNewSaleDtlList2', params, config)
}
//设置推新人款式维度禁用启用，校验住的全部跳过
export const SetHotSaleBrandPushNewSaleEnable2 = (params, config = {}) => { return request.post(apiPrefix + 'SetHotSaleBrandPushNewSaleEnable2', params, config) }
//万单存档，存档后下个月开始就不会统计了万单了，校验跳过版
export const HotSaleBrandPushNewSaleStyleCunDang2 = (params, config = {}) => { return request.post(apiPrefix + 'HotSaleBrandPushNewSaleStyleCunDang2', params, config) }


//运营询价
//分页获取运营询价
export const getOperationalInquiryPage = (params, config = {}) => { return request.post(apiPrefix + 'GetOperationalInquiryPage', params, config) }
//导出运营询价
export const exportOperationalInquiry = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportOperationalInquiry', params, config) }

//保存运营询价
export const saveOperationalInquiry = (params, config = {}) => { return request.post(apiPrefix + 'SaveOperationalInquiry', params, config) }
//确认运营询价
export const confirmOperationalInquiry = (params, config = {}) => { return request.post(apiPrefix + 'ConfirmOperationalInquiry', params, config) }
//删除运营询价
export const delOperationalInquiry = (params, config = {}) => { return request.post(apiPrefix + 'DelOperationalInquiry', params, config) }
//驳回运营询价
export const rejectOperationalInquiry = (params, config = {}) => { return request.post(apiPrefix + 'RejectOperationalInquiry', params, config) }

//获取公司任何人员下级(入参为查询人的ddUserId)
export const getEmployees = (params, config = {}) => { return request.get(apiPrefix + 'GetEmployees', { params: params, ...config }) }

////获取仓库品牌对应关系 GetWarehouseBrandCorrespondingNexusList
export const getWarehouseBrandCorrespondingNexusList = (params, config = {}) => { return request.post(apiPrefix + 'GetWarehouseBrandCorrespondingNexusList',  params, config ) }

//保存仓库品牌对应关系 SaveWarehouseBrandCorrespondingNexus
export const saveWarehouseBrandCorrespondingNexus = (params, config = {}) => { return request.post(apiPrefix + 'SaveWarehouseBrandCorrespondingNexus', params, config) }








//获取采购推新库存资金
export const GetHotSaleBrandPushNewKczjData = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleBrandPushNewKczjData', { params: params, ...config }) }
//分页获取采购推新库存资金
export const GetHotSaleBrandPushNewKczjPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewKczjPageList', params, config) }
//采购推新库存资金柱状图
export const GetHotSaleBrandPushNewKczjChart1 = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewKczjChart1', params, config) }
//采购推新库平台库存资金饼图
export const GetHotSaleBrandPushNewKczjChart2 = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewKczjChart2', params, config) }
//采购推新库平台库存资金热力图
export const GetHotSaleBrandPushNewKczjChart3 = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewKczjChart3', params, config) }
//导出采购推新库存资金明细
export const ExportHotSaleBrandPushNewKczjList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportHotSaleBrandPushNewKczjList', params, config) }

///修正建编码进货数据
export const syncBuildGoodsGeneratePurchasePlan = (params, config = {}) => { return request.get(apiPrefix + 'SyncBuildGoodsGeneratePurchasePlan', { params: params, ...config }) }



