import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Kjerp}/crossborder/`
// 跨境分销销售明细：导出 
export const ImportCodeSalesThemeAnalysis_KJ_DistributionAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportCodeSalesThemeAnalysis_KJ_DistributionAsync', params, config) }

// 跨境分销销售明细：导入 
export const CodeSalesThemeAnalysis_KJ_Distribution_Export = (params, config = {}) => { return request.post(apiPrefix + 'CodeSalesThemeAnalysis_KJ_Distribution_Export', params, config) }

// 跨境分销销售明细：查询 
export const GetCodeSalesThemeAnalysis_KJ_DistributionPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetCodeSalesThemeAnalysis_KJ_DistributionPageList', params, config) }

// SHEIN-半托销售明细导入
export const ImportCodeSalesThemeAnalysis_SheIn_BanTuoAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportCodeSalesThemeAnalysis_SheIn_BanTuoAsync', params, config) }

// SHEIN-半托销售明细查询
export const GetCodeSalesThemeAnalysis_SheIn_BanTuoPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetCodeSalesThemeAnalysis_SheIn_BanTuoPageList', params, config) }

// SHEIN-半托销售明细导出
export const CodeSalesThemeAnalysis_SheIn_BanTuo_Export = (params, config = {}) => { return request.post(apiPrefix + 'CodeSalesThemeAnalysis_SheIn_BanTuo_Export', params, config) }

// SHEIN-半托售后明细导入
export const ImportAfterSaleDetails_Ban_SheInAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportAfterSaleDetails_Ban_SheInAsync', params, config) }

// SHEIN-半托售后明细查询
export const GetAfterSaleDetails_Ban_SheInPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetAfterSaleDetails_Ban_SheInPageList', params, config) }

// SHEIN-半托售后明细导出
export const AfterSaleDetails_Ban_SheIn_Export = (params, config = {}) => { return request.post(apiPrefix + 'AfterSaleDetails_Ban_SheIn_Export', params, config) }

// SHEIN-半托账单费用导入
export const ImportBillingCharge_SheIn_BanTuoAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportBillingCharge_SheIn_BanTuoAsync', params, config) }

// SHEIN-半托账单费用查询
export const GetBillingCharge_SheIn_BanTuoPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetBillingCharge_SheIn_BanTuoPageList', params, config) }

// SHEIN-半托账单费用导出
export const CodeBillingCharge_SheIn_BanTuo_Export = (params, config = {}) => { return request.post(apiPrefix + 'CodeBillingCharge_SheIn_BanTuo_Export', params, config) }

//SheIn半托账单费用 账单类型-界面下拉框
export const GetBillTypeSheIn_BanTuo =(params,config) =>{return request.get(apiPrefix + 'GetBillTypeSheIn_BanTuo',{ params: params, ...config })}

