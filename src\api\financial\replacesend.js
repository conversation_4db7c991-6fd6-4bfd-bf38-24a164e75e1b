
import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Financial}/ReplaceSend/`

export const pageReplaceSend = (params, config = {}) => {
   return request.get(apiPrefix + 'PageReplaceSendAsync', { params: params, ...config })}

export const importReplaceSend = (params, config = {}) => {
   return request.post(apiPrefix + 'ImportReplaceSendAsync', params, config)}

export const queryReplaceSendDailyAnalysis = (params, config = {}) => {
   return request.get(apiPrefix + 'QueryReplaceSendDailyAnalysisAsync', { params, ...config }) }

// 代发成本差导入   
export const importNewReplaceSend = (params, config = {}) => {
   return request.post(apiPrefix + 'ImportNewReplaceSendAsync', params, config)}

//代发成本差精准导入
export const importReduceNewReplaceSend = (params, config = {}) => {
      return request.post(apiPrefix + 'ImportReduceNewReplaceSendAsync', params, config)}

// 代发成本差分页   
export const pageReplaceSendNew = (params, config = {}) => {
   return request.get(apiPrefix + 'PageReplaceSendNewAsync', { params, ...config }) }  

// 代发成本差导出
export const exportReplaceSendNew =(params,config ={responseType: 'blob'}) =>{
   return request.get(apiPrefix + 'ExportReplaceSendNewAsync',{params: params, ...config})
}

// 代发成本差单个发货
export const sendNewReplaceSend = (params, config = {}) => {
   return request.post(apiPrefix + 'SendNewReplaceSend', params, config)}

// 代发成本差批量发货   
export const batchSendNewReplaceSend = (params, config = {}) => {
   return request.post(apiPrefix + 'BatchSendNewReplaceSend', params, config)}

// 代发成本差查看日志  
export const getReplaceSendNewLogs = (params, config = {}) => {
   return request.post(apiPrefix + 'GetReplaceSendNewLogs', params, config)}

//快递公司列表
export const getExpressCompany =(params, config = {}) => {
   return request.get(apiPrefix + 'GetExpressCompany', { params, ...config }) }

// 代发成本差更换快递公司
export const saveChangeExpressCompany = (params, config = {}) => {
   return request.post(apiPrefix + 'ChangeExpressCompany', params, config)}
