import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/processingorder/`

//获取当前用户
export const getCurUserIdAndUserName = (params, config = {}) => {
    return request.post(apiPrefix + 'GetCurUserIdAndUserNameAsync', params, config)
}
//分页查询
export const pageProcessingOrder = (params, config = {}) => {
    return request.post(apiPrefix + 'PageProcessingOrderAsync', params, config)
}

//加工单删除 
export const deleteProcessingOrderAsync = (params, config = {}) => {
    return request.delete(apiPrefix + 'DeleteProcessingOrderAsync', { params: params, ...config })
}

//新增修改加工单
export const addOrUpdateProcessReceipt = (params, config = {}) => {
    return request.post(apiPrefix + 'AddOrUpdateProcessReceiptAsync', params, config)
}

//获取加工单详情
export const getProcessReceiptById = (id, config = {}) => {
    return request.get(apiPrefix + `GetProcessReceiptByIdAsync?receiptNo=${id}`, { params: {}, ...config })
}

//获取待完成数据
export const getFinishLoadProcessReceipt = (id, config = {}) => {
    return request.get(apiPrefix + `GetFinishLoadProcessReceiptAsync?receiptNo=${id}`, { params: {}, ...config })
}

//领任务
export const receiveProcessReceipt = (params, config = {}) => {
    return request.post(apiPrefix + 'ReceiveProcessReceiptAsync', params, config)
}

//获取完成信息-分页
export const getProcessReceiptFinishPageData = (params, config = {}) => {
    return request.post(apiPrefix + 'GetProcessReceiptFinishPageData', params, config)
}
//获取完成信息-导出
// export const getProcessReceiptFinishExportData = (params, config = {}) => {
//     return request.post(apiPrefix + 'GetProcessReceiptFinishExportData', params, config)
// }
export const getProcessReceiptFinishExportData = (params, config = { responseType: 'blob' }) => {
    return request.get(apiPrefix + 'ExportProcessReceiptFinishData', { params: params, ...config })
}

//获取完成信息-全部
export const getProcessReceiptFinish = (id, config = {}) => {
    return request.get(apiPrefix + `GetProcessReceiptFinish?receiptNo=${id}`, { params: {}, ...config })
}
//获取完成信息-用户
export const getProcessReceiptUserFinish = (id, config = {}) => {
    return request.get(apiPrefix + `GetProcessReceiptUserFinish?receiptNo=${id}`, { params: {}, ...config })
}

//获取完成信息-全部
export const getProcessReceiptFinishDetail = (id, config = {}) => {
    return request.get(apiPrefix + `GetProcessReceiptFinishDetail?receiptNo=${id}`, { params: {}, ...config })
}

//获取完成信息-用户
export const getProcessReceiptUserFinishDetail = (id, config = {}) => {
    return request.get(apiPrefix + `GetProcessReceiptUserFinishDetail?receiptNo=${id}`, { params: {}, ...config })
}


//加工前校验
export const finishProcessReceiptBeforeCheck = (id, config = {}) => {
    return request.get(apiPrefix + `FinishProcessReceiptBeforeCheck?receiptNo=${id}`, { params: {}, ...config })
}

//完成加工单
export const finishProcessReceipt = (params, config = {}) => {
    return request.post(apiPrefix + 'FinishProcessReceiptAsync', params, config)
}

//归档加工单
export const passProcessReceipt = (id, config = {}) => {
    return request.get(apiPrefix + `PassProcessReceiptAsync?receiptNo=${id}`, { params: {}, ...config })
}

//关闭加工单
export const cancelProcessReceipt = (id, config = {}) => {
    return request.get(apiPrefix + `CancelProcessReceiptAsync?receiptNo=${id}`, { params: {}, ...config })
}

//管理员最高权限 帮他完成
export const adminFinishProcessReceipt = (params, config = {}) => {
    return request.post(apiPrefix + 'AdminFinishProcessReceipt', params, config)
}
//管理员最高权限 修改内容
export const adminUpdateProcessReceipt = (params, config = {}) => {
    return request.post(apiPrefix + 'AdminUpdateProcessReceipt', params, config)
}
//管理员最高权限 踢人
export const adminProcessReceiptOutUser = (params, config = {}) => { 
    return request.get(apiPrefix + 'AdminProcessReceiptOutUser', { params: params, ...config }) 
}


