import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/DistOrdShare/`

//导入分销商单量数据
export const ImportDistOrderFile = (params, config = {}) => { return request.post(apiPrefix + 'ImportDistOrdShare',  params, config ) }
//查询分销商单量数据
export const getDistOrderData = (params, config = {}) => { return request.post(apiPrefix + 'QueryDistOrdShare',  params, config ) }
//获取最近一周的数据
export const getLastDistOrderData = (params, config = {}) => { return request.get(apiPrefix + 'QueryLastDistOrdShare', {params: params, ...config} ) }
//更新分销商单量数据
export const upDateDistOrderFile = (params, config = {}) => { return request.post(apiPrefix + 'UpdateDistOrdShare',  params, config ) }