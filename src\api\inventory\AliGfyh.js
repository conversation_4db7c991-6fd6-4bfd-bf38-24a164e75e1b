import request from "@/utils/request";
const apiPrefix = `${process.env.VUE_APP_BASE_API_ImportInventory}/AliGfyh/`;

export const PageAliYanHuoGoodsInfo = (params, config = {}) =>
  request.post(apiPrefix + "PageAliYanHuoGoodsInfo", params, config);

export const InnerCheckRlt = (params, config = {}) =>
  request.post(apiPrefix + "InnerCheckRlt", params, config);

export const PageAliYanHuoGoodsInfoWaitImg = (params, config = {}) =>
    request.post(apiPrefix + "PageAliYanHuoGoodsInfoWaitImg", params, config);

export const ExportPageAliYanHuoGoodsInfo = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'ExportPageAliYanHuoGoodsInfo', params, config)
}
