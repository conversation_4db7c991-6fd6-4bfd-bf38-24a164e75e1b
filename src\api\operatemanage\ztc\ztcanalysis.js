import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/ztcanalysis/`

export const ztcAnalysisTable = (params, config = {}) => {
    return request.post(apiPrefix + 'ZTCAnalysisTableAsync', { ...params, ...config })
}

export const ztcAnalysisPerDayTable = (params, config = {}) => {
    return request.post(apiPrefix + 'ZTCAnalysisPerDayTableAsync', { ...params, ...config })
}

export const ztcAnalysisChart = (params, config = {}) => {
    return request.post(apiPrefix + 'ZTCAnalysisChartAsync', {...params, ...config})
}

export const ztcAnalysisPie = (params, config = {}) => {
    return request.post(apiPrefix + 'ZTCAnalysisPieAsync', {...params, ...config})
}

export const importGuestPiece = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportGuestPieceAsync', params, config)
}

export const getGuestPiecePageList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetGuestPiecePageList', { params, ...config })
  }