import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/operate/`

//导入退款管理数据
export const importTaoFactoryRefundData = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportTaoFactoryRefundData',  params, config )
}

// 获取退款数据
export const getRefundData = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'GetRefundData', params, config)
}

// 导出退款数据
export const exportRefundData = (params, config = { responseType: 'blob' }) => {
    params = params || {}
    return request.post(apiPrefix + 'ExportRefundData', params, config)
}

//申领申诉
export const applyRefundDataRepresent = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'ApplyRefundDataRepresent', params, config)
}

//撤销申领
export const removeRefundDataApply = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'RemoveRefundDataApply', params, config)
}

//获取ID预警
export const getIDWarning = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'GetIDWarning', params, config)
}

//导出ID预警
export const exportIDWarning = (params, config = { responseType: 'blob' }) => {
    params = params || {}
    return request.post(apiPrefix + 'ExportIDWarning', params, config)
}

//ID预警趋势图取数
export const getIDWarningMap = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'GetIDWarningMap', params, config)
}

//获取退款统计
export const getRefundStatistics = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'GetRefundStatistics', params, config)
}

//导出退款统计
export const exportRefundStatistics = (params, config = { responseType: 'blob' }) => {
    params = params || {}
    return request.post(apiPrefix + 'ExportRefundStatistics', params, config)
}

//退款统计趋势图取数
export const getRefundStatisticsMap = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'GetRefundStatisticsMap', params, config)
}

//获取申述明细
export const getRepresentationDetail = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'GetRepresentationDetail', params, config)
}

//导出申述明细
export const exportRepresentationDetail = (params, config = { responseType: 'blob' }) => {
    params = params || {}
    return request.post(apiPrefix + 'ExportRepresentationDetail', params, config)
}

//设置申述人
export const setRepresentationPerson = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'SetRepresentationPerson', params, config)
}

//文件导入-导入商家申述明细
export const importTaoFactoryShopRepresentData = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportTaoFactoryShopRepresentData',  params, config )
}

// 获取退款类型
export const getRefundType = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'GetRefundType', params, config)
}

// 获取店铺
export const getShopNameInProductEntity = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'GetShopNameInProductEntity', params, config)
}

// 获取组长
export const getLeaderNameInProductEntity = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'GetLeaderNameInProductEntity', params, config)
}

// 获取专员
export const getCommissionerNameInProductEntity = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'GetCommissionerNameInProductEntity', params, config)
}

// 获取助理
export const getAssistantNameInProductEntity = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'GetAssistantNameInProductEntity', params, config)
}

// 获取买家退款原因
export const getRefundReason = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'GetRefundReason', params, config)
}

// 获取申述人
export const getRepresentationPerson = (params, config = {}) => {
    params = params || {}
    return request.post(apiPrefix + 'GetRepresentationPerson', params, config)
}
