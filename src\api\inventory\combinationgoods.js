import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/combinationGoods/`

//组合编码api

//导入
export const importData = (params,config ={}) =>{
    return request.post(apiPrefix + 'ImportDataAsync', params, config)
}

//分页查询
export const getList = (params,config ={}) =>{
    return request.post(apiPrefix+'GetListAsync', params, config)
}

//导出
export const exportData =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportDataAsync',{params: params, ...config})
}

//增改
export const addOrUpdate = (params, config = {}) => {
    return request.post(apiPrefix + 'AddOrUpdateAsync', params, config)
}

//删除
export const deleteData = (params, config = {}) => {
    return request.delete(apiPrefix + 'DeleteAsync',{ params, ...config})
}

//删除批量
export const deleteDataBatch = (params, config = {}) => {
    return request.delete(apiPrefix + 'DeleteBatchAsync',{params, ...config})
}

//获取单个
export const getById = (id, config = {}) => {
    return request.get(apiPrefix + `GetByIdAsync?id=${id}`, {}, config)
}

//查询 明细
export const getDetailList = (params,config ={}) =>{
    return request.post(apiPrefix+'GetDetailListAsync', params, config)
}

//增改 明细
export const addOrUpdateDetail = (params, config = {}) => {
    return request.post(apiPrefix + 'AddOrUpdateDetailAsync', params, config)
}

//删除 明细
export const deleteDetail = (params, config = {}) => {
    return request.delete(apiPrefix + 'DeleteDetailAsync',{ params, ...config})
}

