<template >    
    <el-dialog :title="title" :visible.sync="dialogVisibleSyj" width="30%" 
        :close-on-click-modal="false" v-dialogDrag  append-to-body
        v-loading="false"  @closed="onClosed"  >
        <span>               
            <el-upload ref="upload2"  :auto-upload="false" :multiple="false" :limit="1" action :accept="accept" 
            :http-request="uploadFile" :on-success="uploadSuccess" v-loading="uploading" >
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitUpload">上传</el-button>

                <div slot="tip" v-if="!!tip" class="el-upload__tip">{{tip}}</div>
            </el-upload>
        </span>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisibleSyj = false">关闭</el-button>
        </span>
    </el-dialog>
    
</template>

<script>

export default {
    name:"simpleimport",
    data() {
        return {
            dialogVisibleSyj:true,
            accept:'',   
            title:'导入',  
            tip:'只能上传xlxs',
            uploading:false,
            autoClose:true,
        }
    },
    computed:{
        
    },
    created(){        
    },
    beforeMount(){        
       
    },
    mounted(){       
        let self=this;        
    },
    methods:{ 
        async uploadFile(item) {
             let self=this;      
            this.uploading=true;
            const form = new FormData();
            form.append("upfile", item.file);              
            const res =await this.upAction(form);
            this.onOk(res);
            if(res && res.success){
                if(self.autoClose)
                    self.dialogVisibleSyj=false;
            }
            this.uploading=false;
        },
        async uploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            let self=this;          
            
        },
        async onSubmitUpload() {
            this.$refs.upload2.submit()
        },   
        upAction(){
            //上传方法，外部导入
        }   ,    
        onOk(data){
            if(this.callOk && typeof this.callOk==="function")  {
                this.callOk(data);
            }
        },
        onClosed(){  
            if(this.callCancel && typeof this.callCancel==="function")  {
                this.callCancel();
            }
            this.$destroy(true);//销毁组件
            //this.$el.parentNode.removeChild(this.$el);//在父元素中移除dom元素
        }
    }
}
</script>

<style>

</style>