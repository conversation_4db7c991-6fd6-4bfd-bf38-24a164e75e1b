import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_CwManage}`

export const getAllOnlineBank = (params, config = {}) => { return request.get(apiPrefix + '/' + 'BankFlow' + '/' + 'GetAllOnlineBank', { params: params, ...config }) }

export const queryOnlineBankSet = (params, config = {}) => { return request.post(apiPrefix + '/' + 'CashierSet' + '/' + 'QueryOnlineBankSet', params, config) }

export const queryTaxData = (params, config = {}) => { return request.post(apiPrefix + '/' + 'TaxData' + '/' + 'QueryTaxData', params, config) }
