import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API}/oprationlog/`

// 操作日志
export const getOprationLogPage = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'getpage', params, config)
}

// 操作日志
export const getOprationLogPage2 = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'GetPage2', params, config)
}

// 操作日志
export const GetPageReqLogFilters = (params, config = {}) => { return request.get(apiPrefix + 'GetPageReqLogFilters', { params: params, ...config }) }

// 操作日志
export const PagePageReqLog = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'PagePageReqLog', params, config)
}

// 操作日志导出
export const exportAsync2 = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportAsync2', params, config) }

// 操作日志
export const getExceptionPage = (params, config = {}) => { return request.get(apiPrefix + 'GetExceptionPage', { params: params, ...config }) }

// 新增更新日志
export const addLogUpdate = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'AddLogUpdate', params, config)
}

// 更新日志信息
export const getUpdateLog = (params, config = {}) => {
  return request.get(apiPrefix + 'GetUpdateLogAsync', { params: params, ...config })
}

// 更新日志信息
export const getLogTimeAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'GetLogTimeAsync', { params: params, ...config })
}

// 删除日志信息
export const deleteLogUpdate = (params, config = {}) => {
  return request.put(apiPrefix + 'DeleteLogUpdate', params, config)
}

// 日志信息
export const getUpdate = (params, config = {}) => {
  return request.get(apiPrefix + 'GetUpdateAsync', { params: params, ...config })
}

export const getAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'GetAsync', { params: params, ...config })
}

// http://**************:8002/api/admin/oprationlog/GetPurchaseLogOneAsync
export const getPurchaseLogOneAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'GetPurchaseLogOneAsync', { params: params, ...config })
}


//获取模块列表
export const getPurchaseLogModuleAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'GetPurchaseLogModuleAsync', { params: params, ...config })
}


//添加列表信息
export const addPurchaseLog = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'AddPurchaseLog', params, config)
}

//删除列表信息

export const deletePurchaseLog = (params, config = {}) => {
  return request.put(apiPrefix + 'DeletePurchaseLog', params, config)
}
// //点击事件

export const getPurchaseLogAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'GetPurchaseLogAsync', { params: params, ...config })
}
//排序

export const updateSortPurchaseLogOneAsync = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'UpdateSortPurchaseLogOneAsync', params, config)
}
//添加公告点击信息
export const addPurchaseClickLog = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'AddPurchaseClickLogAsync', params, config)
}

//上传视频
export const addPurchaseUplod = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'AddPurchaseUplodAsync', params, config)
}

// 分页获取操作日志列表信息
export const pagePageReqLogList = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'PagePageReqLogList', params, config)
}

//页面访问日志导出
export const pagePageReqLogExportAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'PagePageReqLogExportAsync', params, config) }


// 设置页面开发人员信息
export const AddOperationLogAsync = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'AddOperationLogAsync', params, config)
}

//HotPageReqRecord
export const HotPageReqRecord = () => {
  return request.get(apiPrefix + 'HotPageReqRecord?appType=ERP', )
}