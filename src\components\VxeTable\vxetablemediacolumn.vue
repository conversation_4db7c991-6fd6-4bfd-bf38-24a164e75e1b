<template>
    <vxe-column  v-if="col.type=='images'"
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field+'images'"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow  :tree-node="!!col.treeNode? true:false"
                :width="col.width"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="col.align?col.align:'center'"
                >
                    <template #default="{ row }">

                        <template v-if=" row && !!row[col.prop] && row[col.prop].length>2">

                            <template v-if="row[col.prop][0]=='[' && JSON.parse(row[col.prop]).length>1">
                                <el-badge
                                    class="badgeimage20221212"
                                    :value="JSON.parse(row[col.prop]).length" style="margin-top:0px;margin-right:40px;">
                                    <el-image  :src="(row[col.prop][0]=='['?(formatImg(row[col.prop])[0].url):(formatImg(row[col.prop]) ? formatImg(row[col.prop])[0] : '' ) )"
                                    class="images20221212"
                                    :preview-src-list="(row[col.prop][0]=='['
                            ?(()=>{
                                let tempArray=JSON.parse(row[col.prop]);
                                let tempRltArr=[];
                                tempArray.forEach(x=>tempRltArr.push(x.url));
                                return tempRltArr
                            })()
                            :(()=>{
                                return [row[col.prop]]
                            })()  )">
                                    </el-image>
                                </el-badge>
                            </template>
                            <template v-else>
                                <el-image  :src="formatImg(row[col.prop])[0].url ? formatImg(row[col.prop])[0].url : formatImg(row[col.prop])[0]"

                                class="images20221212"
                                :preview-src-list="(row[col.prop][0]=='['
                            ?(()=>{
                                let tempArray=JSON.parse(row[col.prop]);
                                let tempRltArr=[];
                                tempArray.forEach(x=>tempRltArr.push(x.url));
                                return tempRltArr
                            })()
                            :(()=>{
                                return [row[col.prop]]
                            })()  )">
                                </el-image>
                            </template>
                        </template>

                    </template>
    </vxe-column>
    <vxe-column  v-else-if="col.type=='editNumber'"
        :field="col.prop? col.prop : ('col'+ colIndex)"
        :title="col.label"
        show-overflow
        :tree-node="!!col.treeNode? true:false"
        :width="col.width"
        :min-width="col.minwidth?col.minwidth:null"
        :sortable="!!col.sortable"
        :fixed="col.fixed?col.fixed:''"
        :edit-render="{}"
        :column-config="{resizable: true}" :key="col.prop+'editNumber'" >
        <template #edit="{row}">
                <vxe-input v-model="row[col.prop]" type="float"
                :digits="col.digits?col.digits:2"
                :controls="col.controls?col.controls:false"
                :align="col.align?col.align:'left'"
                :min ="col.min?col.min:-9999999999"  :max ="col.max?col.max:9999999999"
                placeholder="请输入" ></vxe-input>
        </template>
    </vxe-column>
    <vxe-column  v-else-if="col.type=='editselect'"
        :field="col.prop? col.prop : ('col'+ colIndex)"
        :title="col.label"
        show-overflow
        :tree-node="!!col.treeNode? true:false"
        :width="col.width"
        :min-width="col.minwidth?col.minwidth:null"
        :sortable="!!col.sortable"
        :fixed="col.fixed?col.fixed:''"
        :edit-render="{}"
        :column-config="{resizable: true}" :key="col.prop+'editselect'">
            <template  #default="scope">
                <span>{{ formatSelText(scope.row[col.prop] ,col.options)}}</span>
            </template>
            <template #edit="{ row }">
                <vxe-select  v-model="row[col.prop]" transfer placeholder="请选择">
                    <vxe-option v-for="item in col.options" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
            </template>
    </vxe-column>
    <vxe-column  v-else-if="col.type=='editselectmore'"
        :field="col.prop? col.prop : ('col'+ colIndex)"
        :title="col.label"
        show-overflow
        :tree-node="!!col.treeNode? true:false"
        :width="col.width"
        :min-width="col.minwidth?col.minwidth:null"
        :sortable="!!col.sortable"
        :fixed="col.fixed?col.fixed:''"
        :edit-render="{}"
        :column-config="{resizable: true}" :key="col.prop+'editselectmore'">
            <template  #default="scope">
                <span>{{ formatSelTextMore(scope.row[col.prop] ,col.options)}}</span>
            </template>
            <template #edit="{ row }">
                <vxe-select  v-model="row[col.prop]"  multiple clearable>
                    <vxe-option v-for="(item,index) in col.options" :key="index" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
            </template>
    </vxe-column>
    <vxe-column  v-else-if="col.type=='editText'"
        :field="col.prop? col.prop : ('col'+ colIndex)"
        :title="col.label"
        show-overflow
        :tree-node="!!col.treeNode? true:false"
        :width="col.width"
        :min-width="col.minwidth?col.minwidth:null"
        :sortable="!!col.sortable"
        :fixed="col.fixed?col.fixed:''"
        :edit-render="{}"
        :column-config="{resizable: true}" :key="col.prop+'editText'">
            <template #edit="{ row }">
                <vxe-input v-model="row[col.prop]" type="text"
                    :align="col.align?col.align:'left'"
                      :maxlength ="col.maxlength?col.maxlength:5000"  placeholder="请输入" ></vxe-input>
        </template>
    </vxe-column>
    <vxe-column  v-else-if="col.type=='editDate'"
        :field="col.prop? col.prop : ('col'+ colIndex)"
        :title="col.label"
        show-overflow
        :tree-node="!!col.treeNode? true:false"
        :width="col.width"
        :min-width="col.minwidth?col.minwidth:null"
        :sortable="!!col.sortable"
        :fixed="col.fixed?col.fixed:''"
        :edit-render="{}"
        :column-config="{resizable: true}" :key="col.prop+'editDate'">
            <template #edit="{row  }">
                <vxe-input v-model="row[col.prop]" type="date"
                    :align="col.align?col.align:'left'" label-format="yyyy-MM-dd"
                    placeholder="请输入" ></vxe-input>
            </template>
    </vxe-column>
    <vxe-column  v-else-if="col.type=='split'"
        :field="col.prop? col.prop : ('col'+ colIndex)"
        title="|"
        width="15"  >
         |
    </vxe-column>
    <vxe-column  v-else
    :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
    :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
    show-overflow  :tree-node="!!col.treeNode? true:false"
    :width="col.width"
    :min-width="col.minwidth?col.minwidth:null"
    :sortable="!!col.sortable"
    :fixed="col.fixed?col.fixed:''"
    :align="(col.type && (col.type=='color' || col.type=='split' || col.type=='images' || col.type=='image'))?'center':  (col.align?col.align:'left')"
    >
        <template #default="scope">
            <span v-if="col.type=='color' || col.type=='split'" style="color: #999;">
                |
            </span>
            <template  v-else-if="col.type==='button'">
                <template v-for="(btn,btnIndex) in col.btnList" >
                    <el-link :key="btn.label" :style="''+ (btnIndex>0?'margin-left:5px;':'')"
                    v-if="(!btn.permission||(btn.permission&&checkPermission(btn.permission)))&&!(btn.hasOwnProperty('ishide')&&((typeof btn.ishide=='function'&& btn.ishide(that,scope.row)==true)||btn.ishide==true))"
                    :disabled="(btn.hasOwnProperty('display')&&((typeof btn.display=='function'&& btn.display(scope.row)==true)||btn.display==true))"
                    :type="((!!btn.type)? btn.type: 'primary')" :size="btn.size || size " :icon="btn.icon" @click="btn.handle(that,scope.row)">
                        {{btn.label}}
                    </el-link>
                </template>
            </template>
            <a v-else-if="col.type=='click'"  type="text"  show-overflow="ellipsis"  style="color:#409EFF" @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">
                {{(col.formatter && tonumfuc(col.formatter(scope.row), col.label)) || tonumfuc(scope.row[col.prop], col.label)}}
            </a>
            <div v-else-if="col.type=='html'"  v-html="col.formatter? col.formatter(scope.row): scope.row[col.prop]  "></div>
            <el-switch v-else-if="col.type=='switch'" v-model="scope.row[col.prop]" @change='col.change && col.change(scope.row,that)'></el-switch>

            <span v-else-if="col.type=='custom'||!col.type" :style="col.itemStyle && col.itemStyle(scope.row)" :size="size || btn.size" :class="col.itemClass && col.column.itemClass(scope.row)">
            {{ (()=>{
                if(col.formatter)
                    return tonumfuc(col.formatter(scope.row), col.label);
                else
                    return tonumfuc(scope.row[col.prop], col.label);
            })() }}</span>

            <template v-else-if="col.type==='UrgencyButton'">
                <template v-if="scope.row.taskUrgency==1  ">
                    <vxe-button status="danger" size="mini"   @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">{{scope.row.taskUrgencyName}}</vxe-button>
                </template>
                <template v-else-if="( scope.row.taskUrgency==2)">
                    <vxe-button status="primary" size="mini"   @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">{{scope.row.taskUrgencyName}}</vxe-button>
                </template>
                <template v-else-if="( scope.row.taskUrgency==0)">
                    <vxe-button status="warning" size="mini"   @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">{{scope.row.taskUrgencyName}}</vxe-button>
                </template>
                <template v-else>
                    <vxe-button  size="mini"   @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">{{scope.row.taskUrgencyName}}</vxe-button>
                </template>
            </template>
            <template v-else-if="col.type==='clickflag'">
                <template v-if="scope.row.markCssName=='0'  ">
                    <i  class="vxe-icon-flag-fill" style="color: #ff0101;"   @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])" ></i>
                </template>
                <template v-else>
                    <i  class="vxe-icon-flag-fill" style="color: #dcdfe6;"   @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])" ></i>
                </template>
            </template>
            <template v-else-if="col.type==='fileicon'">
                <i class="vxe-icon-file-txt"   @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])" ></i>
            </template>
            <template v-else-if="col.type==='editicon'">
                <i class="vxe-icon-ellipsis-h"    @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])" ></i>
            </template>
            <template v-else-if="col.type==='time'">
                <span style="color: #999;">
                    {{(col.formatter && col.formatter(scope.row)) || scope.row[col.prop]}}
                </span>
            </template>
            <template v-else-if="col.type==='urgencyediticon'">
                <template v-if="scope.row.isTopOldNum=='0'  ">
                    <span></span>
                </template>
                <template v-else>
                    <i  class="vxe-icon-dot" style="color: #ff0101;"></i>
                </template>
            </template>
            <template v-else-if="col.type==='tag'">
                <el-tag :type="col.tagstatus &&col.tagstatus(scope.row)" disable-transitions v-if="scope.row[col.prop]">
                        {{ (col.formatter && tonumfuc(col.formatter(scope.row), col.label)) || tonumfuc(scope.row[col.prop], col.label) }}
                </el-tag>
            </template>
        </template>
    </vxe-column>
</template>

<script>
    import { tonumfuc } from '@/utils/tonumqian.js'
    import {matchImg} from '@/utils/getCols'
    export default {
         components: {},
        props: {
            that: { type: Object, default: this },
            col: { type: Object, default: () => { } },
            colIndex:{type:Number, default: 0},
            // 表格型号：mini,medium,small
            size: { type: String, default: 'mini' },
            type: { type: String, default: 'primary' },
            descData: { type: Array, default: () => [] }
        },
        data() {
            return {
                tonumfuc,
                imgPreview: { img: "", show: false },
                showImage: false,
                imgList: [],
                ImgWith: null,
                imagedefault: require('@/assets/images/detault.jpeg'),
            }
        },
        created(){},
        async mounted(){},
        methods:{
            formatImg(img){
                return matchImg(img)
            },
            formatSelText (value ,options) {
                return  options.find(ele=>ele.value===value)?.label
            },
            retname(val){
                var newa = [];
                val.map((item)=>{
                    newa.push(item.toString());
                })
                return newa;
            },
            formatSelTextMore(value ,options) {
                var  txt = "";
                for(let num in value)
                {
                    if(num==0)
                    txt+= options.find(ele=>ele.value===value[num])?.label;
                    else
                    txt+= ","+options.find(ele=>ele.value===value[num])?.label;
                }

                return txt
            },
        }
    }
</script>
<style lang="scss" scoped>
        .vxe-table--render-default.border--default .vxe-table--header-wrapper
        {
            background-color: #fafbff;
        }
        /*斑马线颜色*/
        .vxe-table--render-default .vxe-body--row.row--stripe {
            background-color: #fafbff;
        }
        .vxe-table--render-default .vxe-body--row.row--current {
            background-color: #e5ecf5;
        }
        /*滚动条整体部分*/
        .mytable-scrollbar20221212 ::-webkit-scrollbar {
          width: 10px;
          height: 10px;
        }
        /*滚动条的轨道*/
        .mytable-scrollbar20221212 ::-webkit-scrollbar-track {
          background-color: #FFFFFF;
        }
        /*滚动条里面的小方块，能向上向下移动*/
        .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
          background-color: #bfbfbf;
          border-radius: 5px;
          border: 1px solid #F1F1F1;
          box-shadow: inset 0 0 6px rgba(0,0,0,.3);
        }
       .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
          background-color: #A8A8A8;
        }
        .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
          background-color: #787878;
        }
        /*边角，即两个滚动条的交汇处*/
       .mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
          background-color: #FFFFFF;
        }

        // 图片大小
        .mytable-scrollbar20221212  .images20221212{
          max-width: 150px;max-height: 150px;
          width:40px !important;
          height:40px  !important;
        }

        // 图片张数标记
        .mytable-scrollbar20221212 .badgeimage20221212 .el-badge__content.is-fixed{
            top:10px;
        }

        /*  工具箱位置  */
        .vxetoolbar20221212{
            position:absolute ;
            top: 10px;
            right: 5px;
            padding-top:0;
            padding-bottom:0;
            z-index: 999;
            background-color: rgb(255 255 255 / 0%);
        }

        .vxetableheadercell-left-20221216
        {
            text-align: left;
        }

        .vxetableheadercell-center-20221216
        {
            text-align: center;
        }

        .vxetableheadercell-right-20221216
        {
            text-align: right;
        }
        .vxe-icon-ellipsis-h:hover{
            color: #409EFF;
            margin-left: 2px;
            background-color: #F1F1F1;
        }
        .vxe-icon-ellipsis-h{
            color: #999;
            font-size: 15px;
        }
        .vxe-icon-file-txt:hover{
            color: #409EFF;
            margin-left: 2px;
            background-color: #F1F1F1;
            font-weight: 600;
        }
        .vxe-icon-file-txt{
            color: #999;
            font-size: 15px;
        }
        .vxetablecss{
            margin: 0;
        }
        ::v-deep span.vxe-cell--item {
            cursor: pointer !important;
        }
</style>
