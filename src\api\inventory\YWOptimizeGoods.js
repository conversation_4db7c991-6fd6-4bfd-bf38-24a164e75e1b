import request from "@/utils/request";
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/YWOptimizeGoods/`;

export const QueryYWOptimizeGoodsRecordListAsync = (params, config = {}) => {
  return request.post(
    apiPrefix + "QueryYWOptimizeGoodsRecordListAsync",
    params,
    config
  );
};

//获取运维商品明细
export const GetYWOptimizeGoodsRecord = (params, config = {}) => {
  return request.post(apiPrefix + "GetYWOptimizeGoodsRecord", params, config);
};

//保存运维商品优化明细
export const SaveYWOptimizeGoodsRecord = (params, config = {}) => {
  return request.post(apiPrefix + "SaveYWOptimizeGoodsRecord", params, config);
};

//批量保存运维优化商品明细
export const BatchSaveYWOptimizeGoodsRecord = (params, config = {}) => {
  return request.post(
    apiPrefix + "BatchSaveYWOptimizeGoodsRecord",
    params,
    config
  );
};

//批量同意
export const BatchAgreen = (params, config = {}) => {
  return request.post(apiPrefix + "BatchAgreen", params, config);
};

//批量拒绝
export const BatchReject = (params, config = {}) => {
  return request.post(apiPrefix + "BatchReject", params, config);
};

//优化进度列表
export const GetYWOptimizeProgressChangeLogList = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetYWOptimizeProgressChangeLogList",
    params,
    config
  );
};

//优化进度列表
export const GetYWOptimizeCostChangeLogList = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetYWOptimizeCostChangeLogList",
    params,
    config
  );
};

//产品标准
export const GetYWOptimizeSizeChangeLogList = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetYWOptimizeSizeChangeLogList",
    params,
    config
  );
};

//上架数据
export const GetYWOptimizeProductOptimizeRecordList = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetYWOptimizeProductOptimizeRecordList",
    params,
    config
  );
};

//查看竞品利润
export const GetYWOptimizeChooseDtlChangeLogList = (params, config = {}) => {
  return request.post(
    apiPrefix + "GetYWOptimizeChooseDtlChangeLogList",
    params,
    config
  );
};
