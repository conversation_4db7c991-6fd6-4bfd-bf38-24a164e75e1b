import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/ztcregion/`

export const getRegionPageList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetRegionPageList', { params, ...config })
}

export const getRegionCityPageList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetRegionCityPageList', { params, ...config })
}

export const importZTCRegionAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportZTCRegionAsync', params, config)
}

export const importZTCRegionCityAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportZTCRegionCityAsync', params, config)
}
export const exportdata =(params, config = {responseType: 'blob'}) => {
    return request.get(apiPrefix + 'ExportdataList', { params, ...config })
}
export const exportRegionCitydata =(params, config = {responseType: 'blob'}) => {
    return request.get(apiPrefix + 'ExportRegionCityList', { params, ...config })
}
