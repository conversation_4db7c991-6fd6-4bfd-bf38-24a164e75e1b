
   import request from '@/utils/request'
   const apiPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Detail2/`
 
   export const pageTaxesfee = (params, config = {}) => {return request.get(apiPrefix + 'PageTaxesfeeAsync', { params: params, ...config })}
   export const pageDetail2Compute = (params, config = {}) => {return request.get(apiPrefix + 'PageDetail2ComputeAsync', { params: params, ...config })}
   export const pageDetail2MonthSumfee = (params, config = {}) => {return request.get(apiPrefix + 'PageDetail2MonthSumfeeAsync', { params: params, ...config })}       
   export const computDetail2 = (params, config = {}) => {return request.post(apiPrefix + 'ComputDetail2Async',  params, config)}
 
   export const importTaxesfee = (params, config = {}) => {return request.post(apiPrefix + 'ImportTaxesfeeAsync', params, config)}

