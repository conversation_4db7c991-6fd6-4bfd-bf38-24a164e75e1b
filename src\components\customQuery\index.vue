<template>
  <div v-if="condition" class="condition">
    <div>
      <template v-if="!isRoot && (condition.field || condition.filters == null || condition.filters.length == 0)">
        <div class="itemContainer">
          <item v-model="condition" :fields="fields" @update="change" />
          <div>
            <el-button icon="el-icon-plus" circle @click="add" />
            <el-button icon="el-icon-minus" circle @click="selfRemove" />
          </div>
        </div>
      </template>
      <template v-if="isRoot||(condition.filters && condition.filters.length > 0)">
        <div :class="['group', isRoot ? 'isRoot' : '']">
          <div class="groupBody">
            <filterHeader v-model="condition" />
            <div v-for="( item, i) in condition.filters" :key="item.id">
              <CustomQuery v-model="condition.filters[i]" :index="i" :fields="fields" @del="remove(i)" @update="change" />
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import filterHeader from './filterHeader.vue'
import item from './item.vue'
export default {
  name: 'CustomQuery',
  components: {
    filterHeader, item
  },
  model: {
    prop: 'value',
    event: 'update'
  },
  props: {
    value: {
      type: Object,
      default: {}
    },
    isRoot: {
      type: Boolean,
      default: false
    },
    index: {
      type: Number,
      default: 0
    },
    fields: {
      type: Array,
      default: null
    }
  },
  data() {
    return { condition: null }
  },
  watch: {
    value: {
      handler() {
        this.valueChange()
      },
      deep: true
    }
  },
  mounted() {
    this.valueChange()
  },
  methods: {
    valueChange() {
      var value = { id: this.getId(), field: null, operator: 'Equal', value: null, logic: 'And', filters: [] }
      if (!this.value || ((!this.value.filters || this.value.filters.length === 0) && !this.value.field)) {
        this.condition = value
      } else {
        this.condition = this.value
      }
    },
    add() {
      this.condition.filters.push({ id: this.getId(), field: null, operator: 'Equal', value: null, logic: 'And', filters: [] })
      this.change()
    },
    selfRemove() {
      this.$emit('del')
    },
    remove(index) {
      this.condition.filters.splice(index, 1)
      this.change()
    },
    change() {
      if (!this.condition.operator) {
        this.condition.operator = 'Equal'
      }
      this.$emit('update', this.condition)
    },
    getId() {
      return new Date().getTime()
    }
  }
}
</script>

<style scoped lang="scss">
.itemContainer {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.group {
    margin-bottom: 10px;
    padding: 0px;
    border-radius: 10px;
    border: solid 1px #ccc;

    &.isRoot {
        margin-left: 0;
    }

    .groupBody {
        margin: 10px;
    }
}
</style>
