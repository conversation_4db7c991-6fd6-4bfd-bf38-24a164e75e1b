import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/shipperFxOrder/`

//货主分销订单-分页查询
export const getShipperFxOrderList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetShipperFxOrderList', params, config)
}
//货主分销订单-导入
export const importShipperFxOrders = (params, config = {}) => {
  return request.post(apiPrefix + 'ImportShipperFxOrders', params, config)
}
//货主汇总-销售
export const getSumShipperFxSaleInfo = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSumShipperFxSaleInfo', params, config)
}
//货主汇总-库存
export const getSumShipperFxInventoryInfo = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSumShipperFxInventoryInfo', params, config)
}
//货主汇总-昀晗/分销订单
export const getSumYunHanFxSaleInfo = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSumYunHanFxSaleInfo', params, config)
}
//货主汇总-订单趋势图
export const getSumShipperFxOrdersAnalysis = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSumShipperFxOrdersAnalysis', params, config)
}
//货主汇总-库存趋势图
export const getSumShipperFxGoodsAnalysis = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSumShipperFxGoodsAnalysis', params, config)
}

//货主汇总-下拉列表
export const getShipperFxNameList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetShipperFxNameList', params, config)
}

//货主汇总-下拉列表
export const getShipperFxWxAppUserList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetShipperFxWxAppUserList', params, config)
}

//货主汇总-下拉列表
export const updateShipperFxWxAppUserSupplierName = (params, config = {}) => {
  return request.post(apiPrefix + 'UpdateShipperFxWxAppUserSupplierName', params, config)
}

//坪效设置
export const getfatEffectSetList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetFatEffectSetList', params, config)
} 

export const savefatEffectSet = (params, config = {}) => {
  return request.post(apiPrefix + 'SaveFatEffectSet', params, config)
}  

export const delfatEffectSetById = (params, config = {}) => {
  return request.post(apiPrefix + 'DelFatEffectSetById?id='+ params, config)
}   

export const getfatEffectSetById = (params, config = {}) => {
  return request.post(apiPrefix + 'GetFatEffectSetById?id='+ params, config)
} 

export const getShipperFxSelfOrderList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetShipperFxSelfOrderList', params, config)
} 

export const GetShipperFxFeeSet = (params, config = {}) => {
  return request.post(apiPrefix + `GetShipperFxFeeSet?shipperFxName=${params}`)
}

export const SaveShipperFxFeeSet = (params, config = {}) => {
  return request.post(apiPrefix + 'SaveShipperFxFeeSet', params, config)
}

export const GetShipperFxGoodsRptList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetShipperFxGoodsRptList', params, config)
}

export const exportShipperFxGoodsRptList = (params, config = { responseType: 'blob' }) => 
  { return request.post(apiPrefix + 'ExportShipperFxGoodsRptList', params, config) }


export const getShipperFxGoodsRptPdDetailList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetShipperFxGoodsRptPdDetailList', params, config)
}

export const exportShipperFxGoodsRptPdDetailList = (params, config = { responseType: 'blob' }) => 
  { return request.post(apiPrefix + 'ExportShipperFxGoodsRptPdDetailList', params, config) }

export const ExportShipperFxOrderList = (params, config = { responseType: 'blob' }) => 
  { return request.post(apiPrefix + 'ExportShipperFxOrderList', params, config) }

export const ExportShipperFxSelfOrderList = (params, config = { responseType: 'blob' }) => 
  { return request.post(apiPrefix + 'ExportShipperFxSelfOrderList', params, config) }

export const GetBalanceShipperFxLog = (params, config = {}) => {
  return request.post(apiPrefix + 'GetBalanceShipperFxLog', params, config)
}

export const BalanceShipperFxByDate = (params, config = {}) => {
  return request.post(apiPrefix + 'BalanceShipperFxByDate', params, config)
}

export const RejectBalanceShipperFxById = (params, config = {}) => {
  return request.post(apiPrefix + 'RejectBalanceShipperFxById',params, config)
}

export const GetShipperFxGoodsPurchaseNewPlanList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetShipperFxGoodsPurchaseNewPlanList', params, config)
}

export const ExportShipperFxGoodsPurchaseNewPlanList = (params, config = { responseType: 'blob' }) =>
  { return request.post(apiPrefix + 'ExportShipperFxGoodsPurchaseNewPlanList', params, config) }

export const UpdateShipperFxGoodsStockCount = (params, config = {}) => {
  return request.post(apiPrefix + 'UpdateShipperFxGoodsStockCount', params, config)
}

//获取分销商维护列表
export const GetShipperFxNameListAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'GetShipperFxNameListAsync', params, config)
}

//删除货主 
export const DeleteShipperFxName = (params, config = {}) => {
  return request.post(apiPrefix + 'DeleteShipperFxName', params, config)
}

//新增货主 
export const AddShipperFxName = (params, config = {}) => {
  return request.post(apiPrefix + 'AddShipperFxName', params, config)
}