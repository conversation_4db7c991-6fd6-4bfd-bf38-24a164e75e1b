import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/DisPerform/`

//查询
export const QueryDisPerform = (params, config = {}) => {
    return request.post(apiPrefix + 'QueryDisPerform', params, config)
}
//导出分销人员业绩统计
export const ExportDisPerform = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportDisPerform', params, config) }

//获取趋势图
export const GetTrendChart  = (params, config = {}) => { return request.post(apiPrefix + 'GetTrendChart', params, config) }