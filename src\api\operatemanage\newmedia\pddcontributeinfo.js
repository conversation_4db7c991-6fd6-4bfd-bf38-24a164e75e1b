import request from '@/utils/request'
const PddcontributeinfoPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/Pddcontributeinfo/`
export const updateCompetitorPDDJJStatusAsync = (params, config = {}) => {return request.post(PddcontributeinfoPrefix + 'UpdateCompetitorPDDJJStatusAsync', params, config )}
export const getCompetitorPDDMonyAsync = (params, config = {}) => {return request.get(PddcontributeinfoPrefix + 'GetCompetitorPDDMonyAsync', { params: params, ...config })}
export const getGoodStatusAsync = (params, config = {}) => {return request.get(PddcontributeinfoPrefix + 'GetGoodStatusAsync', { params: params, ...config })}
export const getCompetitorPDDMonyChartAsync = (params, config = {}) => {return request.post(PddcontributeinfoPrefix + 'GetCompetitorPDDMonyChartAsync', params, config )}
export const getCompetitorPDDSkuMonyAsync = (params, config = {}) => {return request.post(PddcontributeinfoPrefix + 'GetCompetitorPDDSkuMonyAsync', params, config)}
export const updateCompetitorPDDSkuAsync = (params, config = {}) => {return request.post(PddcontributeinfoPrefix + 'UpdateCompetitorPDDSkuAsync',  params, config)}
export const getCompetitorPDDYestodaySaleAsync = (params, config = {}) => {return request.get(PddcontributeinfoPrefix + 'GetCompetitorPDDYestodaySaleAsync', { params: params, ...config })}
export const competitorPDDSkuAsync = (params, config = {}) => {return request.get(PddcontributeinfoPrefix + 'competitorPDDSkuAsync', { params: params, ...config })}
export const getCompetitorPDDChartAsync = (params, config = {}) => {return request.get(PddcontributeinfoPrefix + 'GetCompetitorPDDChartAsync', { params: params, ...config })}
export const getCompetitorPDDSkuAsync = (params, config = {}) => {return request.get(PddcontributeinfoPrefix + 'GetCompetitorPDDSkuAsync', { params: params, ...config })}
export const pageShopGoodsAsync = (params, config = {}) => {return request.get(PddcontributeinfoPrefix + 'PageShopGoodsAsync', { params: params, ...config })}
export const getCompetitorPDDType1Async = (params, config = {}) => { return request.get(PddcontributeinfoPrefix + 'GetCompetitorPDDType1Async', { params: params, ...config }) }
export const getCompetitorPDDTypeAsync = (params, config = {}) => {return request.get(PddcontributeinfoPrefix + 'GetCompetitorPDDTypeAsync', { params: params, ...config })}
export const getCompetitorPDDTypeAsync2 = (params, config = {}) => {return request.post(PddcontributeinfoPrefix + 'GetCompetitorPDDTypeAsync2', params, config )}
export const getPageCompetitorPDDEntityAsync = (params, config = {}) => {return request.post(PddcontributeinfoPrefix + 'GetPageCompetitorPDDEntityAsync', params, config )}
export const importCompetitorPDDAsync = (params, config = {}) => {return request.post(PddcontributeinfoPrefix + 'ImportCompetitorPDDAsync', params, config)}
export const importPddcontributeinfoAsync = (params, config = {}) => {return request.post(PddcontributeinfoPrefix + 'ImportPddcontributeinfoAsync',  params, config)}
export const getPddcontributeinfoList = (params, config = {}) => {return request.get(PddcontributeinfoPrefix + 'GetPddcontributeinfoList', { params: params, ...config })}
export const deletePddcontributeinfoBatch = (params, config = {}) => {return request.get(PddcontributeinfoPrefix + 'DeleteBatchAsync', { params: params, ...config })}
