import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_MonthBookKeeper}/import/`



//导入抖音达人商务与品对应关系
export const ImportMonthBusinessWiseMan = (params, config = {}) => { return request.post(apiPrefix + 'ImportMonthBusinessWiseMan', params, config) }
//导入抖音达人销售主题分析
export const ImportMonthWiseManSaleThemeAnalysis = (params, config = {}) => { return request.post(apiPrefix + 'ImportMonthWiseManSaleThemeAnalysis', params, config) }

//导入京东虚拟发货-快递
export const ImportJingDongVirtualExpressAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportJingDongVirtualExpressAsync', params, config) }
//导入京东虚拟发货-订单
export const ImportJingDongVirtualOrderAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportJingDongVirtualOrderAsync', params, config) }


//月报仓库薪资导入
export const importMonthWareWages = (params, config = {}) => { return request.post(apiPrefix + 'ImportMonthWareWages', params, config) }

//特殊单回款查询-导入特殊单
export const importSpecialOrders = (params, config = {}) => { return request.post(apiPrefix + 'ImportSpecialOrders', params, config) }

//京东自营入仓月报-月账单导入
export const importMonthBill = (params, config = {}) => { return request.post(apiPrefix + 'ImportMonthBill', params, config) }
//京东自营入仓月报-月出库导入
export const importMonthOutbound = (params, config = {}) => { return request.post(apiPrefix + 'ImportMonthOutbound', params, config) }
//京东自营入仓月报-月入仓成本导入
export const importMonthWarehouseEntryCost = (params, config = {}) => { return request.post(apiPrefix + 'ImportMonthWarehouseEntryCost', params, config) }

//包装费导入
export const importPackingFee = (params, config = {}) => { return request.post(apiPrefix + 'ImportPackingFee', params, config) }
