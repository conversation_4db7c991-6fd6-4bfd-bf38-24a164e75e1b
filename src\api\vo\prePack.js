import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_VerifyOrder}/prePack/`

//GetColumns 预包数据表头
export const getColumns = (params, config = {}) => {
    return request.post(apiPrefix + 'GetColumns', params, config)
}

//查询数据 PageGetData
export const pageGetData = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetData', params, config)
}

//数据导出 ExportData
export const exportData = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportData', params, config)
}

//趋势图获取 getTrendChart
export const getTrendChart = (params, config = {}) => {
    return request.post(apiPrefix + 'GetTrendChart', params, config)
}

//查询打包耗材列表列GetColumns
export const consumableGetColumns = (params, config = {}) => {
    return request.post(apiPrefix + 'consumable/' + 'GetColumns', params, config)
}

//查询打包耗材数据 PageGetData
export const consumablePageGetData = (params, config = {}) => {
    return request.post(apiPrefix + 'consumable/' + 'PageGetData', params, config)
}

//导出打包耗材数据 ExportData
export const consumableExportData = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'consumable/' + 'ExportData', params, config)
}

//查询打包耗材单条数据Get
export const consumableGet = (params, config = {}) => {
    return request.post(apiPrefix + 'consumable/' + 'Get', params, config)
}

//新增或编辑 包装耗材 Merge
export const consumableMerge = (params, config = {}) => {
    return request.post(apiPrefix + 'consumable/' + 'Merge', params, config)
}

//删除 包装耗材 Delete
export const consumableDelete = (params, config = {}) => {
    return request.post(apiPrefix + 'consumable/' + 'Delete', params, config)
}

//获取基础数据列表列 GetColumns
export const baseDataGetColumns = (params, config = {}) => {
    return request.post(apiPrefix + 'baseInfo/' + 'GetColumns', params, config)
}

//分页查询数据 基础数据 PageGetData
export const baseDataPageGetData = (params, config = {}) => {
    return request.post(apiPrefix + 'baseInfo/' + 'PageGetData', params, config)
}

//数据导出 基础信息
export const baseDataExportData = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'baseInfo/' + 'ExportData', params, config)
}

//查询单条数据 基础数据 Get
export const baseDataGet = (params, config = {}) => {
    return request.post(apiPrefix + 'baseInfo/' + 'Get', params, config)
}

//新增或编辑 基础数据 Merge
export const baseDataMerge = (params, config = {}) => {
    return request.post(apiPrefix + 'baseInfo/' + 'Merge', params, config)
}

//删除 基础数据 Delete  
export const baseDataDelete = (params, config = {}) => {
    return request.post(apiPrefix + 'baseInfo/' + 'Delete', params, config)
}

//获取配置 GetSetting
export const getSetting = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSetting', params, config)
}

//保存配置 Setting
export const setting = (params, config = {}) => {
    return request.post(apiPrefix + 'Setting', params, config)
}

//获取待加工预包计划数据 GetWaitPrePacks
export const getWaitPrePacks = (params, config = {}) => {
    return request.post(apiPrefix + 'Manifest/' + 'GetWaitPrePacks', params, config)
}

//创建加工单 CreatePackManifest
export const createPackManifest = (params, config = {}) => {
    return request.post(apiPrefix + 'Manifest/' + 'CreatePackManifest', params, config)
}

//GetColumns 预包计划表头
export const manifestGetColumns = (params, config = {}) => {
    return request.post(apiPrefix + 'Manifest/' + 'GetColumns', params, config)
}

//查询数据 加工计划 PageGetData
export const manifestPageGetData = (params, config = {}) => {
    return request.post(apiPrefix + 'Manifest/' + 'PageGetData', params, config)
}

//数据导出 加工计划 ExportData
export const manifestExportData = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'Manifest/' + 'ExportData', params, config)
}

//批量加工单领取 ClaimPackManifests
export const claimPackManifests = (params, config = {}) => {
    return request.post(apiPrefix + 'Manifest/' + 'ClaimPackManifests', params, config)
}

//批量作废加工单 CancelPackManifests
export const cancelPackManifests = (params, config = {}) => {
    return request.post(apiPrefix + 'Manifest/' + 'CancelPackManifests', params, config)
}

//批量指定加工单加工人员 AssignPackManifests
export const assignPackManifests = (params, config = {}) => {
    return request.post(apiPrefix + 'Manifest/' + 'AssignPackManifests', params, config)
}


//批量指定加工数量和加工完成时间
export const setPackManifestsFinish = (params, config = {}) => {
    return request.post(apiPrefix + 'Manifest/' + 'setPackManifestsFinish', params, config)
}

//导入-预包-包装耗材 ImportData
export const importData = (params, config = {}) => {
    return request.post(apiPrefix + 'consumable/' + 'ImportData', params, config)
}

//导入-预包-基础数据 ImportData
export const baseDataImportData = (params, config = {}) => {
    return request.post(apiPrefix + 'baseInfo/' + 'ImportData', params, config)
}


//GetColumns 预包数据表头
export const getColumns_Unsalable = (params, config = {}) => {
    return request.post(apiPrefix + 'Unsalable/GetColumns', params, config)
}

//查询数据 PageGetData
export const pageGetData_Unsalable = (params, config = {}) => {
    return request.post(apiPrefix + 'Unsalable/PageGetData', params, config)
}

//数据导出 ExportData
export const exportData_Unsalable = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'Unsalable/ExportData', params, config)
}

//趋势图获取 getTrendChart
export const getTrendChart_Unsalable = (params, config = {}) => {
    return request.post(apiPrefix + 'Unsalable/GetTrendChart', params, config)
}