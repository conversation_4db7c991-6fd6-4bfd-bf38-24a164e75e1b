<template>
    <el-link :style=style  type='primary' @click='downloadFile'>
        {{btnText}}
    </el-link>
    </template>

<script>
export default {
  name: 'downloadfile',
  data(){
    return {
        btnText:this.filename+"(下载)",
        progress:0
    }
  },
  props: {
    style:{
        type: String,
        default: 'margin-right:20px'
    },
    url:{
        type: String,
        default: ''
    },
    filename:{
        type: String,
        default: ''
    },
  },
    methods: {
        async downloadFile() {
            let url=this.url;
            let filaname=this.filaname
            try {
                const response = await fetch(url);
                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);

                // 获取总大小
                const contentLength = response.headers.get('content-length');
                const total = contentLength ? parseInt(contentLength, 10) : null;

                // 读取响应流并跟踪进度
                const reader = response.body.getReader();
                const chunks = [];
                let receivedLength = 0;

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    chunks.push(value);
                    receivedLength += value.length;
                    
                    // 更新进度条
                    if (total) {
                        const percent = (receivedLength / total) * 100;
                        this.progress = percent; 
                        this.btnText=this.filename+"(下载中..."+Math.round(percent)+"%)";                  
                        if(percent>=100)
                            this.btnText=this.filename+"(下载完成)";
                    }
                }

                // 合并所有块并创建Blob
                //const blob = new Blob(chunks);
                // 创建包含文件名的 Blob URL
                const blob = new Blob(chunks, { type: chunks.type, name: this.filename });
               // new Blob([blob], { type: blob.type, name: this.filename }); 


                // 创建下载链接
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = this.filename;
                document.body.appendChild(link);
                link.click();
                
                // 清理
                document.body.removeChild(link);
                URL.revokeObjectURL(link.href);
            } catch (error) {
                console.error('下载失败:', error);
                // 失败时跳转到原始链接
                window.location.href = url;
            }
    },
   
  }
}
</script>
