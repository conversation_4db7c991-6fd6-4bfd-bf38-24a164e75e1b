<template>
    <div>
        <template v-if="col.merge" :width="col.width" >
            <vxe-colgroup :title="col.label">
                <template>
                    <div v-for="(coll, collindex) in col.cols" :key="collindex"></div>
                </template> 
            </vxe-colgroup>
        </template> 
    </div>
</template>

<script>
    export default {
        props: {
          
        },
        data() {
       
        },
        created(){
          
        },
        async mounted(){
          
        },
        methods:{
           
        }
    }
</script>
