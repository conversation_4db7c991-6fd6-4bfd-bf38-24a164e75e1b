<template>
    <div>
        <el-select v-model="warehouseId" :multiple="multiple" :clearable="clearable" filterable :collapse-tags="collapsetags"
            :placeholder="placeholder" style="width: 100%" @change="changeWareHouse" :disabled='disabled'>
            <el-option v-for="item in warehouselist" :key="item.name" :label="item.name" :value="item.wms_co_id" />
        </el-select>
    </div>
</template>

<script>
import { getAllWarehouse } from '@/api/inventory/warehouse'
export default {
    data() {
        return {
            warehouselist: [],
            warehouseId: ''
        }
    },
    model: {
        prop: 'value',
        event: 'update'
    },
    props: {
        value: {
            type: Number | String,
            default: ''
        },
        filter: {
            type: Function | Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: '请选择仓库'
        },
        multiple: {
            type: Boolean,
            default: false
        },
        clearable: {
            type: Boolean,
            default: true
        },
        // 仓库过滤
        defaultId: {
            type: Array,
            default: []
        },
        // 合并仓库数据
        // 格式：[{name: '仓库名称', wms_co_id: '仓库id'}]
        // 合并后会排在最前面
        mergeData: {
            type: Array,
            default: []
        },
        disabled: {
            type: Boolean,
            default: false
        },
        collapsetags: {
            type: Boolean,
            default: true
        }
    },
    mounted() {
        this.getWareHouse()
    },
    watch: {
        value: {
            handler(val) {
                this.warehouseId = val
            },
            immediate: true
        }
    },
    methods: {
        changeWareHouse() {
            this.$emit('update', this.warehouseId)
            let wareHouse;
            if (this.multiple) {
                wareHouse = this.warehouselist.filter(x => this.warehouseId.includes(x.wms_co_id))
            } else {
                wareHouse = this.warehouselist.find(x => x.wms_co_id === this.warehouseId)
            }
            this.$emit('chooseWms', wareHouse)
        },
        // 去重并合并
        mergeUniqueByField(arr1, arr2, key = 'wms_co_id') {
          const seen = new Set()
          return arr1.concat(arr2).filter(item => {
            if (!item || seen.has(item[key])) return false
            seen.add(item[key])
            return true
          })
        },
        async getWareHouse() {
            const res = await getAllWarehouse({ orderBy: 'name' })
            var data = res.data.filter((x) => x.name.indexOf('代发') < 0)
            if (this.defaultId && this.defaultId.length > 0) {
                data = data.filter((x) => this.defaultId.includes(x.wms_co_id));
            }
            if (this.filter) {
                data = this.filter(data)
            }
            data.sort((a, b) => {
                    return a.name.localeCompare(b.name, 'zh')
                })
            const baseList = Array.isArray(this.mergeData) ? this.mergeData : this.mergeData ? [this.mergeData] : [] //this.mergeData可能为一个对象
            this.warehouselist = this.mergeUniqueByField(baseList, data, 'wms_co_id')
        }
    }
}
</script>

<style scoped lang="scss"></style>
