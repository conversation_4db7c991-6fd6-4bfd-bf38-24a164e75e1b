import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Manicure/`

//导入大马美甲
export const ImportManicure = (params, config = {}) => { return request.post(apiPrefix + 'ImportManicure',  params, config ) }

//查询大马美甲
export const QueryManicure = (params, config = {}) => { return request.post(apiPrefix + 'QueryManicure',  params, config ) }

//新增或更新大马美甲
export const InsertOrUpdateManicure = (params, config = {}) => { return request.post(apiPrefix + 'InsertOrUpdateManicure',  params, config ) }

//删除大马美甲
export const DeleteManicure = (params, config = {}) => { return request.post(apiPrefix + 'DeleteManicure',  params, config ) }
