import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/DaiPai/`

//同步表结构
export const SyncStructure = (params, config = {}) => { return request.post(apiPrefix + 'SyncStructure', params, config) }
//分页查询供应商
export const PageDpSupplierListAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageDpSupplierListAsync', params, config) }

//获取供应商详情
export const GetDpSupplierByIdAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetDpSupplierByIdAsync', { params: params, ...config }) }

//保存供应商主表及联系人，商品明细要单独操作
export const SaveDpSupplierAsync = (params, config = {}) => { return request.post(apiPrefix + 'SaveDpSupplierAsync', params, config) }

//获取供应商商品
export const GetSupplierGoodsByIdAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetSupplierGoodsByIdAsync', { params: params, ...config }) }

//删除供应商商品 软删除
export const DelSupplierGoodsAsync = (params, config = {}) => { return request.get(apiPrefix + 'DelSupplierGoodsAsync', { params: params, ...config }) }

//保存供应商商品
export const SaveSupplierGoodsAsync = (params, config = {}) => { return request.post(apiPrefix + 'SaveSupplierGoodsAsync', params, config) }

//获取Yh配置的关联商品列表
export const PageHpYgGoodsAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHpYgGoodsAsync', params, config) }

//保存代拍Yh与供应商商品关联关系
export const SaveDpYhGoodsRelAsync = (params, config = {}) => { return request.post(apiPrefix + 'SaveDpYhGoodsRelAsync', params, config) }

//分页查询代拍供应商SKU数据
export const PageSupplierSkusAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageSupplierSkusAsync', params, config) }
//导入代拍厂家
export const ImportSuppliersAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportSuppliersAsync', params, config) }
//获取代拍商品关联 
export const GetDpYhGoodsRelAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetDpYhGoodsRelAsync', { params: params, ...config }) }


//分页查询待代拍导入订单
export const PageDpImpOrderListAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageDpImpOrderListAsync', params, config) }
//导入待代拍订单
export const ImportDpImpOrdersAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportDpImpOrdersAsync', params, config) }
//获取内部代拍订单byId
export const GetDpImpOrderByIdAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetDpImpOrderByIdAsync', { params: params, ...config }) }
//生成运营代拍订单
export const GenerateDpSpOrderAsync = (params, config = {}) => { return request.post(apiPrefix + 'GenerateDpSpOrderAsync', params, config) }



//分页查询已生成的厂家代拍订单
export const PageDpSpOrderListAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageDpSpOrderListAsync', params, config) }
//查看厂家代拍订单
export const GetDpSpOrderByIdAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetDpSpOrderByIdAsync', { params: params, ...config }) }