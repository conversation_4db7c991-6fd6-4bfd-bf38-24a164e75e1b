import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API}/api/`

// 接口管理
export const getApi = (params, config = {}) => {
  return request.get(apiPrefix + 'get', { params: params, ...config })
}
export const getApiList = (params, config = {}) => {
  return request.get(apiPrefix + 'getlist', { params: params, ...config })
}
export const removeApi = (params, config = {}) => {
  return request.delete(apiPrefix + 'delete', { params: params, ...config })
}
export const batchRemoveApi = (params, config = {}) => {
  return request.put(apiPrefix + 'BatchDelete', params, config)
}
export const editApi = (params, config = {}) => {
  return request.put(apiPrefix + 'update', params, config)
}
export const addApi = (params, config = {}) => {
  return request.post(apiPrefix + 'add', params, config)
}
/**
 * 同步api
 */
export const syncApi = (params, config = {}) => {
  return request.post(apiPrefix + 'sync', params, config)
}
//获取系统管理模块 api
export const getV2SwaggerJson = (params, config = {}) => {
  return request.get(apiPrefix + 'GetModuleApiList?modulePort=8000', { params: params, ...config })
}
//获取财务模块 api
export const getFinancialV2SwaggerJson = (params, config = {}) => {
  return request.get(apiPrefix + 'GetModuleApiList?modulePort=8010', { params: params, ...config })
}
// export const getFinancialV2SwaggerJson = (params, config = {}) => {
//   return request.get('http://localhost:8010/swagger/V2/swagger.json', { params: params, ...config })
// }
//获取订单模块 api
export const getOrderV2SwaggerJson = (params, config = {}) => {
  return request.get(apiPrefix + 'GetModuleApiList?modulePort=8020', { params: params, ...config })
}
// //获取运营模块 api
// export const getOperateManageV2SwaggerJson = (params, config = {}) => {
//   return request.get('http://localhost:8020/swagger/V2/swagger.json', { params: params, ...config })
// }
//获取运营模块 api
export const getOperateManageV2SwaggerJson = (params, config = {}) => {
  return request.get(apiPrefix + 'GetModuleApiList?modulePort=8030', { params: params, ...config })
}

//获取进销存模块 api
export const getInventoryV2SwaggerJson = (params, config = {}) => {
  return request.get(apiPrefix + 'GetModuleApiList?modulePort=8040', { params: params, ...config })
}