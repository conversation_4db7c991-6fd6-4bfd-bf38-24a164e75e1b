<!--表格组件 -->
<template>
<section class="ces-table-page">
  <!-- 表格操作按钮 -->
    <section class="ces-handle" v-if='isHandle'>
      <el-button 
        v-for='item in tableHandles' 
        :key='item.label'
        :size="item.size || size" 
        :type="item.type || type" 
        :icon='item.icon' 
        @click="item.handle(that)">{{item.label}}</el-button>
    </section>
    <!-- 数据表格 -->
    <section class="ces-table">
        <el-table  :data='tableData' :size='size' height="100%" 
          :border  ='isBorder'
          @select='select' 
          @sort-change='sortchange'
          @filter-change='filterchange'
          v-loading='loading' 
          :defaultSelections='defaultSelections'
          ref="cesTable">
            <el-table-column v-if="isSelection" type="selection" align="center" ></el-table-column>
            <el-table-column v-if="isIndex" type="index" :label="indexLabel" align="center" width="50"></el-table-column>
            <el-table-column sortable='custom' v-for="item in tableCols" 
              :key="item.id"
              :prop="item.prop" 
              :label="item.label" 
              :width="item.width"
              :align="item.align" 
              :render-header="item.require?renderHeader:null"
              >
                <template slot-scope="scope" >
                  <span v-if="item.type==='html'" v-html="item.html(scope.row)"></span>
                  <span v-if="item.type==='button'" >
                    <el-button v-for="btn in item.btnList" :key="btn.label"
                      :disabled="btn.isDisabled && btn.isDisabled(scope.row)"
                      :type="btn.type || type" 
                      :size="btn.size || size " 
                      :icon="btn.icon" 
                      @click="btn.handle(that,scope.row)">{{btn.label}}</el-button>
                    </span>
                  <el-input v-if="item.type==='input'" v-model="scope.row[item.prop]" :size="size || btn.size"
                    :disabled="item.isDisabled && item.isDisabled(scope.row)"
                    @focus="item.focus && item.focus(scope.row)"></el-input>
                  <el-select v-if="item.type==='select'" v-model="scope.row[item.prop]" :size="size || btn.size"  :props="item.props"
                    :disabled="item.isDisabled && item.isDisabled(scope.row)" 
                    @change='item.change && item.change(that,scope.row)'>
                      <el-option v-for="op in item.options" :label="op.label" :value="op.value" :key="op.value"></el-option>
                  </el-select>
                  <el-radio-group v-if="item.type==='radio'" v-model="scope.row[item.prop]"
                    :disabled="item.isDisabled && item.isDisabled(scope.row)" :size="size || btn.size" 
                    @change='item.change && item.change(that,scope.row)'>
                      <el-radio v-for="ra in item.radios" :label="ra.value" :key="ra.value">{{ra.label}}</el-radio>
                  </el-radio-group>
                  <el-checkbox-group v-if="item.type==='checkbox'" v-model="scope.row[item.prop]" 
                    :disabled="item.isDisabled && item.isDisabled(scope.row)" :size="size || btn.size" 
                    @change='item.change && item.change(that,scope.row)'>
                      <el-checkbox v-for="ra in item.checkboxs" :label="ra.value" :key='ra.value'>{{ra.label}}</el-checkbox>
                  </el-checkbox-group>
                  <el-rate v-if="item.type==='rate'" v-model="scope.row[item.prop]"
                    :disabled="item.isDisabled && item.isDisabled(scope.row)" :size="size || btn.size" 
                    @change='item.change && item.change(scope.row)'></el-rate>
                  <el-switch v-if="item.type==='switch'" v-model="scope.row[item.prop]" :size="size || btn.size" 
                    :active-value='item.values&&item.values[0]'
                    :inactive-value='item.values&&item.values[1]'
                    :disabled="item.isDisabled && item.isDisabled(scope.row)"
                    @change='item.change && item.change(scope.row)'></el-switch>
                  <img v-if="item.type==='image'" :src="scope.row[item.prop]" @click="item.handle && item.handle(scope.row)"/>
                  <el-slider v-if="item.type==='slider'" v-model="scope.row[item.prop]" 
                  :disabled="item.isDisabled && item.isDisabled(scope.row)" :size="size || btn.size" 
                    @change='item.change && item.change(scope.row)'></el-slider>
                  <span v-if="!item.type" 
                    :style="item.itemStyle && item.itemStyle(scope.row)" :size="size || btn.size" 
                    :class="item.itemClass && item.item.itemClass(scope.row)">{{(item.formatter && item.formatter(scope.row)) || scope.row[item.prop]}}</span>
                </template>
              </el-table-column>
        </el-table>
    </section>

    <section class="ces-pagination"  v-if='isPagination'>
        <el-pagination style='display: flex;justify-content: center;height: 100%;align-items: center;'
            @current-change="tablePage.handlePageNumChange(that,arguments[0])"
            @size-change="tablePage.handlePageSizeChange(that,arguments[0])"
            layout="total,sizes ,prev, pager, next,jumper"
            :page-size="tablePage.pageSize"
            :current-page="tablePage.pageNum"
            :total="tablePage.total"
        ></el-pagination>
    </section>

    
<div class="cj" v-if='showCJ'>
  <el-row :gutter="20">
    <el-col :span="18">
      <el-select 
      v-model="cjSelect" 
      filterable 
      multiple
      placeholder="请选择">
        <el-option
          v-for="item in people"
          :key="item.value"
          :label="item.text"
          :value="item.value">
        </el-option>
      </el-select>
    </el-col>
    <el-col :span="2">
        <el-button style="margin-top:5px">确认</el-button>
    </el-col>
  </el-row>
</div>
</section>
</template>
<script>

export default {
  props:{
    that: { type: Object, default: this },
    // 表格型号：mini,medium,small
    size:{type:String,default:'mini'},
    type:{type:String,default:'primary'},
    isBorder:{type:Boolean,default:true},
    loading:{type:Boolean,default:false},
    // 表格操作
    isHandle:{type:Boolean,default:false},
    tableHandles:{type:Array,default:()=>[]},
    // 表格数据
    tableData:{ type:Array,default:()=>[]},
    // 表格列配置
    tableCols:{ type:Array,default:()=>[]},
    // 是否显示表格复选框
    isSelection:{type:Boolean,default:false},
    defaultSelections:{ type:[Array,Object], default:()=>null},
    // 是否显示表格索引
    isIndex:{type:Boolean,default:false},
    indexLabel: {type:String,default:'#'},
    // 是否显示分页
    isPagination:{type:Boolean,default:true},
    // 分页数据
    tablePage:{ type:Object,default:()=>({pageSize:10,pageNum:1,total:0})},
    //排序
    orderby:{type:Object,default:()=>({order:"ascending",name:'id'})},
    filter:{},
    showCJ:false
  },
  data(){
    return {
      local_orderby: this.orderby,
    }
  },
  watch:{
    'defaultSelections'(val) {
        this.$nextTick(function(){
          if(Array.isArray(val)){
            val.forEach(row=>{
              this.$refs.cesTable.toggleRowSelection(row)
            })
          }else{
            this.$refs.cesTable.toggleRowSelection(val)
          }
        })      
    },
     provinceValue (val) {
        this.provinceValueData = val
    }
  },
  methods:{
    // 表格勾选
    select(rows,row){
      this.$emit('select',rows,row);
    },
    // 全选
    selectAll(rows){
      this.$emit('select',rows)
    },
    sortchange(column){
       this.orderby={order:column.order,name:column.prop}
       this.$emit('sortchange',column)
    },
    filterchange(filters){
      // this.orderby={order:column.order,name:column.prop}
       this.$emit('sortchange',column)
    },
    renderHeader(h,obj) {
      return h('span',{class:'ces-table-require'},obj.column.label)
    },
    renderHeader1(h, { column, $index }) {
    return (
      <el-input></el-input>
    )
},
    renderHeader2(h, column) {
      const tempObj = {
      options: [{
        label: '所有状态',
        value: '2'
      }, {
        label: '未完成',
        value: '0'
      }, {
        label: '已完成',
        value: '1'
      }],
      colorMap: ['#dd4b39', '#00ff00', '#00a1d6'],
      dataMap: ['未完成', '已完成', '所有状态']
    } 
          let This = this
          let arr = []
          tempObj.options.map((i) => {
            arr.push(h('el-option', {
              domProps: {
                value: i.value
              },
              nativeOn: {
                click: This.handleState
              },
              style: {
                color: tempObj.colorMap[i.value]
              }
            }, i.label))
          })
          return h('div', [
            h('el-select', {
              attrs: {
                value: This.label,
              }
            }, arr)
          ])
        },

        chengjiaoFilter(h, { column }){
            console.log('111');            
              if (column.property == 'status') {
              return h('div',{style: 'margin-top:9px',},
                [h('b', {
                  style: 'font-size:15px;cursor:pointer',
                  on: {//这个是你的点击方法
                    click: () => {
                      this.chengjiaoFilter1()
                    }
                  }
                },'邀约人员'),
                h('i',{
                  style:'color:#7E662E;font-size:12px',
                  class:'el-icon-arrow-down'
                })]
            )}
    },
        // 控制筛选框是否显示
    chengjiaoFilter1(){
      console.log('成交筛选'); 
      this.showCJ = !this.showCJ
    }
  },
}
</script>
<style>
.ces-table-require::before{
  content:'*';
  color:red;
}
</style>