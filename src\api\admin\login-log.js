import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API}/loginlog/`

// 登录日志
export const getLoginLogPage = (params, config = {}) => {
  params = params || {}
  return request.post(apiPrefix + 'getpage', params, config)
}
export const queryPageTaskLog = (params, config = {}) => {  return request.get(apiPrefix + 'QueryPageTaskLog', { params: params, ...config })}
export const queryEnmImportFileTypes = (params, config = {}) => {  return request.get(apiPrefix + 'QueryEnmImportFileTypes', { params: params, ...config })}
// 访问日志
export const PageReqLog = (module, page, tab, action, config = {}) => { 
    function removeSpaces(str) {
        return str.replace(/\s+/g, '');
    }
    
  let p2={"ModuleName": removeSpaces(module),"PageName":removeSpaces(page),"PageTab":removeSpaces(tab),"ActionName":removeSpaces(action),"AppType":"ERP"};
  p2.ReqPath=window.location.pathname;

  return request.post(apiPrefix + 'PageReqLog', p2, config)
}

import Vue from 'vue'

Vue.prototype.$PageReqLog= PageReqLog;
