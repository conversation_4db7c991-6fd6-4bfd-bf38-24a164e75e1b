import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_CustomerExt}/ProductGoodsDocInfo/`


//获取产品ID对应的资料库
export const GetProductDocInfoById = (params, config = {}) => {
  return request.get(apiPrefix + 'GetProductDocInfoById', { params: params, ...config })
}

//获取产品ID对应的资料库
export const GetProductDocInfoById2 = (params, config = {}) => {
  return request.get(apiPrefix + 'GetProductDocInfoById2', { params: params, ...config })
}
//保存商品资料库（客服）
export const SaveGoodsDocInfoRecordKfInfo = (params, config = {}) => { return request.post(apiPrefix + 'SaveGoodsDocInfoRecordKfInfo', params, config) }