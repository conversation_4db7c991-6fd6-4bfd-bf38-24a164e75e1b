import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Express}/expressdataanalyse/`

//获取快递路线
export const getExpressRoutePageList = (params, config = {}) => { return request.post(apiPrefix + 'GetExpressRoutePageList', params, config) }
//快递路线导出
export const exportExpressRoute = (params, config = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportExpressRoute', params, config)

//导入快递揽收信息
export const importExpressDataAnalyseDouYinLanShou = (params, config = {}) => { return request.post(apiPrefix + 'ImportExpressDataAnalyseDouYinLanShou', params, config) }

//获取快递揽收
export const getExpressLanShouPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetExpressLanShouPageList', params, config) }

//获取快递揽收
export const getExpressLanShouChart = (params, config = {}) => { return request.post(apiPrefix + 'GetExpressLanShouChart', params, config) }
