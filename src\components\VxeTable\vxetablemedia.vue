<template>
<div :style="[{ height: (tableHandles != null ? '96%' : '100%') }, { width: '100%' }]" class="vxetablecss">
    <el-button-group>
        <template v-for='item in tableHandles'>
            <el-button v-if="(!item.permission || (item.permission && checkPermission(item.permission)))"
                :key='item.label' :size="item.size || size" :type="item.type || type" :icon='item.icon || ""'
                @click="item.handle(that)">{{ item.label }}</el-button>
        </template>
        <slot name="extentbtn" />
    </el-button-group>
    <vxe-toolbar ref="xToolbar" custom class="vxetoolbar20221212" v-if="showToolbar">
          <template #buttons>
            <slot name="tbHeader" />
          </template>
    </vxe-toolbar>
    <vxe-table :row-config="{isCurrent: true, isHover: true}" :size="size"
    :align="align"
    resizable ref="xTable" :loading="loading"
    :tree-config="treeProp"
    :data="tableData"
    :sort-config="{sortMethod: customSortMethod}"
    :scroll-y="{gt:100}"
    :scroll-x="{gt:100}"
    :span-method="mergeRowMethod"
    :column-config="{resizable: resizable,maxFixedSize: 300, maxFixedSize: 20,
	resizable: true,}"

    class="vxetable202212161323 mytable-scrollbar20221212"
    :row-style="rowStyleFun"
    :height="height"
    stripe
    :border="border"
    :custom-config="{storage: true,resizable: true, useKey: true, restoreStore:restoreStore,updateStore:updateStore}"
    :id="id"
    :header-cell-class-name="headerCellClassName"
    :show-footer="showsummary"
    :footer-method="footerMethod"
    v-bind:name="tablekey"
    :edit-config="editconfig"
    @checkbox-change="selectChangeEvent"
    @cell-dblclick="rowChange"
    @checkbox-all="checkboxall"
    @footer-cell-click="footercellclick"
    keep-source
    show-footer-overflow
    show-overflow
    >
    <!-- @edit-actived="editClosedEvent"
    border show-overflow-->

        <vxe-column v-if="hasSeq" type="seq" width="55" fixed="left"></vxe-column>
        <vxe-column v-if="hascheck" type="checkbox" width="40"  fixed="left" ></vxe-column>


        <slot name="left" />
        <template v-for="(col,colIndex) in tableCols">
            <template v-if="(!col.permission || (col.permission && checkPermission(col.permission)))">
                <template v-if="!col.type&&!col.merge">
                    <vxe-column :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                    :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                    show-overflow :tree-node="!!col.treeNode? true:false"
                    :width="col.width"
                    :min-width="col.minwidth?col.minwidth:null"
                    :sortable="!!col.sortable"
                    :fixed="col.fixed?col.fixed:''"
                    :align="col.align?col.align:'left'"
                    >
                        <template #default="{ row }" v-if="col.formatter">
                            {{col.formatter? tonumfuc(col.formatter(row), col.label): tonumfuc(row[col.prop], col.label)}}
                        </template>
                    </vxe-column>
                </template>

                <vxe-column  v-else-if="col.type=='images'"
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow  :tree-node="!!col.treeNode? true:false"
                :width="col.width"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="col.align?col.align:'center'"
                >
                    <template #default="{ row }">

                        <template v-if=" row && !!row[col.prop] && row[col.prop].length>2">

                            <template v-if="row[col.prop][0]=='[' && JSON.parse(row[col.prop]).length>1">
                                <el-badge
                                    class="badgeimage20221212"
                                    :value="JSON.parse(row[col.prop]).length" style="margin-top:0px;margin-right:40px;">
                                    <el-image  :src="(row[col.prop][0]=='['?(formatImg(row[col.prop])[0].url):(formatImg(row[col.prop]) ? formatImg(row[col.prop])[0] : '' ) )"
                                    class="images20221212"
                                    :preview-src-list="(row[col.prop][0]=='['
                            ?(()=>{
                                let tempArray=JSON.parse(row[col.prop]);
                                let tempRltArr=[];
                                tempArray.forEach(x=>tempRltArr.push(x.url));
                                return tempRltArr
                            })()
                            :(()=>{
                                return [row[col.prop]]
                            })()  )">
                                    </el-image>
                                </el-badge>
                            </template>
                            <template v-else>
                                <el-image  :src="formatImg(row[col.prop])[0].url ? formatImg(row[col.prop])[0].url : formatImg(row[col.prop])[0]"

                                class="images20221212"
                                :preview-src-list="(row[col.prop][0]=='['
                            ?(()=>{
                                let tempArray=JSON.parse(row[col.prop]);
                                let tempRltArr=[];
                                tempArray.forEach(x=>tempRltArr.push(x.url));
                                return tempRltArr
                            })()
                            :(()=>{
                                return [row[col.prop]]
                            })()  )">
                                </el-image>
                            </template>
                        </template>

                    </template>
                </vxe-column>

                <template v-else-if="col.merge" >
                    <vxe-colgroup :title="col.label" :key="col.label+'tt'">
                        <template v-for="(coll, collindex) in col.cols" >
                            <vxe-column  v-if="coll.type=='editNumber'" :key="collindex"
                                :field="coll.prop? coll.prop : ('coll'+ collindex)"
                                :title="coll.label"
                                show-overflow  :tree-node="!!coll.treeNode? true:false"
                                :width="coll.width"
                                :min-width="coll.minwidth?coll.minwidth:null"
                                :sortable="!!coll.sortable"
                                :fixed="coll.fixed?coll.fixed:''"
                                :align="coll.align?coll.align:'center'"
                                :edit-render="{autofocus: '.vxe-input--inner'}">
                                <template #edit="{ row }">
                                        <el-input-number  style="width:80px ;" v-model="row[coll.prop]"  :min="0" :max="10000" :precision="2"   :controls="false"  @change="editClosedEvent" placeholder="请输入"></el-input-number>
                                </template>

                            </vxe-column>
                            <vxe-column  v-else :field="coll.prop? coll.prop : ('coll'+ collindex)"  :key="collindex+'tt'"
                                :title="coll.label?coll.label:((coll.type && coll.type=='color' || coll.type=='split')?'|':'')"
                                show-overflow :tree-node="!!coll.treeNode? true:false"
                                :width="coll.width"
                                :min-width="coll.minwidth?coll.minwidth:null"
                                :sortable="!!coll.sortable"
                                :fixed="coll.fixed?coll.fixed:''"
                                :align="coll.align?coll.align:'left'"
                                >
                                    <template #default="{ row }" v-if="coll.formatter">
                                        {{coll.formatter? tonumfuc(coll.formatter(row), coll.label): tonumfuc(row[coll.prop], coll.label)}}
                                    </template>
                                </vxe-column>
                        </template>
                    </vxe-colgroup>
                </template>

                <vxe-column  v-else-if="col.type=='editNumber'"
                    :field="col.prop? col.prop : ('col'+ colIndex)"
                    :title="col.label"
                    show-overflow  :tree-node="!!col.treeNode? true:false"
                    :width="col.width"
                    :min-width="col.minwidth?col.minwidth:null"
                    :sortable="!!col.sortable"
                    :fixed="col.fixed?col.fixed:''"
                    :align="col.align?col.align:'center'"
                    :edit-render="{}"
                    :column-config="{resizable: true}" :key="col.prop">
                    <template #edit="{ col }">
                            <vxe-input v-model="col.prop" type="text" placeholder="请输入数值"></vxe-input>
                    </template>
                    <!-- <template #default="{ row }">
                        <span>{{ row.prop }}</span>
                    </template> -->
                </vxe-column>
                <vxe-column  v-else
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field+'tw'"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow  :tree-node="!!col.treeNode? true:false"
                :width="col.width"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="(col.type && (col.type=='color' || col.type=='split' || col.type=='images' || col.type=='image'))?'center':  (col.align?col.align:'left')"
                >
                    <template #default="scope">
                        <span v-if="col.type=='color' || col.type=='split'" style="color: #999;">
                            |
                        </span>
                        <template  v-else-if="col.type==='button'">
                            <template v-for="(btn,btnIndex) in col.btnList" >
                                <el-link :key="btn.label" :style="''+ (btnIndex>0?'margin-left:5px;':'')"
                                v-if="(!btn.permission||(btn.permission&&checkPermission(btn.permission)))&&!(btn.hasOwnProperty('ishide')&&((typeof btn.ishide=='function'&& btn.ishide(that,scope.row)==true)||btn.ishide==true))"
                                :disabled="(btn.hasOwnProperty('display')&&((typeof btn.display=='function'&& btn.display(scope.row)==true)||btn.display==true))"
                                :type="((!!btn.type)? btn.type: 'primary')" :size="btn.size || size " :icon="btn.icon" @click="btn.handle(that,scope.row)">
                                    {{btn.label}}
                                </el-link>
                            </template>
                        </template>
                        <a v-else-if="col.type=='click'"  type="text"  show-overflow="ellipsis"  style="color:#409EFF" @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">
                            {{(col.formatter && col.formatter(scope.row)) || scope.row[col.prop]}}
                        </a>
                        <a v-else-if="col.type=='pointsclick'" type="text" show-overflow="ellipsis" :style="col.style ? col.style(scope.row) : {}" @click="col.handle && col.handle(that, scope.row, col, scope.row[col.prop])">
                          {{ (col.formatter && col.formatter(scope.row)) || scope.row[col.prop] }}
                        </a>
                        <div v-else-if="col.type=='html'"  v-html="col.formatter? col.formatter(scope.row): scope.row[col.prop]  " @click="handleClick($event,scope.row[col.prop])"></div>
                        <el-switch v-else-if="col.type=='switch'" v-model="scope.row[col.prop]" @change='col.change && col.change(scope.row,that)'></el-switch>

                        <span v-else-if="col.type=='custom'||!col.type" :style="col.itemStyle && col.itemStyle(scope.row)" :size="size || btn.size" :class="col.itemClass && col.column.itemClass(scope.row)">
                        {{ (()=>{
                            if(col.formatter)
                                return tonumfuc(col.formatter(scope.row), col.label);
                            else
                                return tonumfuc(scope.row[col.prop], col.label);
                        })() }}</span>

                        <template v-else-if="col.type==='UrgencyButton'">
                            <template v-if="scope.row.taskUrgency==1  ">
                                <vxe-button status="danger" size="mini"   @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">{{scope.row.taskUrgencyName}}</vxe-button>
                            </template>
                            <template v-else-if="( scope.row.taskUrgency==2)">
                                <vxe-button status="primary" size="mini"   @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">{{scope.row.taskUrgencyName}}</vxe-button>
                            </template>
                            <template v-else-if="( scope.row.taskUrgency==0)">
                                <vxe-button status="warning" size="mini"   @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">{{scope.row.taskUrgencyName}}</vxe-button>
                            </template>
                            <template v-else>
                                <vxe-button  size="mini"   @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">{{scope.row.taskUrgencyName}}</vxe-button>
                            </template>
                        </template>
                        <template v-else-if="col.type==='clickflag'">
                            <template v-if="scope.row.markCssName=='0'  ">
                                <i  class="vxe-icon-flag-fill" style="color: #ff0101;"   @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])" ></i>
                            </template>
                            <template v-else>
                                <i  class="vxe-icon-flag-fill" style="color: #dcdfe6;"   @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])" ></i>
                            </template>
                        </template>
                        <template v-else-if="col.type==='fileicon'">
                            <i class="vxe-icon-file-txt"   @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])" ></i>
                        </template>
                        <template v-else-if="col.type==='editicon'">
                            <i class="vxe-icon-ellipsis-h"    @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])" ></i>
                        </template>
                        <template v-else-if="col.type==='time'">
                            <span style="color: #999;">
                                {{(col.formatter && col.formatter(scope.row)) || scope.row[col.prop]}}
                            </span>
                        </template>

                        <template v-else-if="col.type==='urgencyediticon'">
                            <template v-if="scope.row.isTopOldNum=='0'  ">
                                <span></span>
                            </template>
                            <template v-else>
                                <i  class="vxe-icon-dot" style="color: #ff0101;"></i>
                            </template>
                        </template>
                    </template>
                </vxe-column>



            </template>
        </template>


        <slot name="right" />

    </vxe-table>

</div>
</template>

<script>
import { EXPRESSIONWRAPPER_TYPES } from '@babel/types'
// import VXETable from 'vxe-table'
// import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
// import ExcelJS from 'exceljs'
import {matchImg} from '@/utils/getCols'
import { tonumfuc } from '@/utils/tonumqian.js'
import { GetVxeTableColumnCacheAsync,SetVxeTableColumnCacheAsync } from '@/api/admin/business'
import { SaveProductIdViewLog } from '@/api/operatemanage/PddChart'
// VXETable.use(VXETablePluginExportXLSX, {
//       ExcelJS
//     })

    export default {
        name:"vxetablebase",
        props: {
            editconfig:{ type: Object, default: () => { return {  } } },
            treeProp: { type: Object, default: () => { return {  } } },
            hasSeq: { type: Boolean, default: () => { return true } },
            hascheck: { type: Boolean, default: () => { return false } },
            showToolbar: { type: Boolean, default: () => { return true } },
            // 表格数据
            tableData: { type: Array, default: () => [] },
            // 表格型号：mini,medium,small
            size: { type: String, default: 'mini' },
            type: { type: String, default: 'primary' },
            isBorder: { type: Boolean, default: true },
            // 表格列配置
            tableCols: { type: Array, default: () => [] },
            isRemoteSort:{ type: Boolean, default: () => { return true } },
            id:{type:String,default:()=>{ return new Date().valueOf().toString()}},
            that:{type:Object,default:()=>{return null}},
            loading:{type:Boolean,default:()=>{return false;}},
            border:{type:Boolean | Object ,default:()=>{return 'default'}},
            tableHandles: { type: Array, default: () => [] },
            showsummary: { type: Boolean, default: false },
            align: { type: String, default: '' }, //对齐方式
            summaryarry: { type: Object, default: () => { } },
            tablekey: { type: String, default: '' },//表格key
            height: { type: String, default: '100%' },//固定表头作用
        },
        data() {
            return {
                tonumfuc,
                lastSortArgs:{
                    field:"",
                    order:"",
                },
                arrlist:[],
                summarycolumns: [],
                tablecolumns:[],
                aaaa: ''
            }
        },
        created(){
            // VXETable.use(VXETablePluginExportXLSX);
            this.$nextTick(() => {
              // 手动将表格和工具栏进行关联
              this.$refs.xTable.connect(this.$refs.xToolbar)
            })
        },
        async mounted(){
            this.$nextTick(() => {
                this.tablecolumns = this.$refs.xTable.getColumns()
                this.arrlist = [];
                this.tableCols.map((item)=>{
                    if(!item.istrue){
                        this.arrlist.push(item.prop)
                    }
                })
            })
          await this.ShowHidenColums(this.arrlist);
        },
        methods:{
            handleClick(e, prop) {
                if (!prop) return
                if (!e.target.parentNode.innerHTML.includes('复') && !e.target.parentNode.innerHTML.includes('查 ')) return
                let res = JSON.parse(JSON.stringify(prop));
                if (res.length > 6) {
                    res = res.substring(0, 2) + '**' + res.substring(res.length - 2, res.length);
                }
                if (e.target.innerHTML == '复') {
                    var _this = this;
                    this.$copyText(prop).then(function (e) {
                        _this.$message({ message: "内容已复制到剪切板！", type: "success" });
                    }, function (e) {
                        _this.$message({ message: "抱歉，复制失败！", type: "warning" });
                    })
                    this.sendLog(prop, '复制宝贝ID', 'ERP')
                } else if (e.target.innerHTML == '查 ') {
                    if (e.target.parentNode.innerHTML.includes(res)) {
                        e.target.parentNode.innerHTML = e.target.parentNode.innerHTML.replace(res, prop)
                    }
                    this.sendLog(prop, '查看宝贝ID', 'ERP')
                } else {
                    if (res == e.target.innerHTML || prop == e.target.innerHTML) {
                        this.sendLog(prop, '打开链接', 'ERP')
                    }
                }
            },
            async sendLog(proCode, action, source) {
                await SaveProductIdViewLog({ proCode, action, source })
            },
            formatImg(img){
                return matchImg(img)
            },
            async restoreStore({id, type, storeData}){
               
               let resp=  await GetVxeTableColumnCacheAsync({tableId:id});
               let store =null;
               if(resp && resp.success && resp.data){
                   store = JSON.parse(resp.data);
               }
               if(store.fixedData){
                   this.tableCols.map((item)=>{
                       item.fixed = store.fixedData[item.prop]
                   })
               }

               return store??storeData;

           },
           async updateStore({id, type, storeData}){
                let newobj = {};
                let mergearr = [];

                this.tableCols.map((item)=>{
                    if(item.merge){
                        mergearr.push({
                            name:  item.prop,
                            value: item.cols[0]['prop']
                        });
                    }
                })

                this.$refs.xTable.getColumns().map((item)=>{
                    
                    if(item.type){
                        return;
                    }
                    mergearr.map((itemm)=>{
                        if(item.field === itemm.value){
                            // item.fixed = itemm.value
                            newobj[itemm.name] = item.fixed
                        }
                    })
                    newobj[item.field]  = item.fixed;
                })
                storeData.fixedData = newobj;

                await SetVxeTableColumnCacheAsync({tableId:id,ColumnConfig:JSON.stringify(storeData)});
            },
            editRowEvent (row){
                const $table = this.$refs.xTable
                $table.setActiveRow(row)
            },
            cancelRowEvent (row) {
               const $table = this.$refs.xTable
                // 还原行数据
                $table.revertData()

            },
            exportData(filename){
                this.$refs.xTable.exportData({filename:filename,    sheetName: 'Sheet1',type: 'xlsx' })
            },
            editClosedEvent(){
                this.$emit('editclosed',true);
            },
            // 通用行合并函数（将相同多列数据合并为一行）
            mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
              const fields = ['samekey']
              const cellValue = row[column.property]
              if (cellValue && fields.includes(column.property)) {
                const prevRow = visibleData[_rowIndex - 1]
                let nextRow = visibleData[_rowIndex + 1]
                if (prevRow && prevRow[column.property] === cellValue) {
                  return { rowspan: 0, colspan: 0 }
                } else {
                  let countRowspan = 1
                  while (nextRow && nextRow[column.property] === cellValue) {
                    nextRow = visibleData[++countRowspan + _rowIndex]
                  }
                  if (countRowspan > 1) {
                    return { rowspan: countRowspan, colspan: 1 }
                  }
                }
              }
            },
            //行切换事件
            rowChange({ newValue, oldValue, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }){
                this.$emit('rowChange', row);
            },
            //批量控制列的显影
           async ShowHidenColums(arrlist){

                this.$refs.xTable.getTableColumn().collectColumn.forEach(column => {
                    if (arrlist.includes(column.property)) {
                        column.visible = false
                    }else{
                        column.visible = true
                    }
                })
                if (this.$refs.xTable) {
                    this.$refs.xTable.refreshColumn()
                }
            },
            //清空全选
            clearSelection(){
                this.$refs.xTable.clearCheckboxRow()
            },
            async checkboxall(){
                const records = this.$refs.xTable.getCheckboxRecords()
                this.$emit('checkboxall', records);
            },
            async toggleRowSelection(val){
                await this.$refs.xTable.clearCheckboxRow()
                await this.$refs.xTable.setCheckboxRow(val, true)
                console.log("组件内数据",val)
            },
            async selectChangeEvent ({ checked }) {
                const records = this.$refs.xTable.getCheckboxRecords()
                await  this.$emit('selectchangeevent',records);
            },
            rowStyleFun({ row, rowIndex, $rowIndex }){
                if(row && row.isend==1){
                    return { color:'#c9c9c9'};
                }
            },
            cellStyleFun({ row, rowIndex, column }){
                let rltStyle={};
                let col=column;
                var colArg=this.tableCols.find(x=>x.prop==col.property );

                if(colArg ){
                    if(colArg.type && (colArg.type=="images" ||colArg.type=="image"))
                        rltStyle={
                            ...rltStyle,
                            ...{
                                textAlign:"center"
                            }
                        };

                    if(colArg.align)
                        rltStyle={
                            ...rltStyle,
                            ...{
                                textAlign:colArg.align
                            }
                        };

                }
                return rltStyle;
            },
            customSortMethod({ data, sortList }){
                if(this.isRemoteSort){
                    if(sortList && sortList.length>0){
                        if(sortList[0].field != this.lastSortArgs.field || sortList[0].order!=this.lastSortArgs.order){
                            this.lastSortArgs={...sortList[0]};
                            this.$emit('sortchange',{
                                order:(this.lastSortArgs.order.indexOf('desc')>-1?'descending':'asc'),
                                prop:this.lastSortArgs.field
                            });
                        }
                    }
                }else{
                    this.$refs.xTable.sort(sortList[0].field, sortList[0].order)
                }
            },
            headerCellClassName ({ column, columnIndex }) {
                let className='';
                var col=this.tableCols.find(x=>x.prop==column.property );

                if (col && col.align ) {
                    className=' '+`vxetableheadercell-${column.align}-20221216`;
                }else if(col && col.type && (col.type=="images" || col.type=="image")){
                    className=' '+`vxetableheadercell-center-20221216`;
                }

                return className;
            },
             // 格式化函数，处理千位分隔符和小数位
                formatNumber(number){
                const absNumber = Math.abs(number);
                const options = {
                minimumFractionDigits: absNumber >= 100 ? 0 : 2,
                maximumFractionDigits: absNumber >= 100 ? 0 : 2,
                };
                return new Intl.NumberFormat('zh-CN', options).format(number);
                }, 
                            
            footerMethod ({ columns, data }) {
                const sums = [];
                if (!this.summaryarry)
                    return sums
                var arr = Object.keys(this.summaryarry);
                if (arr.length == 0)
                    return sums
                //const { columns, data } = param;
                var hashj = false;
                columns.forEach((column, index) => {
                    if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
                        var sum = this.summaryarry[column.property + '_sum'];
                        if (sum == null) return;
                        else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
                        // else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2)
                        else if (Math.abs(parseInt(sum)) < 100){
                            if(   Number(Math.abs(parseInt(sum))) === Math.floor(Number(Math.abs(parseInt(sum)))) ){
                                sums[index] = sum;
                            }
                            sums[index] = this.formatNumber(sum);
                        }
                        else sums[index] = this.formatNumber(sum)

                    }
                    else sums[index] = ''
                });
              /*  if (this.summarycolumns.length == 0) {
                    this.summarycolumns = columns;
                    this.initsummaryEvent();
                } */
                return [sums]
            },
            initsummaryEvent() {
                let self = this;
                let table;
                if(this.tablekey) table = document.querySelector('[name='+this.tablekey+'] .vxe-table--footer-wrapper>table');
                else table = document.querySelectorAll('.vxe-table--footer-wrapper>table');
                if(table?.length>0) table = table[0]
                this.$nextTick(() => {
                    self.summarycolumns.forEach((column, index) => {
                        if (column.property) {
                            table.rows[0].cells[index].style.cursor= "pointer";
                            table.rows[0].cells[index].style.color= "red";
                        }
                    })
                })
            },

            footercellclick({ items, $rowIndex, column, columnIndex, $columnIndex, $event }){
                let self = this;
                var  col = findcol(self.tableCols, column.property);
                if (col && col.summaryEvent)
                    self.$emit('summaryClick', column.property)

                function findcol(cols, property) {
                    let column;
                    for (var i = 0; i < cols.length; i++) {
                        var c = cols[i];
                        if (column) break
                        else if (c.prop && c.prop.toLowerCase() == property.toLowerCase()) {
                            column = c;
                            break
                        }
                        else if (c.cols && c.cols.length > 0) column = findcol(c.cols, property)
                    }
                    return column
                }
            }
        }
    }
</script>

<style lang="scss" scoped>
        .vxe-table--render-default.border--default .vxe-table--header-wrapper
        {
            background-color: #fafbff;
        }
        /*斑马线颜色*/
        .vxe-table--render-default .vxe-body--row.row--stripe {
            background-color: #fafbff;
        }
        .vxe-table--render-default .vxe-body--row.row--current {
            background-color: #e5ecf5;
        }
        /*滚动条整体部分*/
        .mytable-scrollbar20221212 ::-webkit-scrollbar {
          width: 10px;
          height: 10px;
        }
        /*滚动条的轨道*/
        .mytable-scrollbar20221212 ::-webkit-scrollbar-track {
          background-color: #FFFFFF;
        }
        /*滚动条里面的小方块，能向上向下移动*/
        .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
          background-color: #bfbfbf;
          border-radius: 5px;
          border: 1px solid #F1F1F1;
          box-shadow: inset 0 0 6px rgba(0,0,0,.3);
        }
       .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
          background-color: #A8A8A8;
        }
        .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
          background-color: #787878;
        }
        /*边角，即两个滚动条的交汇处*/
       .mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
          background-color: #FFFFFF;
        }

        // 图片大小
        .mytable-scrollbar20221212  .images20221212{
          max-width: 150px;max-height: 150px;
          width:40px !important;
          height:40px  !important;
        }

        // 图片张数标记
        .mytable-scrollbar20221212 .badgeimage20221212 .el-badge__content.is-fixed{
            top:10px;
        }

        /*  工具箱位置 自动分配 */
        .vxetoolbar20221212{
            position:absolute ;
            top: 20px;
            right: 5px;
            padding-top:0;
            padding-bottom:0;
            z-index: 999;
            background-color: rgb(255 255 255 / 0%);
        }

        .vxetableheadercell-left-20221216
        {
            text-align: left;
        }

        .vxetableheadercell-center-20221216
        {
            text-align: center;
        }

        .vxetableheadercell-right-20221216
        {
            text-align: right;
        }
        .vxe-icon-ellipsis-h:hover{
            color: #409EFF;
            margin-left: 2px;
            background-color: #F1F1F1;
        }
        .vxe-icon-ellipsis-h{
            color: #999;
            font-size: 15px;
        }
        .vxe-icon-file-txt:hover{
            color: #409EFF;
            margin-left: 2px;
            background-color: #F1F1F1;
            font-weight: 600;
        }
        .vxe-icon-file-txt{
            color: #999;
            font-size: 15px;
        }
        .vxetablecss{
            margin: 0;
        }
        ::v-deep span.vxe-cell--item {
            cursor: pointer !important;
        }
</style>
