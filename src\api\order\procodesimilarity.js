import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/procodesimilarity/`

//分页查询主数据
export const pageProCodeSimilarity = (params,config ={}) =>{
    return request.post(apiPrefix+'PageProCodeSimilarityAsync', params, config)
}

//导出
export const exportProCodeSimilarity =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportProCodeSimilarityAsync',{params: params, ...config})
}

//导出系列编码全部
export const exportProCodeSimilarityListNew =(params,config ={responseType: 'blob'}) =>{
    return request.post(apiPrefix + 'ExportProCodeSimilarityListNewAsync',params,config)
}

//导出
export const exportProCodeSimilarity1 =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportProCodeSimilarityListAsync',{params: params, ...config})
}

//导出详情
export const exportProCodeSimilarityDetailList =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportProCodeSimilarityDetailListAsync',{params: params, ...config})
}


//获取最新更新时间
export const getLastUpdateTime = (params,config ={}) =>{
    return request.get(apiPrefix+'GetLastUpdateTimeAsync', params, config)
}

//获取最新更新时间
export const getLastUpdateGroupTime = (params,config ={}) =>{
    return request.get(apiPrefix+'GetLastUpdateGroupTimeAsync', params, config)
}

//获取最新更新时间
export const getProCodeSimilarityImportLogTime = (params,config ={}) =>{
    return request.get(apiPrefix+'GetProCodeSimilarityImportLogTimeAsync', params, config)
}

//获取最新更新时间
export const getProCodeSimilarityGroupImportLogTime = (params,config ={}) =>{
    return request.get(apiPrefix+'GetProCodeSimilarityGroupImportLogTimeAsync', params, config)
}

//分页查询明细数据
export const pageProCodeSimilarityDetail = (params,config ={}) =>{
    return request.post(apiPrefix+'PageProCodeSimilarityDetailAsync', params, config)
}

//分页获取系列编码数据
export const pageSeriesGoods = (params,config ={}) =>{
    return request.post(apiPrefix+'PageSeriesGoodsAsync', params, config)
}

//分页获取系列编码数据
export const pageSeriesGoodsPring = (params,config ={}) =>{
    return request.post(apiPrefix+'PageSeriesGoodsPringAsync', params, config)
}

//分页获取系列编码对应商品编码
export const getSeriesGoodsCode = (params,config ={}) =>{
    return request.post(apiPrefix+'GetSeriesGoodsCodeAsync', params, config)
}

//分页获取主产品编码数据
export const pageProCodeSimilarityGoods = (params,config ={}) =>{
    return request.post(apiPrefix+'PageProCodeSimilarityGoodsAsync', params, config)
}

//分页获取明细产品编码数据
export const pageProCodeSimilarityDetailGood = (params,config ={}) =>{
    return request.post(apiPrefix+'PageProCodeSimilarityDetailGoodsAsync', params, config)
}

//获取主数据summary
export const getProCodeSimilaritySummary = (params,config ={}) =>{
    return request.post(apiPrefix+'GetProCodeSimilaritySummaryAsync', params, config)
}

//获取系列编码公共
export const getPublicStyleCodeAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'GetPublicStyleCodeAsync', params, config)
}

//获取明细数据summary
export const getProCodeSimilarityDetailSummary = (params,config ={}) =>{
    return request.post(apiPrefix+'GetProCodeSimilarityDetailSummaryAsync', params, config)
}

//获取订单量报表
export const proCodeSimilarityAnalysis = (params,config ={}) =>{
    return request.get(apiPrefix+'ProCodeSimilarityAnalysis', {params: params, ...config})
}

export const updatecustomerservicergroup = (params, config = {}) => {
    return request.get(apiPrefix + 'UpdateCustomerServicerGroupAsync', { params: params, ...config })
}

export const deleteCustomerServicerGroup = (params, config = {}) => {
    return request.get(apiPrefix + 'DeleteCustomerServicerGroupAsync', { params: params, ...config })
}

//获取订单量报表
export const queryProCodeSimilarityAnalysis = (params,config ={}) =>{
    return request.get(apiPrefix + 'QueryProCodeSimilarityAnalysisAsync', {params: params, ...config})
}

//获取周转天数详情
export const proDaysTurnover = (params,config ={}) =>{
    return request.get(apiPrefix + 'ProDaysTurnoverAsync', {params: params, ...config})
}

//获取周转天数明细
export const getDayZZRateDetail = (params,config ={}) =>{
    return request.get(apiPrefix + 'GetDayZZRateDetail', {params: params, ...config})
}

//获取周转天数详情
export const getProCodeSimilarityStateName = (params,config ={}) =>{
    return request.get(apiPrefix + 'GetProCodeSimilarityStateName', {params: params, ...config})
}

//添加系列状态
export const addProCodeSimilarityStateName = (params,config ={}) =>{
    return request.post(apiPrefix+'AddProCodeSimilarityStateName', params, config)
}

//添加状态
export const addProCodeSimilarityState = (params,config ={}) =>{
    return request.post(apiPrefix+'AddProCodeSimilarityState', params, config)
}

//删除状态
export const deleteProCodeSimilarityState = (params,config ={}) =>{
    return request.post(apiPrefix+'DeleteProCodeSimilarityState', params, config)
}

//添加系列编码增长率
export const addTbProCodeSimilarityGrowSet = (params,config ={}) =>{
    return request.post(apiPrefix+'AddTbProCodeSimilarityGrowSet', params, config)
}

//导出系列编码增长率
export const ExportTbProCodeSimilarityGrow =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportTbProCodeSimilarityGrowAsync', {params: params, ...config})
}

//获取系列编码增长率
export const getTbProCodeSimilarityGrow = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetTbProCodeSimilarityGrowAsync', params, config)
}

//设置产品保护状态
export const setTbProCodeSimilarityGrowProviteStatus = (params,config ={}) =>{
    return request.post(apiPrefix + 'SetProCodeSimilarityProviteStatus', params, config)
}

//分页查询明细数据
export const pageProCodeSimilarityForMonthDetail = (params,config ={}) =>{
    return request.post(apiPrefix+'PageProCodeSimilarityForMonthDetailAsync', params, config)
}

//分页查询主数据
export const pageProCodeSimilarityByNewReport = (params,config ={}) =>{
    return request.post(apiPrefix+'PageProCodeSimilarityByNewReportAsync', params, config)
}

//获取主数据summary
export const getProCodeSimilarityForMonthSummary = (params,config ={}) =>{
    return request.post(apiPrefix+'GetProCodeSimilarityForMonthSummaryAsync', params, config)
}


//获取主数据summary
export const proCodeSimilarityReceivIsGroupId = (params,config ={}) =>{
  return request.post(apiPrefix+'ProCodeSimilarityReceivIsGroupId', params, config)
}

export const addProCodeSimilarityReceiv = (params,config ={}) =>{
  return request.post(apiPrefix+'AddProCodeSimilarityReceiv', params, config)
}
//---------------------
export const getProCodeSimilarityDetailAsync = (params,config ={}) =>{
    return request.post(apiPrefix+'GetProCodeSimilarityDetailAsync', params, config)
}

//---------------------
export const getProCodeDetailAsync = (params,config ={}) =>{
    return request.get(apiPrefix+'GetProCodeDetailAsync', {params: params, ...config})
}

//系列编码报表-获取所有设置的系列编码
export const getAllTbProCodeSimilarityByName = (params, config = {}) => { return request.post(apiPrefix + 'GetAllTbProCodeSimilarityByName?name='+params.name, config) }


//系列编码报表-设置的系列编码的采购类目(公司类目)
export const setStylesBrandProductCategory = (params,config ={}) =>{
  return request.post(apiPrefix+'SetStylesBrandProductCategory', params, config)
}

//GetStyleCodePlatformHKDayCount 
export const getStyleCodePlatformHKDayCount = (params,config ={}) =>{
  return request.post(apiPrefix+'GetStyleCodePlatformHKDayCount', params, config)
}