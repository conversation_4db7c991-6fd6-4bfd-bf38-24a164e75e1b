import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/orderReplaceWaybillNo/`

//淘系换单号-获取最新导入时间
export const getLatestSyncTime = (params, config = {}) => {
  return request.get(apiPrefix + 'GetLatestSyncTime', { params: params, ...config })
}
//淘系换单号-获取快递公司
export const getExpressCompanyList = (params, config = {}) => {
  return request.get(apiPrefix + 'GetExpressCompanyList', { params: params, ...config })
}
//淘系换单号-获取淘系换单号列表
export const getOrderReplaceWaybillNoPage = (params, config = {}) => {
  return request.post(apiPrefix + 'GetOrderReplaceWaybillNoPage', params, config)
}
//淘系换单号-导出淘系换单号
export const exportOrderReplaceWaybillNo = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'ExportOrderReplaceWaybillNo', params, config)
}
//淘系换单号-导入淘系换单号
export const importOrderReplaceWaybillNo = (params, config = {}) => {
  return request.post(apiPrefix + 'ImportOrderReplaceWaybillNo', params, config)
}
