import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/purchaseOrderGoodsPostage/`

//获取采购岗位名称 GetPurchasePostNameList
export const getPurchasePostNameList = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchasePostNameList', params, config) }

//获取采购架构名称 GetPurchaseDeptNameList
export const getPurchaseDeptNameList = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseDeptNameList', params, config) }

//获取采购架构名称 GetPurchaseDeptNameList
export const getPurchaseDept2List = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseDept2List', params, config) }

//获取采购单商品包邮情况分页列表 GetPurchaseOrderGoodsPostagePageList
export const getPurchaseOrderGoodsPostagePageList = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseOrderGoodsPostagePageList', params, config) }

//获取采购单商品包邮情况分页列表 ExportPurchaseOrderGoodsPostagePageList
export const exportPurchaseOrderGoodsPostagePageList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPurchaseOrderGoodsPostagePageList', params, config) }

//获取不包邮数据 GetPurchaseOrderGoodsPostageByCharge
export const getPurchaseOrderGoodsPostageByCharge = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseOrderGoodsPostageByCharge', params, config) }

//包邮数据保存 SavePurchaseOrderGoodsPostageByFree
export const savePurchaseOrderGoodsPostageByFree = (params, config = {}) => { return request.post(apiPrefix + 'SavePurchaseOrderGoodsPostageByFree', params, config) }

//获取包邮数据 GetPurchaseOrderGoodsPostageByFree
export const getPurchaseOrderGoodsPostageByFree = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseOrderGoodsPostageByFree', params, config) }

//不包邮数据保存 SavePurchaseOrderGoodsPostageByCharge
export const savePurchaseOrderGoodsPostageByCharge = (params, config = {}) => { return request.post(apiPrefix + 'SavePurchaseOrderGoodsPostageByCharge', params, config) }

//删除商品编码数据 DeletePurchaseOrderGoodsPostage
export const deletePurchaseOrderGoodsPostage = (params, config = {}) => { return request.post(apiPrefix + 'DeletePurchaseOrderGoodsPostage', params, config) }

//按商品编码查历史日志 GetPurchaseOrderGoodsPostageHisLogByGoodsCode
export const getPurchaseOrderGoodsPostageHisLogByGoodsCode = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchaseOrderGoodsPostageHisLogByGoodsCode', params, config) }