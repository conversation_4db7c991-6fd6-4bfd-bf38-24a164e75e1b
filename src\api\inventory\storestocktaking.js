import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/storestocktaking/`

export const pageStoreStockTaking = (params, config = {}) => { return request.post(apiPrefix + 'PageStockTakingAsync',  params, config ) }

export const pageStockTakingGroup =(params, config = {}) => { return request.post(apiPrefix + 'PageStockTakingGroupAsync',  params, config )}

export const pageStoreStockTakingLog = (params, config = {}) => { return request.get(apiPrefix + 'PageStockTakingLogAsync', { params: params, ...config }) }

export const importStoreStockTakingLog= (params, config = {}) => {return request.post(apiPrefix + 'ImportStoreStockTakingLogAsync', params, config)}

export const getStoreStockTakingApproveLog= (params, config = {}) => {return request.post(apiPrefix + 'GetStoreStockTakingApproveLogAsync', params, config)}
//盘点操作日志导出
export const exportStoreStockTakingLog = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportStoreStockTakingLogAsync', params, config) }
