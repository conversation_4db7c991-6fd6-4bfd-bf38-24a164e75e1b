<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="referrer" content="no-referrer" />
        <link rel="stylesheet" href="/approvalform/html/api/elment.css">
        <script src="/approvalform/html/api/vue.min.js"></script>
        <script src="/approvalform/html/api/elment.js"></script>
        <script src="/approvalform/html/api/jquery.min.js"></script>
        <script src="/approvalform/html/api/html2canvas.js"></script>
        <title>当日申报数据</title>
        <style type="text/css">
            .linebreak {
                overflow: hidden;
                /*超出部分隐藏*/
                text-overflow: ellipsis;
                /* 超出部分显示省略号 */
                white-space: nowrap;
                /*规定段落中的文本不进行换行 */
                width: 100%;
            }
            .el-pagination__jump {
                margin-left:0px;
            }
        </style>
    </head>
    <body>
        <div id="app">
            <el-container direction="vertical" style=" border: 1px #ccc solid;">
                <template>
                    <el-table ref="tableBox" :data="list" align="center" style="width: 100%;" row-key="id" border :max-height="tableHeight">
                        <el-table-column type="index" min-width="20" fixed></el-table-column>
                        <el-table-column prop="status" label="状态" min-width="100"></el-table-column>
                        <el-table-column prop="styleCode" label="系列编码" min-width="100"></el-table-column>
                        <el-table-column prop="goodsCode" label="商品编码" min-width="100"></el-table-column>
                        <el-table-column prop="qty" label="数量" min-width="80"></el-table-column>
                        <el-table-column prop="applyTime" label="申报时间" min-width="180"></el-table-column>
                        <el-table-column prop="applicant" label="申报人" min-width="100"></el-table-column>
                        <el-table-column prop="groupName" label="运营组" min-width="100"></el-table-column>
                        <el-table-column prop="businessId" label="流程编号" min-width="100"></el-table-column>
                        <el-table-column prop="sourceType" label="来源类型" min-width="100"></el-table-column>
                    </el-table>
                </template> 
            </el-container>
        </div>
        <script>
            var vm = new Vue({
                el: '#app',
                data: function () {
                    return {
                        thisLonding: true,
                        list: [],
                        tableHeight: null,
                        type: null,
                        indexNo: null,
                        batchId: null,
                        styleCode: null,
                        batchNo: null
                    }
                },
                async mounted () {
                    this.getStyleSheetInfo();
                    this.beginShowing();
                },
                methods: {
                    beginShowing () {
                        this.$nextTick(function () {
                            // 文档显示区域的高度 -
                            if (this.$refs.tableBox) {
                                this.tableHeight = 835;
                                this.$refs.tableBox.doLayout()
                            }
                        })
                    },
                    async getStyleSheetInfo () {
                        var me = this;
                        let searchURL = window.location.search;
                        searchURL = searchURL.substring(1, searchURL.length);
                        this.batchNo = searchURL.split("&")[0].split("=")[1];
                        if(this.batchNo=="1" || this.batchNo=="2"){
                            if(this.batchNo == "1"){ 
                                this.indexNo = searchURL.split("&")[1].split("=")[1];
                                $.ajax({
                                    url: '/api/Inventory/PurchaseOrderNew/GetToDayApplyPurchaseOrderGoodsInfo?indexNo=' + this.indexNo,
                                    type: 'GET',
                                    dataType: 'json',
                                    data: { },
                                    success: function (response) {
                                        me.list = response.data;
                                    },
                                    error: function (xhr, textStatus, errorThrown) {
                                        console.log('Error: ', errorThrown);
                                    }
                                });
                            }else{
                                this.batchId = searchURL.split("&")[1].split("=")[1];
                                this.styleCode = searchURL.split("&")[2].split("=")[1];
                                $.ajax({
                                    url: '/api/Inventory/GoodsCodeStock/GetToDayApplyStockGoodsInfo?batchId=' + this.batchId + '&styleCode=' + this.styleCode,
                                    type: 'GET',
                                    dataType: 'json',
                                    data: { },
                                    success: function (response) {
                                        me.list = response.data;
                                    },
                                    error: function (xhr, textStatus, errorThrown) {
                                        console.log('Error: ', errorThrown);
                                    }
                                });
                            }
                        }else{
                            $.ajax({
                                url: '/api/Inventory/GoodsCodeStock/GetOnDayApplyOtherGoodsInfo?batchNo=' + this.batchNo,
                                type: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                dataType: 'json',
                                data: { },
                                success: function (response) {
                                    me.list = response.data;
                                },
                                error: function (xhr, textStatus, errorThrown) {
                                    console.log('Error: ', errorThrown);
                                }
                            }); 
                        }
                    },
                }
            });
        </script>
    </body>
</html>