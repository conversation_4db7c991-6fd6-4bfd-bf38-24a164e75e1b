import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_PackProcess}/PackagesProcessing/`
//新增/编辑包装加工信息
export const editPackagesProcess = (params, config) => { return request.post(apiPrefix + 'EditPackagesProcess', params, config) }
//获取编辑包装加工信息
export const getPackagesProcesseditInfo = (params, config) => { return request.get(apiPrefix + 'GetPackagesProcessingDetial?packagesProcessId=' + params, config) }
//获取包装加工列表
export const getPackagesProcessingListAsync = (params, config) => { return request.post(apiPrefix + 'GetPackagesProcessingListAsync', params, config) }
//删除包装加工记录信息
export const deletePackagesProcessing = (params, config = {}) => { return request.post(apiPrefix + 'DeletePackagesProcessing', { 'packagesProcessIds': params, config }) }
//获取包装内表明细
export const getPackagesProcessingDetialListAsync = (params, config) => { return request.get(apiPrefix + 'GetPackagesProcessingDetialListAsync?packagesProcessId=' + params, config) }
//编辑操作记录
export const editPackagesProcessRecord = (params, config) => { return request.post(apiPrefix + 'EditPackagesProcessRecord', params, config) }
//新增操作记录
export const addPackagesProcessRecord = (params, config) => { return request.post(apiPrefix + 'AddPackagesProcessRecord', params, config) }
//加工调入
export const updateProcessBatch = (params, config) => { return request.post(apiPrefix + 'UpdateProcessBatch', params, config) }
//成品调出
export const updateDispatcheBatch = (params, config) => { return request.post(apiPrefix + 'UpdateDispatcheBatch', params, config) }
//删除操作记录信息
export const deleteReord = (params, config = {}) => { return request.post(apiPrefix + 'DeleteReord?recordIds=' + params.recordIds + '&typeId=' + params.typeId, config) }
//新增/编辑包装备注信息
export const editRecord = (params, config) => { return request.post(apiPrefix + 'UpdateRemark', params, config) }
//获取备注信息
export const getRemarkList = (params, config = {}) => { return request.get(apiPrefix + 'GetRemarkInfo?packagesProcessId=' + params, config) }
//删除备注
export const delRemark = (params, config = {}) => { return request.post(apiPrefix + 'DelRemark?remarkId=' + params.remarkId, { params: params, ...config }) }

//更新紧急程度
export const updateUrgencyDegree = (params, config = {}) => { return request.post(apiPrefix + 'UpdateUrgencyDegree?processId=' + params.processId + '&degreeType=' + params.degreeType, { params: params, ...config }) }
//更新完成状态
export const updateFinishState = (params, config = {}) => { return request.post(apiPrefix + 'UpdateState', { 'packagesProcessIds': params.processId, ...config }) }
//批量更新合格证
export const updateCertificateBatch = (params, config = {}) => { return request.post(apiPrefix + 'UpdateCertificateBatch', params) }
//导出包装加工成品
export const exportPackagesProcessingListAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPackagesProcessingList', params, config) }
//获取包装加工明细
export const exportPackagesProcessingDetialListAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPackagesProcessingDetialList', params, config) }

//获取明细操作
export const getPackagesProcessingRecordListAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetPackagesProcessingRecordListAsync', { params: params, ...config }) }

//获取人物
export const getRecordUser = (params, config = {}) => { return request.get(apiPrefix + 'GetRecordUser', { params: params, ...config }) }

export const getAllUser = (params, config = {}) => { return request.get(apiPrefix + 'GetAllUser', { params: params, ...config }) }

//获取登录人userid
export const getCurrentUser = (params, config = {}) => { return request.post(apiPrefix + 'GetCurrentUser', params, config) }

//获取状态
export const getUrgencyDegreeyList = (params, config = {}) => { return request.get(apiPrefix + 'GetUrgencyDegreeyList', { params: params, ...config }) }

//成品调出
export const pckagesProcessingCallOut = (params, config = {}) => { return request.post(apiPrefix + 'PckagesProcessingCallOut', params, config) }

// 成品调入
export const pckagesProcessingCallIn = (params, config = {}) => { return request.post(apiPrefix + 'PckagesProcessingCallIn', params, config) }

//获取调出明细
export const getCallOutRecordListAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetCallOutRecordListAsync', { params: params, ...config }) }

//批量统计
// export const statAction = (params, config = {}) => {  return request.get(apiPrefix + 'StatAction', { params: params, ...config }) }
export const statAction = (params, config = {}) => { return request.post(apiPrefix + 'statAction', { 'packagesProcessIds': params, config }) }

//获取批量统计列表
export const getStatListAsync = (params, config) => { return request.post(apiPrefix + 'GetStatListAsync', params, config) }

//获取批量存档列表
export const getArchiveListAsync = (params, config) => { return request.post(apiPrefix + 'GetArchiveListAsync', params, config) }

//批量存档
export const archiveAction = (params, config = {}) => { return request.post(apiPrefix + 'ArchiveAction', { 'packagesProcessIds': params, config }) }

//取消统计
export const unStatAction = (params, config = {}) => { return request.post(apiPrefix + 'UnStatAction', { 'packagesProcessIds': params, config }) }

//半成品
export const updateHalfProductImg = (params, config) => { return request.post(apiPrefix + 'UpdateHalfProductImg', params, config) }

//保存排序
export const saveDataOrderListDataAsync = (params, config) => { return request.post(apiPrefix + 'SaveDataOrderListDataAsync', params, config) }

//修改工价
export const updateWorkPrice = (params, config) => { return request.post(apiPrefix + 'UpdateWorkPrice', params, config) }

//获取仓库信息
export const getAllWarehouse = (params, config = {}) => { return request.get(apiPrefix + 'GetAllWarehouse', { params: params, ...config }) }

//编辑调出调入信息
export const editCallInOutRecord = (params, config = {}) => { return request.post(apiPrefix + 'EditCallInOutRecord', params, config) }

//编辑调出调入发起审批
export const initiateApprovalCallInOut = (params, config = {}) => { return request.post(apiPrefix + 'InitiateApprovalCallInOut', params, config) }

//获取创建人
export const getCreateUserList = (params, config = {}) => { return request.get(apiPrefix + 'GetCreateUserList', { params: params, ...config }) }

//获取调出记录明细
export const getCallOutSelRecordListAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetCallOutSelRecordListAsync', { 'packagesProcessIds': params, ...config }) }

//编辑质检数量
export const packagesProcessQuality = (params, config = {}) => { return request.post(apiPrefix + 'PackagesProcessQuality', params, config) }

//统计页面-任务状态
export const getStatQuantity = (params, config = {}) => { return request.post(apiPrefix + 'GetStatQuantity', params, config) }

//统计页面-包装品牌
export const getStatBrandList = (params, config = {}) => { return request.post(apiPrefix + 'GetStatBrandList', params, config) }

//统计页面-包装材料
export const getStatMaterialList = (params, config = {}) => { return request.post(apiPrefix + 'GetStatMaterialList', params, config) }

//统计页面-包装机型
export const getStatMachineTypeList = (params, config = {}) => { return request.post(apiPrefix + 'GetStatMachineTypeList', params, config) }

//统计页面-包装尺寸
export const getStatPackageSizeList = (params, config = {}) => { return request.post(apiPrefix + 'GetStatPackageSizeList', params, config) }

//统计页面-任务列表
export const getStatTaskList = (params, config = {}) => { return request.post(apiPrefix + 'GetStatTaskList', params, config) }

//加工统计
export const getStatProcessList = (params, config = {}) => { return request.post(apiPrefix + 'GetStatProcessList', params, config) }

//加工内嵌
export const getStatProcessPopList = (params, config = {}) => { return request.post(apiPrefix + 'GetStatProcessPopList', params, config) }

//统计内嵌
export const getStatTaskPopList = (params, config = {}) => { return request.post(apiPrefix + 'GetStatTaskPopList', params, config) }

//批量重启
export const restartAction = (params, config = {}) => { return request.post(apiPrefix + 'RestartAction', { 'packagesProcessIds': params, config }) }

//批量终止
export const endAction = (params, config = {}) => { return request.post(apiPrefix + 'EndAction', { 'packagesProcessIds': params, config }) }

//取消标记
export const unMarkAction = (params, config = {}) => { return request.post(apiPrefix + 'UnMarkAction', { 'packagesProcessIds': params, config }) }

//标记
export const markAction = (params, config = {}) => { return request.post(apiPrefix + 'MarkAction', { 'packagesProcessIds': params, config }) }

//取消存档
export const unArchiveAction = (params, config = {}) => { return request.post(apiPrefix + 'UnArchiveAction', { 'packagesProcessIds': params, config }) }

//批量保存工价模板
export const saveWorkPriceTemplate = (params, config = {}) => { return request.post(apiPrefix + 'SaveWorkPriceTemplate', { 'packagesProcessIds': params, config }) }

//获取工价模板
export const getPriceTemplateList = (params, config = {}) => { return request.post(apiPrefix + 'GetPriceTemplateList', params, config) }

//修改模板工价
export const updateTemplatesWorkPrice = (params, config) => { return request.post(apiPrefix + 'UpdateTemplatesWorkPrice', params, config) }

//创建任务按工价二次修改
export const replaceTemplatesWorkPrice = (params, config = {}) => { return request.post(apiPrefix + 'ReplaceTemplatesWorkPrice', params, config) }

//删除
export const delPackagesPrice = (params, config = {}) => { return request.post(apiPrefix + 'DelPackagesPrice?templeteId=' + params, { ...config }) }

//导出工价模板
export const exportPackagesProcessingPricSet = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPackagesProcessingPricSet', params, config) }

//导入工价模板
export const importPriceSetAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportPriceSetAsync', params, config) }

//打印
export const getPackagesProcessingPrintList = (params, config = {}) => { return request.post(apiPrefix + 'GetPackagesProcessingPrintList', params, config) }
// export const getPackagesProcessingPrintList = (params, config = {}) => {return request.post(apiPrefix + 'GetPackagesProcessingPrintList', {'ids':params, ...config })}

//获取包装加工列表编辑页面
export const getPackagesProcessingListDetialAsync = (params, config) => { return request.post(apiPrefix + 'GetPackagesProcessingListDetialAsync', params, config) }

//获取包装加工列表
export const getPackagesProcessingProductList = (params, config) => { return request.post(apiPrefix + 'GetPackagesProcessingProductList', params, config) }

//导出商品资料
export const exportPackagesProcessingProductData = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPackagesProcessingProductData', params, config) }

//获取商品资料明细
export const getPackagesProcessingProductDetial = (params, config) => { return request.get(apiPrefix + 'GetPackagesProcessingProductDetial?goodsCode=' + params, { ...config }) }

//编辑商品资料
export const editPackagesProcessingProduct = (params, config) => { return request.post(apiPrefix + 'EditPackagesProcessingProduct', params, config) }

//获取商品资料编辑日志
export const getProductLogList = (params, config) => { return request.get(apiPrefix + 'GetProductLogList?goodsCode=' + params, { ...config }) }

//获取包装加工首页统计
export const getStatProcessHomeList = (params, config) => { return request.get(apiPrefix + 'GetStatProcessHomeList', { params: params, ...config }) }

// 更新模板图片
export const updatePriceTemplateProductImg = (params, config) => { return request.post(apiPrefix + 'UpdatePriceTemplateProductImg', params, config) }

//包装加工复制
export const copyPackagesProcessing = (params, config) => { return request.post(apiPrefix + 'CopyPackagesProcessing', params, config) }

//新增工价模板
export const addPackagesProcessingPrice = (params, config) => { return request.post(apiPrefix + 'AddPackagesProcessingPrice', params, config) }

//上传图片
export const editPackagesProcessingProductImg = (params, config) => { return request.post(apiPrefix + 'EditPackagesProcessingProductImg', params, config) }

//获取商品图片
// export const getPackagesProcessingProductImg =(params,config) =>{return request.get(apiPrefix + 'GetPackagesProcessingProductImg',params,config)}
export const getPackagesProcessingProductImg = (params, config = {}) => { return request.get(apiPrefix + 'GetPackagesProcessingProductImg', { params: params, ...config }) }


//导入包装信息
export const importPackagesProductAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportPackagesProductAsync', params, config) }

//获取包装信息品牌搜索
export const getSelList = (params, config) => { return request.get(apiPrefix + 'GetSelList', { params: params, ...config }) }

//获取其他员工列表
export const getPackagesOtherEmployees = (params, config) => { return request.get(apiPrefix + 'GetPackagesOtherEmployees', { params: params, ...config }) }

//编辑其他员工信息
export const editPackagesOtherEmployees = (params, config) => { return request.post(apiPrefix + 'EditPackagesOtherEmployees', params, config) }

//删除其他员工信息
export const delPackagesOtherEmployees = (params, config) => { return request.get(apiPrefix + 'DelPackagesOtherEmployees?id=' + params, config) }

//每日加工数量统计
export const getStatTaskProcessList = (params, config) => { return request.post(apiPrefix + 'GetStatTaskProcessList', params, config) }

//获取列表
export const getArrivalDetails = (params, config) => { return request.post(apiPrefix + 'GetArrivalDetails', params, config) }

//绑定解绑加工
export const bindArrivalDetails = (params, config) => { return request.post(apiPrefix + 'BindArrivalDetails', params, config) }

//获取卸货数据列表
export const getunloadListAsync = (params, config) => { return request.post(apiPrefix + 'GetunloadListAsync', params, config) }

//修改卸货数据工价
export const updateUnloadWorkPriceAsync = (params, config) => { return request.post(apiPrefix + 'UpdateUnloadWorkPriceAsync', params, config) }

//修改移箱入库数据工价
export const updateMoveStockWorkPriceAsync = (params, config) => { return request.post(apiPrefix + 'UpdateMoveStockWorkPriceAsync', params, config) }

//获取移箱入库列表
export const getMoveStockListAsync = (params, config) => { return request.post(apiPrefix + 'GetMoveStockListAsync', params, config) }

//获取移箱入库搜索人员
export const getMoveStockSelMemberAsync = (params, config) => { return request.get(apiPrefix + 'GetMoveStockSelMemberAsync', { params: params, ...config }) }

//导出卸货数据
export const exportPackagesProcessingUnloadData = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPackagesProcessingUnloadData', params, config) }

//导入卸货数据
export const importUnloadSetAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportUnloadSetAsync', params, config) }

//导出移箱入库数据
export const exportMoveStockData = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportMoveStockData', params, config) }

//导入移箱入库数据
export const importMoveStockAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportMoveStockAsync', params, config) }

//导出计件提成信息
export const getPieceDeductExportData = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'GetPieceDeductExportData', params, config) }

//删除卸货数据
export const deleteUnloadAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteUnloadAsync', params, config) }

//删除移箱入库数据
export const deleteMoveStockAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteMoveStockAsync', params, config) }

//一键删除卸货数据
export const deleteUnloadAllAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteUnloadAllAsync', params, config) }

//一键删除移箱入库数据
export const deleteMoveStockAllAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteMoveStockAllAsync', params, config) }

//加工编码绑定-成品绑定半成品-导出
export const exportPackagesWorkPriceData = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPackagesWorkPriceData', params, config) }

//加工编码绑定-成品绑定半成品-工价设置导入
export const importWorkPriceAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportWorkPriceAsync', params, config) }

//加工编码绑定-成品绑定半成品-更新图片
export const updateFinishBindHalfProductImg = (params, config = {}) => { return request.post(apiPrefix + 'UpdateFinishBindHalfProductImg', params, config) }

//加工编码绑定-成品绑定半成品-创建加工
export const createPackagesProcessing = (params, config = {}) => { return request.post(apiPrefix + 'CreatePackagesProcessing', params, config) }

//加工编码绑定-成品绑定半成品-删除成品绑定
export const delFinishBindHalf = (params, config = {}) => {  return request.delete(apiPrefix + 'DelFinishBindHalf', { params: params, ...config })}

//加工编码绑定-成品绑定半成品-编辑绑定半成品
export const updateFinishBindHalfDetial = (params, config = {}) => { return request.post(apiPrefix + 'UpdateFinishBindHalfDetial', params, config) }

//加工编码绑定-成品绑定半成品-获取成品绑定半成品列表
export const getFinishBindHalfList = (params, config = {}) => { return request.post(apiPrefix + 'GetFinishBindHalfList', params, config) }

//加工编码绑定-成品绑定半成品-获取半成品绑定成品列表
export const getHalfBindFinishList = (params, config = {}) => { return request.post(apiPrefix + 'GetHalfBindFinishList', params, config) }

//获取半成品绑定成品明细
export const getHalfBindFinishDetail = (params, config) => { return request.get(apiPrefix + 'GetHalfBindFinishDetail', { params: params, ...config }) }

//获取半成品绑定成品明细
export const getFinishBindHalfDetail = (params, config) => { return request.get(apiPrefix + 'GetFinishBindHalfDetail', { params: params, ...config }) }

//包装加工批量操作工价模板
export const saveWorkPrice = (params, config = {}) => { return request.post(apiPrefix + 'SaveWorkPrice', params, config) }

//加工编码绑定-成品绑定半成品-获取半成品绑定成品列表
export const addPackagesFinishBindHalf = (params, config = {}) => { return request.post(apiPrefix + 'AddPackagesFinishBindHalf', params, config) }
//RestoreAction  还原删除

//获取列表类型集合
export const getPackagesTypeListAsync = (params, config) => { return request.get(apiPrefix + 'GetPackagesTypeListAsync', { params: params, ...config }) }

//获取包装加工列表成品编码
export const getPackagesProcessingListFinishCodeAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetPackagesProcessingListFinishCodeAsync', params, config) }
// //新增和更新员工，新增和更新薪资核算信息
// export const addOrUpdatePersonnelPositionAsync =(params,config) =>{return request.post(apiPrefix + 'AddOrUpdatePersonnelPositionAsync',params,config)}

// //人员排序
// export const sortPersonnelPosition =(params,config) =>{return request.post(apiPrefix + 'SortPersonnelPosition',params,config)}

// //删除人员信息
// export const delPersonnelPosition =(params,config) =>{return request.get(apiPrefix + 'DelPersonnelPosition', {params: params,  ...config })}

// //设置是否核算
// export const setPersonnelIsHsAsync =(params,config) =>{return request.get(apiPrefix + 'SetPersonnelIsHsAsync', {params: params,  ...config })}

// //获取员工设置列表/获取薪资核算列表
// export const getPersonnelPositionAsync =(params,config) =>{return request.post(apiPrefix + 'GetPersonnelPositionAsync',params,config)}

// //导入员工设置/薪资
// export const importPackagesPersonnel =(params,config) =>{return request.post(apiPrefix + 'ImportPackagesPersonnel',params,config)}

// //工作统计列表
// export const getPersonnelPositionWorkInfo =(params,config) =>{return request.get(apiPrefix + 'GetPersonnelPositionWorkInfo', {params: params,  ...config })}

// //历史版本存储
// export const runCalculatePersonnelWorkInfo =(params,config) =>{return request.get(apiPrefix + 'RunCalculatePersonnelWorkInfo', {params: params,  ...config })}

// //获取历史版本下拉信息
// export const getHistoryVersionInfo =(params,config) =>{return request.get(apiPrefix + 'GetHistoryVersionInfo', {params: params,  ...config })}

// //删除历史版本
// export const delHistoryVersionInfo =(params,config) =>{return request.get(apiPrefix + 'DelHistoryVersionInfo', {params: params,  ...config })}

// //薪资核算列表历史版本
// export const getHistorytPersonnelPositionInfo =(params,config) =>{return request.get(apiPrefix + 'GetHistorytPersonnelPositionInfo', {params: params,  ...config })}

// //加工记录列表历史版本
// export const getHistoryRecordTaskInfo =(params,config) =>{return request.get(apiPrefix + 'GetHistoryRecordTaskInfo', {params: params,  ...config })}

// //加工调入调出记录列表历史版本
// export const getHistoryRecordCallInOutTaskInfo =(params,config) =>{return request.get(apiPrefix + 'GetHistoryRecordCallInOutTaskInfo', {params: params,  ...config })}

// //工作统计列表历史版本 /
// export const getHistoryPersonnelPositionWorkInfo =(params,config) =>{return request.get(apiPrefix + 'GetHistoryPersonnelPositionWorkInfo', {params: params,  ...config })}

// //钉钉发送薪资通知 /
// export const sendSalery =(params,config) =>{return request.post(apiPrefix + 'SendSalery',params,config)}

// //导出成品信息历史版本 /
// export const exportHistoryPackagesProcessingList =(params,config) =>{return request.post(apiPrefix + 'ExportHistoryPackagesProcessingList',params,config)}

// //导出半成品信息 /
// export const exportPackagesProcessingDetialList =(params,config) =>{return request.post(apiPrefix + 'ExportPackagesProcessingDetialList',params,config)}

// //获取设置信息根据设置id /
// export const getPackagesSetDataById =(params,config) =>{return request.get(apiPrefix + 'GetPackagesSetDataById', {params: params,  ...config })}

// //获取设置信息根据类型 //
// export const getPackagesSetData =(params,config) =>{return request.get(apiPrefix + 'GetPackagesSetData', {params: params,  ...config })}

// //新增编辑保存 /
// export const savePackagesSet =(params,config) =>{return request.post(apiPrefix + 'SavePackagesSet',params,config)}

// //删除设置
// export const deletePackagesSet =(params,config) =>{return request.get(apiPrefix + 'DeletePackagesSet', {params: params,  ...config })}

// //设置保存排序 /
// export const saveDataOrderListDataAsync =(params,config) =>{return request.post(apiPrefix + 'SaveDataOrderListDataAsync',params,config)}

//查询最晚库存更新时间
export const getLastUpdateStockTime = (params, config = {}) => { return request.get(apiPrefix + 'GetLastUpdateStockTime', { params: params, ...config }) }

//编辑成品
export const updateFinishBindDetial = (params, config = {}) => { return request.post(apiPrefix + 'UpdateFinishBindDetial', params, config) }

//刷新库存
export const refreshHalfProductStock = (params, config = {}) => { return request.get(apiPrefix + 'RefreshHalfProductStock', { params: params, ...config }) }

//查看明细
export const getFinishBindHalfDetailList = (params, config = {}) => { return request.get(apiPrefix + 'GetFinishBindHalfDetailList', { params: params, ...config }) }

//替换工价
export const updatePackProcessWorkPriceList = (params, config = {}) => { return request.post(apiPrefix + 'UpdatePackProcessWorkPriceList', params, config) }

//成品绑定半成品搜索人员
export const getSelBindList = (params, config = {}) => { return request.get(apiPrefix + 'GetSelBindList', { params: params, ...config }) }


//加工统计
export const getMainTaskStatistics= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainTaskStatistics', params, config) }

//加工统计弹窗-数据统计
export const getMainStatTaskPopList= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatTaskPopList', params, config) }

//班次汇总
export const getMainStatPlatStatistics= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatPlatStatistics', params, config) }

//班次统计-弹窗
export const getMainStatPlatGroupPopStatisticsChat= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatPlatGroupPopStatisticsChat', params, config) }

//班次统计-趋势图
export const getMainStatPlatStatisticsChat= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatPlatStatisticsChat', params, config) }

//班次统计-加工提成前10-趋势图
export const getMainStatPlatGroupStatisticsChat= (params, config = {}) =>
{ return request.post(apiPrefix + 'GetMainStatPlatGroupStatisticsChat', params, config) }

//耗材图片导入
export const updateConsoumableProductImg = (params, config) => { return request.post(apiPrefix + 'UpdateConsoumableProductImg', params, config) }
