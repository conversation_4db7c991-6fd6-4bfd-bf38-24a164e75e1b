import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_UpLoad}/File/`

export const upLoadFile = (params, config = {}) => {return request.post(apiPrefix + 'UpLoadFileAsync', params, config)}
export const upLoadImage = (params, config = {}) => {return request.post(apiPrefix + 'UpLoadImageAsync', params, config)}
//上传后会压缩图片
export const upLoadImageCompress = (params, config = {}) => {return request.post(apiPrefix + 'UpLoadImageCompressAsync', params, config)}
export const upLoadTempImage = (params, config = {}) => {return request.post(apiPrefix + 'UpLoadTempImageAsync', params, config)}

export const uploadYYFileVideo = (params, config = {}) => {return request.post(apiPrefix + 'UploadYYFileVideoAsync', params, config)}
