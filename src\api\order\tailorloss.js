import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/tailorloss/`

export const getMyGoodsByCode = (params, config = {}) => { return request.get(apiPrefix + 'GetMyGoodsByCode', { params})} 

//裁剪-原材料出库-导入
export const importTailorOrder = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportTailorOrder', params, config)
}

//裁剪订单分页查询
export const getTailorOrderPageList = (params,config ={}) =>{
    return request.post(apiPrefix+'GetTailorOrderPageList', params, config)
}

//裁剪-原材料出库-导入
export const importMaterialOutStore = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportMaterialOutStore', params, config)
}

//修改备注
export const enabledMaterialOutStore = (params, config = {}) => {
    return request.get(apiPrefix + 'EnabledMaterialOutStore', { params: params, ...config })
}

//裁剪-原材料出库分页查询
export const getMaterialOutStorePageList = (params,config ={}) =>{
    return request.post(apiPrefix+'GetMaterialOutStorePageList', params, config)
}

//裁剪-损耗统计分页查询
export const getLossStatisticsPageList = (params,config ={}) =>{
    return request.post(apiPrefix+'GetLossStatisticsPageList', params, config)
}

//裁剪-成品半成品设置分页查询
export const exportLossStatisticsList = (params, config = { responseType: 'blob' }) => { 
    return request.post(apiPrefix + 'ExportLossStatisticsList', params, config) 
}

//修改备注
export const updateOrderRemark = (params, config = {}) => {
    return request.get(apiPrefix + 'UpdateOrderRemark', { params: params, ...config })
}

//裁剪-成品半成品设置分页查询
export const getTailorLossGoodsSetPageList = (params,config ={}) =>{
    return request.post(apiPrefix+'GetTailorLossGoodsSetPageList', params, config)
}

//裁剪-成品半成品设置分页查询
export const exportTailorLossGoodsSetList = (params, config = { responseType: 'blob' }) => { 
    return request.post(apiPrefix + 'ExportTailorLossGoodsSetList', params, config) 
}

//新增编辑-成品半成品设置
export const saveTailorLossGoodsSet = (params,config ={}) =>{
    return request.post(apiPrefix+'SaveTailorLossGoodsSet', params, config)
}
//删除-成品半成品设置
export const deleteTailorLossGoodsSet = (params, config = {}) => {
    return request.get(apiPrefix + 'DeleteTailorLossGoodsSet', { params: params, ...config })
}

//复制当前所有半成品编码或者成品编码,英文逗号间隔
export const getTailorLossGoodsSetAllGoodsCodeHalf = (params, config = {}) => {
    return request.get(apiPrefix + 'GetTailorLossGoodsSetAllGoodsCodeHalf', { params: params, ...config })
}

//获取半成品编码
export const getGoodsCodeHalfList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetGoodsCodeHalfList', { params: params, ...config })
}

//获取出库操作人
export const getOutStoreOperator = (params, config = {}) => {
    return request.get(apiPrefix + 'getOutStoreOperator', { params: params, ...config })
}

