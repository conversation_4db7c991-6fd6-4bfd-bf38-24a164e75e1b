import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Profit}/dayreport/`


//获取代拍日报数据
export const getReplaceDayReportList = (params,config ={}) =>{
    return request.get(apiPrefix+'GetReplaceDayReportListAsync', {params: params, ...config})
}

//获取代拍日报详情数据
export const getReplaceDayReportDetailList = (params,config ={}) =>{
    return request.get(apiPrefix+'GetReplaceDayReportDetailListAsync', {params: params, ...config})
}

//导入代拍订单号
export const importReplace = (params,config ={}) =>{
    return request.post(apiPrefix + 'ImportReplaceAsync', params, config)
}

export const batchUpdateReplace = (params, config = {}) => {
    return request.post(apiPrefix + 'BatchUpdateReplaceAsync',  params, config )
  }

//导出
export const exportReplaceDayReportList =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportReplaceDayReportListAsync',{params: params, ...config})
}

//导入新版代拍日报
export const importReplaceDayReportNew = (params,config ={}) =>{
    return request.post(apiPrefix + 'ImportReplaceDayReportNewAsync', params, config)
}

//获取新版代拍日报分页
export const getReplaceDayReportNewList = (params,config ={}) =>{
    return request.get(apiPrefix+'GetReplaceDayReportNewListAsync', {params: params, ...config})
}

//导入代拍订单退款情况
export const importReplaceRefund =(params,config ={}) =>{
    return request.post(apiPrefix + 'ImportReplaceRefundAsync', params, config)
}
//获取代拍订单退款情况
export const getReplaceRefundList = (params,config ={}) =>{
    return request.get(apiPrefix+'GetReplaceRefundListAsync', {params: params, ...config})
}

//导出
export const exportReplaceReFundList =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportReplaceReFundListAsync',{params: params, ...config})
}

export const batchDeleteReplaceRefund = (params, config = {}) => {
    return request.post(apiPrefix + 'BatchDeleteReplaceRefundAsync',  params, config )
  }

//导出新版代拍日报
export const exportReplaceDayReportNewList =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportReplaceDayReportNewListAsync',{params: params, ...config})
}
