import request from '@/utils/request'
const InquirsPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/PddInquirs/`

export const pagePddGroupKfEfficiencyList = (params, config = {}) => { return request.post(InquirsPrefix + 'PagePddGroupKfEfficiencyList', params, config) }
export const exportPddGroupKfEfficiencyList = (params, config = { responseType: 'blob' }) => { return request.post(InquirsPrefix + 'ExportPddGroupKfEfficiencyList', params, config) }
export const pagePddGroupKfEfficiencyListMap = (params, config = {}) => { return request.get(InquirsPrefix + 'PagePddGroupKfEfficiencyListMap', { params: params, ...config }) }

export const pagePddUserKfEfficiencyList = (params, config = {}) => { return request.get(InquirsPrefix + 'PagePddUserKfEfficiencyList', { params: params, ...config }) }
export const exportPddUserKfEfficiencyList = (params, config = { responseType: 'blob' }) => { return request.post(InquirsPrefix + 'ExportPddUserKfEfficiencyList', params, config) }
export const getInquirsStatisticsByShopListByUser = (params, config = {}) => { return request.get(InquirsPrefix + 'GetInquirsStatisticsByShopListByUser', { params: params, ...config }) }

export const pddImportInquirsAsync = (params, config = {}) => { return request.post(InquirsPrefix + 'ImportPddInquirsAsync', params, config) }
export const pddImportPddSalaryAsync = (params, config = {}) => { return request.post(InquirsPrefix + 'ImportPddSalaryAsync', params, config) }
export const getPddInquirsList = (params, config = {}) => { return request.get(InquirsPrefix + 'GetPddInquirsList', { params: params, ...config }) }
export const deleteInquirsBatch = (params, config = {}) => { return request.get(InquirsPrefix + 'DeleteBatchAsync', { params: params, ...config }) }
export const getPddShopInquirsStatisticsList = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddShopInquirsStatisticsList', params, config) }
export const getPddShopInquirsStatisticsListMap = (params, config = {}) => { return request.get(InquirsPrefix + 'GetPddShopInquirsStatisticsListMap', { params: params, ...config }) }
export const exportPddShopInquirsStatisticsList = (params, config = { responseType: 'blob' }) => { return request.post(InquirsPrefix + 'ExportPddShopInquirsStatisticsList', params, config) }

export const GetGroupLogList = (params, config = {}) => { return request.post(InquirsPrefix + 'GetGroupLogList', params, config) }
export const GetPddInquirsNotExistsList = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddInquirsNotExistsList', params, config) }


export const getPddShopInquirsStatisticsMargeList = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddShopInquirsStatisticsMargeList', params, config) }
export const exportPddShopInquirsStatisticsMargeList = (params, config = { responseType: 'blob' }) => { return request.post(InquirsPrefix + 'ExportPddShopInquirsStatisticsMargeList', params, config) }
export const getPddShopInquirsStatisticsMargeMap = (params, config = {}) => { return request.get(InquirsPrefix + 'GetPddShopInquirsStatisticsMargeMap', { params: params, ...config }) }
export const getPddShopInquirsStatisticsMargeUserList = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddShopInquirsStatisticsMargeUserList', params, config) }


export const pagePddUserKfEfficiencyListMap = (params, config = {}) => { return request.get(InquirsPrefix + 'PagePddUserKfEfficiencyListMap', { params: params, ...config }) }


export const getPddGroupList = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddGroupList', params, config) }

export const getPddGroup = (params, config = {}) => { return request.post(InquirsPrefix + 'getPddGroup', params, config) }
export const getPddGroup2 = (params, config = {}) => { return request.get(InquirsPrefix + 'GetPddGroup2', { params: params, ...config }) }

export const getPddShop = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddShop', params, config) }

export const importPddGroupAsync = (params, config = {}) => { return request.post(InquirsPrefix + 'ImportPddGroupAsync', params, config) }

export const deleteInquirs = (params, config = {}) => { return request.post(InquirsPrefix + 'DeleteInquirs', params, config) }

export const addgroup = (params, config = {}) => { return request.get(InquirsPrefix + 'AddGroup', { params: params, ...config }) }
export const deletegroup = (params, config = {}) => { return request.get(InquirsPrefix + 'DeleteGroupAsync', { params: params, ...config }) }
export const updategroupinfo = (params, config = {}) => { return request.get(InquirsPrefix + 'UpdateGroup', { params: params, ...config }) }

export const getInquirsStatisticsByShopListMonth = (params, config = {}) => { return request.get(InquirsPrefix + 'GetInquirsStatisticsByShopListMonth', { params: params, ...config }) }

export const batchUpdatePddLeaveGroup = (params, config = {}) => { return request.get(InquirsPrefix + 'batchUpdatePddLeaveGroupAsync', { params: params, ...config }) }

//分页获取拼多多绩效计算系数
export const getPddInquireGradeComputePageList = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddInquireGradeComputePageList', params, config) }

//保存拼多多绩效计算系数
export const savePddInquireGradeWay = (params, config = {}) => { return request.post(InquirsPrefix + 'SavePddInquireGradeWay', params, config) }

//获取提成系数趋势图
export const getPddInquireGradeComputeChat = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddInquireGradeComputeChat', params, config) }

//获取历史提成系数
export const getPddInquireGradeWayPageList = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddInquireGradeWayPageList', params, config) }

//设置账号参与绩效计算
export const addPddInquireGradeComputeSnick = (params, config = {}) => { return request.get(InquirsPrefix + 'AddPddInquireGradeComputeSnick', { params: params, ...config }) }

//导出拼多多绩效计算结果
export const exportPddInquireGradeComputeList = (params, config = { responseType: 'blob' }) => { return request.post(InquirsPrefix + 'ExportPddInquireGradeComputeList', params, config) }

//分页获取拼多多绩效计算结果(组)
export const getPddInquireGradeComputePageList_Group = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddInquireGradeComputePageList_Group', params, config) }

//分页获取拼多多绩效计算结果(店)
export const getPddInquireGradeComputePageList_Shop = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddInquireGradeComputePageList_Shop', params, config) }

//分页获取拼多多绩效计算结果(人)
export const getPddInquireGradeComputePageList_User = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddInquireGradeComputePageList_User', params, config) }

//获取拼多多绩效计算结果趋势图(组)
export const getPddInquireGradeComputeChat_Group = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddInquireGradeComputeChat_Group', params, config) }

//获取拼多多绩效计算结果趋势图(店)
export const getPddInquireGradeComputeChat_Shop = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddInquireGradeComputeChat_Shop', params, config) }

//获取拼多多绩效计算结果趋势图(人)
export const getPddInquireGradeComputeChat_User = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddInquireGradeComputeChat_User', params, config) }

//分页获取拼多多绩效计算结果(店-人)
export const getPddInquireGradeComputePageList_Shop_User = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddInquireGradeComputePageList_Shop_User', params, config) }

//分页获取拼多多绩效计算结果(店-人)弹窗
export const getPddInquireGradeComputePageList_UserGradeDtl = (params, config = {}) => { return request.post(InquirsPrefix + 'GetPddInquireGradeComputePageList_UserGradeDtl', params, config) }

//保存替班人数据
export const saveSubstitutePersonData = (params, config = {}) => { return request.post(InquirsPrefix + 'SaveSubstitutePersonData', params, config) }

//获取替班人
export const getSubstituteList = (params, config = {}) => { return request.post(InquirsPrefix + 'GetSubstituteList', params, config) }

//分页获取替班人设置记录
export const getSubstitutePersonPage = (params, config = {}) => { return request.post(InquirsPrefix + 'GetSubstitutePersonPage', params, config) }



// 获取所有分区信息
export const getAllPartitions = (params, config = {}) => { return request.get(InquirsPrefix + 'GetAllPartitions', { params: params, ...config }) }

// 检查是否存在重复的组信息
export const checkDuplicateGroup = (params, config = {}) => { return request.post(InquirsPrefix + 'CheckDuplicateGroup', params, config) }

// 检查是否存在重复的组信息（更新时）
export const checkDuplicateGroupUpdate = (params, config = {}) => { return request.post(InquirsPrefix + 'CheckDuplicateGroupUpdate', params, config) }
