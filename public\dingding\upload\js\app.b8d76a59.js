(function(){"use strict";var e={5323:function(e,t,n){var a=n(8249),o=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[t("div",{staticClass:"bodyupload"},[t("el-upload",{attrs:{drag:"",action:"","list-type":"text",multiple:"","auto-upload":!1,"show-file-list":!1,"on-change":e.changeFile}},[t("i",{staticClass:"el-icon-upload"}),t("div",{staticClass:"el-upload__text"},[e._v(" 点击按钮上传， "),t("em",[e._v("点击上传")])])])],1),t("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingshow,expression:"loadingshow"}],attrs:{fullscreen:!0,visible:e.loadingshow,width:"30%","element-loading-text":e.total>0?"上传进度"+(100*e.total/e.newnum).toFixed(0)+"%":"上传进度0%","element-loading-spinner":"el-icon-loading"},on:{"update:visible":function(t){e.loadingshow=t}}}),e.video?t("div",{staticClass:"uploadImg"},[t("video",{attrs:{src:e.video,controls:""}})]):e._e()],1)},i=[];n(7106);function r(e,t="base64"){return new Promise((n=>{let a=new FileReader;"base64"===t?a.readAsDataURL(e):"buffer"===t&&a.readAsArrayBuffer(e),a.onload=e=>{n(e.target.result)}}))}var l=n(4148),s=n.n(l),u=n(6021),d=n.n(u),c={name:"App",data(){return{loadingshow:!1,total:0,video:null,btn:!1,filenum:0,batchnumber:"",atfterUplaodData:"",chunkCount:0,jsondata:"",arraylist:[],newnum:0}},created(){var e=navigator.userAgent;e.indexOf("AlipayClient")>-1&&document.writeln('<script src="https://appx/web-view.min.js"><\/script>')},filters:{btnText(e){return e?"继续":"暂停"},totalText(e){return e>100?100:e}},mounted(){dd.onMessage=function(e){console.log(e),setTimeout((()=>{dd.postMessage({upfile:e})}),1e3)}},methods:{async changeFile(e){var t=this;if(t.loadingshow=!0,!e)return;e=e.raw,console.log("打印文件file",e),t.filenum=t.filenum+1;let n,a,o=await r(e,"buffer"),i=new(d().ArrayBuffer);i.append(o),n=i.end(),a=/\.([0-9a-zA-Z]+)$/i.exec(e.name)[1];const l=1048576;let s=Math.ceil(e.size/l);console.log("打印每片数",s);let u=[],c=0;t.newnum=t.newnum+Number(s),console.log("打印总端数",t.newnum);for(var f=0;f<s;f++){let t={chunk:e.slice(c,c+l),filename:`${n}_${f}.${a}`};c+=l,u.push(t)}this.partList=u,this.hash=n,await this.sendRequest(s),console.log("全部切片",u)},async sendRequest(e){let t=[],n=this;this.partList.forEach(((a,o)=>{let i=()=>{let t=new FormData;return t.append("data",a.chunk),t.append("batchnumber",n.batchnumber?n.batchnumber:""),t.append("filename",a.filename),t.append("index",o+1),t.append("total",e),s().post("/api/uploadnew/file/XMTVideoUploadBlockAsync",t,{headers:{"Content-Type":"multipart/form-data"}}).then((e=>{if(e=e.data,console.log("data数值",e),e.data&&(n.batchnumber=e.data),1==e.code&&(n.total+=1,n.partList.splice(o,1)),e.data.relativePath){let t=JSON.stringify(e.data);n.arraylist.push(t);let a=n.arraylist.join("-");console.log("_this.arraylist.length888",n.arraylist.length),console.log("_this.filenum999",n.filenum),n.arraylist.length==n.filenum&&(console.log("最后一次循环",a),dd.postMessage({upfile:a}))}}))};t.push(i)})),console.log("结束打印",n.jsondata);let a=0,o=async()=>{this.abort||a>=t.length||(await t[a](),a++,o())};o()},handleBtn(){if(this.btn)return this.abort=!1,void(this.btn=!1);this.btn=!0,this.abort=!0}}},f=c,p=n(2349),h=(0,p.Z)(f,o,i,!1,null,"686cd07e",null),g=h.exports,m=n(9359),v=n.n(m);a["default"].use(v()),a["default"].config.productionTip=!1,new a["default"]({render:e=>e(g)}).$mount("#app")}},t={};function n(a){var o=t[a];if(void 0!==o)return o.exports;var i=t[a]={exports:{}};return e[a](i,i.exports,n),i.exports}n.m=e,function(){var e=[];n.O=function(t,a,o,i){if(!a){var r=1/0;for(d=0;d<e.length;d++){a=e[d][0],o=e[d][1],i=e[d][2];for(var l=!0,s=0;s<a.length;s++)(!1&i||r>=i)&&Object.keys(n.O).every((function(e){return n.O[e](a[s])}))?a.splice(s--,1):(l=!1,i<r&&(r=i));if(l){e.splice(d--,1);var u=o();void 0!==u&&(t=u)}}return t}i=i||0;for(var d=e.length;d>0&&e[d-1][2]>i;d--)e[d]=e[d-1];e[d]=[a,o,i]}}(),function(){n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,{a:t}),t}}(),function(){n.d=function(e,t){for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}}(),function(){n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){var e={143:0};n.O.j=function(t){return 0===e[t]};var t=function(t,a){var o,i,r=a[0],l=a[1],s=a[2],u=0;if(r.some((function(t){return 0!==e[t]}))){for(o in l)n.o(l,o)&&(n.m[o]=l[o]);if(s)var d=s(n)}for(t&&t(a);u<r.length;u++)i=r[u],n.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return n.O(d)},a=self["webpackChunk_20200726"]=self["webpackChunk_20200726"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))}();var a=n.O(void 0,[998],(function(){return n(5323)}));a=n.O(a)})();
//# sourceMappingURL=app.b8d76a59.js.map