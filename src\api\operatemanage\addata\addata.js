import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/ProductManager/`

//分页查询广告数据
export const getProductAD = (params, config = {}) => {
  return request.get(apiPrefix + 'GetProductADAsync', { params:params, ...config })
}
export const importProductPDD = (params, config = {}) => {return request.post(apiPrefix + 'ImportProductPDD', params, config)}
export const importProductListPDD = (params, config = {}) => {return request.post(apiPrefix + 'ImportProductListPDD', params, config)}
export const importPDDAdv = (params, config = {}) => {return request.post(apiPrefix + 'ImportPDDAdv', params, config)}
