import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/ProductManager/`

export const getProductADCharAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'GetProductADCharAsync', { params: params, ...config })
}

export const getProductADOneCharAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'GetProductADOneCharAsync', { params: params, ...config })
}

export const SaveProductIdViewLog = (params, config = {}) => {
  return request.post(apiPrefix + 'SaveProductIdViewLog', params, config)
}

export const GetProductIdViewLogPage = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProductIdViewLogPage', params, config )
}