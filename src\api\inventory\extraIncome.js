import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/ExtraIncome/`

// 红包收入-分页
export const getExtraIncomePage = (params, config = {}) => {
  return request.post(apiPrefix + 'GetExtraIncomePage', params, config)
}

// 红包收入-删除
export const delExtraIncome = (params, config = {}) => {
  return request.post(apiPrefix + 'DelExtraIncome', params, config)
}

// 红包收入-获取提交人列表
export const getDDUserList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetDDUserList', params, config)
}

// 红包收入-获取所在部门列表
export const getDeptViews = (params, config = {}) => {
  return request.post(apiPrefix + 'GetDeptViews', params, config)
}

// 红包收入-分页获取提成配置
export const getExtraIncomeSetPage = (params, config = {}) => {
  return request.post(apiPrefix + 'GetExtraIncomeSetPage', params, config)
}

// 红包收入-保存提成配置
export const saveExtraIncomeSet = (params, config = {}) => {
  return request.post(apiPrefix + 'SaveExtraIncomeSet', params, config)
}

// 红包收入-导出
export const exportExtraIncome = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'ExportExtraIncome', params, config)
}


