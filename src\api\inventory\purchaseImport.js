import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_ImportInventory}/purchase/`

export const importPurchaseOrder = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchaseOrderAsync', params, config) }
export const importPurchasePayment = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchasePaymentAsync', params, config) }
export const importPurchaseReturnGoods = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchaseReturnGoodsAsync', params, config) }
export const importWarehousingOrder = (params, config = {}) => { return request.post(apiPrefix + 'ImportWarehousingOrderAsync', params, config) }
export const importPurchasePlan = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchasePlanAsync', params, config) }
export const importPurchasePlan2 = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchasePlan2Async', params, config) }

//商品主题分析历史数据导入
export const importPurchasePlan2History = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchasePlan2HistoryAsync', params, config) }


//退款导入
export const importReturnAmount = (params, config = {}) => { return request.post(apiPrefix + 'ImportReturnAmountAsync', params, config) }

//采购建议导入（新）
export const importNewPurchasePlan2 = (params, config = {}) => { return request.post(apiPrefix + 'ImportNewPurchasePlan2Async', params, config) }

//导入晨光采购信息 ImportPurchaseExtInfo
export const importPurchaseExtInfo = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchaseExtInfo', params, config) }

//商品资料库导入 ImportBatchGoodsDocRecordCg
export const importBatchGoodsDocRecordCg = (params, config = {}) => { return request.post(apiPrefix + 'ImportBatchGoodsDocRecordCg', params, config) }

//导入采购退货出库 ImportPurchaseReturnExWarehouese
export const importPurchaseReturnExWarehouese = (params, config = {}) => { return request.post(apiPrefix + 'ImportPurchaseReturnExWarehouese', params, config) }
