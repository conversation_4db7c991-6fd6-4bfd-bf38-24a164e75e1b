import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/pddactualtimedata/`

//获取产品数据
export const getPddActualTimeDataByProAsync  = (params, config = {}) => {  return request.get(apiPrefix + 'GetPddActualTimeDataByProAsync', { params: params, ...config }) } 
                
//获取分组数据
export const getPddActualTimeDataGroupByAsync  = (params, config = {}) => {  return request.get(apiPrefix + 'GetPddActualTimeDataGroupByAsync', { params: params, ...config }) } 

//产品趋势图
export const getPddActualTimeAnalysisProAsync = (params, config = {}) => {  return request.get(apiPrefix + 'GetPddActualTimeAnalysisProAsync', { params: params, ...config }) } 

//分组趋势图
export const getPddActualTimeAnalysisGroupByAsync = (params, config = {}) => {  return request.get(apiPrefix + 'GetPddActualTimeAnalysisGroupByAsync', { params: params, ...config }) } 

//获取店铺概览数据
export const getPddActualTimeDataByShopOverviewAsync  = (params, config = {}) => {  return request.get(apiPrefix + 'GetPddActualTimeDataShopOverviewAsync', { params: params, ...config }) } 

//店铺概览趋势图
export const getPddActualTimeAnalysisByShopOverviewAsync = (params, config = {}) => {  return request.get(apiPrefix + 'GetPddActualTimeAnalysisByShopOverviewAsync', { params: params, ...config }) } 

//明细汇总趋势图
export const getPddActualTimeAnalysisBySummaryAsync = (params, config = {}) => {  return request.get(apiPrefix + 'GetPddActualTimeAnalysisBySummaryAsync', { params: params, ...config }) }

//店铺概览汇总趋势图
export const getPddActualTimeAnalysisByShopSummaryAsync = (params, config = {}) => {  return request.get(apiPrefix + 'GetPddActualTimeAnalysisByShopSummaryAsync', { params: params, ...config }) } 

//店铺概览导出
export const exportPddActualTimeDataByShopOverviewAsync = (params, config = {responseType: 'blob' }) => {  return request.get(apiPrefix + 'ExportPddActualTimeDataByShopOverviewAsync', { params: params, ...config }) } 