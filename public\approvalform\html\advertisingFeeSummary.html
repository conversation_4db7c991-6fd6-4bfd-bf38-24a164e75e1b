<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="referrer" content="no-referrer" />
  <link rel="stylesheet" href="/approvalform/html/api/elment.css">
  <script src="/approvalform/html/api/vue.min.js"></script>
  <script src="/approvalform/html/api/elment.js"></script>
  <script src="/approvalform/html/api/jquery.min.js"></script>
  <script src="/approvalform/html/api/html2canvas.js"></script>
  <title>淘系广告费汇总</title>
</head>

<body>
  <div id="app" style="margin:0 auto;overflow-y: hidden;">
    <div style="display: flex;">
      <el-tabs v-model="h5Type" type="card" @tab-click="handleClick">
        <el-tab-pane label="其他" name="4"></el-tab-pane>
        <el-tab-pane label="有花费无成交" name="1"></el-tab-pane>
        <el-tab-pane label="投产低于" name="2"></el-tab-pane>
        <el-tab-pane label="成交成本" name="3"></el-tab-pane>
      </el-tabs>
    </div>
    <el-container direction="vertical" style=" border: 1px #ccc solid;">
      <template>
        <el-table ref="tableBox" :data="data" align="center" style="width: 100%;" row-key="id" border
          :max-height="tableHeight" v-loading="loading">
          <el-table-column type="index" min-width="20" fixed></el-table-column>
          <el-table-column prop="productName" label="场景名字"></el-table-column>
          <el-table-column prop="planID" label="计划ID"></el-table-column>
          <el-table-column prop="planName" label="计划名字" width="250"></el-table-column>
          <el-table-column prop="proCode" label="主体ID"></el-table-column>
          <el-table-column prop="shopName" label="店铺" width="200"></el-table-column>
          <el-table-column prop="groupName" label="组长"></el-table-column>
          <el-table-column prop="operateSpecialUserName" label="专员"></el-table-column>
          <el-table-column prop="useMoney" label="花费"></el-table-column>
          <el-table-column prop="clickThroughRate" label="点击率">
            <template slot-scope="scope">
              {{!scope.row.clickThroughRate ? '' : (scope.row.clickThroughRate * 100 ).toFixed(2) + "%"}}
            </template>
          </el-table-column>
          <el-table-column prop="averageCostPerClick" label="平均点击花费"></el-table-column>
          <el-table-column prop="clickConversionRate" label="点击转化率">
            <template slot-scope="scope">
              {{!scope.row.clickConversionRate ? '' : (scope.row.clickConversionRate * 100 ).toFixed(2) + "%"}}
            </template>
          </el-table-column>
          <el-table-column prop="returnOnInvestment" label="投入产出比">
            <template slot-scope="scope">
              <!-- {{!scope.row.returnOnInvestment ? '' : (scope.row.returnOnInvestment * 100 ).toFixed(2) + "%"}} -->
              {{scope.row.returnOnInvestment.toFixed(2)}}
            </template>
          </el-table-column>
          <el-table-column prop="totalTransactionCost" label="总成交成本"></el-table-column>
          <el-table-column prop="transactionAmount" label="总成交金额"></el-table-column>
        </el-table>
      </template>
    </el-container>
  </div>
  <script>
    var vm = new Vue({
      el: '#app',
      data: function () {
        return {
          h5Type: '1',
          loading: true,
          data: [],
          tableHeight: null,
          typeId: null,
          currentPage: 1,
          pageSize: 20,
          total: 0,
          title: ''
        }
      },
      created() {

      },
      async mounted() {
        this.getStyleSheetInfo();
        this.beginShowing()
      },
      methods: {
        handleClick(event) {
          this.getStyleSheetInfo();
        },
        beginShowing() {
          this.$nextTick(function () {
            // 文档显示区域的高度 -
            if (this.$refs.tableBox) {
              this.tableHeight = 835;
              this.$refs.tableBox.doLayout()
            }
          })
        },
        async getStyleSheetInfo() {
          let me = this;
          let searchURL = window.location.search;
          searchURL = searchURL.substring(1, searchURL.length);
          this.batchNo = searchURL.split("&")[0].split("=")[1];
          me.loading = true;
          let parm = {};
          $.ajax({
            url: '/api/bookkeeper/dayreportV2/getDataProductAdv_TXList?id=' + this.batchNo + '&h5Type=' + this.h5Type,
            type: 'GET',
            dataType: 'json',
            success: function ({ data }) {
              me.data = data.list
              me.loading = false
            },
            error: function (xhr, textStatus, errorThrown) {
              me.loading = false
              console.log('Error: ', errorThrown);
            }
          });
        },
      }
    });
  </script>
</body>

</html>
