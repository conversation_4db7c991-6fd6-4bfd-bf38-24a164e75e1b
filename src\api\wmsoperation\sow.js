import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_WmsOperation}/sow/`

//获取播种列表数据
export const QuerySowList = (params, config = {}) => {return request.get(apiPrefix + 'QuerySowList', {params:params,...config} )}

//开始播种
export const StartSow = (params, config = {}) => {return request.get(apiPrefix + 'StartSow', {params:params,...config} )}

//查询工作量列表
export const QueryWorkloadList = (params, config = {}) => {return request.get(apiPrefix + 'QueryWorkloadList', {params:params,...config} )}

//更新拣货人
export const BatchUpdatePicker = (params, config = {}) => {return request.post(apiPrefix + 'BatchUpdatePicker', params,config )}

// export const exportAttendanceOrderNodesList = (params, config = {responseType: 'blob'}) => { return request.get(apiPrefix + 'ExportAttendanceOrderNodesList',  { params: params, ...config })}

//播种同步按钮
export const syncSnowData = (params, config = {}) => {return request.get(apiPrefix + 'SyncSnowData', {params:params,...config} )}


//分页查询
export const QueryPickDetailList = (params, config = {}) => {return request.get(apiPrefix + 'QueryPickDetailList', {params:params,...config} )}


//不分页查询
export const PrintPickDetail = (params, config = {}) => {return request.get(apiPrefix + 'PrintPickDetail', {params:params,...config} )}


//查询订单仓库明细
export const QueryOrderBinsDetail = (params, config = {}) => {return request.get(apiPrefix + 'QueryOrderBinsDetail', {params:params,...config} )}


//获取拣货商品列表
export const QueryBatchPickList = (params, config = {}) => {return request.get(apiPrefix + 'QueryBatchPickList', {params:params,...config} )}


//获取播种商品列表
export const QueryBatchSowList = (params, config = {}) => {return request.get(apiPrefix + 'QueryBatchSowList', {params:params,...config} )}

//删除批次数据 
export const DeleteSowBatch = (params, config = {}) => {return request.get(apiPrefix + 'DeleteSowBatch', {params:params,...config} )}

//完成播种 
export const FinishSow = (params, config = {}) => {return request.get(apiPrefix + 'FinishSow', {params:params,...config} )}

//聚集订单 
export const ComputeKMeansOrder = (params, config = {}) => {return request.get(apiPrefix + 'ComputeKMeansOrder', {params:params,...config} )}

//复制订单号
export const CopyClustOrders = (params, config = {}) => {return request.get(apiPrefix + 'CopyClustOrders', {params:params,...config} )}

//获取聚集订单列表数据
export const QueryKMeansBatchList = (params, config = {}) => {return request.get(apiPrefix + 'QueryKMeansBatchList', {params:params,...config} )}

//删除聚集订单批次
export const DeleteClustBatch = (params, config = {}) => {return request.get(apiPrefix + 'DeleteClustBatch', {params:params,...config} )}

//同步播种框 关联聚集订单
export const LinkKMeansIdx = (params, config = {}) => {return request.get(apiPrefix + 'LinkKMeansIdx', {params:params,...config} )}


//打印快递条码
export const PrintExpressBarCodes = (params, config = {}) => {return request.get(apiPrefix + 'PrintExpressBarCodes', {params:params,...config} )}

//获取播种通道列表
export const GetSowingChannel = (params, config = {}) => {return request.get(apiPrefix + 'GetSowingChannel', {params:params,...config} )}

//获取仓库列表
export const GetInventorys = (params, config = {}) => {return request.get(apiPrefix + 'GetInventorys', {params:params,...config} )}

//工作量导出
export const ExportWorkloadList = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'ExportWorkloadList', params,config )}

//工价维护列表
export const GetPriceMaintenance = (params, config = {}) => {return request.get(apiPrefix + 'GetPriceMaintenance', {params:params,...config} )}

//工价编辑
export const EditPriceMaintenance = (params, config = {}) => {return request.post(apiPrefix + 'EditPriceMaintenance', params,config )}

//工价新增
export const AddPriceMaintenance = (params, config = {}) => {return request.post(apiPrefix + 'AddPriceMaintenance', params,config )}

//工价批量修改
export const BatchEditPriceMaintenance = (params, config = {}) => {return request.post(apiPrefix + 'BatchEditPriceMaintenance', params,config )}
