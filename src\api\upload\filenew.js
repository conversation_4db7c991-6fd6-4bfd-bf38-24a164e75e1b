import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_UpLoadNew}/file/`
const apiPrefix2 = `${process.env.VUE_APP_BASE_API_UpLoadNew_Domin}`


export const xMTVideoUploadBlockAsync = (params, config = {}) => {
    console.log('xMTVideoUploadBlockAsync',xMTVideoUploadBlockAsync)
    return request.post(apiPrefix2+ apiPrefix + 'xmtvideouploadblockasync', params, config)
}

export const uploadYYFileVideoBlockAsync = (params, config = {}) => {return request.post(apiPrefix2+apiPrefix + 'UploadYYFileVideoBlockAsync', params, config)}

export const uploadCommonFile = (params, config = {}) => {return request.post(apiPrefix2+apiPrefix + 'UploadCommonFileAsync', params, config)}