import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Profit}/warehousewages/`


//获取钉钉表用户
export const getDingtalkUsers = (params, config = {}) => {
    return request.get(apiPrefix + 'GetDingtalkUsers', { params: params, ...config })
}
//获取仓库
export const getTbWarehouseList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetTbWarehouseList', { params: params, ...config })
}
//获取仓库
export const getOnePostNameList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetOnePostNameList', { params: params, ...config })
}

//计件数据源-分页查询
export const getWarehouseUserWorkDataPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseUserWorkDataPageList', params, config)
}
//计件数据源-批次删除
export const deleteWarehouseUserWorkDataBatch = (params, config = {}) => {
    return request.get(apiPrefix + 'DeleteWarehouseUserWorkDataBatch', { params: params, ...config })
}

//计件数据源-分页查询
export const getWarehouseUserWorkDataWeightNumPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseUserWorkDataWeightNumPageList', params, config)
}
//计件数据源明细-批次删除
export const deleteWarehouseUserWorkDataWeightNumBatch = (params, config = {}) => {
    return request.get(apiPrefix + 'DeleteWarehouseUserWorkDataWeightNumBatch', { params: params, ...config })
}

//仓库岗位工作项-分页查询
export const getWarehousePostWagesPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehousePostWagesPageList', params, config)
}
//人效统计-导出
export const exportWarehouseWagesComputeList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportWarehouseWagesComputeList', params, config) }

//人效统计-分页查询
export const getWarehouseWagesComputePageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseWagesComputePageList', params, config)
}
//人效统计-趋势图-明细行点击
export const getWarehouseWagesComputeChat = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseWagesComputeChat', params, config)
}
//人效统计-趋势图-底部汇总点击
export const getWarehouseWagesComputeChat2 = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseWagesComputeChat2', params, config)
}
//人效统计-趋势图-首页
export const getWarehouseWagesComputeChat3 = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseWagesComputeChat3', params, config)
}
//人效统计-趋势图-首页
export const getWarehouseWagesComputeChat4 = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseWagesComputeChat4', params, config)
}
//人效统计趋势图-底部汇总-出仓成本
export const getWarehouseWagesComputeChat5 = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseWagesComputeChat5', params, config)
}
//获取所有仓库岗位工作项取值字段
export const getWarehousePostWorkItemColList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetWarehousePostWorkItemColList', { params: params, ...config })
}
//获取岗位工作项
export const getWarehousePostWagesById = (params, config = {}) => {
    return request.get(apiPrefix + 'GetWarehousePostWagesById', { params: params, ...config })
}
//保存岗位工作项
export const saveWarehousePostWages = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveWarehousePostWages', params, config)
}
//删除岗位工作项
export const deleteWarehousePostWages = (params, config = {}) => {
    return request.get(apiPrefix + 'DeleteWarehousePostWages', { params: params, ...config })
}

//导入工作项重量明细-可支持多天
export const importWarehousePostWagesWeight = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportWarehousePostWagesWeight', params, config)
}

//导入工作项数量明细-可支持多天
export const importWarehousePostWagesNum = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportWarehousePostWagesNum', params, config)
}

//导入员工计件数据-只支持单天导入
export const importWarehouseUserWorkData = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportWarehouseUserWorkData', params, config)
}
//计算仓库员工人效-按天计算
export const computeWarehouseUserWorkData = (params, config = {}) => {
    return request.get(apiPrefix + 'ComputeWarehouseUserWorkData', { params: params, ...config })
}
//导入员工工作项-只支持导入全部，只给管理员用
export const importWarehouseWagesWorkItem = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportWarehouseWagesWorkItem', params, config)
}
//仓库岗位账号个人工价-分页查询
export const getWarehouseWagesAccountPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseWagesAccountPageList', params, config)
}
//获取仓库岗位账号个人工价
export const getWarehouseWagesAccountById = (params, config = {}) => {
    return request.get(apiPrefix + 'GetWarehouseWagesAccountById', { params: params, ...config })
}
//删除仓库岗位账号个人工价
export const deleteWarehouseWagesAccountById = (params, config = {}) => {
    return request.get(apiPrefix + 'DeleteWarehouseWagesAccountById', { params: params, ...config })
}
//保存仓库岗位账号个人工价
export const saveWarehouseWagesAccount = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveWarehouseWagesAccount', params, config)
}
//仓库岗位账号个人工价-修改日志
export const getWarehouseWagesAccountLogList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetWarehouseWagesAccountLogList', { params: params, ...config })
}
//订单工作量-分页查询
export const getWarehouseWagesOrderWorkPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseWagesOrderWorkPageList', params, config)
}


//常规计件分页查询
export const getWarehouseRoutineWagesPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseRoutineWagesPageList', params, config)
}
//分拣计件分页查询
export const getWarehouseSortingWagesPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseSortingWagesPageList', params, config)
}
//集包计件分页查询
export const getWarehouseSumPackWagesPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseSumPackWagesPageList', params, config)
}
//集包计件人员分页查询
export const getWarehouseSumPackUserPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseSumPackUserPageList', params, config)
}
//集包计件人员新增编辑
export const warehouseSumPackUserSave = (params, config = {}) => {
    return request.post(apiPrefix + 'WarehouseSumPackUserSave', params, config)
}
//删除集包人员
export const warehouseSumPackUserDelete = (params, config = {}) => {
    return request.get(apiPrefix + 'WarehouseSumPackUserDelete', { params: params, ...config })
}

//计算集包计件
export const computeWarehouseSumPack = (params, config = {}) => {
    return request.get(apiPrefix + 'ComputeWarehouseSumPack', { params: params, ...config })
}

//分摊薪资分页查询
export const getWarehouseWagesAvgPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseWagesAvgPageList', params, config)
}

//计算分摊薪资
export const computeWarehouseWagesAvg = (params, config = {}) => {
    return request.get(apiPrefix + 'ComputeWarehouseWagesAvg', { params: params, ...config })
}

//计时薪资分页查询
export const getWarehouseMonthSalaryPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseMonthSalaryPageList', params, config)
}

//计时人员分页查询
export const getWarehouseMonthSalaryUserPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseMonthSalaryUserPageList', params, config)
}

//计时人员分页查询
export const getWarehouseMonthSalaryUserSetPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseMonthSalaryUserSetPageList', params, config)
}

//计时人员-禁用启用
export const enabledWarehouseMonthSalaryUser = (params, config = {}) => {
    return request.get(apiPrefix + 'EnabledWarehouseMonthSalaryUser', { params: params, ...config })
}

//计时人员-删除
export const deleteWarehouseMonthSalaryUser = (params, config = {}) => {
    return request.get(apiPrefix + 'DeleteWarehouseMonthSalaryUser', { params: params, ...config })
}

//保存计时人员
export const saveWarehouseMonthSalaryUser = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveWarehouseMonthSalaryUser', params, config)
}

//批量设置月薪信息
export const batchWarehouseMonthSalaryUserSet = (params, config = {}) => {
    return request.post(apiPrefix + 'BatchWarehouseMonthSalaryUserSet', params, config)
}

//设置应出勤
export const mustDayWarehouseMonthSalaryUserSet = (params, config = {}) => {
    return request.get(apiPrefix + 'MustDayWarehouseMonthSalaryUserSet', { params: params, ...config })
}

//计算月薪
export const computeWarehouseMonthSalaryUser = (params, config = {}) => {
    return request.get(apiPrefix + 'ComputeWarehouseMonthSalaryUser', { params: params, ...config })
}

//导入计时员工
export const importWarehouseMonthSalaryUser = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportWarehouseMonthSalaryUser', params, config)
}

//仓库发件量分页查询
export const getWarehouseOrderCountPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseOrderCountPageList', params, config)
}
//仓库发件量新增编辑
export const warehouseOrderCountSave = (params, config = {}) => {
    return request.post(apiPrefix + 'WarehouseOrderCountSave', params, config)
}
//删除订单发件量
export const deleteWarehouseOrderCount = (params, config = {}) => {
    return request.get(apiPrefix + 'DeleteWarehouseOrderCount', { params: params, ...config })
}

//手工单计件分页查询
export const getWarehouseManualWagesPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseManualWagesPageList', params, config)
}

//卸货拍摄入库薪资分页查询
export const getWarehouseVideoStorageWagesPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseVideoStorageWagesPageList', params, config)
}

//计算卸货拍摄入库薪资
export const computeWarehouseVideoStorageWages = (params, config = {}) => {
    return request.get(apiPrefix + 'ComputeWarehouseVideoStorageWages', { params: params, ...config })
}

//卸货入库单价设置表-分页查询
export const getWarehouseVideoStorageWagesSetPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseVideoStorageWagesSetPageList', params, config)
}

//设置卸货入库单价
export const setWarehouseVideoStorageWages = (params, config = {}) => {
    return request.post(apiPrefix + 'SetWarehouseVideoStorageWages', params, config)
}

//仓库薪资单日单仓确认
export const computeDataOk = (params, config = {}) => {
    return request.get(apiPrefix + 'ComputeDataOk', { params: params, ...config })
}

//仓库薪资单日单仓确认驳回
export const computeDataNoOk = (params, config = {}) => {
    return request.get(apiPrefix + 'ComputeDataNoOk', { params: params, ...config })
}

//仓库薪资单日单仓确认分页查询
export const getWarehouseWagesDataIsOkPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWarehouseWagesDataIsOkPageList', params, config)
}
//发起确认
export const warehouseComputeDataOk_Launch = (params, config = {}) => {
    return request.get(apiPrefix + 'WarehouseComputeDataOk_Launch', { params: params, ...config })
}
