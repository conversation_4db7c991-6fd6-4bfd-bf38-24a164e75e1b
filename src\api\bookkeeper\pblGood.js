import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/pblGood/`

//公共品表格-查询扫码公共品数据
export const pageGetPblGoods = (params, config = {}) => {
  return request.post(apiPrefix + 'PageGetPblGoods', params, config)
}

//公共品表格-查询趋势图
export const getTrendChart = (params, config = {}) => {
  return request.post(apiPrefix + 'GetTrendChart', params, config)
}

//公共品表格-查询详情列表数据
export const getStyleInfos = (params, config = {}) => {
  return request.post(apiPrefix + 'GetStyleInfos', params, config)
}
