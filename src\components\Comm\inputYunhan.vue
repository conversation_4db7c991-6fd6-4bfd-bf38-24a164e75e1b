<template>
	<div class="all_boady">
    <el-input @change="keyChange" :key="keys" :maxlength="maxlength?maxlength:''" :clearable="clearable?clearable:istrue" @clear="clear" :style="width?{width: width}:{width: '200px'}" v-model.trim="input" :placeholder="placeholder?placeholder:'请输入文字'" @keyup.enter.native="keyUp('form')"></el-input>
    <el-dialog
      :title="title?title:'标题'"
      :visible.sync="dialogVisible"
      append-to-body
      width="30%"
      v-dialogDrag
      :before-close="handleClose">
      <el-input
        type="textarea"
        :maxlength="maxlength?maxlength:''"
        :rows="row?row:12"
        :placeholder="placeholdertext?placeholdertext:'请分行输入'"
        @input='maxinput'
        v-model='textarea'>
      </el-input>
      <div v-if="showRowCount" style="height: 30px;text-align: right;">
        <span v-show="!showBlank?textarea:true">{{ rowCount }}/{{ maxRows }}</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">{{btn?btn:'确认'}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
	export default {
  data() {
    return {
      that: this,
      input: '',
      dialogVisible: false,
      textarea: '',
      istrue: false,
      rowCount:0,
    }
  },
  props: {
    keys: {
      type: String,
    },
    inputt: {
      type: String,
    },
    title: {
      type: String,
    },
    row: {
      type: Number,
    },
    placeholder: {
      type: String,
    },
    placeholdertext: {
      type: String,
    },
    btn: {
      type: String,
    },
    clearable: {
      type: Boolean,
    },
    clearabletext: {
      type: Boolean,
      default: false
    },
    maxlength: {
      type: Number,
    },
    width: {
      type: String,
    },
    maxRows:{
      type:Number,
      default:()=>{
        return 50;
      }
    },
    showRowCount:{
      type: Boolean,
      default: false
    },
    showBlank:{
      type: Boolean,
      default: true
    },
    //有值时且弹出弹框
    valuedOpen:{
      type:Boolean,
      default:()=>{
        return false;
      }
    }
  },
  watch: {
    inputt: 'inputtfuc'
  },
  async mounted() {
    var _this = this;

    if(_this.inputt){
      this.input =  _this.inputt;
    }
    _this.$emit('callback',_this.input);
  },
  methods: {
    inputtfuc(e){
      this.input =  e;
      if(this.clearabletext){
        if(!this.inputt||this.inputt==''){
          this.textarea = ''
        }
      }

    },
    keyChange(e){
      // this.$emit('callback', this.input);
      this.$emit('update:inputt', e)
    },
    keyUp(e){
          var _this = this;

          //有值时且弹出弹框,回显数据并不再继续向下执行
          if( _this.valuedOpen ){
            _this.textarea = _this.input.split(',').join('\n');
            _this.dialogVisible = true;
            return
          }

          if (_this.input) {
              // _this.$emit('callback', _this.input);
              _this.$emit('entersearch', _this.input);
          } else {
              _this.dialogVisible = true;
          }



    },
    handleClose(done) {
        this.$confirm('确认关闭？')
          .then(_ => {
            done();
          })
          .catch(_ => {});
      },
    submit(){
      var _this = this;
      _this.dialogVisible = false;
      // replace(/^http:\/\/[^/]+\//, "");
      // var string = _this.textarea.replace(" ",",")/\r\n/g
      // var string = _this.textarea.trim().split(/\s+/);
      var string = _this.textarea.trim().replace(/\n/g,",");
      _this.input = string.replace(/\s*/g,"");
      _this.$emit('callback',string);
    },
    //计文本域输入行数并做截断处理
    count(params) {
      var index = params.indexOf('\n');
      var num = 0;
      while(index !== -1) {
        num++;
        index = params.indexOf('\n', index + 1);
      };
      return num;
    },
    clear(){
      var _this = this;
      _this.input = '';
      _this.$emit('callback','');
      _this.dialogVisible = false;
    },
    maxinput(){
      var _this = this;
      const allstring = _this.textarea.toString();
      if(_this.maxlength<allstring.length){
        this.$message('内容过长，请重新输入');
        return;
      }
      //限制最大输入行数
      var number = this.count(allstring);
      this.rowCount = number;

      const arr = _this.textarea.split('\n');
      let strin = arr[number-1];
      //对输入数据数组进行去重处理
      var newArr = [];  //新数组存放去重后的值
      var repeatArr = _this.textarea.split('\n');  //取原数组
      for(var i = 0; i < repeatArr.length; i++) {
        if(newArr.indexOf(repeatArr[i]) === -1) {
          newArr.push(repeatArr[i]);
        }
      }
      _this.textarea = newArr.join('\n');
      if(number > this.maxRows) {
        strin = arr[this.maxRows];
        _this.textarea =_this.textarea.slice(0,_this.textarea.indexOf(strin))
        this.$message('最多输入'+this.maxRows+'行');
        return;
      }
    }
  }
    }
</script>

<style lang="scss" scoped>
 .all_boady{
  margin: 0 auto;
 }
 ::v-deep .el-dialog__wrapper{
  transition-duration: 0.3s;
  // background-color: aqua;
}
</style>
