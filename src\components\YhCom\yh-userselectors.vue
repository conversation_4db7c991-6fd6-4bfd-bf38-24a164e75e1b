<template>
    <el-select v-model="innerValue" multiple collapse-tags :clearable="clearable" filterable remote :style="cststyle"
        reserve-keyword :placeholder="placeholder" :remote-method="remoteMethod" @change="valueChanged($event)"
        @clear="clear" :loading="loading" :title="getName()">
        <el-option v-for="item in options" :key="'userSelector' + item.value + item.extData.defaultDeptId"
            :label="item.label" :value="item.value">
            <span>{{ item.label }}</span>
            <span style=" color: #8492a6; ">({{ item.extData.position }},{{ item.extData.empStatusText }}{{
                item.extData.jstUserName
                    ? "," + item.extData.jstUserName : "" }})</span>
            <span style=" color: #8492a6; "> {{ item.extData.deptName }}</span>
        </el-option>
    </el-select>
</template>

<script>
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
export default {
    name: 'YhUserelector',
    props: {
        placeholder: {
            type: String,
            default() {
                return "请输入"
            }
        },
        rows: {
            type: Number,
            default: 50
        },
        value: {
            type: Array,
            default() {
                return [];
            }
        },
        text: {
            type: Array,
            default() {
                return []
            }
        },
        clearable: {
            type: Boolean,
            default() { return true; }
        },
        cststyle: {
            type: Object,
            default() {
                return {}
            }
        },
        Unbound: {
            type: Boolean,
            default() {
                return false;
            }
        }
    },
    data() {
        return {
            loading: false,
            options: [],
            innerValue: [],
            innerText: "",
            orgOptions: [],
            allOptios: [],
            selectUser: []
        }
    },
    computed: {

    },
    async mounted() {
        this.orgOptions = [...this.options];
        this.$nextTick(() => {
            if (this.value && Array.isArray(this.value) && this.value.length > 0) {
                let options = []
                this.value.forEach((val, valIndex) => {
                    options.push({ value: val, label: this.text[valIndex], extData: { defaultDeptId: null, position: null, empStatusText: null, jstUserName: null, deptName: null } })
                })
                this.$set(this, 'innerValue', this.value);
                this.options = options;
                this.allOptios = options;
            }
        });
    },
    methods: {
        getName() {
            return this.selectUser?.map(item => item.label).join(",")
        },
        clear() {
            this.$emit("update:value", []);
            this.$emit("update:text", []);
            this.$emit("change", []);
        },
        valueChanged(newValue) {
            let res = [];
            let text = []
            if (newValue && newValue.length > 0) {
                const data = [...this.options, ...this.allOptios].filter((item, index, arr) => {
                    return arr.findIndex(t => t.value === item.value) === index;
                });
                res = data.filter(item => newValue.includes(item.value))
            }
            const text1 = res.map(item => item.label);
            const value1 = res.map(item => item.value);
            this.$set(this, 'selectUser', res);
            this.$emit("update:value", value1);
            this.$emit("update:text", text1);
            this.$emit("change", res);
        },
        async remoteMethod(query) {
            if (query && query.length > 50) return this.$message.error("输入内容过长");
            this.loading = true;
            if (query !== '') {
                let rlt = await QueryAllDDUserTop100({ keywords: query });
                if (rlt && rlt.success) {
                    if (this.Unbound) {
                        rlt.data.unshift({ userName: "未绑定", ddUserId: "-1", defaultDeptId: null, position: null, empStatusText: null, jstUserName: null, deptName: null });
                    }
                    this.options = rlt.data?.map(item => {
                        return { label: item.userName, value: item.ddUserId, extData: item }
                    });
                    const res = [...this.options, ...this.allOptios].filter((item, index, arr) => {
                        return arr.findIndex(t => t.value === item.value) === index;
                    });
                    this.$set(this, 'allOptios', res);
                }
            } else {
                this.options = [...this.orgOptions];
            }
            this.loading = false;
        }
    }
}
</script>
