import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/unsalable/`

export const getProCodeTurnover = (params, config = {}) => { return request.post(apiPrefix + `GetProCodeTurnover`, params, config) }
export const getProCodeUnsalable = (params, config = {}) => { return request.post(apiPrefix + `GetProCodeUnsalable`, params, config) }
export const getUnsalableChangeStatus = (params, config = {}) => { return request.post(apiPrefix + `GetProCodeUnsalableChangeStatus`, params, config) }
export const getUnsalableChangeStatusAnalysis = (params, config = {}) => { return request.post(apiPrefix + `GetProCodeUnsalableChangeStatusAnalysis`, params, config) }
export const getProCodeNoSaleAnalysis = (params, config = {}) => { return request.post(apiPrefix + `GetProCodeNoSaleAnalysis`, params, config) }
export const getProCodeNoSaleAnalysisByDate = (params, config = {}) => { return request.post(apiPrefix + `GetProCodeNoSaleAnalysisByDate`, params, config) }
export const getProCodeUnsalableAnalysisByDate = (params, config = {}) => { return request.post(apiPrefix + `GetProCodeUnsalableAnalysisByDate`, params, config) }
export const getProCodeUnsalableAnalysisByGroup = (params, config = {}) => { return request.post(apiPrefix + `GetProCodeUnsalableAnalysisByGroup`, params, config) }
export const exportProCodeUnsalable = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportProCodeUnsalable', params, config) }
