import request from '@/utils/request'
const PddBackGroundManage = `${process.env.VUE_APP_BASE_API_OperateManage}/PddBackGroundManage/`

export const getPddEmailTypes = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPddEmailTypes', { params: params, ...config })}
export const getPingduoduoFundDetailList = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPingduoduoFundDetailList', { params: params, ...config })}
export const getPingduoduoBGEmailGroupList = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPingduoduoBGEmailGroupList', { params: params, ...config })}
export const getPingduoduoBGEmailList = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPingduoduoBGEmailList', { params: params, ...config })}
export const getPingduoduoBGViolationList = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPingduoduoBGViolationList', { params: params, ...config })}
export const getPingduoduoBGShopViolationList = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPingduoduoBGShopViolationList', { params: params, ...config })}
export const getPingduoduoBGComplainList = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPingduoduoBGComplainList', { params: params, ...config })}
export const getPingduoduoBGLiveViolationList = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPingduoduoBGLiveViolationList', { params: params, ...config })}
export const getPingduoduoBGAftermarketViolationList = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPingduoduoBGAftermarketViolationList', { params: params, ...config })}
export const getPingduoduoPromotionFeeList = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPingduoduoPromotionFeeList', { params: params, ...config })}
export const getPddPromotionFeeAnalysisAsync = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPddPromotionFeeAnalysisAsync', { params: params, ...config })}
export const getPingduoduoOptimizationOpinionList = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPingduoduoOptimizationOpinionList', { params: params, ...config })}
export const getPingduoduoOptimizationOpinionAnalysisAsync = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPingduoduoOptimizationOpinionAnalysisAsync', { params: params, ...config })}
//客服数据
export const getPingduoduoCustomerPointList = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPingduoduoCustomerPointList', { params: params, ...config })}
export const getPingduoduoCustomerPointAnalysisAsync = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPingduoduoCustomerPointAnalysisAsync', { params: params, ...config })}
//export const importPingduoduoFundDetailAsync = (params, config = {}) => {return request.post(PddBackGroundManage + 'ImportPingduoduoFundDetailAsync',  params, config)}
export const importPingduoduoFundDetail = (params, config = {}) => {return request.post(PddBackGroundManage + 'ImportPingduoduoFundDetail',  params, config)}
export const getImportPingduoduoFundDetailTaskLog = (params, config = {}) => {return request.post(PddBackGroundManage + 'GetImportPingduoduoFundDetailTaskLog', params, config)}

export const exportPingduoduoFundDetailList = (params, config = { responseType: 'blob' }) => {return request.get(PddBackGroundManage + 'ExportPingduoduoFundDetailList', { params: params, ...config })}
export const exportPingduoduoBGEmailGroupList = (params, config = { responseType: 'blob' }) => {return request.get(PddBackGroundManage + 'ExportPingduoduoBGEmailGroupList', { params: params, ...config })}
export const exportPingduoduoBGShopViolationList = (params, config = { responseType: 'blob' }) => {return request.get(PddBackGroundManage + 'ExportPingduoduoBGShopViolationList', { params: params, ...config })}
export const exportPingduoduoBGComplainList = (params, config = { responseType: 'blob' }) => {return request.get(PddBackGroundManage + 'ExportPingduoduoBGComplainList', { params: params, ...config })}
export const exportPingduoduoBGLiveViolationList = (params, config = { responseType: 'blob' }) => {return request.get(PddBackGroundManage + 'ExportPingduoduoBGLiveViolationList', { params: params, ...config })}
export const exportPingduoduoBGAftermarketViolationList = (params, config = { responseType: 'blob' }) => {return request.get(PddBackGroundManage + 'ExportPingduoduoBGAftermarketViolationList', { params: params, ...config })}
export const exportPingduoduoBGViolationList = (params, config = { responseType: 'blob' }) => {return request.get(PddBackGroundManage + 'ExportPingduoduoBGViolationList', { params: params, ...config })}
export const exportPingduoduoPromotionFeeList = (params, config = { responseType: 'blob' }) => {return request.get(PddBackGroundManage + 'ExportPingduoduoPromotionFeeList', { params: params, ...config })}
//客服数据
export const exportPingduoduoCustomerPointList = (params, config = { responseType: 'blob' }) => {return request.get(PddBackGroundManage + 'ExportPingduoduoCustomerPointList', { params: params, ...config })}

export const setPddBackGroundImportant = (params, config = {}) => {return request.post(PddBackGroundManage + 'SetPddBackGroundImportant',  params, config)}
export const handlePddBackGroundEmail = (params, config = {}) => {return request.post(PddBackGroundManage + 'HandlePddBackGroundEmail',  params, config)}
export const getPddEmailDetial = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPddEmailDetial',{ params: params, ...config })}
export const setPddBackGroundViolationImportant = (params, config = {}) => {return request.post(PddBackGroundManage + 'SetPddBackGroundViolationImportant',  params, config)}
export const handlePddBackGroundViolation = (params, config = {}) => {return request.post(PddBackGroundManage + 'HandlePddBackGroundViolation',  params, config)}
export const getPddBackGroundShopViolationList = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetPddBackGroundShopViolationList',{ params: params, ...config })}
export const exportPingduoduoOptimizationOpinionList = (params, config = { responseType: 'blob' }) => {return request.get(PddBackGroundManage + 'ExportPingduoduoOptimizationOpinionList', { params: params, ...config })}
export const getHandelerList = (params, config = {}) => {return request.get(PddBackGroundManage + 'GetHandelerList',{ params: params, ...config })}

//导入拼多多后台视频数据
export const importPddBackGroundVidioReportList = (params, config = {}) => {return request.post(PddBackGroundManage + 'ImportPddBackGroundVidioReportList',  params, config)}
//分页获取拼多多后台视频报表数据
export const getPddBackGroundVidioReportList = (params, config = {}) => {return request.post(PddBackGroundManage + 'GetPddBackGroundVidioReportList',  params, config)}

//导出拼多多后台视频报表数据
export const exportPddBackGroundVidioReportList = (params, config = { responseType: 'blob' }) => {
    return request.post(PddBackGroundManage + 'ExportPddBackGroundVidioReportList', params, config)
}

//获取拼多多后台视频数据趋势图
export const getPddBackGroundVidioReportAnasilys = (params, config = {}) => {return request.post(PddBackGroundManage + 'GetPddBackGroundVidioReportAnasilys',  params, config)}

//获取拼多多资金站内信-账户资金-趋势图
export const getPingduoduoFundDetailListMap = (params, config = {}) => {return request.post(PddBackGroundManage + 'GetPingduoduoFundDetailListMap',  params, config)}
