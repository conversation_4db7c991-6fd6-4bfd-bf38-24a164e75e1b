import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/customNormsGoods/`

///获取阳光隔热膜成本设置 GetCostSetByYGGRM
export const getCostSetByYGGRM = (params, config = {}) => { return request.post(apiPrefix + 'GetCostSetByYGGRM', params, config) }

//保存阳光隔热膜成本设置 SaveCostSetByYGGRM
export const saveCostSetByYGGRM = (params, config = {}) => { return request.post(apiPrefix + 'SaveCostSetByYGGRM', params, config) }

//获取阳光隔热膜测算列表 GetYGGRMCSRecordPageList
export const getYGGRMCSRecordPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetYGGRMCSRecordPageList', params, config) }

//根据买家ID获取阳光隔热膜数据 GetYGGRMCSRecordByBuyUserId
export const getYGGRMCSRecordByBuyUserId = (params, config = {}) => { return request.post(apiPrefix + 'GetYGGRMCSRecordByBuyUserId', params, config) }

//保存阳光隔热膜数据 SaveYGGRMCSRecord
export const saveYGGRMCSRecord = (params, config = {}) => { return request.post(apiPrefix + 'SaveYGGRMCSRecord', params, config) }

//获取阳光隔热膜成本设置列表（缓存） GetCostSetByYGGRMCache
export const getCostSetByYGGRMCache = (params, config = {}) => { return request.post(apiPrefix + 'GetCostSetByYGGRMCache', params, config) }

//根据ID获取阳光隔热膜数据 GetYGGRMCSRecordById
export const getYGGRMCSRecordById = (params, config = {}) => { return request.post(apiPrefix + 'GetYGGRMCSRecordById', params, config) }

//获取报价成本售价列表 GetYGGRMSaleOrderRecordList
export const getYGGRMSaleOrderRecordList = (params, config = {}) => { return request.post(apiPrefix + 'GetYGGRMSaleOrderRecordList', params, config) }

//删除阳光隔热膜订单
export const deleteYGGRMCSRecord = (params, config = {}) => { return request.post(apiPrefix + 'DeleteYGGRMCSRecord', params, config) }

//保存阳光隔热膜成本设置（保存售价）SaveSalePriceSetByYGGRM
export const saveSalePriceSetByYGGRM = (params, config = {}) => { return request.post(apiPrefix + 'SaveSalePriceSetByYGGRM', params, config) }

//获取透明桌垫成本设置列表 GetCostSetByTMZD
export const getCostSetByTMZD = (params, config = {}) => { return request.post(apiPrefix + 'GetCostSetByTMZD', params, config) }

//保存透明桌垫成本设置（保存成本） SaveCostSetByTMZD
export const saveCostSetByTMZD = (params, config = {}) => { return request.post(apiPrefix + 'SaveCostSetByTMZD', params, config) }

//获取透明桌垫成本设置列表（缓存） GetCostSetByTMZDCache
export const getCostSetByTMZDCache = (params, config = {}) => { return request.get(apiPrefix + 'GetCostSetByTMZDCache', { ...config,params, }) }

//获取透明桌垫测算列表 GetTMZDCSRecordPageList
export const getTMZDCSRecordPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetTMZDCSRecordPageList', params, config) }

//根据买家ID获取透明桌垫数据 GetTMZDCSRecordByBuyUserId
export const getTMZDCSRecordByBuyUserId = (params, config = {}) => { return request.post(apiPrefix + 'GetTMZDCSRecordByBuyUserId', params, config) }

//根据ID获取透明桌垫数据 GetTMZDCSRecordById
export const getTMZDCSRecordById = (params, config = {}) => { return request.post(apiPrefix + 'GetTMZDCSRecordById', params, config) }

//保存透明桌垫数据 SaveTMZDCSRecord
export const saveTMZDCSRecord = (params, config = {}) => { return request.post(apiPrefix + 'SaveTMZDCSRecord', params, config) }

//获取透明桌垫报价成本售价列表 GetTMZDSaleOrderRecordList
export const getTMZDSaleOrderRecordList = (params, config = {}) => { return request.post(apiPrefix + 'GetTMZDSaleOrderRecordList', params, config) }

//删除阳光隔热膜订单
export const deleteTMZDCSRecord = (params, config = {}) => { return request.post(apiPrefix + 'DeleteTMZDCSRecord', params, config) }

//导入定制款报价明细 ImportCustomMadeMultipleAsync
export const importCustomMadeMultipleAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportCustomMadeMultipleAsync', params, config) }

//GetCustomMadeRecordDtlLogAsync 获取定制款报价明细日志
export const getCustomMadeRecordDtlLogAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetCustomMadeRecordDtlLogAsync', params, config) }

//修改阳光隔热膜定制款明细 UpdateYGGRMRecordDtlAsync
export const updateYGGRMRecordDtlAsync = (params, config = {}) => { return request.post(apiPrefix + 'UpdateYGGRMRecordDtlAsync', params, config) }

//修改透明桌垫定制款明细 UpdateTMZDRecordDtlAsync
export const updateTMZDRecordDtlAsync = (params, config = {}) => { return request.post(apiPrefix + 'UpdateTMZDRecordDtlAsync', params, config) }

//阳光隔热膜成本明细-数据导出
export const exportYGGRMSaleOrderRecordList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportYGGRMSaleOrderRecordList', params, config) }

//透明桌垫成本明细-数据导出
export const exportTMZDSaleOrderRecordList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportTMZDSaleOrderRecordList', params, config) }

//导出阳光隔热膜列表 ExportYGGRMCSRecordList
export const exportYGGRMCSRecordList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportYGGRMCSRecordList', params, config) }

//导出透明桌垫列表 ExportTMZDCSRecordPageList
export const exportTMZDCSRecordPageList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportTMZDCSRecordPageList', params, config) }

//透明桌垫重新设置重量 ReSetTMZDWeight
export const reSetTMZDWeight = (params, config = {}) => { return request.post(apiPrefix + 'ReSetTMZDWeight', params, config) }

//导入快递费设置 ImportCustomNormsFreightFeeSet
export const importCustomNormsFreightFeeSet = (params, config = {}) => { return request.post(apiPrefix + 'ImportCustomNormsFreightFeeSet', params, config) }

//导出快递费设置列表 ExportCustomNormsFreightFeeSet
export const exportCustomNormsFreightFeeSet = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'exportCustomNormsFreightFeeSet', params, config) }

//获取快递费设置列表 GetCustomNormsFreightFeeSet
export const getCustomNormsFreightFeeSet = (params, config = {}) => { return request.post(apiPrefix + 'GetCustomNormsFreightFeeSet', params, config) }

//编辑保存快递费设置 SaveCustomNormsFreightFeeSet
export const saveCustomNormsFreightFeeSet = (params, config = {}) => { return request.post(apiPrefix + 'SaveCustomNormsFreightFeeSet', params, config) }

//删除快递费设置数据 DeleteCustomNormsFreightFeeSet
export const deleteCustomNormsFreightFeeSet = (params, config = {}) => { return request.post(apiPrefix + 'DeleteCustomNormsFreightFeeSet', params, config) }

//获取皮桌垫成本设置列表 GetCostSetByPZD
export const getCostSetByPZD = (params, config = {}) => { return request.post(apiPrefix + 'GetCostSetByPZD', params, config) }

//保存皮桌垫成本设置 SaveCostSetByPZD
export const saveCostSetByPZD = (params, config = {}) => { return request.post(apiPrefix + 'SaveCostSetByPZD', params, config) }

//获取皮桌垫成本设置列表(缓存) GetCostSetByPZDCache
export const getCostSetByPZDCache = (params, config = {}) => { return request.get(apiPrefix + 'GetCostSetByPZDCache', { ...config,params, }) }

//获取皮桌垫测算列表 GetPZDCSRecordPageList
export const getPZDCSRecordPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetPZDCSRecordPageList', params, config) }

//导出皮桌垫测算列表 ExportPZDCSRecordPageList
export const exportPZDCSRecordPageList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPZDCSRecordPageList', params, config) }

//根据ID获取皮桌垫数据 GetPZDCSRecordById
export const getPZDCSRecordById = (params, config = {}) => { return request.post(apiPrefix + 'GetPZDCSRecordById', params, config) }

//保存皮桌垫数据 SavePZDCSRecord
export const savePZDCSRecord = (params, config = {}) => { return request.post(apiPrefix + 'SavePZDCSRecord', params, config) }

//删除皮桌垫订单 DeletePZDCSRecord
export const deletePZDCSRecord = (params, config = {}) => { return request.post(apiPrefix + 'DeletePZDCSRecord', params, config) }

//重置皮桌垫重量 ReSetPZDWeight
export const reSetPZDWeight = (params, config = {}) => { return request.post(apiPrefix + 'ReSetPZDWeight', params, config) }

//获取皮桌垫成本明细列表 GetPZDSaleOrderRecordList
export const getPZDSaleOrderRecordList = (params, config = {}) => { return request.post(apiPrefix + 'GetPZDSaleOrderRecordList', params, config) }

//导出皮桌垫成本明细列表 ExportPZDSaleOrderRecordList
export const exportPZDSaleOrderRecordList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPZDSaleOrderRecordList', params, config) }

export const addSetByPZD = (params, config = {}) => { return request.post(apiPrefix + 'addSetByPZD', params, config) }

export const updateSetByPZD = (params, config = {}) => { return request.post(apiPrefix + 'UpdateSetByPZD', params, config) }

export const delSetByPZD = (params, config = {}) => { return request.post(apiPrefix + 'DelSetByPZD', params, config) }

