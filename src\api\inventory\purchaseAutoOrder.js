import request from "@/utils/request";
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/PurchaseAutoOrder/`;

export const GetPurchaseAutoOrderDataPage = (params, config) => {
  return request.post(
    apiPrefix + "GetPurchaseAutoOrderDataPage",
    params,
    config
  );
};

export const ExportPurchaseAutoOrderData = (
  params,
  config = { responseType: "blob" }
) => {
  return request.post(
    apiPrefix + "ExportPurchaseAutoOrderData",
    params,
    config
  );
};

//带入计划采购建议
export const BringInPurchaseNewPlan2Data = (params, config) => {
  return request.post(
    apiPrefix + "BringInPurchaseNewPlan2Data",
    params,
    config
  );
};

//获取自动开单配置数据
export const GetPurchaseAutoOrderDataCalSet = (params, config) => {
  return request.post(
    apiPrefix + "GetPurchaseAutoOrderDataCalSet",
    params,
    config
  );
};

//编辑单条自动开单数据
export const EditPurchaseAutoOrderData = (params, config) => {
  return request.post(apiPrefix + "EditPurchaseAutoOrderData", params, config);
};

//批量编辑自动开单数据
export const BatchEditPurchaseAutoOrderData = (params, config) => {
  return request.post(
    apiPrefix + "BatchEditPurchaseAutoOrderData",
    params,
    config
  );
};

//自动开单日志
export const GetPurchaseAutoOrderDataLogPage = (params, config) => {
  return request.post(
    apiPrefix + "GetPurchaseAutoOrderDataLogPage",
    params,
    config
  );
};

//导出自动开单日志
export const ExportPurchaseAutoOrderDataLog = (
  params,
  config = { responseType: "blob" }
) => {
  return request.post(
    apiPrefix + "ExportPurchaseAutoOrderDataLog",
    params,
    config
  );
};

//获取备注日志
export const GetPurchaseAutoOrderDataRemarkLogList = (params, config) => {
  return request.post(
    apiPrefix + "GetPurchaseAutoOrderDataRemarkLogList",
    params,
    config
  );
}