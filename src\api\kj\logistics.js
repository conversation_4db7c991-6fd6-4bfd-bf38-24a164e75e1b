import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Kj}/Logistics/`

//跨境物流渠道管理 列表查询
export const getShippingMethodPageList = (params, config) => { return request.post(apiPrefix + 'getShippingMethodPageList', params, config) }

//跨境物流渠道管理  更新运输方式可用状态 面单发货是否可用
export const updateShippingMethodAvailable = (params, config) => { return request.post(apiPrefix + 'updateShippingMethodAvailable', params, config) }

//跨境物流渠道管理  导出
export const exportShippingMethodPageList = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'exportShippingMethodPageList', params, config)
}   