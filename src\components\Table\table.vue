<template>
    <div id="mainmidle"
        :style="[{ height: (isSelectColumn == true ? '95%' : '100%') }, { width: '100%' }, { 'margin': ' 0' }]">
        <div ref="btns" :style="{height: selectColumnHeight,width:'100%'}" v-if="isSelectColumn">
            <div style="float: left;">
                <el-popover placement="right" title="列隐藏/显示" trigger="click" width="420" v-if="isSelectColumnCols">
                    <el-checkbox-group v-model="checkedColumns" size="mini">
                        <el-checkbox v-for="(item,i) in checkBoxGroup" :key="i" :label="item" :value="item"></el-checkbox>
                    </el-checkbox-group>
                    <el-button slot="reference" type="primary" size="small" plain><i
                            class="el-icon-arrow-down el-icon-menu" />列隐藏/显示</el-button>
                </el-popover>
            </div>
            <div style="float: left;width:calc(100% - 116px);height:30px;overflow-x: auto;overflow-y: auto; white-space:nowrap;" class="yh202306081055">
                <el-button-group style="white-space:nowrap;" >
                    <template v-for='item in tableHandles'>
                        <el-button :disabled="item.disabled" :title="item.title" v-if="(!item.permission || (item.permission && checkPermission(item.permission)))
                        &&
                        ((!item.ishide || !(!!item.ishide && item.ishide(that) )))
                        "
                            :key='item.label' :size="item.size || size" :type="item.type || type" :icon='item.icon || ""'
                            @click="item.handle(that)">{{ item.label }}</el-button>
                    </template>
                    <slot name="extentbtn" />
                </el-button-group>
            </div>

        </div>
        <!-- row-key="id" -->
        <el-table :data='tableData' class="table-wrapper" :default-expand-all='defaultexpandall' :height="tableHeight" :border='isBorder' @select='select'  @row-click='rowclick' @select-all='selectAll' @sort-change='sortchange' @cell-click='cellclick' @expand-change='expandchange' v-loading='loading' :tree-props='treeprops' :show-summary='showsummary' :summary-method="getSummaries" :defaultSelections='defaultSelections' :row-style='customRowStyle' highlight-current-row :stripe="stripe" :header-cell-style='headerCellStyle||heardcolumnbackgroundStyle' ref="cesTable" :row-key="rowkey" :cell-style="columnbackgroundStyle" v-bind:name="tablekey">
            <template v-if="hasexpand">
                <slot />
            </template>
            <el-table-column v-if="isSelection" type="selection" :selectable="selectablefuc" align="center" width="30" :fixed="tablefixed"></el-table-column>
            <el-table-column v-if="isIndex" type="index" :label="indexLabel" align="center" :width="indexWidth" :fixed="tablefixed"></el-table-column>
            <template>
                <slot name="leftone" />
            </template>
            <template v-for="(item, index) in childtableCols">
                <template v-if="item.istrue && item.type !='color'">
                    <cescolumnmerge :showoverflowtooltip="showoverflowtooltip" v-if="item.merge &&(!item.permission||(item.permission&&checkPermission(item.permission)))" :that="that" :key="index" :column="item" :size="size" :type="type" :descData="descData" />
                    <cescolumn :showoverflowtooltip="showoverflowtooltip" v-else :that="that" :key="index" :column="item" :size="size" :type="type" :descData="descData" @previewImageGoodsCode="previewImageGoodsCode" @showImg="showImg" @preview="preview" @playerVideo="playerVideo" @showLogDetail="showLogDetail" @startSession="startSession"/>
                </template>
                <template v-if="item.type=='color'">
                    <el-table-column type="color" :property="item.backgroudColor" align="center" width="2"  ></el-table-column>
                </template>
            </template>
            <template v-if="hasexpandRight">
                <slot name="right" />
            </template>
        </el-table>
        <el-dialog :visible.sync="showImage" :modal="false" :show-close="false" :width="ImgWith" v-dialogDrag>
            <img ref="imgdialog" :src="imgList[0]" />
        </el-dialog>
        <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeFunc" style="z-index:9999;" />
        <div class="imgDolg" v-show="imgPreview.show" @click.stop="imgPreview.show = false">
            <i class="el-icon-close" id="imgDolgClose" @click.stop="imgPreview.show = false"></i>
            <img @click.stop="imgPreview.show = true" :src="imgPreview.img" />
        </div>

        <!--视频播放-->
        <el-dialog title="视频播放" :visible.sync="videoDialogVisible" width="50%" @close="closeVideoPlyer">
            <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
            <span slot="footer" class="dialog-footer">
                <el-button @click="videoDialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog :visible.sync="exportColumnsDialogProp.visible" width="300" draggable="true" title="导出选项"  v-dialogDrag>
            <el-row>
                <el-col :span="24" style="height:400px;overflow: auto;">
                    <el-tree
                    ref="exportColTree"
                    :data="exportColumnsDialogProp.data"
                    show-checkbox
                    node-key="id"
                    default-expand-all
                    :default-checked-keys="exportColumnsDialogProp.colIds"
                    :props="{
                        'label':'label'
                    }"
                    >
                    </el-tree>

                </el-col>
                <el-col :span="24" style="text-align:right">
                    <el-button type="primary" @click="setExportCols">确定</el-button>
                    <el-button @click="exportColumnsDialogProp.visible=false;" >取消</el-button>
                </el-col>
            </el-row>

        </el-dialog>

        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            v-dialogDrag>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNoInner="orderNoInner" :orderNo="orderNo" :isTx="isTx"
                style="z-index:10000;height:600px" />
        </el-dialog>
    </div>
</template>
<script>
//播放器
import videoplayer from '@/views/media/video/videoplayer'
import { getTableColumnCache, setTableColumnCache } from '@/api/admin/business'
import cescolumnmerge from "@/components/Table/yhcolumnmerge.vue";
import cescolumn from "@/components/Table/yhcolumn.vue";
import { getUserDingCode } from '@/api/admin/user'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { addOrUpdate as addRephotography } from "@/api/inventory/goodsimagerephotography";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
export default {
    components: { cescolumnmerge, cescolumn, ElImageViewer, videoplayer,OrderActionsByInnerNos },
    props: {
        that: { type: Object, default: this },
        hasexpand: { type: Boolean, default: false },
        hasexpandRight: { type: Boolean, default: false },
        // 表格型号：mini,medium,small
        size: { type: String, default: 'mini' },
        type: { type: String, default: 'primary' },
        isBorder: { type: Boolean, default: true },
        loading: { type: Boolean, default: false },
        tableHandles: { type: Array, default: () => [] },
        // 表格数据
        tableData: { type: Array, default: () => [] },
        // 表格列配置
        tableCols: { type: Array, default: () => [] },
        tablefixed: { type: Boolean, default: false },
        // 是否显示表格复选框
        isSelection: { type: Boolean, default: false },
        defaultSelections: { type: [Array, Object], default: () => null },
        // 是否显示表格索引
        isIndex: { type: Boolean, default: false },
        indexLabel: { type: String, default: '#' },
        //排序
        orderby: { type: Object, default: () => ({ order: "ascending", name: 'id' }) },
        filter: {},
        treeprops: {},
        showsummary: { type: Boolean, default: true },
        summaryarry: { type: Object, default: () => { } },
        descData: { type: Array, default: () => [] },
        isSelectColumn: { type: Boolean, default: true },//是否显示列筛选按钮
        isSelectColumnCols: { type: Boolean, default: true },//是否显示列筛选按钮
        customRowStyle: { type: Function, default: null },//行style样式
        headerCellStyle: { type: Function, default: null },//表头单元格style样式
        tablekey: { type: String, default: '' },//表格key
        rowkey: { type: String, default: '' },//表格key
        selectColumnHeight: { type: String, default: '30px' },//表格key
        indexWidth: { type: [String, Number], default: '40' },//索引宽度
        stripe: {type: Boolean, default: true},//斑马线显示
        defaultexpandall:{type : Boolean,default:false},
        height:{type:String,default:()=>{return '0'}},
        isselectable: {type: Boolean, default: false},//复选是否可选中
        showoverflowtooltip: {type: Boolean, default: true},
        cstmExportFunc:{type:Function,default:()=>{//导出
            return null;
        }},
        indexWidth:{type:Number,default:()=>{return 50;}},

    },
    data() {
        return {
            childtableCols: { type: Array, default: () => [] },
            checkBoxGroup: [],
            checkedColumns: [],
            imgPreview: { img: "", show: false },
            showImage: false,
            showGoodsImage: false,
            imgList: [],
            ImgWith: null,
            CansetTableColumn: false,
            summarycolumns: [],
            videoDialogVisible: false,
            videoplayerReload: false,
            videoUrl: '',
            bgcolorlist: [],
            arraynewone: [],
            arraynewtwo: [],
            colortime: 0,
            orderNo:null,
            dialogHisVisible: false,
            orderNoInner: null,
            isTx: false,
            exportColumnsDialogProp:{
                visible:false,
                data:[],
                colIds:[],
                colFields:[],
                colNames:[],
                mapFields:{},
                isLoadMapFields:false
            }
        }
    },
    mounted() {
        // debugger
        // console.log("单元格",this.tableCols)
        this.initCheckedColumns(this.tableCols);
        let self=this;
        _.delay(function(){
            self.$refs.cesTable.doLayout();
        },700);

    },
    updated(){
        // this.timefin()
        // this.setTimeout(()=>{
        //     this.timefin();
        // },10000)
    },
    computed:{
        tableHeight(){
            if(this.height!=undefined && this.height!=null && this.height!="0"){
                return this.height;
            }

            return !this.isSelectColumn ? '100%' : '100%';
        }
    },
    watch: {
        async checkedColumns(val, value) {
            if (!this.isSelectColumn) return;
            let checked = [];
            let unchecked = [];
            var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
            this.childtableCols.forEach((item, index) => {
                if (val.includes(item.label)) {
                    checked.push(item.label)
                    this.showhiddenColumn(item, true)
                    this.$nextTick(() => { this.$refs.cesTable.doLayout(); });
                }
                else {
                    unchecked.push(item.label)
                    this.showhiddenColumn(item, false)
                    this.$nextTick(() => { this.$refs.cesTable.doLayout(); });
                }
            })
            if (this.CansetTableColumn)
                await setTableColumnCache({ key: key, displays: checked, hides: unchecked })
        },

        async tableCols(val, value) {
            this.initCheckedColumns(val)
        },
        tableData(val, value) {
            this.$nextTick(() => { this.$refs.cesTable.doLayout(); });
        },
    },
    methods: {
        async startSession(id, type) {
                const { data, success } = await getUserDingCode({ type, id })
                if (success) {
                    if (!data) return this.$message.error('未获取到钉钉id')
                    window.open(`dingtalk://dingtalkclient/action/sendmsg?spm=dingtalk_id=${data}`, '_self')
                }
            },
        showLogDetail(params) {
            this.orderNoInner = params.orderType == 'orderNoInner' ? params.val : null
            this.orderNo = params.orderType == 'orderNo' ? params.val : null
            if (this.orderNo && params.platform && (params.platform == 1 || params.platform == 4 || params.platform == 8 || params.platform == 9)) {
                this.isTx = true
            }else{
                this.isTx = false
            }
            this.dialogHisVisible = true;
        },
        setExportCols(){
        if(!this.exportColumnsDialogProp.visible){
            this.exportColumnsDialogProp.visible=true;
            let allcolumns=this.$refs.cesTable.columns;
            let tempCols=[...allcolumns];
            if(tempCols.length>0 && tempCols[0].label==null){
                tempCols[0].label="#";
            }
            tempCols =  tempCols.filter(x=>x.label != null && x.label!='操作' && x.label != '');
            let tempRoot={
                id:'_root',
                parentId:"_root",
                children:tempCols,
                label:'全部'
            };


            if(this.exportColumnsDialogProp.data.length==0){
                const visibleColumn = this.$refs.cesTable.columns;

                visibleColumn.forEach(item=>{
                    this.exportColumnsDialogProp.colIds.push(item.id);
                });
            }

            if(this.exportColumnsDialogProp.isLoadMapFields==false){
                //如果还没加载字段与导出的映射关系，先加载
                this.tableCols.forEach(col=>{
                    if(col.prop && col.exportField){
                        this.exportColumnsDialogProp.mapFields[col.prop]=col.exportField;
                    }
                    if(col.cols && col.cols.length>0){
                        col.cols.forEach(col1=>{
                            if(col1.prop && col1.exportField){
                                this.exportColumnsDialogProp.mapFields[col1.prop]=col1.exportField;
                            }
                            if(col1.cols && col1.cols.length>0){
                                col1.cols.forEach(col2=>{
                                    if(col2.prop && col2.exportField){
                                        this.exportColumnsDialogProp.mapFields[col2.prop]=col2.exportField;
                                    }
                                });
                            }
                        });

                    }
                });

                this.exportColumnsDialogProp.isLoadMapFields=true;
            }

            this.exportColumnsDialogProp.data=[tempRoot];
            return;
        }

        let selNodes=this.$refs.exportColTree.getCheckedNodes(true);
        this.exportColumnsDialogProp.colIds=selNodes.map(x=>x.id);

        this.exportColumnsDialogProp.colFields=selNodes.map(x=>{
            return x.property  &&  this.exportColumnsDialogProp.mapFields[x.property] ? this.exportColumnsDialogProp.mapFields[x.property] :x.property;
        });

        if(this.cstmExportFunc &&  typeof(this.cstmExportFunc)=='function' ){
            console.log('开始调用导出');
            this.cstmExportFunc({"YH_EXT_ExportColumns":[...this.exportColumnsDialogProp.colFields]});
            console.log('导出数据结束');
        }

        this.exportColumnsDialogProp.visible=false;
    },
        async checkedColumnsShow(val, value) {
            if (!this.isSelectColumn) return;
            let checked = [];
            let unchecked = [];
            var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
            this.childtableCols.forEach((item, index) => {
                if (val.includes(item.label)) {
                    checked.push(item.label)
                    this.showhiddenColumn(item, true)
                    this.$nextTick(() => { this.$refs.cesTable.doLayout(); });
                }
                else {
                    unchecked.push(item.label)
                    this.showhiddenColumn(item, false)
                    this.$nextTick(() => { this.$refs.cesTable.doLayout(); });
                }
            })
            if (this.CansetTableColumn)
                await setTableColumnCache({ key: key, displays: checked, hides: unchecked })
        },
        async initCheckedColumns(_tableCols) {
            _tableCols.forEach((item, index) => {
                if(item.bordercolor)
                    this.bgcolorlist.push(item.bordercolor)
                if(item.type !='color'&&!item.bordercolor)
                    this.checkBoxGroup.push(item.label);
            })
            await this.$nextTick(async function () {
                this.CansetTableColumn = false
                this.childtableCols = this.tableCols;
                if (!this.isSelectColumn) return;
                let checked = [];
                var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
                let res = await getTableColumnCache({ key: key })
                if (res?.data?.hideContent) {
                    this.tableCols.forEach((item, index) => {
                        if (!res.data.hideContent.includes(item.label))
                            checked.push(item.label);
                    })
                }
                if (checked.length > 0) this.checkedColumns = checked;
                else this.checkedColumns = this.checkBoxGroup
            });

            let _that = this;
            setTimeout(function () { _that.CansetTableColumn = true; }, 2000);
        },
        showhiddenColumn(column, ishow) {
            if (!!column.merge && !!column.cols && column.cols.length > 0) {
                //console.log('childtableCols',this.childtableCols)
                //column.cols.forEach(f=> this.showhiddenColumn(f,ishow))
            }
            else if (!column.label)
                column.istrue = true;
            else if (!column.merge)
                column.istrue = ishow;
        },
        updateData() {
            if (this.tableData) {
                this.tableData.forEach(element => {
                    if (!element.id) {
                        element.id = this.randrom()
                    }
                });
            }
            console.log('this.tableData', this.tableData)
        },
        randrom() {
            var e = 10;
            var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
                a = t.length,
                n = "";
            for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
            return n;
        },
        select(rows, row) {
            this.$emit('select', rows, row);
        },
        rowclick(row, column, event){
            this.$emit('rowclick', row, column,event);
        },
        // 全选
        selectAll(rows) {
            this.$emit('select', rows)
        },
        sortchange(column) {
            this.$emit('sortchange', column)
        },
        cellclick(row, column, cell, event) {
            this.$emit('cellclick', row, column, cell, event)
        },
        expandchange(row, args) {
            this.$emit('expandchange', row, args)
        },
        clearSort() {
            this.$refs.cesTable.clearSort();
        },
        btnHand(hand) {
            console.log("hand", hand)
            this.$emit(hand)
        },

           // 格式化函数，处理千位分隔符和小数位
           formatNumber (number){
                    const absNumber = Math.abs(number);
                    const options = {
                        minimumFractionDigits: absNumber >= 100 ? 0 : 2,
                        maximumFractionDigits: absNumber >= 100 ? 0 : 2,
                    };
                    return new Intl.NumberFormat('zh-CN', options).format(number);
                },
        getSummaries(param) {
            const sums = [];
            if (!this.summaryarry)
                return sums
            var arr = Object.keys(this.summaryarry);
            if (arr.length == 0)
                return sums
            const { columns, data } = param;
            var hashj = false;
            columns.forEach((column, index) => {
                // if (column.width>70&&hashj==false) {
                //   //sums[index] = '合计';
                //  // hashj=true
                //   return;
                // }
                if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
                    var sum = this.summaryarry[column.property + '_sum'];
                    if (sum == null) return;
                    else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
                    // else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2)
                    else if (Math.abs(parseInt(sum)) < 100){
                        if(   Number(Math.abs(parseInt(sum))) === Math.floor(Number(Math.abs(parseInt(sum)))) ){
                            sums[index] = sum;
                        }
                        sums[index] = sum.toFixed(2);
                    }
                    // else sums[index] = sum.toFixed(0)
                    else sums[index] = this.formatNumber(sum);
                }
                else sums[index] = ''
            });
            if (this.summarycolumns.length == 0) {
                this.summarycolumns = columns;
                this.initsummaryEvent();
            }
            return sums
        },
        initsummaryEvent() {
            let self = this;
            let table;
            if(this.tablekey) table = document.querySelector('[name='+this.tablekey+'] .el-table__footer-wrapper>table');
            else table = document.querySelectorAll('.el-table__footer-wrapper>table');
            if(table?.length>0) table=table[0]
            this.$nextTick(() => {
                self.summarycolumns.forEach((column, index) => {
                    if (column.property) {
                        var col = findcol(self.tableCols, column.property);
                        if (col && col.summaryEvent) {
                            table.rows[0].cells[index].style.color = "red";
                            table.rows[0].cells[index].onclick = function () {
                                self.$emit('summaryClick', column.property)
                            }
                        }
                    }
                })
            })

            function findcol(cols, property) {
                let column;
                for (var i = 0; i < cols.length; i++) {
                    var c = cols[i];
                    if (column) break
                    else if (c.prop && c.prop.toLowerCase() == property.toLowerCase()) {
                        column = c;
                        break
                    }
                    else if (c.cols && c.cols.length > 0) column = findcol(c.cols, property)
                }
                return column
            }
        },
        async closeVideoPlyer() {
            this.videoplayerReload = false;
        },
        async playerVideo(videoUrl) {
            this.videoplayerReload = false;
            this.videoplayerReload = true;
            this.videoDialogVisible = true;
            this.videoUrl = videoUrl;
        },
        async closeFunc() {
            this.showGoodsImage = false;
        },
        async preview(imgUrl) {
            this.showGoodsImage = true;
            this.imgList = [];
            this.imgList.push(imgUrl);
        },
        // 图片点击放大
        async showImg(e) {
            this.showGoodsImage = true;
            this.imgList = [];
            this.imgList.push(e.target.src);
        },
        //商品编码大图预览 增加自定义相机按钮code商品编码，name商品名称
        async previewImageGoodsCode(imgUrl, code, name) {
            this.showGoodsImage = true;
            this.imgList = [];
            this.imgList.push(imgUrl);
            var that = this;
            //增加自定义相机按钮，记录重拍
            setTimeout(() => {
                var container = document.getElementsByClassName('el-image-viewer__actions__inner');
                if (container && container.length > 0) {
                    container = container[0];
                    var child = document.createElement('li');
                    child.title = "重拍";
                    child.className = "el-icon-camera";
                    child.onclick = async function () {
                        //that.$confirm('确定重拍吗？','提示').then(async ()=>{
                        if (confirm("确定重拍吗？")) {
                            var params = {
                                goodsCode: code,
                                goodsName: name,
                                goodsImage: imgUrl
                            };
                            var res = await addRephotography(params);
                            if (!res?.success) {
                                return;
                            }
                            else {
                                that.$message({ message: "重拍登记成功", type: "success" });
                            }
                        }
                        //}).catch(()=>{});

                    };
                    container.appendChild(child);
                }
            }, 100);
        },
        //清空选择
        clearSelection() {
            this.$refs.cesTable.clearSelection();
        },
        //取消/选择某一行
        toggleRowSelection(row, selected) {
            this.$refs.cesTable.toggleRowSelection(row, selected);
        },
        //取消/选择所有
        toggleAllSelection(selected) {
            this.$refs.cesTable.toggleAllSelection(selected);
        },
        doLayout() {
            this.$nextTick(() => { this.$refs.cesTable.doLayout(); });
        },
        columnbackgroundStyle({ row, column, rowIndex, columnIndex }) {
            let _this = this;
            if(column.type=='color'){
                return'background:'+ column.property;
            }
        },
        sendApi(val){
            if(val.length>0&&newcolorindex!=val.length-1){
                newcolorindex = newcolorindex+1
                return val[newcolorindex]
            }else if(newcolorindex==val.length-1){
                newcolorindex = 0;
                return val[0]
            }
        },
        heardcolumnbackgroundStyle({ row, column, rowIndex, columnIndex }) {
            let _this = this;
            if(column.type=='color'){
                return'background:'+ column.property;
            }
        },
        selectablefuc(row, index){
            if(this.isselectable){
            let result;
            this.$emit('isselectablefuc', {row,index}, val=>{result = val})
            return result;
            }else {
                return true;
            }
        },
        // sumcolor(){
        //     let demo = document.getElementById("el-tableid");
        //     console.log("打印demio",demo)
        //     debugger
        // },
    },
}
</script>
<style lang="scss" scoped>
.el-table th>.cell {
    padding-left: 8px;
}

.yh202306081055::-webkit-scrollbar{
    width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 5px;
}

.el-table .caret-wrapper {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    height: 34px;
    width: 0;
    vertical-align: middle;
    cursor: pointer;
    overflow: initial;
    position: relative;
}

.el-table__footer-wrapper {
    margin-top: -2px;
    font-size: 9px;
    padding: 0px;
}

.el-table .cell {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    word-break: break-all;
    line-height: 23px;
    padding-right: 1px;
}

.el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
    background-color: #f1f1f1;
    color: #0756f5;
}

.ces-table-require::before {
    content: "*";
    color: red;
}

.table-wrapper {
    width: 100%;
    height: calc(100% - 35px);
    margin: 0;
}

.el-table {
    overflow: visible !important;
    height: 99.9% !important;
    width: 99.9% !important;
}

.el-table__empty-block {
    height: 550px;
    border: none;
}

/* .el-table {
                                                                                                  width: 99.9% !important;
                                                                                                } */
/* .wendang p img{max-height: 60px;} */
.wendang img {
    max-height: 50px;
}

.table_column_show {
    display: block;
}

.table_column_hidden {
    display: none;
}
</style>
<style lang="scss" scoped>
.imgDolg {
    width: 100vw;
    height: 100vh;
    position: fixed;
    z-index: 99999;
    background-color: rgba(140, 134, 134, 0.6);
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: auto;

    #imgDolgClose {
        position: fixed;
        top: 35px;
        cursor: pointer;
        right: 7%;
        font-size: 50px;
        color: white;
    }

    // img{
    //    width: 80%;
    // }
}
// ::v-deep .tr{
//         pointer-events:none;
//     }
</style>
