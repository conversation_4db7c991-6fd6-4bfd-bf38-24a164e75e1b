import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/purchaseHike/`

//分页查询核价操作数据 PageGetApplyData
export const pageGetApplyData = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetApplyData', params, config)
}

//新增或编辑 采购涨价审批 GetRealTimeLackOrderPageList
export const mergePurchaseHikeApply = (params, config = {}) => {
    return request.post(apiPrefix + 'MergePurchaseHikeApply', params, config)
}

//查询涨价审批数据 GetApplyData
export const getApplyData = (params, config = {}) => {
    return request.post(apiPrefix + 'GetApplyData', params, config)
}

//删除申请数据 DeletePurchaseHikeApply
export const deletePurchaseHikeApply = (params, config = {}) => {
    return request.post(apiPrefix + 'DeletePurchaseHikeApply', params, config)
}

//GetApprovalRecords  查询审批记录
export const getApprovalRecords = (params, config = {}) => {
    return request.post(apiPrefix + 'GetApprovalRecords', params, config)
}


//ExportApplyData 导出数据
export const exportApplyData = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportApplyData', params, config)
}

//GetManagerInfo
export const getManagerInfo = (params, config = {}) => {
    return request.post(apiPrefix + 'GetManagerInfo', params, config)
}

//发生申请 SendApply
export const sendApply = (params, config = {}) => {
    return request.post(apiPrefix + 'SendApply', params, config)
}

//获取成本
export const getCostPrice = (params, config = {}) => {
  return request.post(apiPrefix + 'GetCostPrice', params, config)
}

//批量获取商品编码信息 GetGoodsPrevPriceList
export const getGoodsPrevPriceList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGoodsPrevPriceList', params, config)
}

