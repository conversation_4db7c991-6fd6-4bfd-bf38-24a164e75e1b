import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OpenPlatform}/ProviderQuotation/`


//供应商报价-已提交
export const getProviderQuotationRecordPageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderQuotationRecordPageList', params, config)
}

//供应商报价-未提交
export const getProviderQuotationCGRecordPageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderQuotationCGRecordPageList',params, config)
}

//供应商报价-新品提交
export const getProviderNewGoodsPageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderNewGoodsPageList',params, config)
}

//供应商报价-提交历史
export const getProviderQuotationHisRecordPageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderQuotationHisRecordPageList',params, config)
}

//获取供应商记录
export const getProviderPersonSetPageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderPersonSetPageList',params, config)
}

//获取供应商浏览记录
export const getProviderVisitLogPageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderVisitLogPageList',params, config)
}

//获取供应商搜索记录
export const getProviderSearchLogPageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderSearchLogPageList',params, config)
}

//获取系列编码列表数据
export const getProviderStylePageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderStylePageList',params, config)
}

//添加类别
export const addProviderCategory = (params,config ={}) =>{
    return request.post(apiPrefix + 'AddProviderCategory',params, config)
}

//获取类别
export const getProviderCategory = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderCategory',params, config)
}

//设置类别
export const setProviderStyleCategory = (params,config ={}) =>{
    return request.post(apiPrefix + 'SetProviderStyleCategory',params, config)
}

//设置销量
export const setProviderStyleMultiple = (params,config ={}) =>{
    return request.post(apiPrefix + 'SetProviderStyleMultiple',params, config)
}

//设置上下架
export const setProviderStyleIsShow = (params,config ={}) =>{
    return request.post(apiPrefix + 'SetProviderStyleIsShow',params, config)
}

//系列编码导入
export const importProviderGoodsDataAsync = (params,config ={}) =>{
    return request.post(apiPrefix + 'ImportProviderGoodsDataAsync',params, config)
}   

//设置展示图片
export const setProviderStylePic = (params,config ={}) =>{
    return request.post(apiPrefix + 'SetProviderStylePic',params, config)
}

//跟进
export const addProviderDockingResult = (params,config ={}) =>{
    return request.post(apiPrefix + 'AddProviderDockingResult',params, config)
}

//跟进记录
export const GetProviderDockingResultList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderDockingResultList',params, config)
}

//报价指派
export const setProviderDockingUser = (params,config ={}) =>{
    return request.post(apiPrefix + 'SetProviderDockingUser',params, config)
}

//新品指派
export const setProviderNewGoodsDockingUser = (params,config ={}) =>{
    return request.post(apiPrefix + 'SetProviderNewGoodsDockingUser',params, config)
}

//新品跟进
export const addProviderNewGoodsDockingResult = (params,config ={}) =>{
    return request.post(apiPrefix + 'AddProviderNewGoodsDockingResult',params, config)
}

//新品跟进记录
export const getProviderNewGoodsDockingResultList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderNewGoodsDockingResultList',params, config)
}  

//获取采购人员列表
export const getBrandUsers = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetBrandUsers',params, config)
}

//供应商报价-总览
export const getProviderQuotationRecordOverViewPageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderQuotationRecordOverViewPageList',params, config)
}

//供应商报价-汇总（分组）
export const getProviderQuotationRecordOverViewGroupPageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderQuotationRecordOverViewGroupPageList',params, config)
}

//设置商品名称(商品)
export const setProviderGoodName = (params,config ={}) =>{
    return request.post(apiPrefix + 'SetProviderGoodName',params, config)
}

//设置对接人
export const setProviderStyleDockUser = (params,config ={}) =>{
    return request.post(apiPrefix + 'SetProviderStyleDockUser',params, config)
}

//系列编码弹窗
export const getProviderQuotationOverViewGroupDialogStylePageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderQuotationOverViewGroupDialogStylePageList',params, config)
}
//商品编码弹窗
export const getProviderQuotationOverViewGroupDialogGoodPageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderQuotationOverViewGroupDialogGoodPageList',params, config)
}
//供应商弹窗
export const getProviderQuotationOverViewGroupDialogProviderPageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderQuotationOverViewGroupDialogProviderPageList',params, config)
}

//一键下架商品编码
export const setProviderGoodIsShow = (params,config ={}) =>{
    return request.post(apiPrefix + 'SetProviderGoodIsShow',params, config)
}

//获取客服列表
export const getProviderStyleWXAccountList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderStyleWXAccountList',params, config)
}

//保存客服列表
export const saveProviderStyleWXAccount = (params,config ={}) =>{
    return request.post(apiPrefix + 'SaveProviderStyleWXAccount',params, config)
}

//获取系列编码列表
export const getProviderStyleWXBindPageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetProviderStyleWXBindPageList',params, config)
}

//一键绑定
export const setProviderStyleWXBind = (params,config ={}) =>{
    return request.post(apiPrefix + 'SetProviderStyleWXBind',params, config)
}

//咨询记录著列表
export const getWXConsultRecordPageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetWXConsultRecordPageList',params, config)
}

//电话记录著列表
export const getPhoneConsultRecordPageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetPhoneConsultRecordPageList',params, config)
}

//添加电话记录
export const addPhoneConsultDockingResult = (params,config ={}) =>{
    return request.post(apiPrefix + 'AddPhoneConsultDockingResult',params, config)
}

//查看微信记录
export const getPhoneConsultDockingResultList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetPhoneConsultDockingResultList',params, config)
}

//添加微信记录
export const addWXConsultDockingResult = (params,config ={}) =>{
    return request.post(apiPrefix + 'AddWXConsultDockingResult',params, config)
}

//查看微信记录
export const getWXConsultDockingResultList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetWXConsultDockingResultList',params, config)
}

export const exportProviderQuotationRecord = (params,config ={responseType: 'blob'}) =>{
    return request.post(apiPrefix + 'ExportProviderQuotationRecord',params, config) 
} 

//创建系列编码
export const addNewProviderStyle = (params,config ={}) =>{
    return request.post(apiPrefix + 'AddNewProviderStyle',params, config) 
}  

//一键更换系列编码
export const moveProviderGoodToProviderStyle = (params,config ={}) =>{
    return request.post(apiPrefix + 'MoveProviderGoodToProviderStyle',params, config) 
}  

//获取系列编码下拉
export const getTop50ProviderStyleByStyleCode = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetTop50ProviderStyleByStyleCode',params, config) 
}  

