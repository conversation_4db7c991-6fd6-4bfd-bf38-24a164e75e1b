
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
export default async function  AjaxBigFile(file,i,batchnumber) {
  var name = file.name; //文件名
  var size = file.size; //总大小shardSize = 2 * 1024 * 1024,
  var shardSize = 10 * 1024 * 1024;
  var shardCount = Math.ceil(size / shardSize); //总片数
  if (i >= shardCount) {
      return;
  }
  //计算每一片的起始与结束位置
  var start = i * shardSize;
  var end = Math.min(size, start + shardSize);
  //构造一个表单，FormData是HTML5新增的
  var form = new FormData();
  i=i+1;
  form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
  form.append("batchnumber", batchnumber);
  form.append("fileName", name);
  form.append("total", shardCount); //总片数
  form.append("index", i); //当前是第几片
  const res = await xMTVideoUploadBlockAsync(form);
  if (res?.success) {
      if(i == shardCount){
        return res.data;
      }else{
         await  this.AjaxFile(file, i,res.data);
      }
  }else{
      this.$message({ message: res?.msg, type: "warning" });
  }
}
