import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/orderdeductmoney/`

//导入
export const importData = (params,config ={}) =>{
    return request.post(apiPrefix + 'ImportOrderDeductMoneyTabooAsync', params, config)
}

//分页查询
export const pageData = (params,config ={}) =>{
    return request.post(apiPrefix+'PageOrderDeductMoneyTabooAsync', params, config)
}

//导出
export const exportData =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportOrderDeductMoneyTabooAsync',{params: params, ...config})
}

//增改
export const addOrUpdate = (params, config = {}) => {
    return request.post(apiPrefix + 'AddOrUpdateTabooAsync', params, config)
}

//删除
export const deleteData = (params, config = {}) => {
    return request.delete(apiPrefix + 'DeleteTabooAsync',{ params, ...config})
}

//获取单个
export const getById = (id, config = {}) => {
    return request.get(apiPrefix + `GetTabooByIdAsync?id=${id}`, {}, config)
}

//分页查询 统计
export const getDeductMoneyTabooSum = (params,config ={}) =>{
    return request.post(apiPrefix+'GetDeductMoneyTabooSumAsync', params, config)
}

//导出 统计
export const exportDeductMoneyTabooSum =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportDeductMoneyTabooSumAsync',{params: params, ...config})
}

//分页查询 明细
export const getDeductMoneyTabooDetail = (params,config ={}) =>{
    return request.post(apiPrefix+'GetDeductMoneyTabooDetailAsync', params, config)
}

//导出 明细
export const exportDeductMoneyTabooDetail =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportDeductMoneyTabooDetailAsync',{params: params, ...config})
}

export const getDeductMoneyTabooDetailOrderNos=(params,config={})=>{
    return request.post(apiPrefix + 'GetDeductMoneyTabooDetailOrderNosAsync',params,config);
}