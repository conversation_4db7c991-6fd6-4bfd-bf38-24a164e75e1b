import request from '@/utils/request'

const LookBoardPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/LookBoard/`
export const importLookBoardAsync = (params, config = {}) => {return request.post(LookBoardPrefix + 'ImportLookBoardAsync',  params, config)}
export const getLookBoardList = (params, config = {}) => {return request.post(LookBoardPrefix + 'GetLookBoardList', params, config )}
export const deleteLookBoardBatch = (params, config = {}) => {return request.get(LookBoardPrefix + 'DeleteBatchAsync', { params: params, ...config })}

export const importNewMediaTaoXiContributeinfo = (params, config = {}) => {return request.post(LookBoardPrefix + 'ImportNewMediaTaoXiContributeinfoAsync',  params, config)}
export const getNewMediaTaoXiContributeinfoList = (params, config = {}) => {return request.get(LookBoardPrefix + 'GetNewMediaTaoXiContributeinfoList', { params: params, ...config })}

export const importNewMediaTaoXiPerformance = (params, config = {}) => {return request.post(LookBoardPrefix + 'ImportNewMediaTaoXiPerformance',  params, config)}



export const getNewMediaTaoXiPerformanceList = (params, config = {}) => {return request.get(LookBoardPrefix + 'GetNewMediaTaoXiPerformanceList', { params: params, ...config })}


export const getPersonnelPerformanceList = (params, config = {}) => {return request.get(LookBoardPrefix + 'GetPersonnelPerformanceList', { params: params, ...config })}

export const getShopPerformanceList = (params, config = {}) => {return request.get(LookBoardPrefix + 'GetShopPerformanceList', { params: params, ...config })}
export const getOriginalVideoShopPerformanceTxList = (params, config = {}) => {return request.get(LookBoardPrefix + 'GetOriginalVideoShopPerformanceTxList', { params: params, ...config })}

export const deleteTaoXiContributeinfo = (params, config = {}) => { return request.delete(LookBoardPrefix + 'DeleteTaoXiContributeinfoAsync', { params: params, ...config }) }

// 导出视频数据管理
export const exportLookBoardList = (params, config = { responseType: 'blob' }) => {
    return request.post(LookBoardPrefix + 'ExportLookBoardList', params, config)
}

// 导入人员维护
export const importVideoContributorAsync = (params, config = {}) => {
    return request.post(LookBoardPrefix + 'ImportVideoContributorAsync', params, config)
}

// 查询人员维护
export const getVideoContributorList = (params, config = {}) => {
    return request.post(LookBoardPrefix + 'GetVideoContributorList', params, config)
}

// 导出人员维护
export const exportVideoContributorList = (params, config = { responseType: 'blob' }) => {
    return request.post(LookBoardPrefix + 'ExportVideoContributorList', params, config)
}

// 编辑人员维护
export const editVideoContributor = (params, config = {}) => {
    return request.post(LookBoardPrefix + 'EditVideoContributor', params, config)
}

// 删除人员维护
export const deleteVideoContributor = (params, config = {}) => {
    return request.post(LookBoardPrefix + 'DeleteVideoContributor', params, config)
}

// 人员维护姓名选择器
export const getVideoContributorSelectorList = (params, config = {}) => {
    return request.post(LookBoardPrefix + 'GetVideoContributorSelectorList', params, config)
}
