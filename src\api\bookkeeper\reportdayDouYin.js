import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/DayReportDouYin/`


//商务人员
export const GetBusinessMan = (params, config = {}) => { return request.get(apiPrefix + 'GetBusinessMan', { params: params, ...config }) }

//一键更改商务人员
export const OneKeyUpdateBusinessMan = (params, config = {}) => {
    return request.post(apiPrefix + 'OneKeyUpdateBusinessMan', params, config)
}

//抖音管理达人信息
export const GetAllWiseManList = (params, config = {}) => { return request.get(apiPrefix + 'GetAllWiseManList', { params: params, ...config }) }
//抖音达人信息分页
export const GetAllWiseManPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetAllWiseManPageList', params, config)
}
//导入抖音达人
export const ImportAllWiseMan = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportAllWiseMan', params, config)
}
//导入抖音达人商务关系
export const ImportProductDyWiseManBzRef = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportProductDyWiseManBzRef', params, config)
}
//导入抖音达人商务关系
export const importProductDySampleAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'importProductDySampleAsync', params, config)
}



//抖音达人信息
export const SaveDouYinWiseManAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveDouYinWiseManAsync', params, config)
}

//获取抖音日报商务达人修改日志
export const GetProductDyWiseManLogList = (params, config = {}) => { return request.get(apiPrefix + 'GetProductDyWiseManLogList', { params: params, ...config }) }


//保存抖音达人商务关系
export const SaveProductDyWiseManBzRef = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveProductDyWiseManBzRef', params, config)
}

//抖音达人商务关系列表
export const GetDYBusinessWiseManList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDYBusinessWiseManList', params, config)
}


//抖音达人抖音号列表
export const GetAllWiseManDetailList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetAllWiseManDetailList', params, config)
}

//抖音达人抖音号列表
export const GetAllWiseManDetailListPage = (params, config = {}) => { return request.get(apiPrefix + 'GetAllWiseManDetailListPage', { params: params, ...config }) }

//获取抖音商务达人商务信息
export const GetAllBusinessDetailList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetAllBusinessDetailList', params, config)
}
//抖音商务日报只有组长以上看全部，其他看自己
export const GetBusinessManInfo = (params, config = {}) => {
    return request.get(apiPrefix + 'GetBusinessManInfo', params, config)
}


//导出商务达人关系
export const ExportDouYinWiseManExcelAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportDouYinWiseManExcelAsync', params, config) }

//抖音商务日报
export const GetBusinessDayReportDY = (params, config = {}) => {
    return request.post(apiPrefix + 'GetBusinessDayReportDY', params, config)
}

//抖音商务BD业绩统计分页查询
export const GetDyWiseManWorkPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDyWiseManWorkPageList', params, config)
}
//抖音商务BD业绩统计分页查询
export const GetDyWiseManWorkChat = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDyWiseManWorkChat', params, config)
}

// //抖音寄样商务信息分页查询
export const getProductDySamplePageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetProductDySamplePageList', params, config)
}

//通过商务钉钉ID获取商务所有的达人及产品
export const getProductDySampleWiseManBzRefList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetProductDySampleWiseManBzRefList', { params: params, ...config })
}

//保存寄样商务信息
export const saveProductDySample = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveProductDySample', params, config)
}

//抖音寄样商务信息-跟踪
export const getProductDySampleTrackPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetProductDySampleTrackPageList', params, config)
}

//保存寄样商务信息-跟踪
export const saveProductDySampleTrack = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveProductDySampleTrack', params, config)
}