import request from '@/utils/request'
const InquirsPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/Inquirs/`
export const importInquirsAsync = (params, config = {}) => { return request.post(InquirsPrefix + 'ImportInquirsAsync', params, config) }
export const getInquirsList = (params, config = {}) => { return request.get(InquirsPrefix + 'GetInquirsList', { params: params, ...config }) }
export const deleteInquirsBatch = (params, config = {}) => { return request.get(InquirsPrefix + 'DeleteBatchAsync', { params: params, ...config }) }
export const GetInquirsNotExistsList = (params, config = {}) => { return request.post(InquirsPrefix + 'GetInquirsNotExistsList', params, config) }

//获取组distinct
export const getTaoXiGroup = (params, config = {}) => { return request.get(InquirsPrefix + 'GetTaoXiGroup', { params: params, ...config }) }
//获取组distinct
export const getTaoXiShop = (params, config = {}) => { return request.get(InquirsPrefix + 'GetTaoXiShop', { params: params, ...config }) }
//获取组distinct
export const getTaoXiSname = (params, config = {}) => { return request.get(InquirsPrefix + 'GetTaoXiSname', { params: params, ...config }) }
//获取组distinct
export const getTaoXiSnick = (params, config = {}) => { return request.get(InquirsPrefix + 'GetTaoXiSnick', { params: params, ...config }) }

//获取所有淘系计算系数的名字--获取历史提成系数
export const getTaoBaoInquireGradeWayNameList = (params, config = {}) => { return request.get(InquirsPrefix + 'GetTaoBaoInquireGradeWayNameList', { params: params, ...config }) }

//获取淘系单个计算系数--获取历史提成系数下面的列表
export const getTaoBaoInquireGradeWayByName = (params, config = {}) => { return request.get(InquirsPrefix + 'GetTaoBaoInquireGradeWayByName', { params: params, ...config }) }

//保存淘系单个计算系数--弹窗的保存
export const saveTaoBaoInquireGradeWay = (params, config = {}) => { return request.post(InquirsPrefix + 'SaveTaoBaoInquireGradeWay', params, config) }

//获取接待人数阈值列表
export const getTaoBaoInquireGradeSetList = (params, config = {}) => { return request.get(InquirsPrefix + 'GetTaoBaoInquireGradeSetList', { params: params, ...config }) }

//保存设置接待人数阈值 --保存
export const SaveTaoBaoInquireGradeSet = (params, config = {}) => { return request.post(InquirsPrefix + 'SaveTaoBaoInquireGradeSet', params, config) }

//删除阈值列表数据 --删除
export const deleteTaoBaoInquireGradeSet = (params, config = {}) => { return request.post(InquirsPrefix + 'DeleteTaoBaoInquireGradeSet', params, config) }

//获取客服等级列表
export const getTaoBaoInquireGradeUserList = (params, config = {}) => { return request.post(InquirsPrefix + 'GetTaoBaoInquireGradeUserList', params, config) }

//设置账号参与计算
export const addTaoBaoInquireGradeComputeSnick = (params, config = {}) => { return request.get(InquirsPrefix + 'AddTaoBaoInquireGradeComputeSnick', { params: params, ...config }) }

//批量设置客服等级
export const batchSetTaoBaoInquireGradeUser = (params, config = {}) => { return request.post(InquirsPrefix + 'BatchSetTaoBaoInquireGradeUser', params, config) }

//获取淘宝所有组名字
export const getTaoBaoGroupNameList = (params, config = {}) => { return request.get(InquirsPrefix + 'GetTaoBaoGroupNameList', { params: params, ...config }) }

//分页获取淘系 新绩效核算表(售前组)
export const getTaoBaoInquireGradeComputePageList = (params, config = {}) => { return request.post(InquirsPrefix + 'GetTaoBaoInquireGradeComputePageList', params, config) }

//获取淘系计算结果趋势图
export const getTaoBaoInquireGradeComputeChat = (params, config = {}) => { return request.post(InquirsPrefix + 'GetTaoBaoInquireGradeComputeChat', params, config) }

//分页获取淘系绩效计算结果(组)
export const getTaoBaoInquireGradeComputePageList_Group = (params, config = {}) => { return request.post(InquirsPrefix + 'GetTaoBaoInquireGradeComputePageList_Group', params, config) }

//获取淘系计算结果趋势图(组)
export const getTaoBaoInquireGradeComputeChat_Group = (params, config = {}) => { return request.post(InquirsPrefix + 'GetTaoBaoInquireGradeComputeChat_Group', params, config) }

//分页获取淘系绩效计算结果(店)
export const getTaoBaoInquireGradeComputePageList_Shop = (params, config = {}) => { return request.post(InquirsPrefix + 'GetTaoBaoInquireGradeComputePageList_Shop', params, config) }

//获取淘系计算结果趋势图(店)
export const getTaoBaoInquireGradeComputeChat_Shop = (params, config = {}) => { return request.post(InquirsPrefix + 'GetTaoBaoInquireGradeComputeChat_Shop', params, config) }

//分页获取淘系绩效计算结果(店-人)
export const getTaoBaoInquireGradeComputePageList_Shop_User = (params, config = {}) => { return request.post(InquirsPrefix + 'GetTaoBaoInquireGradeComputePageList_Shop_User', params, config) }

//分页获取淘宝绩效计算结果(人)
export const getTaoBaoInquireGradeComputePageList_User = (params, config = {}) => { return request.post(InquirsPrefix + 'GetTaoBaoInquireGradeComputePageList_User', params, config) }

//获取淘宝绩效计算结果趋势图(人)
export const GetTaoBaoInquireGradeComputeChat_User = (params, config = {}) => { return request.post(InquirsPrefix + 'GetTaoBaoInquireGradeComputeChat_User', params, config) }

//某人第N名次明细
export const getTaoBaoInquireGradeComputePageList_UserGradeDtl = (params, config = {}) => { return request.post(InquirsPrefix + 'GetTaoBaoInquireGradeComputePageList_UserGradeDtl', params, config) }

//导出淘系绩效结果
export const exportTaoBaoInquireGradeComputeList = (params, config = { responseType: 'blob' }) => { return request.post(InquirsPrefix + 'ExportTaoBaoInquireGradeComputeList', params, config) }