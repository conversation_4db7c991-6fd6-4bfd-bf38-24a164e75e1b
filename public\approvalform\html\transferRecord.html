<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <link rel="stylesheet" href="/approvalform/html/api/elment.css">
    <script src="/approvalform/html/api/vue.min.js"></script>
    <script src="/approvalform/html/api/elment.js"></script>
    <script src="/approvalform/html/api/jquery.min.js"></script>
    <script src="/approvalform/html/api/html2canvas.js"></script>
    <title>调拨记录</title>
    <style type="text/css">
        .linebreak {
            overflow: hidden;
            /*超出部分隐藏*/
            text-overflow: ellipsis;
            /* 超出部分显示省略号 */
            white-space: normal;
            /*规定段落中的文本不进行换行 */
            width: 100%;
        }
        .el-pagination__jump {
            margin-left:0px;
        }
    </style>
</head>

<body>
    <div id="app" style="margin:0 auto;">
        <el-container direction="vertical" style=" border: 1px #ccc solid;">
            <template>
                <el-table  ref="tableBox" :data="list" :max-height="tableHeight" style="width: 100%;" row-key="id" border>
                    <el-table-column type="index" min-width="20" fixed></el-table-column>
                    
                    <el-table-column prop="sku_id" label="商品编码" min-width="80" align="center"></el-table-column>
                    <el-table-column prop="io_date" label="单据日期" min-width="100" align="center"></el-table-column>
                    <el-table-column prop="link_warehouse" label="调入仓" min-width="80" align="center"></el-table-column>
                    <el-table-column prop="warehouse" label="调出仓" min-width="80" align="center"></el-table-column>
                    <el-table-column prop="inId" label="调入单号" min-width="80" align="center"></el-table-column>
                    <el-table-column prop="outId" label="调出单号" min-width="80" align="center"></el-table-column>
                    <el-table-column prop="type" label="调拨类型" min-width="50" align="center"></el-table-column>
                </el-table>
            </template>
        </el-container>
    </div>
    <script>
        var vm = new Vue({
            el: '#app',
            data: function () {
                return {
                    //正式上线时，请切换为正式地址，末尾不许带 /
                    //测试：http://**************
                    //正式：http://************* 
                    // thisInterfaceUrl: "http://**************",
                    thisLonding: true,
                    list: [],
                    pagedDataList: [],
                    tableHeight: null,
                    ShowID: null,
                    total: 0,
                }
            },
            //生命周期钩子进行数据初始化和异步请求
            created () {
                this.getStyleSheetInfo();
            },
            async mounted () {
                this.getStyleSheetInfo();
                this.beginShowing()
            },
            methods: {
                beginShowing () {
                    this.$nextTick(function () {
                        // 文档显示区域的高度 - 
                        if (this.$refs.tableBox) {
                            this.tableHeight = window.innerHeight - 20;
                            this.$refs.tableBox.doLayout()
                        }
                    })
                },
                //从后端获取完整的数据列表
                async getStyleSheetInfo () {
                    var me = this;
                    let searchURL = window.location.search;
                    searchURL = searchURL.substring(1, searchURL.length);
                    this.ShowID = searchURL.split("&")[0].split("=")[1];
                    me.thisLonding = true;
                    let targetUrl = `/api/inventory/allocate/GetH5PageData?ShowID=` + this.ShowID;
                    let parm = {};
                    $.ajax({
                        url: targetUrl,
                        type: 'GET',
                        dataType: 'json',
                        data: parm,
                        success: function (response) {
                            me.list = response.data.list;
                            me.total = response.data.list.length;
                        },
                        error: function (xhr, textStatus, errorThrown) {
                            console.log('Error: ', errorThrown);
                        }
                    });
                }
            }
        });
    </script>
</body>

</html>