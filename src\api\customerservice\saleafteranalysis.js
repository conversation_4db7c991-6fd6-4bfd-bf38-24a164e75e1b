import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/SaleAfterAnalysis/`
//获取售后分析查询分页
export const GetSaleAfterList = (params, config = {}) => {return request.get(apiPrefix + 'GetSaleAfterList', { params: params, ...config })}
//获取售后分析数据 ResonLevel1曲线加饼图
export const GetSaleAfterResonLevel1AnalysisResponse = (params, config = {}) => {return request.get(apiPrefix + 'GetSaleAfterResonLevel1AnalysisResponse', { params: params, ...config })}
// 获取售后分析数据 ResonLevel2曲线 点击ResonLevel1饼图触发
export const GetSaleAfterResonLevel2AnalysisResponse = (params, config = {}) => {return request.get(apiPrefix + 'GetSaleAfterResonLevel2AnalysisResponse', { params: params, ...config })}
//导出售后分析列表
export const ExportDataAsync = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'ExportDataAsync',  params, config)}

//获取退款排行榜
export const getTuiKuanData = (params, config = {}) => {return request.get(apiPrefix + 'GetTuiKuanDataAsync',  { params: params, ...config })}

//获取售后分析汇总趋势图
export const querySaleAfterAnalysisAsync = (params, config = {}) => {return request.get(apiPrefix + 'QuerySaleAfterAnalysisAsync',  { params: params, ...config })}
