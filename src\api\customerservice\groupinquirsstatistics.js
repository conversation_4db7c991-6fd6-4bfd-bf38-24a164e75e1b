import request from '@/utils/request'
const GroupInquirsStatisticsPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/GroupInquirsStatistics/`
 export const getGroupInquirsStatisticsList = (params, config = {}) => {return request.post(GroupInquirsStatisticsPrefix + 'GetGroupInquirsStatisticsList', params,config)}
 export const getInquirsStatisticsList = (params, config = {}) => {return request.post(GroupInquirsStatisticsPrefix + 'GetInquirsStatisticsList', params,config )}
 export const getInquirsStatisticsByShopList = (params, config = {}) => {return request.get(GroupInquirsStatisticsPrefix + 'GetInquirsStatisticsByShopList', { params: params, ...config })}
 export const getShopInquirsStatisticsList = (params, config = {}) => {return request.get(GroupInquirsStatisticsPrefix + 'GetShopInquirsStatisticsList', { params: params, ...config })}
 export const getGroupInquirsStatisticsListMap = (params, config = {}) => {return request.get(GroupInquirsStatisticsPrefix + 'GetGroupInquirsStatisticsListMap', { params: params, ...config })}
 export const getShopInquirsStatisticsListMap = (params, config = {}) => {return request.get(GroupInquirsStatisticsPrefix + 'GetShopInquirsStatisticsListMap', { params: params, ...config })}
 export const getInquirsStatisticsListMap = (params, config = {}) => {return request.get(GroupInquirsStatisticsPrefix + 'GetInquirsStatisticsListMap', { params: params, ...config })}
 export const exportGroupInquirsStatisticsList = (params, config = { responseType: 'blob' }) => { return request.post(GroupInquirsStatisticsPrefix + 'ExportGroupInquirsStatisticsList', params, config) }
 export const exportShopInquirsStatisticsList = (params, config = { responseType: 'blob' }) => { return request.post(GroupInquirsStatisticsPrefix + 'ExportShopInquirsStatisticsList', params, config) }
 export const exportInquirsStatisticsList = (params, config = { responseType: 'blob' }) => { return request.post(GroupInquirsStatisticsPrefix + 'ExportInquirsStatisticsList', params, config) }
 
 




 