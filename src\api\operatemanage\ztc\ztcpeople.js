import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/ztcpeople/`

export const getPageList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPageList', { params, ...config })
}

export const importZTCPeopleAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportZTCPeopleAsync', params, config)
}
export const exportdata =(params, config = {responseType: 'blob'}) => {
    return request.get(apiPrefix + 'ExportdataList', { params, ...config })
}
