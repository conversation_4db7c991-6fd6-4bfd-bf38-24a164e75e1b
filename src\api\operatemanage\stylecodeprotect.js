import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/StyleCodeProtect/`

export const GetStyleCodeProtectPageList = (params, config) => { return request.post(apiPrefix + 'GetStyleCodeProtectPageList', params, config) }
export const ExportStyleCodeProtecteList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportStyleCodeProtecteList', params, config) }

export const GetStyleCodeProtectAlonePageList = (params, config) => { return request.post(apiPrefix + 'GetStyleCodeProtectAlonePageList', params, config) }
export const GetStyleCodeProtectAloneLogPageList = (params, config) => { return request.post(apiPrefix + 'GetStyleCodeProtectAloneLogPageList', params, config) }
export const InsertStyleCodeProtectAlone = (params, config) => { return request.post(apiPrefix + 'InsertStyleCodeProtectAlone', params, config) }
export const DtlStyleCodeProtectAlone = (params, config) => { return request.post(apiPrefix + 'DtlStyleCodeProtectAlone', params, config) }


export const GetStyleCodeProtectGroupPageList = (params, config) => { return request.post(apiPrefix + 'GetStyleCodeProtectGroupPageList', params, config) }
export const InsertStyleCodeProtectGroup = (params, config) => { return request.post(apiPrefix + 'InsertStyleCodeProtectGroup', params, config) }
export const ImportStyleCodeProtectGroup = (params, config) => { return request.post(apiPrefix + 'ImportStyleCodeProtectGroup', params, config) }
export const EndStyleCodeProtectGroup = (params, config) => { return request.post(apiPrefix + 'EndStyleCodeProtectGroup', params, config) }