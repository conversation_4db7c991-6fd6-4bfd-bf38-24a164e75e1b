import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/abnormal/`

export const editAbnormalInventory =(params,config) =>{return request.post(apiPrefix + 'EditAbnormalInventoryAsync',params,config)}
export const getLastUpdateTime = (params, config = {}) => {return request.get(apiPrefix + 'GetLastUpdateTimeAsync', { params: params, ...config })}
export const queryAbnormalInventory = (params, config = {}) => {return request.get(apiPrefix + 'QueryAbnormalInventoryAsync', { params: params, ...config })}
export const queryAbnormalInventoryDetail = (params, config = {}) => {return request.get(apiPrefix + 'QueryAbnormalInventoryDetailAsync', { params: params, ...config })}
export const queryAbnormalInventoryRecord =(params,config) =>{return request.get(apiPrefix + 'QueryAbnormalInventoryRecordAsync', { params: params, ...config })}

export const getAllAbnormalCheckErrorOderNo =(params,config) =>{return request.get(apiPrefix + 'GetAllAbnormalCheckErrorOderNoAsync', { params: params, ...config })}
export const getAllAbnormalCheckErrorGoodsCode =(params,config) =>{return request.get(apiPrefix + 'GetAllAbnormalCheckErrorGoodsCodeAsync', { params: params, ...config })}
export const getAllAbnormalReasonRate =(params,config) =>{return request.post(apiPrefix + 'GetAllAbnormalReasonRateAsync',params,config)}
export const pageAbnormalInventory =(params,config) =>{return request.post(apiPrefix + 'PageAbnormalInventoryAsync',params,config)}
export const pageWarehouseGoods =(params,config) =>{return request.get(apiPrefix + 'PageWarehouseGoodsAsync',{ params: params, ...config })}
export const queryAbnormalGeneralNow =(params,config) =>{return request.get(apiPrefix + 'QueryAbnormalGeneralNowAsync',{ params: params, ...config })}
export const pageAbnormalGeneral =(params,config) =>{return request.get(apiPrefix + 'PageAbnormalGeneralAsync',{ params: params, ...config })}
export const pageAbnormalMonthGeneral =(params,config) =>{return request.get(apiPrefix + 'PageAbnormalGeneralMonthAsync',{ params: params, ...config })}
export const queryFStockRate =(params,config) =>{return request.get(apiPrefix + 'QueryFStockRateAsync',{ params: params, ...config })}
export const queryFStockGoodsCode =(params,config) =>{return request.get(apiPrefix + 'QueryFStockGoodsCodeAsync',{ params: params, ...config })}
export const queryAbnormalGeneralAnalysis =(params,config) =>{return request.get(apiPrefix + 'QueryAbnormalGeneralAnalysis',{ params: params, ...config })}
export const queryAbnormalGeneralMonthAnalysis =(params,config) =>{return request.get(apiPrefix + 'QueryAbnormalGeneralMonthAnalysis',{ params: params, ...config })}

export const queryAbnormalOderDetail =(params,config={}) =>{return request.post(apiPrefix + 'QueryAbnormalOderDetailAsync',  params,config )}
export const importAbnormal = (params, config = {}) => {return request.post(apiPrefix + 'ImportAbnormalAsync', params, config)}
export const importWarehouseGoodsCode = (params, config = {}) => {return request.post(apiPrefix + 'ImportWarehouseGoodsCodeAsync', params, config)}
export const exportAbnormalInventory = (params, config = {responseType: 'blob'}) => { return request.get(apiPrefix + 'ExportAbnormalInventoryAsync',  { params: params, ...config })}



//首页展示缺货编码
export const getHomeStockout = (params, config = {}) => { return request.get(apiPrefix + 'GetHomeStockoutAsync',  { params: params, ...config })}

//首页展示库存分析趋势
export const getInventoryAnalyse = (params, config = {}) => { return request.get(apiPrefix + 'GetInventoryAnalyseAsync',  { params: params, ...config })}

//获取库存分析数据
export const getInventoryAnalyseList = (params, config = {}) => { return request.get(apiPrefix + 'GetInventoryAnalyseListAsync',  { params: params, ...config })}

//获取库存分析数据
export const getInventoryAnalyseChart = (params, config = {}) => { return request.get(apiPrefix + 'GetInventoryAnalyseChartAsync',  { params: params, ...config })}

//获取库存分析数据
export const GetLastUpdateTimeAbnormalInventory = (params, config = {}) => { return request.get(apiPrefix + 'GetLastUpdateTimeAbnormalInventoryAsync',  { params: params, ...config })}

//实时压单月汇总导出
export const exportAbnormalGeneralMonth = (params, config = {responseType: 'blob'}) => { return request.get(apiPrefix + 'ExportAbnormalGeneralMonthAsync',  { params: params, ...config })}
		
//获取对应仓库库存数
export const getAllAbnormalsellStock =(params,config={}) =>{return request.post(apiPrefix + 'GetAllAbnormalsellStockAsync',  params,config )}

//缺货板块（新）
export const pageOutOfStockSectionReport =(params,config) =>{return request.post(apiPrefix + 'PageOutOfStockSectionReport',params,config)}
export const getOutOfStockSectionReportLastTimeAsync = (params, config = {}) => {return request.get(apiPrefix + 'GetOutOfStockSectionReportLastTimeAsync', { params: params, ...config })}
export const getLastUpdateTimeOutOfStockSectionReportAsync = (params, config = {}) => {return request.get(apiPrefix + 'GetLastUpdateTimeOutOfStockSectionReportAsync', { params: params, ...config })}
export const queryTimeOutOfStockSectionReportDtlAsync = (params, config = {}) => {return request.get(apiPrefix + 'QueryTimeOutOfStockSectionReportDtlAsync', { params: params, ...config })}
export const getAllTimeOutOfStockSectionReportSellStock =(params,config={}) =>{return request.post(apiPrefix + 'GetAllTimeOutOfStockSectionReportSellStock',  params,config )}
//缺货时间记录
export const pageOutOfStockTimeLog =(params,config={}) =>{return request.post(apiPrefix + 'PageOutOfStockTimeLog',  params,config )}
export const queryTimeOutOfStockSectionReportOderDetailAsync =(params,config={}) =>{return request.post(apiPrefix + 'QueryTimeOutOfStockSectionReportOderDetailAsync',  params,config )}
export const getAllOutOfStockSectionReportOderNoAsync =(params,config) =>{return request.get(apiPrefix + 'GetAllOutOfStockSectionReportOderNoAsync', { params: params, ...config })}
export const getAllOutOfStockSectionReportGoodsCodeAsync =(params,config) =>{return request.get(apiPrefix + 'GetAllOutOfStockSectionReportGoodsCodeAsync', { params: params, ...config })}
export const editOutOfStockSectionReportFollowRemarkAsync = (params,config) =>{return request.post(apiPrefix + 'EditOutOfStockSectionReportFollowRemarkAsync',params,config)}
export const getOutOfStockSectionReportFollowRemarksAsync = (params,config) =>{return request.post(apiPrefix + 'GetOutOfStockSectionReportFollowRemarksAsync',params,config)}
export const getAllOutOfStockSectionReportReasonRate = (params,config) =>{return request.post(apiPrefix + 'GetAllOutOfStockSectionReportReasonRate',params,config)}
export const getBrandDeptList =(params,config) =>{return request.post(apiPrefix + 'GetBrandDeptList',params,config)}
export const exportOutOfStockSection =(params,config = { responseType: "blob" }) =>{return request.post(apiPrefix + 'ExportOutOfStockSection',params,config)}

//GetGoodsInventoryListAsync 库存管理
export const getGoodsInventoryListAsync = (params, config = {}) => {return request.post(apiPrefix + 'GetGoodsInventoryListAsync',  params,config )}

//缺货订单同步采购单跟进 PurchaseOrderFollowUpAsync
export const purchaseOrderFollowUpAsync = (params, config = {}) => {return request.post(apiPrefix + 'PurchaseOrderFollowUpAsync',  params,config )}