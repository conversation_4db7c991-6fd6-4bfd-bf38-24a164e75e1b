export function tonumfuc(innerText, title) {
    if (!innerText) {
        return innerText;
    }

    function formatNumber(num) {
        if (!num) {
            return 0;
        }
        if (num < 100 && num >= 0) {
            if (num === Math.floor(num)) {
                return num;
            } else {
                return num.toFixed(2);
            }
        }
        else if (num >= 100 && num < 1000) {
            // return num.toLocaleString('en-US');
            return num.toFixed(0);
        }
        else if (num >= 1000) {

            return Number(num.toFixed(0)).toLocaleString('en-US');
        }
        else if (num < 0 && num > -100) {
            // 转换为整数
            // let intValue = Math.floor(num);

            // if (num < 0 && num !== intValue) {
            // intValue += 1;
            // }
            // // 添加千位符
            // return intValue.toLocaleString('en-US');
            if (num === Math.floor(num)) {
                return num;
            } else {
                return num.toFixed(2);
            }

        } else if (num <= -100 && -1000 < num) {
            // 转换为整数
            // let intValue = Math.floor(num);

            // if (num < 0 && num !== intValue) {
            // intValue += 1;
            // }
            // // 添加千位符
            // return intValue.toLocaleString('en-US');
            // return Math.floor(num);
            return num.toFixed(0);


        } else if (num <= -1000) {
            return Number(num.toFixed(0)).toLocaleString('en-US');
        }
        // else if(num>=100&&num<1000) {
        //     return Math.floor(num);
        // }
        else {
            return num;
        }
    }

    function containsChinese(str) {
        const regex = /[\u4e00-\u9fa5]/;
        return regex.test(str);
    }

    function formatPercentage(str) {
        if (containsChinese(str) || str.indexOf('/') > -1 || str.indexOf('|') > -1) {
            return str;
        }

        if (str.endsWith('%')) {

            const value = parseFloat(str.slice(0, -1));

            if (value >= 100) {
                return Math.floor(value) + '%';
            } else if (value < 0) {
                if (value > -100 && value < 0) {
                    if (value === Math.floor(value)) {
                        return value + '%';
                    } else {
                        return value.toFixed(2) + '%';
                    }
                } else {
                    return Math.floor(value) + '%';
                }
            } else {
                // return value.toFixed(2) + '%';
                if (value === Math.floor(value)) {
                    return value + '%';
                } else {
                    return value.toFixed(2) + '%';
                }
            }
        } else {
            return str;
        }
    }
    function isAllDigits(str) {
        // return /^\d+$/.test(str);
        // return /^\d+(\.\d+)?$/.test(str);
        return /^-?\d+(\.\d+)?$/.test(str);
    }
    function isNumberType(value) {
        return typeof value === 'number';
    }

    // if(!isAllDigits(innerText)){
    //     return innerText;
    // }
    if (typeof innerText === 'string' && innerText.indexOf('%') > -1) {
        return formatPercentage(innerText);
    };
    let titleArr = ['号', '码', '批次', 'ID', '年月日', '工价', '报价', "年月", "月日",
        "日期", "时间", "电话", 'Id', 'ID', 'id', '平台', '方式', '内部订单', '通用系数', '名称', "内部单号", "原值", "新值",'备注',
        "SKU"];
    // if(titleArr.includes(title)){
    //     return innerText;
    // };
    let isinclude = false;
    titleArr.map((item) => {
        if (title && title.indexOf(item) > -1) {
            isinclude = true;
        }
    })
    if (isinclude) {
        return innerText;
    }

    if ((isAllDigits(innerText) || isNumberType(innerText))) {
        let numa = innerText ? Number(innerText) : 0;
        let numm = typeof innerText === 'number' ? innerText : numa;
        return formatNumber(numm);
    }
    return innerText;
};
