import axios from 'axios'
import store from '@/store'
import { toLogout } from '@/router'
import Vue from 'vue'
import { refresh } from '@/api/admin/auth'
import { log } from 'mathjs'
// import JSONbig from 'json-bigint'

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
axios.defaults.headers['Cache-Control'] = 'no-cache'
axios.defaults.headers['Pragma'] = 'no-cache'
// axios.defaults.transformResponse = [(data)=> {
//   try {
//     // 如果转换成功则返回转换的数据结果
//     return JSONbig.parse(data)
//   } catch (err) {
//     // 如果转换失败，则包装为统一数据格式并返回
//     return {
//       data
//     }
//   }
// }
// ]
const requestAxios = axios.create({
  baseURL: '', // url = base url + request url
  timeout: 600000 // 请求延时
})

// 拦截请求
requestAxios.interceptors.request.use(
  config => {
    if (config.api?.auth === false) {
      return config
    }
    const token = store.getters.token
    if (token) {
      config.headers.Authorization = 'Bearer ' + token
    }
    return config
  },
  err => {
    return Promise.reject(err)
  }
)
// 拦截响应
requestAxios.interceptors.response.use(
  response => {
    const { config, data, headers } = response
    //console.log('sajkdhsajkdhasjkdha',response);
    if(response && data.authCode == 401){
      if (data.msg) {
          Vue.prototype.$message.error({
            dangerouslyUseHTMLString: true,
            message: data.msg,
            duration: config.api?.msgDuration ?? 3000
        })
      }
      toLogout()
    }
    
    if (config.url.toLowerCase().indexOf("/export") > -1) {
      if (data.type == 'application/json') {
        const reader = new FileReader()
      
        reader.onload = function () {
          const { msg, success } = JSON.parse(reader.result)
          if (success && msg) {
            Vue.prototype.$message.success({
              message: msg,
              duration: config.api?.msgDuration ?? 3000
            })
          } else if (msg) {
              Vue.prototype.$message.error({
                dangerouslyUseHTMLString: true,
                message: msg,
                duration: config.api?.msgDuration ?? 3000
            })
          }
        }
        reader.readAsText(data)
      }
      else {
        return { headers: headers, data: data }
      }
    }
    else {
      if (!data.success && !config.api?.noErrorMsg && data.msg) {
        const duration = config.api?.msgDuration >= 0 ? config.api?.msgDuration : 3000
          Vue.prototype.$message.error({
            dangerouslyUseHTMLString: true,
            message: data.msg,
            duration: duration
        })
      }
      return data
    }
  },
  async error => {
    const res = { success: false, code: 0, msg: '' }
    if (error?.response) {
      if (error.config._request) {
        return res
      }

      const { config, data, status } = error.response
      if (_.isNumber(status)) {
        res.code = status
      }
      if (_.isPlainObject(data)) {
        _.merge(res, data)
      } else if (_.isString(data)) {
        res.msg = data || error.response.statusText
      }

      // 刷新令牌
      const { code } = res
      if (code === 401) {
        const resRefresh = await refresh({ token: store.getters.token })
        if (resRefresh.code === 1) {
          store.commit('user/setToken', resRefresh.data.token)
          error.config._request = true
          return requestAxios.request(error.config)
        } else {
          toLogout()
          return res
        }
      }
      var _err = Promise.reject(error);
      console.log('resdata._err', _err)
      // 错误消息
      if (!config.api?.noErrorMsg && res.msg) {
        const duration = config.api?.msgDuration >= 0 ? config.api?.msgDuration : 3000
        Vue.prototype.$message.error({
          dangerouslyUseHTMLString: true,
          message: res.msg,
          duration: duration
        })
      }
    }
    return res
  }
)

export default requestAxios
