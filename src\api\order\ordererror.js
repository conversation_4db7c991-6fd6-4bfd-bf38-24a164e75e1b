import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/ordererror/`

//异常分类 导入
export const importOrderErrorType = (params,config ={}) =>{
    return request.post(apiPrefix + 'ImportOrderErrorTypeAsync', params, config)
}

//异常分类 分页查询
export const pageOrderErrorType = (params,config ={}) =>{
    return request.post(apiPrefix+'PageOrderErrorTypeAsync', params, config)
}

//异常分类 导出
export const exportOrderErrorType =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportOrderErrorTypeAsync',{params: params, ...config})
}

//异常看板查询
export const getOrderErrorReport = (params,config ={}) =>{
    return request.post(apiPrefix+'GetOrderErrorReportAsync', params, config)
}

//异常看板订单明细
export const getOrderErrorDetail = (params,config ={}) =>{
    return request.post(apiPrefix+'GetOrderErrorDetailAsync', params, config)
}

//异常看板获取订单号
export const getOrderDetailOrderNos = (params,config ={}) =>{
    return request.post(apiPrefix+'GetOrderDetailOrderNosAsync', params, config)
}


//异常看板 明细 导出
export const exportOrderDetail =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportOrderDetailAsync',{params: params, ...config})
}

//获取部门
export const getDutyDept = (params,config ={}) =>{
    return request.post(apiPrefix+'GetDutyDeptAsync', params, config)
}

//变更订单明细异常类型
export const changeOrderDetailErrorType = (params,config ={}) =>{
    return request.post(apiPrefix+'ChangeOrderDetailErrorTypeAsync', params, config)
}


//获取最后更新时间
export const getLastUpdateTime = (params,config ={}) =>{
    return request.post(apiPrefix+'GetLastUpdateTimeAsync', params, config)
}

//获取耗时图表
export const getOrderErrorChartsTime = (params,config ={}) =>{
    return request.post(apiPrefix+'GetOrderErrorChartsTimeAsync', params, config)
}

//获取销售额图表
export const getOrderErrorChartsAmount = (params,config ={}) =>{
    return request.post(apiPrefix+'GetOrderErrorChartsAmountAsync', params, config)
}

//获取订单数图表
export const getOrderErrorChartsOrderCount = (params,config ={}) =>{
    return request.post(apiPrefix+'GetOrderErrorChartsOrderCountAsync', params, config)
}

//异常看板订单查询
export const getOrderErrorOrder = (params,config ={}) =>{
    return request.post(apiPrefix+'GetOrderErrorOrderAsync', params, config)
}
//异常看板单个订单明细
export const getOrderErrorOrderDetail = (params,config ={}) =>{
    return request.post(apiPrefix+'GetOrderErrorOrderDetailAsync', params, config)
}


//异常类型 修改部门
export const changeOrderErrorTypeDutyDept = (params,config ={}) =>{
    return request.post(apiPrefix+'ChangeOrderErrorTypeDutyDeptAsync', params, config)
}

//异常类型 获取部门信息
export const getDutyDeptInfo = (params,config ={}) =>{ return request.post(apiPrefix+'GetDutyDeptInfoAsync', params, config)}

//异常类型 修改行
export const changeOrderErrorType = (params,config ={}) =>{ return request.post(apiPrefix+'ChangeOrderErrorTypeAsync', params, config)}

//修改单个异常订单的erp操作状态，是否处理
export const changeIsDealByErp = (params,config ={}) =>{return request.post(apiPrefix+'ChangeIsDealByErpAsync', params, config)}

export const queryOrderWarnByGroupId = (params, config = {}) => {return request.get(apiPrefix + `QueryOrderWarnByGroupIdAsync`, { params, ...config })}
export const pageOrderWarn = (params, config = {}) => {return request.get(apiPrefix + `PageOrderWarnAsync`, { params, ...config })}
export const queryOrderWarnGoods = (orderNoInner, config = {}) => {return request.get(apiPrefix + `QueryOrderWarnGoodsAsync?orderNoInner=`+orderNoInner)}
export const pageOrderSaleOutStock = (params, config = {}) => {return request.get(apiPrefix + `PageOrderSaleOutStockAsync`, { params, ...config })}
export const queryOrderSaleOutStockSum = (params, config = {}) => {return request.get(apiPrefix + `QueryOrderSaleOutStockSumAsync`, { params, ...config })}
export const getAllOrderWarnOrderNoInner = (params, config = {}) => {return request.get(apiPrefix + `GetAllOrderWarnOrderNoInnerAsync`, { params, ...config })}

export const importOrderSaleOutStock = (params,config ={}) =>{return request.post(apiPrefix + 'ImportOrderSaleOutStockAsync', params, config)}


