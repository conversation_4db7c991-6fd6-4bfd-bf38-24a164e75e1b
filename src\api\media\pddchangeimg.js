import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Media}/pddchangeimg/`

//新增或更改
export const addOrUpdateShootingVideoTaskAsync = (params, config = {}) => { return request.post(apiPrefix + 'AddOrUpdateShootingVideoTaskAsync', params, config) }
//列表查询
export const pageShootingViewTaskAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageShootingViewTaskAsync', params, config) }

//获取任务附件
export const getShootingTaskFliesAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetShootingTaskFliesAsync', { params: params, ...config }) }


// 
export const unPickShootingTaskAsync = (params, config = {}) => { return request.get(apiPrefix + 'unPickShootingTaskAsync', { params: params, ...config }) }


// 
export const pickShootingTaskAsync = (params, config = {}) => { return request.get(apiPrefix + 'pickShootingTaskAsync', { params: params, ...config }) }

// 
export const unConfrimShootingTaskAsync = (params, config = {}) => { return request.get(apiPrefix + 'UnConfrimShootingTaskAsync', { params: params, ...config }) }

export const shootUrgencyCilckAsync = (params, config = {}) => { return request.get(apiPrefix + 'ShootUrgencyTaskAsync', { params: params, ...config }) }

// 
export const confrimShootingTaskAsync = (params, config = {}) => { return request.get(apiPrefix + 'ConfrimShootingTaskAsync', { params: params, ...config }) }


//上传成果文件
export const uploadSuccessAttachment = (params, config = {}) => { return request.post(apiPrefix + 'UploadSuccessAttachment', params, config) }


//获取成果文件
export const getUploadSuccessAttachment = (params, config = {}) => { return request.get(apiPrefix + 'getUploadSuccessAttachment', { params: params, ...config }) }



// 获取成果文件评分
export const getUploadSuccessAttachmentScore = (params, config = {}) => { return request.get(apiPrefix + 'GetUploadSuccessAttachmentScore', { params: params, ...config }) }


//提交成果文件评分
export const subSuccessAttachmentScore = (params, config = {}) => { return request.get(apiPrefix + 'SubSuccessAttachmentScore', { params: params, ...config }) }

//提交打包请求
export const packagingCompressionTask = (params, config = {}) => { return request.get(apiPrefix + 'PackagingCompressionTask', { params: params, ...config }) }

//获取打包状态
export const getPackagingCompressionTaskStatus = (params, config = {}) => { return request.get(apiPrefix + 'GetPackagingCompressionTaskStatus', { params: params, ...config }) }


//获取打包进度状态
export const pageShootingPackageViewTaskAsync = (params, config = {}) => { return request.get(apiPrefix + 'PageShootingPackageViewTaskAsync', { params: params, ...config }) }


//任务批量完成
export const taskOverActionsAsync = (params, config = {}) => { return request.post(apiPrefix + 'TaskOverActionsAsync', params, config) }


//任务批量终止
export const taskShopActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'TaskShopActionAsync', params, config) }

//任务批量重新启动
export const taskRestartActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'TaskRestartActionAsync', params, config) }

//删除到回收站操作
export const deleteShootingTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteShootingTaskActionAsync', params, config) }

//回收站删除
export const deleteTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'DeleteTaskActionAsync', params, config) }

//回收站删除
export const endShootingTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'EndShootingTaskActionAsync', params, config) }

// 
export const caclShootingTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'CaclShootingTaskActionAsync', params, config) }

// 
export const unCaclShootingTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'UnCaclShootingTaskActionAsync', params, config) }

//保存详情备注
export const saveShootingTaskMarkAsync = (params, config = {}) => { return request.post(apiPrefix + 'SaveShootingTaskMarkAsync', params, config) }
//删除详情备注
export const delShootingTaskMarkAsync = (params, config = {}) => { return request.get(apiPrefix + 'DelShootingTaskMarkAsync', { params: params, ...config }) }

//获取备注信息
export const getShootingTaskMarkAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetShootingTaskMarkAsync', { params: params, ...config }) }


//删除附件
export const delShootingTploadFileTaskAsync = (params, config = {}) => { return request.get(apiPrefix + 'DelShootingTploadFileTaskAsync', { params: params, ...config }) }



//详情页
export const getPageDetailImgInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetPageDetailImgInfo', { params: params, ...config }) }

//总之重启
export const endRestartActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'EndRestartActionAsync', params, config) }


// 获取当前用户角色
export const getUserRoleList = (params, config = {}) => { return request.post(apiPrefix + 'GetUserRoleList', params, config) }

//获取人员下拉
export const getShootingViewPersonAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetShootingViewPersonAsync', { params: params, ...config }) }

//校验任务是否完成
export const checkShootingTaskAction = (params, config = {}) => { return request.get(apiPrefix + 'CheckShootingTaskAction', { params: params, ...config }) }

//导出
export const exportShootingTaskReport =
  (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportChangeImgeTaskReport', { params: params, ...config }) }


//下单发货
export const getShootingTaskOrderListById = (params, config = {}) => { return request.get(apiPrefix + 'GetShootingTaskOrderListById', { params: params, ...config }) }
export const shootingTaskAddOrderSaveCheckTaskIds = (params, config = {}) => { return request.post(apiPrefix + 'ShootingTaskAddOrderSaveCheckTaskIds', params, config) }
export const getCityAllData = (params, config = {}) => { return request.get(apiPrefix + 'GetCityAllData', { params: params, ...config }) }
export const shootingTaskAddOrderSave = (params, config = {}) => { return request.post(apiPrefix + 'ShootingTaskAddOrderSave', params, config) }


//统计1
export const getOperationGroupStatistics = (params, config = {}) => { return request.post(apiPrefix + 'GetOperationGroupStatistics', params, config) }
//获取统计2
export const shootingTaskStatistics = (params, config = {}) => { return request.post(apiPrefix + 'ShootingTaskStatistics', params, config) }

//获取统计3
export const modelingTaskDetailStatistics = (params, config = {}) => { return request.post(apiPrefix + 'ModelingTaskDetailStatistics', params, config) }

//获取统计2
export const modelingTaskStatistics = (params, config = {}) => { return request.post(apiPrefix + 'ModelingTaskStatistics', params, config) }

//获取统计2
export const getFpStrpStatistics = (params, config = {}) => { return request.post(apiPrefix + 'GetFpStrpStatistics', params, config) }

//获取统计2
export const getOperationGroupStatisticsForPerson = (params, config = {}) => { return request.post(apiPrefix + 'GetOperationGroupStatisticsForPerson', params, config) }

//获取统计2
export const getUploadSuccessAttachmentNew = (params, config = {}) => { return request.get(apiPrefix + 'getUploadSuccessAttachmentNew', { params: params, ...config }) }
// 
export const signShootingTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'SignShootingTaskActionAsync', params, config) }

// 
export const unSignShootingTaskActionAsync = (params, config = {}) => { return request.post(apiPrefix + 'UnSignShootingTaskActionAsync', params, config) }



export const taskStatusStatistics = (params, config = {}) => { return request.post(apiPrefix + 'TaskStatusStatistics', params, config) }
export const taskByPlatformStatistics = (params, config = {}) => { return request.post(apiPrefix + 'TaskByPlatformStatistics', params, config) }
export const taskByGroupStatisticsMain = (params, config = {}) => { return request.post(apiPrefix + 'TaskByGroupStatisticsMain', params, config) }
export const taskByGroupStatistics = (params, config = {}) => { return request.post(apiPrefix + 'TaskByGroupStatistics', params, config) }
export const taskByFpTaskStatisticsMain = (params, config = {}) => { return request.post(apiPrefix + 'TaskByFpTaskStatisticsMain', params, config) }
export const taskByFpTaskStatistics = (params, config = {}) => { return request.post(apiPrefix + 'TaskByFpTaskStatistics', params, config) }
export const taskByFpTaskStatisticsDetail = (params, config = {}) => { return request.post(apiPrefix + 'TaskByFpTaskStatisticsDetail', params, config) }

export const taskByFpTaskForUserStatistics = (params, config = {}) => { return request.post(apiPrefix + 'TaskByFpTaskForUserStatistics', params, config) }
export const taskByPersionStatistics = (params, config = {}) => { return request.post(apiPrefix + 'TaskByPersionStatistics', params, config) }
export const taskRegionalComparisonStatistics = (params, config = {}) => { return request.post(apiPrefix + 'TaskRegionalComparisonStatistics', params, config) }
export const taskTypeStatistics = (params, config = {}) => { return request.post(apiPrefix + 'TaskTypeStatistics', params, config) }
export const taskModelingStatisticsMain = (params, config = {}) => { return request.post(apiPrefix + 'TaskModelingStatisticsMain', params, config) }
export const tasScoreStatisticsMain = (params, config = {}) => { return request.post(apiPrefix + 'TasScoreStatisticsMain', params, config) }
export const tasScoreForUserStatistics = (params, config = {}) => { return request.post(apiPrefix + 'TasScoreForUserStatistics', params, config) }

//统计
//任务汇总统计
export const getTaskTotalStatistics= (params, config = {}) => {  return request.post(apiPrefix + 'GetTaskTotalStatistics', params, config)  }
//分配人员详情
export const getShootingTaskStatistics= (params, config = {}) => {  return request.post(apiPrefix + 'GetShootingTaskStatistics', params, config)  }
//分配信息统计
export const getFpStrpStatisticsForPerson = (params, config = {}) => { return request.post(apiPrefix + 'GetFpStrpStatisticsForPerson', params, config) }
//每日完成统计
export const getTaskDailyCompletionStatistics= (params, config = {}) => {return request.post(apiPrefix + 'GetTaskDailyCompletionStatistics', params, config)  }


