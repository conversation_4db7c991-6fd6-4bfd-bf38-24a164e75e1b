import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_VerifyOrder}/verifyOrder/`

//导入-库存数据
export const importInventoryAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportInventoryAsync', params, config)
}

//导入-订单数据
export const importOrderAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportOrderAsync', params, config)
}

//分页获取商品编码销售数据
export const pageGetStatData = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetStatData', params, config)
}

//获取指定系列、商品 趋势图 系列编码、商品编码、开始创建、截止创建
export const getStatTrendChart = (params, config = {}) => {
    return request.post(apiPrefix + 'GetStatTrendChart', params, config)
}

//获取合计数据 趋势图 开始创建、截止创建  
export const getSummaryStatTrendChart = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSummaryStatTrendChart', params, config)
}

//分页获取商品编码数据
export const pageGetVoOrder = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetVoOrder', params, config)
}

//分页获取调拨记录
export const pageGetAllotItem = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetAllotItem', params, config)
}

//分页操作日志 
export const pageGetLogs = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetLogs', params, config)
}

//指定订单商品编码发起调拨  
export const orderGoodsCodeAllot = (params, config = {}) => {
    return request.post(apiPrefix + 'OrderGoodsCodeAllot', params, config)
}

//获取审单订单统计数据 PageGetDayStat
export const pageGetDayStat = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetDayStatForMobilize', params, config)
}

//GetMobilize 获取全局订单量设置
export const getMobilize = (params, config = {}) => {
    return request.post(apiPrefix + 'GetMobilize', params, config)
}

//指定订单商品编码发起调拨 SetMobilize
export const setMobilize = (params, config = {}) => {
    return request.post(apiPrefix + 'SetMobilize', params, config)
}

//GetWareHouses 获取全局仓库设置
export const getWareHouses = (params, config = {}) => {
    return request.post(apiPrefix + 'GetWareHouses', params, config)
}

//SetWareHouses 设置全局仓库设置
export const setWareHouses = (params, config = {}) => {
    return request.post(apiPrefix + 'SetWareHouses', params, config)
}

//汇总数据导出 ExportStatData
export const exportStatData = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportStatData', params, config)
}

//ReleaseOrders 放单
export const releaseOrders = (params, config = {}) => {
    return request.post(apiPrefix + 'ReleaseOrders', params, config)
}

// 获取sku的放单历史 /GetReleaseRecord
export const getReleaseRecord = (params, config = {}) => {
    return request.post(apiPrefix + 'GetReleaseRecord', params, config)
}

//获取二次组团数据 GetSecondaryMobilize
export const getSecondaryMobilize = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSecondaryMobilize', params, config)
}

//二次组团设置 SetSecondaryMobilize
export const setSecondaryMobilize = (params, config = {}) => {
    return request.post(apiPrefix + 'SetSecondaryMobilize', params, config)
}

//获取通道GetThorougnfares
export const getThorougnfares = (params, config = {}) => {
    return request.post(apiPrefix + 'GetThorougnfares', params, config)
}

//获取SKU的二次组团放单的历史
export const getSecMbzReleaseRecord = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSecMbzReleaseRecord', params, config)
}
