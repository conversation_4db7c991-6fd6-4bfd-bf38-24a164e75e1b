import request from '@/utils/request'
const GroupPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/Distributor/`

//分销商查询
export const getDistributor = (params, config = {}) => request.get(GroupPrefix + 'GetDistributor', { params: params, ...config })

//分销商日志接口
export const getDistributorLog = (params, config = {}) => request.get(GroupPrefix + 'GetDistributorLog', { params: params, ...config })

//分销商单个信息查询接口
export const GetDistributorInfo = (id) => request.get(GroupPrefix + `GetDistributorInfo?drp_co_id=${id}`)

//分销商更新推荐人
export const updateDistributor = (params, config = {}) => request.post(GroupPrefix + 'UpdateDistributor', params, config)

//查询分销商,最多查询100个
export const queryAllDistributorTop100 = (params, config = {}) => request.get(GroupPrefix + 'QueryAllDistributorTop100', {params: params, ...config })

//推荐人汇总查询接口
export const getDistributorTCSummary = (params, config = {}) => request.get(GroupPrefix + 'GetDistributorTCSummary', { params: params, ...config })

//推荐人汇总计算接口
export const calculateSummary = (params, config = {}) => request.post(GroupPrefix + 'CalculateSummary', params, config)

//推荐人明细查询接口
export const getDistributorTCDetail = (params, config = {}) => request.post(GroupPrefix + 'GetDistributorTCDetail', params, config)

//根据推荐人id获取被推荐人信息
export const queryAllDistributorByTJR = (params, config = {}) => request.get(GroupPrefix + 'QueryAllDistributorByTJR', { params: params, ...config })

//分页查询外部推荐人
export const getOutSiders = (params, config = {}) => request.get(GroupPrefix + 'GetOutSiders', { params: params, ...config })

//获取外部推荐人下拉数据
export const queryAllDistributorOutSiders = (params, config = {}) => request.get(GroupPrefix + 'QueryAllDistributorOutSiders', { params: params, ...config })

//添加或更新外部推荐人
export const addOrUpdateOutSiders = (params, config = {}) => request.post(GroupPrefix + 'AddOrUpdateOutSiders', params, config)

//分页查询外部推荐人操作日期接口
export const getOutSidersLog = (params, config = {}) => request.get(GroupPrefix + 'GetOutSidersLog', { params: params, ...config })