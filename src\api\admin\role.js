import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API}/role/`

// 角色管理
export const getRole = (params, config = {}) => {
  return request.get(apiPrefix + 'get', { params: params, ...config })
}
// export const getRoleListPage = (params, config = {}) => {
//   params = params || {}
//   return request.post(apiPrefix + 'getpage', params, config)
// }
export const addRole = (params, config = {}) => {
  return request.post(apiPrefix + 'add', params, config)
}
export const editRole = (params, config = {}) => {
  return request.put(apiPrefix + 'update', params, config)
}
export const removeRole = (params, config = {}) => {
  return request.delete(apiPrefix + 'softdelete', { params: params, ...config })
}
export const batchRemoveRole = (params, config = {}) => {
  return request.put(apiPrefix + 'BatchsoftDelete', params, config)
}
//根据角色ID获取到该角色的用户信息
export const GetRoleUsers = (params, config = {}) => {
  return request.get(apiPrefix + 'GetRoleUsers', { params: params, ...config })
}
//移除用户
export const DeleteRoleUsers = (params, config = {}) => {
  return request.delete(apiPrefix + 'DeleteRoleUsers', { params: params, ...config })
}
//新增用户
export const addUser = (params, config = {}) => {
  return request.post(apiPrefix + 'add', params, config)
}
export const addRoleUser = (params, config = {}) => {
  return request.post(apiPrefix + 'AddRoleUser', params, config)
}
//获取菜单下拉数据
export const GetMenuDropDownList = () => {
  return request.get(apiPrefix + 'GetMenuDropDownList')
}
//获取树形角色数据
export const getRoleListPage = () => {
  return request.post(apiPrefix + 'getpage')
}

//删除校验
export const validateSoftDeleteAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'ValidateSoftDeleteAsync', { params: params, ...config })
}

//获取树形角色数据
export const getRoleUserListPage = () => {
  return request.post(apiPrefix + 'GetRoleTrees')
}

//获取树形角色数据-除开本身角色
export const getRoleListPageByFilter = (params, config = {}) => {
  return request.get(apiPrefix + 'GetRoleTreesByFilter',{ params: params, ...config })
}



//获取树形角色数据
export const GetCaiGouRoleTreesAsync = () => {
  return request.post(apiPrefix + 'GetCaiGouRoleTreesAsync')
}