import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/purchaseData/`

//基础价格变动数据
export const getChangedPurchaseDataAsync = (params, config = {}) => {
    return request.get(apiPrefix + 'GetChangedPurchaseDataAsync', {params, ...config})
}

//导出价格变动数据
export const exportChangedPurchaseDataAsync = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportChangedPurchaseDataAsync', params, config)
}

//编辑成本-核价数据
export const editCostPriceAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'EditCostPriceAsync', params, config)
}

//删除指定区间
export const deleteSplit = (id, config = {}) => {
    return request.post(apiPrefix + 'DeleteSplit/'+id, null, config)
}

//领取核价
export const receiveSplit = (params, config = {}) => {
    return request.post(apiPrefix + 'ReceiveSplit', params, config)
}

//调货操作
export const allot = (params, config = {}) => {
    return request.post(apiPrefix + 'Allot', params, config)
}

//审批领取核价信息
export const approveSplit = (params, config = {}) => {
    return request.post(apiPrefix + 'ApproveSplit', params, config)
}

//领取核价信息分页
export const getSplitsAsync = (params, config = {}) => {
    return request.get(apiPrefix + 'GetSplitsAsync', {params, ...config})
}

//采购涨价降价绩效统计明细
export const getPurchaseDataSplitAmount = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPurchaseDataSplitAmount', {params, ...config})
}

//采购涨价降价绩效统计明细导出
export const exportPurchaseDataSplitAmount = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportPurchaseDataSplitAmount', params, config)
}

//采购涨价降价绩效统计汇总
export const getPurchaseDataSplitAmountStat = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPurchaseDataSplitAmountStat', {params, ...config})
}

//采购涨价降价绩效统计汇总导出
export const exportPurchaseDataSplitAmountStat = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportPurchaseDataSplitAmountStat', params, config)
}

//采购涨价降价绩效统计明细
export const getPurchaseDataSplitAmount_Person = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPurchaseDataSplitAmount_Person', {params, ...config})
}

//采购涨降价绩效统计明细导出
export const exportPurchaseDataSplitAmount_Person = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportPurchaseDataSplitAmount_Person', params, config)
}