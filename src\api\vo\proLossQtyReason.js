import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_VerifyOrder}/ProLossQtyReason/`

//GetColumns 掉量分析
export const getColumns = (params, config = {}) => {
    return request.post(apiPrefix + 'GetColumns', params, config)
}

//查询数据 PageGetData
export const pageGetData = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetData', params, config)
}

//数据导出 ExportData
export const exportData = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportData', params, config)
}

//趋势图获取 getTrendChart
export const getTrendChart = (params, config = {}) => {
    return request.post(apiPrefix + 'GetTrendChart', params, config)
}

//设置掉量原因 CreateProLossQtyReason
export const createProLossQtyReason = (params, config = {}) => {
    return request.post(apiPrefix + 'CreateProLossQtyReason', params, config)
}
