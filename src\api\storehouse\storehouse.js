import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/storehouse/`

export const pageAbnormalInventory = (params, config) => { return request.post(apiPrefix + 'PageAbnormalInventoryAsync', params, config) }
export const queryAbnormalInventoryDetail = (params, config = {}) => { return request.get(apiPrefix + 'QueryAbnormalInventoryDetailAsync', { params: params, ...config }) }

export const pageStorePosition = (params, config = {}) => { return request.get(apiPrefix + 'PageStorePositionAsync', { params: params, ...config }) }
export const importStorePosition = (params, config = {}) => { return request.post(apiPrefix + 'ImportStorePositionAsync', params, config) }


//获取仓位分析仓库
export const getWarehouseList = (params, config = {}) => { return request.get(apiPrefix + 'GetWarehouseList', { params: params, ...config }) }
//保存仓位分析仓库
export const SetWarehouseList = (params, config = {}) => { return request.post(apiPrefix + 'SetWarehouseList', params, config) }

export const queryPositionAnalysis = (params, config = {}) => { return request.post(apiPrefix + 'QueryPositionAnalysis', params, config) }
export const showPositionAnalysis = (params, config = {}) => { return request.post(apiPrefix + 'ShowPositionAnalysis', params, config) }
export const importPositionGoodsAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportPositionGoodsAsync', params, config) }

//批量导入仓位数据，拣货日志
export const importPositionGoodsBatchAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportPositionGoodsBatchAsync', params, config) }
//汇总趋势图
export const showSummaryPositionAnalysis = (params, config = {}) => { return request.post(apiPrefix + 'ShowSummaryPositionAnalysis', params, config) }

export const getWarehouses = (params, config = {}) => { return request.get(apiPrefix + 'GetWarehousesAsync', { params, ...config })}



