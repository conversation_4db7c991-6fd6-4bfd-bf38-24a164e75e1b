<template>
 <div id="mainmidle" style="width: 100%; height: 100%;margin: 0">
    <div ref="btns" style="height: 30px">     
      <el-popover placement="right" title="列筛选" trigger="click" width="420">                        
        <el-checkbox-group v-model="checkedColumns" size="mini">
          <el-checkbox v-for="item in checkBoxGroup" :key="item" :label="item" :value="item"></el-checkbox>
        </el-checkbox-group>
        <el-button slot="reference" type="primary" size="small" plain><i class="el-icon-arrow-down el-icon-menu" />列表项展示筛选</el-button>
      </el-popover>
       <el-button-group>
      <el-button
        v-for='item in tableHandles' 
        :key='item.label'
        :size="item.size || size" 
        :type="item.type || type" 
        :icon='item.icon ||""'
         @click="item.handle(that)"
        >{{item.label}}</el-button>
          <slot name="extentbtn"/>
         </el-button-group>
    </div>
<!-- :height="tableHeight" row-key="id"-->
     <el-table class="table-wrapper"
          :data='tableData'
          :height="tableHeight"
          :border  ='isBorder'
          @select='select'
          @select-all='selectAll'
          @sort-change='sortchange'
          @cell-click='cellclick'
          v-loading='loading'
          :tree-props='treeprops'
          :show-summary='showsummary'
          :summary-method="getSummaries"
          :defaultSelections='defaultSelections'
          ref="cesTable">
          <template v-if="hasexpand">
             <slot/>
          </template>
            <el-table-column v-if="isSelection" type="selection" align="center" ></el-table-column>
            <el-table-column v-if="isIndex" type="index" :label="indexLabel" align="center" width="50"></el-table-column> 
             <!-- :key="item.id?item.id: Math.round(Math.random()*6)"            -->
            <template v-for="(item, index) in tableCols">
              <el-table-column v-if="item.istrue&&!(item.display==false)" 
                :key="index"
                :prop="item.prop" 
                :label="item.label" 
                :width="item.width"
                :align="item.align" 
                :sortable="item.sortable"
                :render-header="item.require?renderHeader:null" show-overflow-tooltip >
                  <template slot-scope="scope">
                    <!-- html -->
                    <!-- <span v-if="item.type==='html'" v-html="item.html(scope.row[item.prop])"></span> -->
                    <span v-if="item.type==='html'" v-html="(item.formatter && item.formatter(scope.row))"></span>                  
                    <span v-else-if="item.type==='format'">{{}} </span>
                    <!-- 按钮  :disabled="btn.isDisabled && btn.isDisabled(scope.row)"-->
                    <span v-else-if="item.type==='button'" >
                      <el-button v-for="btn in item.btnList" :key="btn.label"
                        :disabled="btn.isDisabled"
                        :type="btn.type || type" 
                        :size="btn.size || size " 
                        :icon="btn.icon" 
                        @click="btn.handle(that,scope.row)">{{btn.label}}</el-button>
                    </span>
                    <!-- 输入框 -->
                    <el-input v-else-if="item.type==='input'" v-model="scope.row[item.prop]" :size="size || btn.size"
                      :disabled="item.isDisabled && item.isDisabled(scope.row)"
                      @focus="item.focus && item.focus(scope.row)"></el-input>
                    <!-- 下拉框 -->
                    <el-select v-else-if="item.type==='select'" v-model="scope.row[item.prop]" :size="size || btn.size"  :props="item.props"
                      :disabled="item.isDisabled && item.isDisabled(scope.row)" 
                      @change='item.change && item.change(that,scope.row)'>
                        <el-option v-for="op in item.options" :label="op.label" :value="op.value" :key="op.value"></el-option>
                    </el-select>
                    <!-- 单选 -->
                    <el-radio-group v-else-if="item.type==='radio'" v-model="scope.row[item.prop]"
                      :disabled="item.isDisabled && item.isDisabled(scope.row)" :size="size || btn.size" 
                      @change='item.change && item.change(that,scope.row)'>
                        <el-radio v-for="ra in item.radios" :label="ra.value" :key="ra.value">{{ra.label}}</el-radio>
                    </el-radio-group>
                    <!-- 复选框 -->
                    <el-checkbox-group v-else-if="item.type==='checkbox'" v-model="scope.row[item.prop]" 
                      :disabled="item.isDisabled && item.isDisabled(scope.row)" :size="size || btn.size" 
                      @change='item.change && item.change(that,scope.row)'>
                        <el-checkbox v-for="ra in item.checkboxs" :label="ra.value" :key='ra.value'>{{ra.label}}</el-checkbox>
                    </el-checkbox-group>
                    <!-- 评价 -->
                    <el-rate v-else-if="item.type==='rate'" v-model="scope.row[item.prop]"
                      :disabled="item.isDisabled && item.isDisabled(scope.row)" :size="size || btn.size" 
                      @change='item.change && item.change(scope.row)'></el-rate>
                    <!-- 开关 -->
                    <el-switch v-else-if="item.type==='switch'" v-model="scope.row[item.prop]" :size="size || btn.size" 
                      :active-value='item.values&&item.values[0]'
                      :inactive-value='item.values&&item.values[1]'
                      :disabled="item.isDisabled && item.isDisabled(scope.row)"
                      @change='item.change && item.change(scope.row)'></el-switch>
                    <!-- 图像 -->
                    <img v-else-if="item.type==='image'" :src="scope.row[item.prop]" @click="item.handle && item.handle(scope.row)"/>
                    <!-- 滑块 -->
                    <el-slider v-else-if="item.type==='slider'" v-model="scope.row[item.prop]" 
                    :disabled="item.isDisabled && item.isDisabled(scope.row)" :size="size || btn.size" 
                      @change='item.change && item.change(scope.row)'></el-slider>
                    <!-- 默认 -->
                    <span v-else-if="!item.type" 
                      :style="item.itemStyle && item.itemStyle(scope.row)" :size="size || btn.size" 
                      :class="item.itemClass && item.item.itemClass(scope.row)">{{(item.formatter && item.formatter(scope.row)) || scope.row[item.prop]}}</span>
                  </template>
              </el-table-column>
            </template>
    </el-table>
</div>
</template>
<script>

export default {
  props:{
    that: { type: Object, default: this },
    hasexpand:{type:Boolean,default:false},
    // 表格型号：mini,medium,small
    size:{type:String,default:'mini'},
    type:{type:String,default:'primary'},
    isBorder:{type:Boolean,default:true},
    loading:{type:Boolean,default:false},
    tableHandles:{type:Array,default:()=>[]},
    // 表格数据
    tableData:{ type:Array,default:()=>[]},
    // 表格列配置
    tableCols:{ type:Array,default:()=>[]},
    // 是否显示表格复选框
    isSelection:{type:Boolean,default:false},
    defaultSelections:{ type:[Array,Object], default:()=>null},
    // 是否显示表格索引
    isIndex:{type:Boolean,default:false},
    indexLabel: {type:String,default:'#'},
    //排序
    orderby:{type:Object,default:()=>({order:"ascending",name:'id'})},
    filter:{},
    treeprops:{},
    showsummary:{type:Boolean,default:true},
    summaryarry:{ type:Object,default:()=>{}},
  },
  data(){
    return {
      checkBoxGroup: [],
	    checkedColumns: [],
      tableHeight: window.innerHeight  - 440	       
    }
  },
  mounted:function(){
    this.$nextTick(function () {
      let exceptHeight=260;//this.$refs.cesTable.$el.offsetTop;
			// this.$refs.table.$el.offsetTop：表格距离浏览器的高度
      console.log('window.innerHeight',window.innerHeight)
      console.log('this.$refs.cesTable.$el.offsetTop',this.$refs.cesTable.$el.offsetTop)
      //debugger
			if(this.$refs.cesTable.$el){
				this.tableHeight = window.innerHeight - this.$refs.cesTable.$el.offsetTop - exceptHeight;
			}
			// 监听窗口大小变化
		 let self = this;
     setTimeout(() => {
       if(self.$refs.cesTable.$el){
					self.tableHeight = window.innerHeight - self.$refs.cesTable.$el.offsetTop - exceptHeight;
				}
      }, 1000);
			window.onresize = function() {
				if(self.$refs.cesTable.$el){
					self.tableHeight = window.innerHeight - self.$refs.cesTable.$el.offsetTop - exceptHeight;
				}
			}
     console.log('self.tableHeight ',this.tableHeight)

		});
  },
  created() {
	    this.tableCols.forEach((item, index) => {
	        this.checkBoxGroup.push(item.label);
	        this.checkedColumns.push(item.label);
	    })
	    this.checkedColumns = this.checkedColumns
      var key= window.location.origin+window.location.pathname;
	    let UnData = localStorage.getItem(key)
	    UnData = JSON.parse(UnData)
	    if (UnData != null) {
	        this.checkedColumns = this.checkedColumns.filter((item) => {
	            return !UnData.includes(item)
	        })
	    }
	},
  watch: {
     checkedColumns(val,value) {
         var key= window.location.origin+window.location.pathname;
          let arr = this.checkBoxGroup.filter(i => !val.includes(i)); // 未选中
          localStorage.setItem(key, JSON.stringify(arr))
          this.tableCols.filter(i => {
              if (arr.indexOf(i.label) != -1) {
                  i.istrue = false;
              } else {
                  i.istrue = true;
              }
          });
      },
     tableData(val,value) {
       console.log('self.tableHeight ',this.tableHeight)
      },
  },
  methods:{
    // 表格勾选
    select(rows,row){
      this.$emit('select',rows,row);
    },
    // 全选
    selectAll(rows){
      this.$emit('select',rows)
    },
    sortchange(column){
       this.$emit('sortchange',column)
    },
    cellclick(row, column, cell, event){
       this.$emit('cellclick',row, column, cell, event)
    },
    btnHand(hand){
      console.log("hand",hand)
      this.$emit(hand)
    },
    getSummaries1(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '总价';
            return;
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
            sums[index] += ' 元';
          } else {
            sums[index] = 'N/A';
          }
        });

        return sums;
      },
    getSummaries(param) {
      const sums = [];
      if (!this.summaryarry)
         return sums
      var arr = Object.keys(this.summaryarry);
      if (arr.length == 0)
          return sums
			const { columns, data } = param;		
			columns.forEach((column, index) => {
				 if (index === 2) {
				   sums[index] = '合计';
				   return;
				 }
         if (this.summaryarry.hasOwnProperty(column.property+'_sum'))
           	sums[index]=this.summaryarry[column.property+'_sum']
         else sums[index]=''
				});
				return sums
	  },
  },
}
</script>
<style>
.ces-table-require::before{
  content:'*';
  color:red;
}
.table-wrapper {
    width: 100%;
    height: calc(100% - 400px);
    margin: 0;
 }
 .el-table{
   overflow:visible !important;
}

/* .el-table__body-wrapper {
    height: 90%!important;   
  } */
</style>