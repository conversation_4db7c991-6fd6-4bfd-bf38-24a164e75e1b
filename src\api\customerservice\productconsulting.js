import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/productconsulting/`

export const importProductconsulting = (params, config = {}) => {
   
    return request.post(apiPrefix + 'ImportProductconsultingMultipleAsync', params, config)
}
export const getRateList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetConversionRateListAsync', { params: params, ...config })
}
export const getRateDetailList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetConversionRateDetailAsync', { params: params, ...config })
}
export const getConsultingList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetProductConsultingListAsync', { params: params, ...config })
}
export const exportRateList = (params, config = {responseType: 'blob'}) => 
{ return request.get(apiPrefix + 'ExportConversionRateAsync',  { params: params, ...config })}



export const getEmployeeListByProductList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetEmployeeListByProductAsync', { params: params, ...config })
}
export const exportProductConsultDetailList = (params, config = {responseType: 'blob'}) => 
{ return request.get(apiPrefix + 'ExportProductConsultingListAsync',  { params: params, ...config })}