import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/BurstStyleProtect/`

export const GetBurstStyleProtectPageList = (params, config) => { return request.post(apiPrefix + 'GetBurstStyleProtectPageList', params, config) }
export const ImportBurstStyleProtect = (params, config) => { return request.post(apiPrefix + 'ImportBurstStyleProtect', params, config) }
export const OneKeyIsLoss = (params, config) => { return request.post(apiPrefix + 'OneKeyIsLoss', params, config) }
export const ExportBurstStyleProtect = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportBurstStyleProtect', params, config)
}
export const GetBurstStyleProtectStylePageList = (params, config) => { return request.post(apiPrefix + 'GetBurstStyleProtectStylePageList', params, config) }

export const ExportBurstStyleProtectStyle = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportBurstStyleProtectStyle', params, config)
}

//爆款流失删除行
export const deleteBurstStyleProtect = (params, config = {}) => { return request.get(apiPrefix + 'DeleteBurstStyleProtect', { params: params, ...config }) }

//撤销流失爆款为未流失
export const revokeLossBurstStyleProtect = (params, config = {}) => { return request.get(apiPrefix + 'RevokeLossBurstStyleProtect', { params: params, ...config }) }

//爆款保护趋势图
export const getBurstStyleProtectChart = (params, config) => { return request.post(apiPrefix + 'GetBurstStyleProtectChart', params, config) }



//批量转爆款
export const BatchZhuanBaoKuan = (params, config) => { return request.post(apiPrefix + 'BatchZhuanBaoKuan', params, config) }

//批量打黑标
export const BatchDaHeiBiao = (params, config) => { return request.post(apiPrefix + 'BatchDaHeiBiao', params, config) }
//导出爆款保护趋势款
export const ExportBurstStyleProtectTrend = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportBurstStyleProtectTrend', params, config)
}

//拼多多不可上架编码分页查询
export const GetBurstStyleProtectPddNoUpPageList = (params, config) => { return request.post(apiPrefix + 'GetBurstStyleProtectPddNoUpPageList', params, config) }
//导入爆款保护-拼多多不可上架编码
export const ImportBurstStyleProtectPddNoUp = (params, config) => { return request.post(apiPrefix + 'ImportBurstStyleProtectPddNoUp', params, config) }
//拼多多不可上架编码导出
export const ExportBurstStyleProtectPddNoUpList = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportBurstStyleProtectPddNoUpList', params, config)
}

//爆款排查分页查询
export const GetBurstStyleCheckPageList = (params, config) => { return request.post(apiPrefix + 'GetBurstStyleCheckPageList', params, config) }
//爆款排查明细分页查询
export const GetBurstStyleCheckProCodePageList = (params, config) => { return request.post(apiPrefix + 'GetBurstStyleCheckProCodePageList', params, config) }


//导出爆款排查
export const ExportBurstStyleCheckProCodeList = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportBurstStyleCheckProCodeList', params, config)
}

