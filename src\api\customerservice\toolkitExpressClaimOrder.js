import request from "@/utils/request";
const GroupPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/ToolkitExpressClaimOrder/`;

export const GetToolkitExpressClaimOrderStatistics = (params, config = {}) => {
  return request.post(
    GroupPrefix + "GetToolkitExpressClaimOrderStatistics",
    params,
    config
  );
};

//汇总趋势图
export const GetToolkitExpressClaimOrderChart = (params, config = {}) => {
  return request.post(
    GroupPrefix + "GetToolkitExpressClaimOrderChart",
    params,
    config
  );
};

//查询明细数据
export const GetToolkitExpressClaimOrderList = (params, config = {}) => {
  return request.post(
    GroupPrefix + "GetToolkitExpressClaimOrderList",
    params,
    config
  );
};

//导出明细
export const ExportToolkitExpressClaimOrderList = (
  params,
  config = { responseType: "blob" }
) => {
  return request.post(
    GroupPrefix + "ExportToolkitExpressClaimOrderList",
    params,
    config
  );
};

//查询通知设置
export const GetToolkitExpressClaimSetNotice = (params, config = {}) => {
  return request.post(
    GroupPrefix + "GetToolkitExpressClaimSetNotice",
    params,
    config
  );
};

//导入明细数据
export const ImportToolkitExpressClaimOrder = (params, config = {}) => {
  return request.post(
    GroupPrefix + "ImportToolkitExpressClaimOrder",
    params,
    config
  );
};

//日志接口
export const GetToolkitExpressClaimOrderLogList = (params, config = {}) => {
  return request.post(
    GroupPrefix + "GetToolkitExpressClaimOrderLogList",
    params,
    config
  );
};

//取消
export const CancelToolkitExpressClaimNotice = (params, config = {}) => {
  return request.post(
    GroupPrefix + "CancelToolkitExpressClaimNotice",
    params,
    config
  );
};

//上传图片
export const AddToolkitExpressClaimOrderPicture = (params, config = {}) => {
  return request.post(
    GroupPrefix + "AddToolkitExpressClaimOrderPicture",
    params,
    config
  );
};

//删除通知设置
export const DelToolkitExpressClaimSetNotice = (params, config = {}) => {
  return request.post(
    GroupPrefix + "DelToolkitExpressClaimSetNotice",
    params,
    config
  );
};

//保存通知设置
export const SaveToolkitExpressClaimSetNotice = (params, config = {}) => {
  return request.post(
    GroupPrefix + "SaveToolkitExpressClaimSetNotice",
    params,
    config
  );
};

export const GetToolkitExpressClaimNoticeLog = (params, config = {}) => {
    return request.post(
        GroupPrefix + "GetToolkitExpressClaimNoticeLog",
        params,
        config
    );
    }
