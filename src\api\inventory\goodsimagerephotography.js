import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/GoodsImageRephotography/`
  
//分页查询
export const getList = (params,config ={}) =>{
    return request.post(apiPrefix+'GetListAsync', params, config)
}

//增改
export const addOrUpdate = (params, config = {}) => {
    return request.post(apiPrefix + 'AddOrUpdateAsync', params, config)
}

//设置重拍
export const saveRephotography = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveRephotographyAsync', params, config)
}

//删除
export const deleteData = (params, config = {}) => {
    return request.delete(apiPrefix + 'DeleteAsync',{ params, ...config})
}

//删除批量
export const deleteDataBatch = (params, config = {}) => {
    return request.delete(apiPrefix + 'DeleteBatchAsync',{params, ...config})
}

//获取单个
export const getById = (id, config = {}) => {
    return request.get(apiPrefix + `GetByIdAsync?id=${id}`, {}, config)
}



