import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_PddPlatform}/ProductOperationLog/`

//查询
export const getRedEnvelopePercentage = (params, config = {}) => {
    return request.post(apiPrefix + 'GetRedEnvelopePercentage', params, config)
}

//导出
export const exportRedEnvelopePercentage = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportRedEnvelopePercentage', params, config)
}

//导入
export const importRedEnvelopePercentage = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportRedEnvelopePercentage', params, config)
}
