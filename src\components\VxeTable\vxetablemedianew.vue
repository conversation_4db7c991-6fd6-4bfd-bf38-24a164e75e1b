<template>
<div :style="[{ height: (tableHandles != null ? '95%' : '100%') }, { width: '100%' }]" class="vxetablecss">
    <el-button-group>
        <template v-for='item in tableHandles'>
            <el-button v-if="(!item.permission || (item.permission && checkPermission(item.permission)))"
                :key='item.label' :size="item.size || size" :type="item.type || type" :icon='item.icon || ""'
                @click="item.handle(that)">{{ item.label }}</el-button>
        </template>
        <slot name="extentbtn" />
    </el-button-group>
    <vxe-toolbar ref="xToolbar" custom class="vxetoolbar20221212" v-if="showToolbar"> 
          <template #buttons>
            <slot name="tbHeader" />
          </template>
    </vxe-toolbar>
    <vxe-table 
    resizable ref="xTable" :loading="loading" 
    class="vxetable202212161323 mytable-scrollbar20221212"
    v-bind:name="tablekey" 
    stripe  
    :column-config="{resizable: resizable,maxFixedSize: 300, maxFixedSize: 20,
	resizable: true,}"
    keep-source
    :row-style="rowStyleFun"
    :data="tableData"
    :align="align"
    :row-config="{isCurrent: true, isHover: true}" :size="size" 
    :tree-config="treeProp" 
    :sort-config="{sortMethod: customSortMethod}"
    :edit-config="editconfig"
    :edit-rules="validRules"
    :scroll-y="{gt:100}"
    :scroll-x="{gt:100}"
    :row-class-name="redLine"
    :span-method="mergeRowMethod"
    :height="height"
    :border="border" 
    :custom-config="{storage: true,resizable: true, useKey: true, restoreStore:restoreStore,updateStore:updateStore}"
    :id="id"
    :header-cell-class-name="headerCellClassName"
    :show-footer="showsummary"
    :footer-method="footerMethod"
    @cell-dblclick="rowChange"
    @checkbox-all="checkboxall"
    @footer-cell-click="footercellclick"
    @checkbox-change="selectChangeEvent" 
    show-overflow> 
    <vxe-column v-if="hascheck" type="checkbox" width="40"  fixed="left" ></vxe-column>  
        <vxe-column v-if="hasSeq" type="seq" width="40" fixed="left"></vxe-column>
        <template v-for="(col,colIndex) in tableCols">
            <template v-if="(!col.permission || (col.permission && checkPermission(col.permission)))"> 
                <vxetabblemergecolumn v-if="col.merge"   :that="that" :col="col" :colIndex="colIndex"  :size="size" :key=" colIndex"/> 
                <vxecolumn v-else  :that="that" :col="col" :colIndex="colIndex" :size="size" :key="colIndex"/> 
            </template> 
        </template>
        <slot name="right" />
    </vxe-table>
</div>
</template>
<script>
// import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'

    
import vxecolumn from "@/components/VxeTable/vxetablemediacolumn.vue"; 
import vxetabblemergecolumn from "@/components/VxeTable/vxetabblemergecolumn.vue"; 
import { GetVxeTableColumnCacheAsync,SetVxeTableColumnCacheAsync } from '@/api/admin/business'
    export default { 
        components: { vxecolumn ,vxetabblemergecolumn},
        props: {
            editconfig:{ type: Object, default: () => { return {  } } },
            treeProp: { type: Object, default: () => { return {  } } },
            hasSeq: { type: Boolean, default: () => { return true } },
            hascheck: { type: Boolean, default: () => { return false } },
            showToolbar: { type: Boolean, default: () => { return true } },
            // 表格数据
            tableData: { type: Array, default: () => [] },
            // 表格型号：mini,medium,small
            size: { type: String, default: 'mini' },
            type: { type: String, default: 'primary' },
            isBorder: { type: Boolean, default: true }, 
            // 表格列配置
            tableCols: { type: Array, default: () => [] },
            isRemoteSort:{ type: Boolean, default: () => { return true } },
            id:{type:String,default:()=>{ return new Date().valueOf().toString()}},
            that:{type:Object,default:()=>{return null}},
            loading:{type:Boolean,default:()=>{return false;}},
            border:{type:Boolean | Object ,default:()=>{return 'default'}},
            tableHandles: { type: Array, default: () => [] },
            showsummary: { type: Boolean, default: false },
            align: { type: String, default: '' }, //对齐方式
            summaryarry: { type: Object, default: () => { } }, 
            validRules: { type: Object, default: () => { } },//校验列规则
            tablekey: { type: String, default: '' },//表格key
            height: { type: String, default: '100%' },//固定表头作用
        },
        data() {
            return {
                lastSortArgs:{
                    field:"",
                    order:"",
                },
                arrlist:[], 
                summarycolumns: [],
                tablecolumns:[],
                aaaa: ''
            }
        },
        created(){
            this.$nextTick(() => {
              // 手动将表格和工具栏进行关联 
              this.$refs.xTable.connect(this.$refs.xToolbar)
            })
        },
        async mounted(){
            this.$nextTick(() => {
                this.tablecolumns = this.$refs.xTable.getColumns()
                this.arrlist = [];
                this.tableCols.map((item)=>{
                    if(!item.istrue){
                        this.arrlist.push(item.prop)
                    }
                })
            })
          await this.ShowHidenColums(this.arrlist);
        },
    methods: {
        async restoreStore({id, type, storeData}){
               
                let resp=  await GetVxeTableColumnCacheAsync({tableId:id});
                let store =null;
                if(resp && resp.success && resp.data){
                    store = JSON.parse(resp.data);
                }
                if(store.fixedData){
                    this.tableCols.map((item)=>{
                        item.fixed = store.fixedData[item.prop]
                    })
                }

                return store??storeData;

            },
            async updateStore({id, type, storeData}){
                let newobj = {};
                let mergearr = [];

                this.tableCols.map((item)=>{
                    if(item.merge){
                        mergearr.push({
                            name:  item.prop,
                            value: item.cols[0]['prop']
                        });
                    }
                })

                this.$refs.xTable.getColumns().map((item)=>{
                    
                    if(item.type){
                        return;
                    }
                    mergearr.map((itemm)=>{
                        if(item.field === itemm.value){
                            // item.fixed = itemm.value
                            newobj[itemm.name] = item.fixed
                        }
                    })
                    newobj[item.field]  = item.fixed;
                })
                storeData.fixedData = newobj;

                await SetVxeTableColumnCacheAsync({tableId:id,ColumnConfig:JSON.stringify(storeData)});
            },
             //加上红线
        redLine ({ row, rowIndex, $rowIndex }) {
            if (!(row.employeeStatus == 2)) {
                return '';
            } else {
                return 'droprow';
            }
        },
            //校验
            async  validate(){
                const $table = this.$refs.xTable;
                let errMap = await $table.validate().catch(errMap => errMap)
                if (errMap) {
                    return false;
                } else {
                    return true;
                }
            },
            //局部刷新行数据
            reloadRow(row){
                const $table = this.$refs.xTable;
                $table.reloadRow(row, {});
            },
            //行数据是否发送变化
            isUpdateByRow(row){
                const $table = this.$refs.xTable
                return $table.isUpdateByRow(row);
            },
            //行内编辑 
            editRowEvent (row){
                const $table = this.$refs.xTable
                row.editstatus = 1;
                $table.setActiveRow(row)
            }, 
            //取消编辑，还原数据
            cancelRowEvent (row) {
                const $table = this.$refs.xTable;
                // 还原行数据
                $table.clearActived(row);
                row.editstatus = 0;
                $table.revertData(row);
            },
            //清除编辑状态
            clearActivedRowEvent (row) {
              const $table = this.$refs.xTable
              row.editstatus = 0;
              $table.clearActived(row); 
            },
            //导出
            exportData(filename){
                this.$nextTick(() => {
                    this.$refs.xTable.exportData({filename:filename,    sheetName: 'Sheet1',type: 'xlsx' })
                })
    
            },
        
            editClosedEvent(){
                this.$emit('editclosed',true);
            },
            // 通用行合并函数（将相同多列数据合并为一行）
            mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
              const fields = ['samekey']
              const cellValue = row[column.property]
              if (cellValue && fields.includes(column.property)) {
                const prevRow = visibleData[_rowIndex - 1]
                let nextRow = visibleData[_rowIndex + 1]
                if (prevRow && prevRow[column.property] === cellValue) {
                  return { rowspan: 0, colspan: 0 }
                } else {
                  let countRowspan = 1
                  while (nextRow && nextRow[column.property] === cellValue) {
                    nextRow = visibleData[++countRowspan + _rowIndex]
                  }
                  if (countRowspan > 1) {
                    return { rowspan: countRowspan, colspan: 1 }
                  }
                }
              }
            },
            //行切换事件
            rowChange({ newValue, oldValue, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }){
                this.$emit('rowChange', row);
            },
            //批量控制列的显影
           async ShowHidenColums(arrlist){ 
           
                this.$refs.xTable.getTableColumn().collectColumn.forEach(column => {
                    if (arrlist.includes(column.property)) {
                        column.visible = false
                    }else{
                        column.visible = true
                    }
                })
                if (this.$refs.xTable) {
                    this.$refs.xTable.refreshColumn()
                } 
            }, 
            //清空全选
            clearSelection(){ 
                this.$refs.xTable.clearCheckboxRow()
            },
            async checkboxall(){ 
                const records = this.$refs.xTable.getCheckboxRecords()
                this.$emit('checkboxall', records);
            },
            async toggleRowSelection(val){ 
                await this.$refs.xTable.clearCheckboxRow()
                await this.$refs.xTable.setCheckboxRow(val, true)
                console.log("组件内数据",val)
            }, 
            async selectChangeEvent ({ checked }) {    
                const records = this.$refs.xTable.getCheckboxRecords()
                await  this.$emit('selectchangeevent',records); 
            },
            rowStyleFun({ row, rowIndex, $rowIndex }){
                if(row && row.isend==1){ 
                    return { color:'#c9c9c9'};
                } 
            },
            cellStyleFun({ row, rowIndex, column }){
                let rltStyle={};
                let col=column;
                var colArg=this.tableCols.find(x=>x.prop==col.property );
                
                if(colArg ){   
                    if(colArg.type && (colArg.type=="images" ||colArg.type=="image"))
                        rltStyle={
                            ...rltStyle,
                            ...{
                                textAlign:"center"
                            }
                        };

                    if(colArg.align)
                        rltStyle={
                            ...rltStyle,
                            ...{
                                textAlign:colArg.align
                            }
                        };

                }
                return rltStyle;
            },
            customSortMethod({ data, sortList }){               
                if(this.isRemoteSort){
                    if(sortList && sortList.length>0){
                        if(sortList[0].field != this.lastSortArgs.field || sortList[0].order!=this.lastSortArgs.order){
                            this.lastSortArgs={...sortList[0]};
                            this.$emit('sortchange',{
                                order:(this.lastSortArgs.order.indexOf('desc')>-1?'descending':'asc'),
                                prop:this.lastSortArgs.field
                            });
                        }
                    }                   
                }else{
                    this.$refs.xTable.sort(sortList[0].field, sortList[0].order)
                }
            },
            headerCellClassName ({ column, columnIndex }) {
                let className='';
                var col=this.tableCols.find(x=>x.prop==column.property );
               
                if (col && col.align ) {
                    className=' '+`vxetableheadercell-${column.align}-20221216`;
                }else if(col && col.type && (col.type=="images" || col.type=="image")){
                    className=' '+`vxetableheadercell-center-20221216`;
                }

                return className;
            },
            footerMethod ({ columns, data }) {
                const sums = [];
                if (!this.summaryarry)
                    return sums
                var arr = Object.keys(this.summaryarry);
                if (arr.length == 0)
                    return sums
                //const { columns, data } = param;
                var hashj = false;
                columns.forEach((column, index) => {
                    if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
                        var sum = this.summaryarry[column.property + '_sum'];
                        if (sum == null) return;
                        else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
                        else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2)
                        else sums[index] = sum.toFixed(2)
                    }
                    else sums[index] = ''
                });
              /*  if (this.summarycolumns.length == 0) {
                    this.summarycolumns = columns;
                    this.initsummaryEvent();
                } */
                return [sums]
            },
            initsummaryEvent() {
                let self = this;
                let table;
                if(this.tablekey) table = document.querySelector('[name='+this.tablekey+'] .vxe-table--footer-wrapper>table');
                else table = document.querySelectorAll('.vxe-table--footer-wrapper>table');
                if(table?.length>0) table = table[0] 
                this.$nextTick(() => {
                    self.summarycolumns.forEach((column, index) => {
                        if (column.property) {
                            table.rows[0].cells[index].style.cursor= "pointer";
                            table.rows[0].cells[index].style.color= "red";
                        }
                    })
                })
            },

            footercellclick({ items, $rowIndex, column, columnIndex, $columnIndex, $event }){
                let self = this;
                var  col = findcol(self.tableCols, column.property);
                if (col && col.summaryEvent)   
                    self.$emit('summaryClick', column.property)
                    
                function findcol(cols, property) {
                    let column;
                    for (var i = 0; i < cols.length; i++) {
                        var c = cols[i];
                        if (column) break
                        else if (c.prop && c.prop.toLowerCase() == property.toLowerCase()) {
                            column = c;
                            break
                        }
                        else if (c.cols && c.cols.length > 0) column = findcol(c.cols, property)
                    }
                    return column
                }
            }
        }
    }
</script>

<style lang="scss" scoped> 
        .vxe-table--render-default.border--default .vxe-table--header-wrapper
        {
            background-color: #fafbff;
        }
        /*斑马线颜色*/
        .vxe-table--render-default .vxe-body--row.row--stripe {
            background-color: #fafbff;
        }
        .vxe-table--render-default .vxe-body--row.row--current {
            background-color: #e5ecf5;
        } 
        /*滚动条整体部分*/
        .mytable-scrollbar20221212 ::-webkit-scrollbar {
          width: 10px;
          height: 10px;
        }
        /*滚动条的轨道*/
        .mytable-scrollbar20221212 ::-webkit-scrollbar-track {
          background-color: #FFFFFF;
        }
        /*滚动条里面的小方块，能向上向下移动*/
        .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
          background-color: #bfbfbf;
          border-radius: 5px;
          border: 1px solid #F1F1F1;
          box-shadow: inset 0 0 6px rgba(0,0,0,.3);
        }
       .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
          background-color: #A8A8A8;
        }
        .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
          background-color: #787878;
        }
        /*边角，即两个滚动条的交汇处*/
       .mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
          background-color: #FFFFFF;
        }

        // 图片大小
        .mytable-scrollbar20221212  .images20221212{
          max-width: 150px;max-height: 150px;
          width:40px !important;
          height:40px  !important;
        }

        // 图片张数标记
        .mytable-scrollbar20221212 .badgeimage20221212 .el-badge__content.is-fixed{
            top:10px;
        }

        /*  工具箱位置 员工设置 */
        .vxetoolbar20221212{
            position:absolute ;
            top: 10px;
            right: 5px;
            // padding-top:0;
            padding-bottom:0;
            z-index: 999;
            background-color: rgb(255 255 255 / 0%);
        }

        .vxetableheadercell-left-20221216
        {
            text-align: left;
        }

        .vxetableheadercell-center-20221216
        {
            text-align: center;
        }

        .vxetableheadercell-right-20221216
        {
            text-align: right;
        }
        .vxe-icon-ellipsis-h:hover{
            color: #409EFF;
            margin-left: 2px;
            background-color: #F1F1F1;
        }
        .vxe-icon-ellipsis-h{
            color: #999; 
            font-size: 15px;
        }
        .vxe-icon-file-txt:hover{
            color: #409EFF;
            margin-left: 2px;
            background-color: #F1F1F1;
            font-weight: 600;
        }
        .vxe-icon-file-txt{
            color: #999; 
            font-size: 15px;
        }
        .vxetablecss{
            margin: 0;
        }
        ::v-deep span.vxe-cell--item {
            cursor: pointer !important;
        }

        ::v-deep .droprow td {
    color: rgb(250, 9, 9);
    position: relative;
}

::v-deep .droprow ::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 0.1px;
    background-color: rgb(250, 9, 9);
    transform: translateY(-50%);
}
</style>