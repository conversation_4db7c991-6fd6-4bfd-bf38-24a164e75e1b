import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/sharedaccount/`

//分页查询共享账号
export const getSharedAccountPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSharedAccountPageList', params, config)
}

//新增修改【共享账号】
export const saveSharedAccount = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveSharedAccount', params, config)
}

//查询详情
export const getSharedAccountById = (id, config = {}) => {
    return request.get(apiPrefix + `GetSharedAccountById?sharedAccountId=${id}`, { params: {}, ...config })
}

//删除【共享账号】
export const deleteSharedAccount = (id, config = {}) => {
    return request.get(apiPrefix + `DeleteSharedAccount?sharedAccountId=${id}`, { params: {}, ...config })
}

//申请【共享账号】
export const applySharedAccount = (id, config = {}) => {
    return request.get(apiPrefix + `ApplySharedAccount?sharedAccountId=${id}`, { params: {}, ...config })
}

//下线【共享账号】
export const downSharedAccount = (id, config = {}) => {
    return request.get(apiPrefix + `DownSharedAccount?sharedAccountId=${id}`, { params: {}, ...config })
}

//分页查询共享账号申请记录
export const getSharedAccountApplyRecordPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSharedAccountApplyRecordPageList', params, config)
}