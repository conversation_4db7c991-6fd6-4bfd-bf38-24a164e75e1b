import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Profit}/purchasepostwages/`


// 采购薪资统计
export const getPurchaseWagesComputeAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseWagesComputeAsync', params, config)
}

// 采购薪资统计
export const getPurchaseWagesComputeGroupAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseWagesComputeGroupAsync', params, config)
}

// 趋势图
export const getPurchaseWagesAnalysisAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseWagesAnalysisAsync', params, config)
}

// 采购岗位工价
export const getPurchasePostWagesAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchasePostWagesAsync', params, config)
}

// 采购岗位工价详情信息
export const getPurchasePositionsAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchasePositionsAsync', params, config)
}

// 采购个人薪资
export const getPurchasePostUserWagesAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchasePostUserWagesAsync', params, config)
}

// 采购个人薪资详情
export const getPurchasePostUserWagesDetailAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchasePostUserWagesDetailAsync', params, config)
}

// 添加or修改岗位薪资
export const addOrUpdPurchasePostWagesAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'AddOrUpdPurchasePostWagesAsync', params, config)
}

// 添加or修改个人薪资
export const addOrUpdPurchasePostUserWagesAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'AddOrUpdPurchasePostUserWagesAsync', params, config)
}

// 操作个人薪资
export const opearPurchasePostUserWagesAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'OpearPurchasePostUserWagesAsync', params, config)
}

// 岗位信息
export const getPurchaseWagesCalPositions = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseWagesCalPositions', params, config)
}

//根据岗位获取采购薪资人员
export const getUserByPositionAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetUserByPositionAsync', params, config)
}

//根据岗位获取采购薪资人员(修改版)
export const getUserByPositionByCurUserAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetUserByPositionByCurUserAsync', params, config)
}

// 操作岗位薪资
export const opearPurchasePostWagesAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'OpearPurchasePostWagesAsync', params, config)
}

// 计算工资
export const handComputeWagesAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'HandComputeWagesAsync', params, config)
}

// 导出采购薪资统计
export const exportPurchaseWagesComputeAsync = (params,config ={responseType: 'blob'}) => {
    return request.post(apiPrefix + 'ExportPurchaseWagesComputeAsync', params, config)
}

// 导出采购岗位工价
export const exportPurchasePostWagesAsync = (params,config ={responseType: 'blob'}) => {
    return request.post(apiPrefix + 'ExportPurchasePostWagesAsync', params, config)
}

// 导出采购个人薪资
export const exportPurchasePostUserWagesAsync = (params,config ={responseType: 'blob'}) => {
    return request.post(apiPrefix + 'ExportPurchasePostUserWagesAsync', params, config)
}

//一键计算

export const calPurchasePostWages = (params, config = {}) => {
    return request.post(apiPrefix + 'CalPurchasePostWages', params, config)
}