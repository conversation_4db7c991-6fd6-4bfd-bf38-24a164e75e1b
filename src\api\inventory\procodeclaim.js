import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/procodeclaim/`

//获取认领系列编码列表
export const getProCodeClaimListAsync = (params,config ={}) =>{ 
    return request.post(apiPrefix + 'GetProCodeClaimListAsync', params, config)
}

//获取认领系列编码日志
export const getProCodeClaimLogListAsync = (params,config ={}) =>{
    return request.post(apiPrefix+'GetProCodeClaimLogListAsync', params, config)
}

//认领系列编码
export const addProCodeLogAsync = (params,config ={}) =>{
    return request.post(apiPrefix+'AddProCodeLogAsync',params, config)
}