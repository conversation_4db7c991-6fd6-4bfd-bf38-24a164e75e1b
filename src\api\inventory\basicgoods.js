import request from '@/utils/request'

const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/BasicGoods/`

//分页查询
export const getList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetListAsync', params, config)
}

//分页查询
export const getListForScan = (params, config = {}) => {
    return request.post(apiPrefix + 'GetListAsync', params, config)
}

//获取普通商品资料自定义查询条件
export const getGoodsCustomizeQuery = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGoodsCustomizeQuery', params, config)
}

//保存普通商品资料自定义查询条件
export const saveGoodsCustomizeQuery = (params, config = {}) => {
  return request.post(apiPrefix + 'SaveGoodsCustomizeQuery', params, config)
}

//分页获取普通商品资料(自定义查询)
export const getCustomizeConfig = (params, config = {}) => {
  return request.post(apiPrefix + 'GetCustomizeConfig', params, config)
}

//获取自定义查询条件配置数据
export const getGoodsListByCustomizeQuery = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGoodsListByCustomizeQuery', params, config)
}

//远程搜索
export const getListByStyleCode = (params, config = {}) => {
    return request.get(apiPrefix + 'GetListByStyleCodeAsync', { params, ...config })
}

//导入
export const importData = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportDataAsync', params, config)
}
//分页查询 sql方式
export const getListSql = (params, config = {}) => {
    return request.post(apiPrefix + 'GetListSqlAsync', params, config)
}

//获取系列编码增长率
export const queryTbProCodeSimilarityGrowAnalysis = (params, config = {}) => {
    return request.get(apiPrefix + 'QueryTbProCodeSimilarityGrowAnalysisAsync', { params: params, ...config })
}
//根据商品编码获取成本价变更记录
export const GetCostPriceListByCodeAsync = (params, config = {}) => {
    return request.get(apiPrefix + 'GetCostPriceListByCodeAsync', { params: params, ...config })
}

//采购涨价降价分页
export const getCostPriceList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetCostPriceListAsync', { params: params, ...config })
}

//采购涨价降价小组汇总
export const GetCostPriceListByBrandSum = (params, config = {}) => {
    return request.get(apiPrefix + 'GetCostPriceListByBrandSumAsync', { params: params, ...config })
}

//采购涨价降价小组汇总
export const GetCostPriceListByGoodsCodeSumAsync = (params, config = {}) => {
    return request.get(apiPrefix + 'GetCostPriceListByGoodsCodeSumAsync', { params: params, ...config })
}

//核价信息
export const GetCostPriceByOne = (params, config = {}) => {
    return request.get(apiPrefix + 'GetCostPriceByOneAsync', { params: params, ...config })
}

//领取数据
export const updateCostPriceBrand = (params, config = {}) => {
    return request.post(apiPrefix + 'UpdateCostPriceBrandAsync', params, config)
}

//核价组操作
export const BatchAddCostPrice = (params, config = {}) => {
    return request.post(apiPrefix + 'BatchAddCostPriceAsync', params, config)
}

//导出
export const exportCostPriceListAsync = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportCostPriceListAsync', params, config)
}

//导出
export const exportCostPriceListByBrandSumAsync = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportCostPriceListByBrandSumAsync', params, config)
}

//导出
export const exportCostPriceListByGoodsCodeSumAsync = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportCostPriceListByGoodsCodeSumAsync', params, config)
}

//是否调拨
export const updateCostPriceState = (params, config = {}) => {
    return request.post(apiPrefix + 'UpdateCostPriceStateAsync', params, config)
}

export const goodsCostChgDetail = (params, config = {}) => {
    return request.get(apiPrefix + 'GoodsCostChgDetailAsync', { params: params, ...config })
}

//采购涨价降价小组汇总
export const getGoodsCostChgInfoReceive = (params, config = {}) => {
    return request.get(apiPrefix + 'GetGoodsCostChgInfoReceiveAsync', { params: params, ...config })
}

// 审批领取核价信息
export const approveCostPriceBrand = (params, config = {}) => {
    return request.post(apiPrefix + 'ApproveCostPriceBrandAsync', params, config)
}



//获取所有仓库
export const getTbWarehouseList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetTbWarehouseList', { params: params, ...config })
}

//根据仓库获取库位
export const getTbWarePositionList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetTbWarePositionList', { params: params, ...config })
}

//根据仓库获取库位
export const deleteGoodsDocRecordCg = (params, config = {}) => {
    return request.get(apiPrefix + 'DeleteGoodsDocRecordCg', { params: params, ...config })
}
//查询采购商品资料库-导出
export const exportGoodsDocRecordCgListGoods = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportGoodsDocRecordCgListGoods', params, config)
}

// 分页查询采购商品资料库-PC
export const pageGoodsDocRecordCgList = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGoodsDocRecordCgList', params, config)
}

// 保存采购商品资料
export const saveGoodsDocRecordCg = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveGoodsDocRecordCg', params, config)
}

//采购商品资料库-查询商品资料及供应商
export const getGoodsDocCgProvidersDto = (params, config = {}) => {
    return request.get(apiPrefix + 'GetGoodsDocCgProvidersDto', { params: params, ...config })
}

// 商品历史采购价列表
export const pageGoodsPriceList = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGoodsPriceList', params, config)
}

// 分页查询采购商品资料库-产品拍摄更新日志
export const pageGoodsDocRecordCgChangeLogList = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGoodsDocRecordCgChangeLogList', params, config)
}

// 分页查询采购商品资料库-产品拍摄更新日志
export const getPurchaseNewPlan2ByGoodsCode = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseNewPlan2ByGoodsCode?goodsCode=' + params.goodsCode, params, config)
}

// 删除变更数据
export const delCostPriceState = (params, config = {}) => {
    return request.post(apiPrefix + 'DelCostPriceState', params, config)
}

// 操作日志
export const getGoodsCostChgInfoLogAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGoodsCostChgInfoLogAsync', params, config)
}

//获取采购商品资料库编码抓取设置表
export const getGoodsDocRecordCgSet = (params, config = {}) => {
    return request.get(apiPrefix + 'GetGoodsDocRecordCgSet', { params: params, ...config })
}

// 保存采购商品资料库编码抓取设置表
export const saveGoodsDocRecordCgSet = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveGoodsDocRecordCgSet', params, config)
}


// 采购商品资料库-获取待拍已拍备注
export const getGoodsDocRecordCgOtherRemarkListByParnetId = (params, config = {}) => {
    return request.get(apiPrefix + 'GetGoodsDocRecordCgOtherRemarkListByParnetId', { params: params, ...config })
}

// 采购商品资料库-批量编辑商品资料
export const batchUpdateGoodsDocRecordCg = (params, config = {}) => {
  return request.post(apiPrefix + 'BatchUpdateGoodsDocRecordCg', params, config)
}

// 采购商品资料库-仓库仓位变更信息
export const pageGetChangeLogs = (params, config = {}) => {
  return request.post(apiPrefix + 'PageGetChangeLogs', params, config)
}

// 最晚更新时间
export const getCostPriceLastUpdateTime = (params, config = {}) => {
  return request.get(apiPrefix + 'GetCostPriceLastUpdateTime', { params: params, ...config })
}

//包材资料分页查询
export const getGoodsDocRecordCgBcPageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGoodsDocRecordCgBcPageList', params, config)
}

//保存包材资料
export const saveGoodsDocRecordCgBc = (params, config = {}) => {
  return request.post(apiPrefix + 'SaveGoodsDocRecordCgBc', params, config)
}

//查询单个包材资料
export const getGoodsDocRecordCgBcById = (params, config = {}) => {
  return request.get(apiPrefix + 'GetGoodsDocRecordCgBcById', { params: params, ...config })
}

//获取首个商品资料库By包材资料Id
export const getGoodsDocRecordCgByBcId = (params, config = {}) => {
  return request.get(apiPrefix + 'GetGoodsDocRecordCgByBcId', { params: params, ...config })
}

//使用次数
export const getGoodsDocRecordCgListByBcId = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGoodsDocRecordCgListByBcId', params, config )
}

//删除前校验是否被使用
export const deleteGoodsDocRecordCgBcCheck = (params, config = {}) => {
  return request.post(apiPrefix + 'DeleteGoodsDocRecordCgBcCheck', params, config )
}

//删除包材资料
export const deleteGoodsDocRecordCgBc = (params, config = {}) => {
  return request.post(apiPrefix + 'DeleteGoodsDocRecordCgBc', params, config )
}

//包材资料-获取普通商品资料
export const getGoodListByName = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGoodListByName', params, config )
}

//商品资料库-保存待拍备注
export const saveVedioOrImgRemarks = (params, config = {}) => {
  return request.get(apiPrefix + 'SaveVedioOrImgRemarks', { params: params, ...config })
}

//商品资料库-超级删除
export const deleteGoodsDocRecordCgUnrestricted = (params, config = {}) => {
  return request.post(apiPrefix + 'DeleteGoodsDocRecordCgUnrestricted', params, config )
}

//商品资料库-获取采购单
export const getPurchaseOrderList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetPurchaseOrderList?goodsCode=' + params.goodsCode, params, config)
}

//商品资料库-同步重量
export const syncGoodsDocRecordCgWeight = (params, config = {}) => {
  return request.post(apiPrefix + 'SyncGoodsDocRecordCgWeight', params, config )
}

//商品资料-分页查询（外部）
export const getGoodsSearchExternalPage = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGoodsSearchExternalPage', params, config )
}

//商品资料-获取仓库列表
export const getWareList = (params, config = {}) => {
  return request.get(apiPrefix + 'GetWareList', { params: params, ...config })
}

//商品资料-保存仓库类型（是否外仓）
export const setWarehouseIsExt = (params, config = {}) => {
  return request.post(apiPrefix + 'SetWarehouseIsExt', params, config )
}

//商品资料-获取商品各仓库存
export const getGoodsStockList = (params, config = {}) => {
  return request.get(apiPrefix + 'GetGoodsStockList', { params: params, ...config })
}

//商品资料-获取商品各仓库存
export const getBrandNameList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetBrandNameList', { params: params, ...config })
}


export const changeBrandAndJstSync = (params, config = {}) => {
  return request.post(apiPrefix + 'ChangeBrandAndJstSync', params, config )
}

//商品资料-获取商品品牌记录
export const getBrandChangeLog = (params, config = {}) => {
  return request.post(apiPrefix + 'GetBrandChangeLog', params,config )
}

//商品资料-获取商品聚水潭品牌记录
export const getBrandChangeSyncLog = (params, config = {}) => {
  return request.post(apiPrefix + 'GetBrandChangeSyncLog', params,config )
}

//编码下架-汇总分页查询
export const getGoodsBanPage = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGoodsBanPage', params,config )
}

//编码下架-明细分页查询
export const getGoodsBanDetailPage = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGoodsBanDetailPage', params,config )
}

//编码下架-明细导出
export const exportGoodsBanDetail = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'ExportGoodsBanDetail', params, config)
}

//编码下架-明细删除
export const confirmBanGoodsCode = (params, config = {}) => {
  return request.post(apiPrefix + 'ConfirmBanGoodsCode', params,config )
}

//编码下架-明细获取编码下架明细日志
export const getGoodsBanDetailLog = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGoodsBanDetailLog', params,config )
}

//获取编码下架通知日志
export const getGoodsBanNoticeLog = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGoodsBanNoticeLog', params,config )
}

//导出编码下架通知日志
export const exportGoodsBanNoticeData = (params, config = {}) => {
  return request.post(apiPrefix + 'ExportGoodsBanNoticeData', params, config)
}

//编码下架-分页获取通知下架
export const getGoodsBanNoticePage = (params, config = {}) => { return request.post(apiPrefix + 'GetGoodsBanNoticePage', params,config) }
//编码下架-批量修改通知下架状态
export const batchEditGoodsBanNoticeStatus = (params, config = {}) => { return request.post(apiPrefix + 'BatchEditGoodsBanNoticeStatus', params,config) }
//编码下架-通知下架明细导出
export const exportGoodsBanNotice = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportGoodsBanNotice', params, config) }
//编码下架-获取品牌设置数据
export const getBrandSetData = (params, config = {}) => { return request.post(apiPrefix + 'GetBrandSetData', params,config) }
//编码下架-保存品牌设置数据
export const saveBrandSetDate = (params, config = {}) => { return request.post(apiPrefix + 'SaveBrandSetDate', params,config) }
//编码下架-删除品牌设置数据
export const delBrandSetDate = (params, config = {}) => { return request.post(apiPrefix + 'DelBrandSetDate', params,config) }
//编码下架-获取库存标准设置
export const getInventoryStandardSet = (params, config = {}) => { return request.post(apiPrefix + 'GetInventoryStandardSet', params,config) }
//编码下架-获取特殊库存标准
export const getSpecialStandardList = (params, config = {}) => { return request.post(apiPrefix + 'GetSpecialStandardList', params,config) }
//编码下架-保存通用库存标准
export const saveGeneralInventoryStandard = (params, config = {}) => { return request.post(apiPrefix + 'SaveGeneralInventoryStandard', params,config) }
//编码下架-保存特殊库存标准
export const saveSpecialStandard = (params, config = {}) => { return request.post(apiPrefix + 'SaveSpecialStandard', params,config) }
//编码下架-删除特殊库存标准
export const delSpecialStandard = (params, config ={}) => { return request.post(apiPrefix + 'DelSpecialStandard', params, config) }
//编码下架-获取页面采购、运营查询下拉
export const getGoodsBanNoticePageQueryCondition = (params, config = {}) => { return request.post(apiPrefix + 'GetGoodsBanNoticePageQueryCondition', params,config) }
//编码下架-汇总数据导出
export const exportGoodsBan = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportGoodsBan', params, config) }
//获取最新通知时间
export const getLastNoticeTime = (params, config = {}) => { return request.post(apiPrefix + 'GetLastNoticeTime', params,config) }

// 1688商品关系 GetAliGoodInfoBindRecordList
export const getAliGoodInfoBindRecordList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetAliGoodInfoBindRecordList', params,config )
}

//导出1688商品关系 ExportAliGoodInfoBindRecordList
export const exportAliGoodInfoBindRecordList = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'ExportAliGoodInfoBindRecordList', params, config)
}

// 商品资料库-获取阿里商品链接 GetAliGoodsLink
export const getAliGoodsLink = (params, config = {}) => {
  return request.post(apiPrefix + 'GetAliGoodsLink', params, config)
}

// 商品资料库-获取商品资料的其他信息 GetGoodsDocRecordOther
export const getGoodsDocRecordOther = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGoodsDocRecordOther', params, config)
}

// 商品资料库-白底图/api/inventory/BasicGoods/SetGoodsWhiteBackImg
export const SetGoodsWhiteBackImg = (params, config = {}) => {
    return request.post(apiPrefix + 'SetGoodsWhiteBackImg', params, config)
  }
