import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/ContinuousProfitAnalysis/`
 
export const getContinuousNoProfit = (params, config = {}) => { return request.post(apiPrefix + 'GetContinuousNoProfitAsync', params, config)}

export const continuousNoProfitNoticeSaveAsync = (params, config = {}) => { return request.post(apiPrefix + 'ContinuousNoProfitNoticeSaveAsync', params, config)}

export const getContinuousNoProfitAnalysisRecordAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetContinuousNoProfitAnalysisRecordAsync', params, config)}

export const saveContinuousNoProfitAnalysisAsync = (params, config = {}) => { return request.post(apiPrefix + 'SaveContinuousNoProfitAnalysisAsync', params, config)} 

//新趋势图
export const newGetContinuousNoProfitAnalysisAsync = (params, config = {}) => { return request.post(apiPrefix + 'NewGetContinuousNoProfitAnalysisAsync', params, config)} 

//新表格
export const newGetContinuousNoProfitRecordAsync = (params, config = {}) => { return request.post(apiPrefix + 'NewGetContinuousNoProfitRecordAsync', params, config)} 


//点击趋势图
export const newGetContinuousNoProfitRecordAnalysisAsync = (params, config = {}) => { return request.post(apiPrefix + 'NewGetContinuousNoProfitRecordAnalysisAsync', params, config)}

//确认
export const newContinuousNoProfitNoticeSaveAsync = (params, config = {}) => { return request.post(apiPrefix + 'NewContinuousNoProfitNoticeSaveAsync', params, config)}

