<template>
  <div>
    <el-select v-model="field" class="field" placeholder="请选择" clearable @change="change">
      <el-option
        v-for="item in fields"
        :key="item.field"
        :label="item.label"
        :value="item.field"
      /></el-select>
  </div>
</template>
<script>
export default {
  model: {
    prop: 'value',
    event: 'update'
  },
  props: {
    value: {
      type: String,
      default: null
    },
    fields: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      field: null
    }
  },
  mounted() {
    this.field = this.value
  },
  methods: {
    change() {
      this.$emit('update', this.field)
    }
  }
}
</script>
<style scoped lang="scss">
.field{width:100px;}
</style>
