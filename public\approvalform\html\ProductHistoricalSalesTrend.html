<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <link rel="stylesheet" href="/approvalform/html/api/elment.css">
    <script src="/approvalform/html/api/vue.min.js"></script>
    <script src="/approvalform/html/api/elment.js"></script>
    <script src="/approvalform/html/api/jquery.min.js"></script>
    <script src="/approvalform/html/api/html2canvas.js"></script>
    <title>编码进货申报--商品历史销量趋势</title>
    <style type="text/css">
        .linebreak {
            overflow: hidden;
            /*超出部分隐藏*/
            text-overflow: ellipsis;
            /* 超出部分显示省略号 */
            white-space: normal;
            /*规定段落中的文本不进行换行 */
            width: 100%;
        }
        .el-pagination__jump {
            margin-left:0px;
        }
    </style>
</head>

<body>
    <div id="app" style="margin:0 auto;">
        <el-container direction="vertical" style=" border: 1px #ccc solid;">
            <template>
                <el-table  ref="tableBox" :data="list" style="width: 100%;" row-key="id" border
                    :max-height="tableHeight">
                    <el-table-column prop="goodsCode" label="商品编码" min-width="50"></el-table-column>
                    <el-table-column prop="styleCode" label="系列编码" min-width="50"></el-table-column>
                    <el-table-column prop="images" label="图片" min-width="50">
                        <template slot-scope="scope">
                            <el-image :src="scope.row.images"  :preview-src-list="[scope.row.images]"
                                style="max-width: 50px; max-height: 50px; margin-left: -10px;" fit="fill" :lazy="true"></el-image>
                        </template>
                    </el-table-column>
                    <el-table-column prop="goodsName" label="商品名称" min-width="50"></el-table-column>
                    <el-table-column prop="inventoryDay" label="库存周转天数" min-width="50"></el-table-column>
                    <el-table-column prop="purchaseDay" label="采购周转天数" min-width="50"></el-table-column>
                    <el-table-column prop="masterStock" label="主仓库库存" min-width="50"></el-table-column>
                    <el-table-column prop="warehouseStock" label="仓库库存数" min-width="50"></el-table-column>
                    <el-table-column prop="salesYesterday" label="昨日总销量" min-width="70">
                    </el-table-column>
                    <el-table-column prop="salesDay3" label="3日总销量/3日总退回" min-width="70">
                        <template slot-scope="scope">
                            <span
                            class="buttonText">{{scope.row.salesDay3}}</span>/
                            <span
                                class="buttonText">{{scope.row.refundDay3}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="salesDay15" label="15日总销量/15日总退回" min-width="70">
                        <template slot-scope="scope">
                            <span
                            class="buttonText">{{scope.row.salesDay15}}</span>/
                            <span
                                class="buttonText">{{scope.row.refundDay15}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="salesDay30" label="30日总销量/30日总退回" min-width="70">
                        <template slot-scope="scope">
                            <span
                            class="buttonText">{{scope.row.salesDay30}}</span>/
                            <span
                                class="buttonText">{{scope.row.refundDay30}}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <!-- <div style="text-align: right;">
                    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :current-page="currentPage" :page-sizes="[20, 50, 100, 150]" :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper" :total="total">
                    </el-pagination>
                </div> -->
            </template>
        </el-container>
    </div>
    <script>
        var vm = new Vue({
            el: '#app',
            data: function () {
                return {
                    //正式上线时，请切换为正式地址，末尾不许带 /
                    //测试：http://**************
                    //正式：http://************* 
                    // thisInterfaceUrl: "http://**************",
                    thisLonding: true,
                    list: [],
                    tableHeight: null,
                    currentPage: 1,
                    pageSize: 20,
                    total: 0,
                }
            },
            created () {
                //this.getStyleSheetInfo();
            },
            async mounted () {
                this.getStyleSheetInfo();
                this.beginShowing()
            },
            methods: {
                handleSizeChange (val) {
                    this.pageSize = val;
                    this.getStyleSheetInfo();
                },
                handleCurrentChange (val) {
                    this.currentPage = val;
                    this.getStyleSheetInfo();
                },
                beginShowing () {
                    this.$nextTick(function () {
                        // 文档显示区域的高度 - 
                        if (this.$refs.tableBox) {
                            this.tableHeight = window.innerHeight - 20;
                            this.$refs.tableBox.doLayout()
                        }
                    })
                },
               
                async getStyleSheetInfo () {
                    var me = this;
                    let searchURL = window.location.search;
                    searchURL = searchURL.substring(1, searchURL.length);
                    let GoodsCode = searchURL.split("GoodsCode")[1].split("=")[1];
                    me.thisLonding = true;
                    let targetUrl = '';
                    let parm = {};
                        targetUrl = '/api/Inventory/GoodsCodeStock/GetProductHistoricalSalesTrend';
                        parm.GoodsCode = GoodsCode;
                    // parm.pageSize = this.pageSize;
                    // parm.currentPage = this.currentPage;
                    $.ajax({
                        url: targetUrl,
                        type: 'GET',
                        dataType: 'json',
                        data: parm,
                        success: function (response) {
                            me.list = response.data.list;
                            me.total = response.data.total;
                        },
                        error: function (xhr, textStatus, errorThrown) {
                            console.log('Error: ', errorThrown);
                        }
                    });
                },
            }
        });
    </script>
</body>

</html>