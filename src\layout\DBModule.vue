<template>
  <div class="container1">
    <!-- 左侧对话列表 -->
    <div class="sidebar">
      <!-- <div @click="AddTags" class="addTags">
        <i class="el-icon-plus"></i> 新对话
      </div> -->
      <div class="conversation-list">
        <el-input class="input-new-tag" v-show="inputVisible" v-model.trim="inputValue" ref="saveTagInput" size="small"
          maxlength="50" @keyup.enter.native="handleInputConfirm">
        </el-input>
        <div v-if="dynamicTags && dynamicTags?.length > 0">
          <div v-for="item in dynamicTags" :class="[
            'conversation-item',
            item.sessionId == activeSessionId ? 'active' : '',
          ]" :key="item.sessionId" @click="chooseTag(item)">
            <div style="display: flex; align-items: center;gap: 10px">
              <i class="el-icon-chat-dot-round"></i>
              <div class="word" :title="item.sessionName">
                {{ item.sessionName }}
              </div>
            </div>
            <!-- <i
              class="el-icon-error deleteTag"
              @click.stop="deleteSession(item)"
            ></i> -->
          </div>
        </div>
        <!-- <div v-else class="conversation-item" style="font-size: 14px">
          暂无对话,新增对话即可开启聊天
        </div> -->
      </div>
    </div>

    <!-- 右侧聊天窗口容器 -->
    <!-- <div v-if="currentSessionId == 1" class="chat-container">
      <iframe
          src="http://192.168.16.55/chatbot/Bt76EUlmIqPFgy1t"
          style="width: 85%; height: 100%; "
          frameborder="0"
          allow="microphone">
      </iframe>
    </div>
    <div v-else class="chat-container">
      <iframe
        src="http://192.168.16.55/workflow/x3DN0js2NftHhKpE"
        style="width: 85%; height: 100%; "
        frameborder="0"
        allow="microphone">
        </iframe>
    </div> -->

    <div class="chat-container" v-if="currentSessionId == 2">
      <iframe src="http://192.168.16.55:3002/chat/share?shareId=qypkv3q7jgmvxrdsqhdnts46"
        style="width: 100%; height: 100%;" frameborder="0" allow="*" />
    </div>


    <div class="chat-container" v-else-if="currentSessionId == 1">
      <iframe src="http://192.168.16.55:3002/chat/share?shareId=ebnowqvq08cx8foyt36m18vc"
        style="width: 100%; height: 100%;" frameborder="0" allow="*" />
    </div>
    <div class="chat-container" v-else-if="currentSessionId == 4">
      <iframe :src="`http://192.168.16.240:8201/ai?token=${getToken}`" style="width: 100%; height: 100%;"
        frameborder="0" allow="*" />
    </div>

    <div class="chat-container" v-else>
      <iframe src="http://192.168.16.55:3002/chat/share?shareId=lln45jeyp6wa0ud3sxzucplp"
        style="width: 100%; height: 100%;" frameborder="0" allow="*" />
    </div>
    <!-- 默认显示的聊天窗口 -->
    <!-- <div class="chat-window">
        <div class="chat-header">
          <div class="word" style="width: 300px" :title="acitveTitle">
            {{ acitveTitle }}
          </div>
          <div style="border-bottom: 1px solid #ccc"></div>
        </div>
        <div
          class="chat-messages"
          v-if="getChartList() && getChartList()?.length > 0"
        >
          <div
            v-for="(item, i) in getChartList()"
            :key="item.messageId"
            :class="[
              'message',
              item.authorRole == 'user' ? 'sent' : 'received',
            ]"
          >
            <div
              v-if="item.authorRole == 'assistant'"
              style="display: flex; align-items: self-start; max-width: 600px"
            >
              <div style="width: 40px; margin-right: 10px">
                <el-avatar :src="yhlogo"></el-avatar>
              </div>
              <div class="message-content">
                <div v-html="renderMdText(item.content)"></div>
                <div
                  class="persistent"
                  v-if="i == getChartList().length - 1 && item.persistent"
                >
                  <el-button
                    type="primary"
                    icon="el-icon-loading"
                    @click="colseSession(item)"
                    >停止会话</el-button
                  >
                </div>
              </div>
            </div>
            <div
              v-else
              style="display: flex; align-items: center; max-width: 600px"
            >
              <div class="message-content" style=" max-width: 600px">
                {{ item.content }}
              </div>
              <div style="margin-left: 10px">
                <el-avatar :src="getAvatar"></el-avatar>
              </div>
            </div>
          </div>
        </div>
        <div class="chat-messages" v-else>
          <div class="message received">
            <div style="display: flex; align-items: self-start">
              <div style="width: 40px; margin-right: 10px">
                <el-avatar :src="yhlogo"></el-avatar>
              </div>
              <div class="message-content">今天想问点什么?</div>
            </div>
          </div>
        </div>
        <div class="chat-input">
          <el-input
            v-model="input"
            id="chat-input-textArea"
            type="textarea"
            placeholder="请输入..."
            @keyup.enter.native="sendQuestion"
            resize="none"
            :autosize="{ minRows: 4, maxRows: 11 }"
          ></el-input>
        </div>
      </div> -->
    <!-- </div> -->

    <div class="closeMoudle">
      <i class="el-icon-minus" @click.stop="$emit('shrink')" style="font-size: 20px;"></i>
      <!-- <i class="el-icon-close" @click.stop="$emit('close')"></i> -->
    </div>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import {
  createNewSession,
  getSessionList,
  getSessionContentList,
  delSession,
  stopExperienceMessage,
} from "@/api/people/peoplessc";
import MarkdownIt from "markdown-it";
import "highlight.js/styles/default.css";
import stores from "@/store";
import { getUserRoleList } from '@/api/media/ShootingVideo';
export default {
  data() {
    return {
      yhlogo: require("@/assets/images/yhlogo.jpg"),
      input: "",
      inputVisible: false,
      inputValue: "",
      activeSessionId: "4",
      token: "",
      dynamicTags: [
        // { "sessionId": 1, "sessionName": '规章制度知识' },
      ],
      roleList: [],
      reportQueryAuth: false,  //报表查询权限
      markdownRender: new MarkdownIt({
        html: true,
        linkify: true,
        typographer: true,
      }),
      acitveTitle: "",
      textArea: null,
      sessionIdList: [],
      currentSessionId: 4,
    };
  },
  computed: {
    getAvatar() {
      return stores.getters.avatar;
    },
    getToken() {
      return stores.getters.token;
    },
  },
  async mounted() {
    this.getUserRoleList();

  },
  methods: {
    shrink() {
      this.$emit('shrink');
    },
    //关闭对话
    async colseSession() {
      const res = this.chatHistory.find(
        (item) => item.sessionId === this.activeSessionId
      );
      res.chatHistory[res.chatHistory.length - 1].persistent = false;
      await stopExperienceMessage({
        sessionId: this.activeSessionId,
        message: res.chatHistory[res.chatHistory.length - 2].content,
      });
    },
    async getUserRoleList() {
      var res = await getUserRoleList();
      if (res?.success) {
        if (res.data.indexOf("系统管理员") > -1 || res.data.indexOf("超级管理员") > -1 || checkPermission('ERP:DATAQUERY:AI')) {
          this.reportQueryAuth = true;
          // this.dynamicTags.push({ "sessionId": 2, "sessionName": 'ERP数据查询' })
          // this.dynamicTags.push({ "sessionId": 3, "sessionName": '数据分析(研发中)' })
          this.dynamicTags.push({ "sessionId": 4, "sessionName": 'ERP业务查询' })
          this.dynamicTags.push({ "sessionId": 1, "sessionName": '规章制度知识' })
        }
        else {
          this.dynamicTags.push({ "sessionId": 4, "sessionName": 'ERP业务查询' })
          this.dynamicTags.push({ "sessionId": 1, "sessionName": '规章制度知识' })
        }
      }
    },
    getChartList() {
      return this.chatHistory.find(
        (item) => item.sessionId === this.activeSessionId
      )?.chatHistory;
    },
    deleteSession(item) {
      this.$confirm("删除后,聊天记录将不可恢复", "确定删除对话?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const formData = new FormData();
          formData.append("sessionId", item.sessionId);
          const { success } = await delSession(formData);
          if (!success) return;
          const index = this.dynamicTags.findIndex(
            (res) => res.sessionId == item.sessionId
          );
          if (index != -1) {
            const res = JSON.parse(JSON.stringify(this.dynamicTags));
            res.splice(index, 1);
            this.$set(this, "dynamicTags", res);
            if (this.dynamicTags?.length > 0) {
              this.activeSessionId = this.dynamicTags[0].sessionId;
              this.acitveTitle = this.dynamicTags[0].sessionName;
            } else {
              this.activeSessionId = null;
              this.acitveTitle = null;
            }
          } else {
            this.activeSessionId = this.dynamicTags[0].sessionId;
            this.acitveTitle = this.dynamicTags[0].sessionName;
          }
          await this.getQuestionDetails();
          this.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch((error) => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    chooseTag(item) {
      this.currentSessionId = item.sessionId;
      this.activeSessionId = item.sessionId;
      this.acitveTitle = item.sessionName;
      if (!this.sessionIdList.includes(item.sessionId)) {
        this.sessionIdList.push(item.sessionId);
        this.getQuestionDetails();
      }
      let chatMessages = document.querySelector(".chat-messages");
      this.$nextTick(() => {
        chatMessages.scrollTop = chatMessages?.scrollHeight;
      });
      this.getFocus();
    },
    getFocus() {
      const textArea = document.querySelector("#chat-input-textArea");
      textArea?.focus();
    },
    renderMdText(text) {
      return this.markdownRender.render(text);
    },
    async getQuestionDetails() {
      const formData = new FormData();
      formData.append("sessionId", this.activeSessionId);
      const { data } = await getSessionContentList(formData);
      let res = {};
      if (data && data.length > 0) {
        res = {
          sessionId: this.activeSessionId,
          chatHistory: data,
        };
      } else {
        res = {
          sessionId: this.activeSessionId,
          chatHistory: [],
        };
      }

      const result = JSON.parse(JSON.stringify(this.chatHistory));
      const index = result.findIndex(
        (item) => item.sessionId == this.activeSessionId
      );
      if (index != -1) {
        result.splice(index, 1, res);
      } else {
        result.push(res);
      }
      this.$set(this, "chatHistory", result);
      let chatMessages = document.querySelector(".chat-messages");
      this.$nextTick(() => {
        chatMessages.scrollTop = chatMessages.scrollHeight;
      });
    },
    //点击添加对话
    AddTags() {
      this.inputVisible = true;
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    async handleInputConfirm() {
      let inputValue = this.inputValue;
      if (!inputValue) return (this.inputVisible = false);
      const formData = new FormData();
      formData.append("name", inputValue);
      this.inputVisible = false;
      const { success } = await createNewSession(formData);
      if (!success) return;
      await this.getSessionListAsync();
      this.inputValue = "";
    },
    //获取会话
    async getSessionListAsync() {
      const { data, success } = await getSessionList();
      if (!success) return;
      if (data && data.length > 0) {
        this.activeSessionId = data[0].sessionId;
        this.acitveTitle = data[0].sessionName;
        this.sessionIdList.push(data[0].sessionId);
        await this.getQuestionDetails();
      }
      this.$set(this, "dynamicTags", data);
      this.getFocus();
      let chatMessages = document.querySelector(".chat-messages");
      this.$nextTick(() => {
        chatMessages.scrollTop = chatMessages.scrollHeight;
      });
    },
    async sendQuestion() {
      if (!this.dynamicTags || this.dynamicTags?.length == 0)
        return this.$message.error("请先创建对话");
      if (!this.input) return;
      const arr = [
        {
          authorRole: "user",
          content: this.input,
          messageId: new Date().getTime(),
        },
        {
          authorRole: "assistant",
          content: "",
          messageId: new Date().getTime() + 1,
          persistent: true,
        },
      ];
      this.$set(this, "sessionIdList", [...new Set(this.sessionIdList)]);
      let addDialogue = JSON.parse(JSON.stringify(this.chatHistory));
      let item = addDialogue?.find(
        (item) => item.sessionId == this.activeSessionId
      );
      let sessionId = this.activeSessionId;
      item.chatHistory = item.chatHistory.concat(arr);
      this.$set(this, "chatHistory", addDialogue);
      const message = JSON.parse(JSON.stringify(this.input));
      this.input = "";
      let chatMessages = document.querySelector(".chat-messages");
      this.$nextTick(() => {
        chatMessages.scrollTop = chatMessages.scrollHeight;
      });
      const response = await fetch(
        `${process.env.VUE_APP_JAVA_API_BLADEGATEWAY}/yunhan-gis-personnel/pullExperienceMessage`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            authorization: "Bearer " + this.token,
          },
          body: JSON.stringify({
            message,
            sessionId: this.activeSessionId,
          }),
        }
      );
      if (!response.body) {
        console.error("ReadableStream not supported in this browser.");
        return;
      }
      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");
      const index = this.chatHistory.findIndex(
        (item) => item.sessionId == sessionId
      );
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          this.chatHistory[index].chatHistory[
            this.chatHistory[index].chatHistory.length - 1
          ].persistent = false;
          break;
        }
        const text = decoder.decode(value, { stream: true });
        const res = text.match(/(?<=content":").*?(?=")/g);
        if (res) {
          res.forEach((item) => {
            item = item.replace(/\\n/g, "\n");
            this.chatHistory[index].chatHistory[
              this.chatHistory[index].chatHistory.length - 1
            ].content += item;
          });
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container1 {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  font-size: 16px;
  font-family: math;

  /* 左侧对话列表 */
  .sidebar {
    padding: 10px;
    box-sizing: border-box;
    width: 220px;
    background-color: #fff;
    border-right: 1px solid #ddd;
    position: relative;

    .addTags {
      position: sticky;
      top: 0;
      background-color: #f0f5ff;
      padding: 10px;
      cursor: pointer;
      color: #0057ff;
      font-weight: 600;
      transition: background-color 0.3s;
      border-radius: 10px;
      border: 1px solid #0057ff;
      font-family: cursive;

      &:hover {
        background-color: #e5eeff;
        border-radius: 10px;
      }
    }

    .conversation-list {
      list-style: none;
      padding: 0;
      margin: 0;
      overflow-y: auto;
      height: calc(100% - 40px);
      color: #7e7e7e;

      //修改滚动条样式
      &::-webkit-scrollbar {
        width: 8px;
      }

      .conversation-item {
        display: flex;
        align-items: center;
        padding: 10px;
        cursor: pointer;
        position: relative;
        transition: background-color 0.3s;
        display: flex;
        justify-content: space-between;

        &:hover {
          background-color: #f5f5f5;
          border-radius: 10px;
        }

        &:hover .deleteTag {
          display: block;
        }

        .deleteTag {
          display: none;
        }
      }
    }
  }

  /* 右侧聊天窗口容器 */
  .chat-container {
    flex: 1;
    position: relative;
    background-color: #fff;
    height: 100%;
    display: flex;

    /* 聊天窗口 */
    .chat-window {
      flex-direction: column;
      width: 100%;
      height: 100%;
      display: flex;
      background-color: #fff;
      justify-content: center;
    }

    .chat-header {
      padding: 10px;
      background-color: #fff;
    }

    .chat-messages {
      flex: 1;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;

      &::-webkit-scrollbar {
        width: 8px;
      }

      /* 消息样式 */
      .message {
        display: flex;
        align-items: flex-start;
        margin-bottom: 10px;
        max-width: 70%;
      }

      .message.received {
        align-self: flex-start;
      }

      .message.sent {
        align-self: flex-end;
      }

      .message-content {
        padding: 5px 15px;
        box-sizing: border-box;
        border-radius: 15px;
        background-color: #fff;
        color: #000;
        transition: background-color 0.3s;
        word-wrap: break-word;
        max-width: 600px;
        word-break: break-all;

        .persistent {
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .message.received .message-content {
        border: 1px solid #ccc;
      }

      .message.sent .message-content {
        border: 1px solid #ccc;
        display: flex;
        align-items: center;
      }
    }

    /* 输入框 */
    .chat-input {
      width: 100%;
      display: flex;
      align-items: center;
      padding: 10px;
      background-color: #fff;
      position: relative;
      box-sizing: border-box;
      overflow: hidden;
    }

    .chat-input ::v-deep .el-textarea__inner {
      border-radius: 5px;
      padding: 5px;
      background-color: #fff;
      width: 100%;
      font-size: 16px !important;

      //修改滚动条样式
      &::-webkit-scrollbar {
        width: 8px;
      }
    }
  }

  .closeMoudle {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    display: flex;
    gap: 10px;
    align-items: center;
    padding-right: 10px;

    .shrink {
      width: 15px;
      height: 2px;
      border-radius: 1px;
      background-color: #000;
      cursor: pointer;
    }
  }
}

.active {
  display: flex;
  // color: black;
  color: #409eff;
  font-weight: 700;
  background-color: #f5f5f5;
  border-radius: 10px;
}

.el-icon-close {
  font-size: 20px;
}

.word {
  width: 140px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
