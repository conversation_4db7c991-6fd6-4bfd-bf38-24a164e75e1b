import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_VerifyOrder}/PrePackOperateConfirm/`

//GetColumns
export const getColumns = (params, config = {}) => {
    return request.post(apiPrefix + 'GetColumns', params, config)
}

//查询数据 PageGetData
export const pageGetData = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetData', params, config)
}

//数据导出 ExportData
export const exportData = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportData', params, config)
}

//获取操作记录
export const pageGetHistories = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetHistories', params, config)
}

//GetColumns
export const getLogColumns = (params, config = {}) => {
    return request.post(apiPrefix + 'History/' + 'GetColumns', params, config)
}
export const logPageGetData = (params, config = {}) => {
    return request.post(apiPrefix + 'History/' + 'PageGetData', params, config)
}








//GetColumns
export const getColumns_ProPrePack = (params, config = {}) => {
    return request.post(apiPrefix + 'ProPrePack/GetColumns', params, config)
}

//查询数据 PageGetData
export const pageGetData_ProPrePack = (params, config = {}) => {
    return request.post(apiPrefix + 'ProPrePack/PageGetData', params, config)
}

//数据导出 ExportData
export const exportData_ProPrePack = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ProPrePack/ExportData', params, config)
}

//设置批量 确认/取消 加工
export const confirmResult = (params, config = {}) => {
    return request.post(apiPrefix + 'ProPrePack/ConfirmResult', params, config)
}

//GetTrendChart
export const getTrendChart_ProPrePack = (params, config = {}) => {
    return request.post(apiPrefix + 'ProPrePack/GetTrendChart', params, config)
}

//批量加工/不加工 SureAndAlarm
export const sureAndAlarm = (params, config = {}) => {
    return request.post(apiPrefix + 'SureAndAlarm', params, config)
}

//表头(一单一品) SingleGoods
export const getColumns_SingleGoods = (params, config = {}) => {
    return request.post(apiPrefix + 'SingleGoods/GetColumns', params, config)
}

//查询数据 PageGetData
export const pageGetData_SingleGoods = (params, config = {}) => {
    return request.post(apiPrefix + 'SingleGoods/PageGetData', params, config)
}

//数据导出 ExportData
export const exportData_SingleGoods = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'SingleGoods/ExportData', params, config)
}

//编码统计 GoodsCode
export const getColumns_GoodsCode = (params, config = {}) => {
    return request.post(apiPrefix + 'GoodsCode/GetColumns', params, config)
}

//查询数据 PageGetData
export const pageGetData_GoodsCode = (params, config = {}) => {
    return request.post(apiPrefix + 'GoodsCode/PageGetData', params, config)
}

//数据导出 ExportData
export const exportData_GoodsCode = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'GoodsCode/ExportData', params, config)
}