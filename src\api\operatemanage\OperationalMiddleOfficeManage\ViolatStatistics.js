import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/operate/`


//查询退款统计数据-系列编码维度
export const getViolatStatisticsStyleCodeData = (params, config = {}) => {
    return request.post(apiPrefix + 'GetViolatStatisticsStyleCodeData', params, config)
}

//查询退款统计数据-宝贝ID维度
export const getViolatStatisticsProCodeData = (params, config = {}) => {
    return request.post(apiPrefix + 'GetViolatStatisticsProCodeData', params, config)
}

//远程搜索系列编码
export const getViolatStatisticsStyleCode = (params, config = {}) => {
    return request.post(apiPrefix + 'GetViolatStatisticsStyleCode', params, config)
}

//导出退款统计数据-系列编码维度
export const exportViolatStatisticsStyleCodeData = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportViolatStatisticsStyleCodeData', params, config)
}

//导出退款统计数据-宝贝ID维度
export const exportViolatStatisticsProCodeData = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportViolatStatisticsProCodeData', params, config)
}

//查询退款统计数据-店铺维度
export const getViolatStatisticsShopData = (params, config = {}) => {
    return request.post(apiPrefix + 'GetViolatStatisticsShopData', params, config)
}

//导出退款统计数据-店铺维度
export const exportViolatStatisticsShopData = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportViolatStatisticsShopData', params, config)
}
//查询退款统计数据-店铺维度     违规类型详情
export const getViolatStatisticsShopDetailData = (params, config = {}) => {
    return request.post(apiPrefix + 'GetViolatStatisticsShopDetailData', params, config)
}
