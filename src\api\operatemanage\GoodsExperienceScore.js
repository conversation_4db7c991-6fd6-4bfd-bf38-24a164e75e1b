import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/goodsExperienceScore/`

// 查询商品体验分
export const getGoodsExperienceScore = (params, config) => {
    return request.post(apiPrefix + 'GetGoodsExperienceScore', params, config)
}

// 导出商品体验分
export const exportGoodsExperienceScore = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportGoodsExperienceScore', params, config)
}
