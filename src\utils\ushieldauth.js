import Vue from 'vue'
import { ushieldAuth } from '@/api/admin/viewUShield'

// 生成随机值
const generateGuid = () => {
  return 'yxxxxxxxxxxxxxxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    var r = Math.random() * 16 | 0,
      v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// U盾权限验证
const authUShield = async (viewId) => {
  var param = {
    "viewId": viewId,
    "authKey": generateGuid(),
    "verifyData": "",
    "shellNum": ""
  }

  var sense_web = SenseWeb();
  sense_web.port = 9080;
  sense_web.request_schema = "http://";

  try {
    // 获取U盾信息
    var ushieldsRestr = await sense_web.get_local_desc();
    var ushieldres = JSON.parse(ushieldsRestr);
    if (ushieldres.code != 0 || !ushieldres.data) {
      Vue.prototype.$message.error({
        message: "未找到加密锁",
        duration: 3000
      })
      return false;
    }

    // 获取第一条U盾信息
    var ushields = JSON.parse(ushieldres.data);
    if (ushields.length == 0) {
      Vue.prototype.$message.error({
        message: "未发现加密锁",
        duration: 3000
      })
      return false;
    }
    else if (ushields.length != 1) {
      Vue.prototype.$message.error({
        message: "发现多把加密锁，请拔出其中一把后重试",
        duration: 3000
      })
      return false;
    }
    var local_desc = JSON.stringify(ushields[0]);

    // 获取U盾编号信息
    var ushieldInfoRestr = await sense_web.get_local_device_p7b();
    var ushieldInfores = JSON.parse(ushieldInfoRestr);
    if (ushieldInfores.code != 0 || !ushieldInfores.data) {
      Vue.prototype.$message.error({
        message: "加密锁信息获取失败",
        duration: 3000
      })
      return false;
    }
    var ushieldInfos = JSON.parse(ushieldInfores.data)
    if (ushieldInfos.length <= 0) {
      Vue.prototype.$message.error({
        message: "未找到加密锁信息",
        duration: 3000
      })
      return false;
    }
    var ushieldInfo = ushieldInfos[0];
    param.shellNum = ushieldInfo.shell_num;

    // 获取加密锁签名数据，需要先使用base64进行编码
    var signdata = window.btoa("SENSELOCK" + param.authKey);
    var verifyDataRestr = await sense_web.slm_ctrl_sign_by_device(local_desc, signdata);
    var verifyDataRes = JSON.parse(verifyDataRestr);
    if (verifyDataRes.code != 0 || !verifyDataRes.data) {
      Vue.prototype.$message.error({
        message: "锁加密信息获取失败",
        duration: 3000
      })
      return false;
    }
    param.verifyData = verifyDataRes.data;

    // 验证权限
    var authRes = await ushieldAuth(param);
    if (authRes.success) {
      return true;
    }
  }
  catch(ex){
    console.error(ex);
    Vue.prototype.$message.error({
      message: "未找到U盾软件，请安装",
      duration: 3000
    })
  }
  return false;
}

export {
  authUShield
}
