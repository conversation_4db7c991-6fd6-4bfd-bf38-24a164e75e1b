import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/SampleChangeCheck/`

//查找商品编码下拉框数据源
export const getGoodsList10Row = (params, config = {}) => { return request.get(apiPrefix + 'GetGoodsList10Row', { params: params, ...config }) }

//样品核对分页查询
export const getSampleChangeCheckPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetSampleChangeCheckPageList', params, config) }

//样品核对单个查询
export const getSampleChangeCheckById = (params, config = {}) => { return request.get(apiPrefix + 'GetSampleChangeCheckById', { params: params, ...config }) }

//新增修改样品核对
export const saveSampleChangeCheck = (params, config = {}) => { return request.post(apiPrefix + 'SaveSampleChangeCheck', params, config) }

//样品核对审批发起
export const sampleChangeCheckApprovalInitiate = (params, config = {}) => { return request.post(apiPrefix + 'SampleChangeCheckApprovalInitiate', params, config) }

//获取审批结果 GetSampleChangeCheckApprovalRecord
export const getSampleChangeCheckApprovalRecord = (params, config = {}) => { return request.get(apiPrefix + 'GetSampleChangeCheckApprovalRecord', { params: params, ...config }) }

//样品核对撤销钉钉记录
export const sampleChangeCheckApprovalBack = (params, config = {}) => { return request.post(apiPrefix + 'SampleChangeCheckApprovalBack', params, config) }

//样品核对日志 GetSampleChangeCheckLogList
export const getSampleChangeCheckLogList = (params, config = {}) => { return request.post(apiPrefix + 'GetSampleChangeCheckLogList', params, config ) }
