import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/DeptComplaint/`
const apiimportPrefix = `${process.env.VUE_APP_BASE_API_ImportInventory}/DeptComplaintImport/`

//获取全部人员
export const getProcStaff = (params, config = {}) => {
    return request.get(apiPrefix + 'QueryProcStaff', { params: params, ...config })
}

//获取管理人员
export const getProcSupv = (params, config = {}) => {
    return request.get(apiPrefix + 'QueryProcSupv', { params: params, ...config })
}

//获取采购部部门
export const getCompDept = (params, config = {}) => {
    return request.get(apiPrefix + 'QueryCompDept', { params: params, ...config })
}

//获取部门投诉
export const getDeptComplaint = (params, config = {}) => {
    return request.post(apiPrefix + 'QueryDeptComplaint', params, config)
}
//编辑部门投诉
export const editDeptComplaint = (params, config = {}) => {
    return request.post(apiPrefix + 'UpdateDeptComplaint', params, config)
}
//新增部门投诉
export const addDeptComplaint = (params, config = {}) => {
    return request.post(apiPrefix + 'AddDeptComplaint', params, config)
}
//删除部门投诉
export const deleteDeptComplaint = (params, config = {}) => {
    return request.get(apiPrefix + 'DeleteDeptComplaint', { params: params, ...config })
}

//导入部门投诉
export const importDeptComplaintFile = (params, config = {}) => {
    return request.post(apiimportPrefix + 'ImportDeptComplaintFile', params, config)
}

//部门投诉导出
export const exportDeptComplaintFile = (params, config = { responseType: 'blob' }) => { return request.post(apiimportPrefix + 'ExportDeptComplaintFile', params, config) }

