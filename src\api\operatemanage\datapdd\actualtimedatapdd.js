import request from '@/utils/request'
const EmpAchievementPrefix = `${process.env.VUE_APP_BASE_API_PddOperateManage}/ActualTimeDataPdd/`

//获取主页数据
  // export const getPddActualTimeDataByProAsync = (params, config = {}) => {return request.get(EmpAchievementPrefix + 'GetPddActualTimeDataByProAsync', { params: params, ...config })}
  export const getPddActualTimeDataByProAsync = (params, config = {}) => {
   return request.post(EmpAchievementPrefix + 'GetPddActualTimeDataByProAsync', params, config)
 }
//
  // export const getShopAchievementList = (params, config = {}) => {return request.get(EmpAchievementPrefix + 'GetShopAchievementList', { params: params, ...config })}

 //发货预警
 export const getProductSendWarningRecord = (params, config = {}) => {
  return request.post(EmpAchievementPrefix + 'GetProductSendWarningRecord', params, config)
}


 //收藏数， 体检分
 export const getPddActualTimeProductLinkInfo = (params, config = {}) => {
    return request.post(EmpAchievementPrefix + 'GetPddActualTimeProductLinkInfo', params, config)
  }

//图表数据
  export const getPddActualTimeProAnalysisAsync = (params, config = {}) => {
    return request.post(EmpAchievementPrefix + 'GetPddActualTimeProAnalysisAsync', params, config)
  }

//详情链接数据
export const getActualTimeProductByTimeId = (params, config = {}) => {
  return request.post(EmpAchievementPrefix + 'GetActualTimeProductByTimeId', params, config)
}

//保存详情数据
export const saveActualTimeProduct = (params, config = {}) => {
  return request.post(EmpAchievementPrefix + 'SaveActualTimeProduct', params, config)
}




//根据时间和ID获取产品全站推广数据
export const getAllStationExtendCreativeByTimeId = (params, config = {}) => {
  return request.post(EmpAchievementPrefix + 'GetAllStationExtendCreativeByTimeId', params, config)
}

//根据时间和ID获取产品全站推广数据(有开始和结束时间)
export const getAllStationExtendReportByTimeId = (params, config = {}) => {
  return request.post(EmpAchievementPrefix + 'GetAllStationExtendReportByTimeId', params, config)
}

//根据时间和ID获取产品全站推广操作日志
export const getAllStationExtendOperateLogByTimeId = (params, config = {})=>{
  return request.post(EmpAchievementPrefix + 'GetAllStationExtendOperateLogByTimeId',params,config)
}

//保存推广信息
export const saveActualTimeAdvData = (params, config = {}) => {
  return request.post(EmpAchievementPrefix + 'SaveActualTimeAdvData',params,config)
}


//根据时间和id获取产品多多场景创意数据
export const getDuoDuoSceneCreativeByTimeId = (params, config = {}) => {
  return request.post(EmpAchievementPrefix + 'GetDuoDuoSceneCreativeByTimeId',params,config)
}

//根据时间和id获取产品多多场景创意数据
export const getDuoDuoSceneReportByTimeId = (params, config = {}) => {
  return request.post(EmpAchievementPrefix + 'GetDuoDuoSceneReportByTimeId',params,config)
}

//根据时间和id获取产品多多场景操作日志
export const getDuoDuoSceneOperateLogByTimeId = (params, config = {}) => {
  return request.post(EmpAchievementPrefix + 'GetDuoDuoSceneOperateLogByTimeId',params,config)
}


//根据时间和id获取产品多多搜索创意数据
export const getDuoDuoSearchCreativeByTimeId = (params, config = {}) => {
  return request.post(EmpAchievementPrefix + 'GetDuoDuoSearchCreativeByTimeId',params,config)
}

//根据时间和id获取产品多多搜索创意数据
export const getDuoDuoSearchReportByTimeId = (params, config = {}) => {
  return request.post(EmpAchievementPrefix + 'GetDuoDuoSearchReportByTimeId',params,config)
}

//根据时间和id获取产品多多搜索操作日志
export const getDuoDuoSearchOperateLogByTimeId = (params, config = {}) => {
  return request.post(EmpAchievementPrefix + 'GetDuoDuoSearchOperateLogByTimeId',params,config)
}


//根据时间和id获取产品直播推广创意数据
export const getLiveExtendCreativeByTimeId = (params, config = {}) => {
  return request.post(EmpAchievementPrefix + 'GetLiveExtendCreativeByTimeId',params,config)
}

//根据时间和id获取产品直播推广创意数据
export const getLiveExtendReportByTimeId = (params, config = {}) => {
  return request.post(EmpAchievementPrefix + 'GetLiveExtendReportByTimeId',params,config)
}

//根据时间和id获取产品直播推广操作日志
export const getLiveExtendOperateLogByTimeId = (params, config = {}) => {
  return request.post(EmpAchievementPrefix + 'GetLiveExtendOperateLogByTimeId',params,config)
}

//批量保存
export const batchSaveActualTimeAdvData = (params, config = {}) => {
  return request.post(EmpAchievementPrefix + 'BatchSaveActualTimeAdvData',params,config)
}