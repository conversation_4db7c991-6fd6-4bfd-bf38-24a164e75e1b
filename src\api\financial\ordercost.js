
   import request from '@/utils/request'
   const apiPrefix = `${process.env.VUE_APP_BASE_API_Financial}/OrderCost/`

   export const pageOrderNoExpressFee = (params, config = {}) => {return request.get(apiPrefix + 'PageOrderNoExpressFeeAsync', { params: params, ...config })}
   export const importOrderNoExpressFee = (params, config = {}) => {return request.post(apiPrefix + 'ImportOrderNoExpressFeeAsync', params, config)}

   export const addVirtualType = (params, config = {}) => {return request.post(apiPrefix + 'AddVirtualTypeAsync', params, config)}
   export const updateVirtualType = (params, config = {}) => {return request.post(apiPrefix + 'UpdateVirtualTypeAsync', params, config)}
   export const deleteVirtualType = (params, config = {}) => {return request.get(apiPrefix + 'DeleteVirtualTypeAsync', { params: params, ...config })}
   export const pageVirtualType = (params, config = {}) => {return request.post(apiPrefix + 'PageVirtualTypeAsync', params, config)}

   export const addPackageMonthAverageCost = (params, config = {}) => {return request.post(apiPrefix + 'AddPackageMonthAverageCostAsync', params, config)}
   export const updatePackageMonthAverageCost = (params, config = {}) => {return request.post(apiPrefix + 'UpdatePackageMonthAverageCostAsync', params, config)}
   export const deletePackageMonthAverageCost = (params, config = {}) => {return request.get(apiPrefix + 'DeletePackageMonthAverageCostAsync', { params: params, ...config })}
   export const pagePackageMonthAverageCost = (params, config = {}) => {return request.post(apiPrefix + 'PagePackageMonthAverageCostAsync', params, config)}
   export const changePackageMonthAverageCostAuditStatus = (params, config = {}) => {return request.post(apiPrefix + 'ChangePackageMonthAverageCostAuditStatusAsync', params, config)}
   export const computeOrderCost = (params, config = {}) => {return request.post(apiPrefix + 'ComputeOrderCostAsync', params, config)}
   export const computePlatformDeduction = (params, config = {}) => {return request.post(apiPrefix + 'ComputePlatformDeductionAsync', params, config)}
   export const pageOrderCostCompute =(params,config={})=>{return request.post(apiPrefix+'PageOrderCostComputeAsync',params,config)}
   export const changeOrderCostComputeStatus=(params,config={})=>{return request.get(apiPrefix+'ChangeOrderCostComputeStatusAsync',{params:params,...config})}
   export const pageOrderCostShare =(params,config={})=>{return request.post(apiPrefix+'PageOrderCostShareAsync',params,config)}
   export const exportOrderNoExpressFee =(params,config ={responseType: 'blob'}) =>{ return request.get(apiPrefix + 'ExportOrderNoExpressFeeAsync',{params: params, ...config})}
   export const exportOrderNoExpressFeeShop =(params,config ={responseType: 'blob'}) =>{ return request.get(apiPrefix + 'ExportOrderNoExpressFeeShopAsync',{params: params, ...config})}
   export const exportOrderCostShare =(params,config ={responseType: 'blob'}) =>{return request.get(apiPrefix + 'ExportOrderCostShareAsync',{params: params, ...config})}
   export const deleteOrderNoExpressFee =(params,config={})=>{return request.post(apiPrefix+'DeleteOrderNoExpressFeeAsync',params,config)} 
   //按店铺删除快递费
   export const deleteOrderNoExpressFeeByShop =(params,config={})=>{return request.post(apiPrefix+'DeleteOrderNoExpressFeeByShop',params,config)} 
  export const pageOrderWithholdFee = (params, config = {}) => {return request.get(apiPrefix + 'PageOrderWithholdFeeAsync', { params: params, ...config })}
  //快递扣款计算
  export const computeOrderWithholdCost = (params, config = {}) => {return request.post(apiPrefix + 'ComputeOrderWithholdCostAsync', params, config)}
  //快递扣款
  export const importOrderWithholdFee = (params, config = {}) => {return request.post(apiPrefix + 'ImportOrderWithholdFeeAsync', params, config)}
  export const changeOrderWithholdCostComputeStatus =(params,config={})=>{return request.get(apiPrefix+'ChangeOrderWithholdCostComputeStatusAsync',{params:params,...config})}
  export const pageOrderWithholdCostComputeAsync =(params,config={})=>{return request.post(apiPrefix+'PageOrderWithholdCostComputeAsync',params,config)}
  export const pageOrderWithholdCostShare =(params,config={})=>{return request.post(apiPrefix+'PageOrderWithholdCostShareAsync',params,config)}
  export const exportOrderWithholdCostShare =(params,config ={responseType: 'blob'}) =>{return request.get(apiPrefix + 'ExportOrderWithholdCostShareAsync',{params: params, ...config})}

  export const exportOrderWithholdFee =(params,config ={responseType: 'blob'}) =>{return request.get(apiPrefix + 'ExportOrderWithholdFeeAsync',{params: params, ...config})}  
  export const deleteOrderWithholdFee =(params,config={})=>{return request.post(apiPrefix+'DeleteOrderWithholdFeeAsync',params,config)}

   