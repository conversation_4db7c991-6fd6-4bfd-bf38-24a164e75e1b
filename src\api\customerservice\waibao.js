import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/OutSource/`

// 分组——列表查询
export const getOutSourceGroupList = (data, config = {}) => {return request.post(apiPrefix + 'GetOutSourceGroupList', data, config)}

// 分组——添加
export const addOutSourceGroupInfo = (data, config = {}) => {return request.post(apiPrefix + 'AddOutSourceGroupInfo', data, config)}

// 分组——修改
export const updateOutSourceGroupInfo = (data, config = {}) => {return request.post(apiPrefix + 'UpdateOutSourceGroupInfo', data, config)}

// 分组——删除
export const deleteOutSourceGroup = (params, config = {}) => {return request.get(apiPrefix + 'DeleteOutSourceGroup', { params: params, ...config })}

// 分组——批量离组
export const updateGroupBatchLeave = (data, config = {}) => {return request.post(apiPrefix + 'UpdateGroupBatchLeave', data, config)}

// 分组——下拉列表
export const getOSGroupByList = (params, config = {}) => {return request.get(apiPrefix + 'GetOSGroupByList', { params: params, ...config })}

// 分组导入
export const importOSGroupAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportOSGroupAsync', params, config) }


// pxx 咨询，导入
export const imporPddInquirsAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImporPddInquirsAsync', params, config) }

// pxx 咨询——列表数据
export const getOSPddInquirsList = (params, config = {}) => { return request.post(apiPrefix + 'GetOSPddInquirsList', params, config) }

// pxx 咨询——删除数据
export const deleteOSPddInquirs = (params, config = {}) => {return request.get(apiPrefix + 'DeleteOSPddInquirs', { params: params, ...config })}

// pxx 咨询——批次删除
export const deleteBatchOSPddInquirs = (params, config = {}) => {return request.get(apiPrefix + 'DeleteBatchOSPddInquirs', { params: params, ...config })}



// pxx 组效率，分页列表
export const getOSPddGroupEfficiencyPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetOSPddGroupEfficiencyPageList', params, config) }

// pxx 组效率，趋势图
export const getOSPddGroupEfficiencyChat = (params, config = {}) => { return request.post(apiPrefix + 'GetOSPddGroupEfficiencyChat', params, config) }

// pxx 组效率，导出
export const exportPddGroupEfficiency = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'ExportPddGroupEfficiency', params, config)}



// pxx 店效率，分页列表
export const getOSPddShopEfficiencyPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetOSPddShopEfficiencyPageList', params, config) }

// pxx 店效率，趋势图
export const getOSPddShopEfficiencyChat = (params, config = {}) => { return request.post(apiPrefix + 'GetOSPddShopEfficiencyChat', params, config) }

// pxx 店效率，导出
export const exportPddShopEfficiency = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'ExportPddShopEfficiency', params, config)}


// pxx 个人，分页列表
export const getOSPddPersonalEfficiencyPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetOSPddPersonalEfficiencyPageList', params, config) }

// pxx 个人，趋势图
export const getOSPddPersonalEfficiencyChat = (params, config = {}) => { return request.post(apiPrefix + 'GetOSPddPersonalEfficiencyChat', params, config) }

// pxx 个人，店效率
export const getOSPddShopPersonalEfficiency = (params, config = {}) => { return request.post(apiPrefix + 'GetOSPddShopPersonalEfficiency', params, config) }

// pxx 个人，导出
export const exportPddPersonEfficiency = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'ExportPddPersonEfficiency', params, config)}




// dy 咨询，导入
export const imporDyInquirsAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImporDyInquirsAsync', params, config) }

// dy 咨询数据导入——列表数据
export const getOSDouYinInquirsList = (params, config = {}) => { return request.post(apiPrefix + 'GetOSDouYinInquirsList', params, config) }

// dy 咨询数据导入——删除数据
export const deleteOSDyInquirs = (params, config = {}) => {return request.get(apiPrefix + 'DeleteOSDyInquirs', { params: params, ...config })}

// dy 咨询数据导入——批次删除
export const deleteBatchOSDyInquirs = (params, config = {}) => {return request.get(apiPrefix + 'DeleteBatchOSDyInquirs', { params: params, ...config })}


//dy  组，分页列表
export const getOSDouYinGroupEfficiencyPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetOSDouYinGroupEfficiencyPageList', params, config) }

// dy 组，趋势图
export const getOSDouYinGroupEfficiencyChart = (params, config = {}) => { return request.post(apiPrefix + 'GetOSDouYinGroupEfficiencyChart', params, config) }

// dy 组，导出
export const exportDouYinByGroup = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'ExportDouYinByGroup', params, config)}



//dy  店，分页列表
export const getOSDouYinShopEfficiencyPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetOSDouYinShopEfficiencyPageList', params, config) }

// dy 店，趋势图
export const getOSDouYinShopEfficiencyChat = (params, config = {}) => { return request.post(apiPrefix + 'GetOSDouYinShopEfficiencyChat', params, config) }

// dy 店，导出
export const exportDouYinByShop = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'ExportDouYinByShop', params, config)}



//dy  个人，分页列表
export const getODDouYinPersonalEfficiencyPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetODDouYinPersonalEfficiencyPageList', params, config) }

// dy 个人，趋势图
export const getOSDouYinPersonalEfficiencyChat = (params, config = {}) => { return request.post(apiPrefix + 'GetOSDouYinPersonalEfficiencyChat', params, config) }

// dy 个人，店效率
export const getOSDouYinShopPersonalEfficiency = (params, config = {}) => { return request.post(apiPrefix + 'GetOSDouYinShopPersonalEfficiency', params, config) }

// dy 个人，导出
export const ExportDouYinByPerson = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'ExportDouYinByPerson', params, config)}


// dy 会话服务，导入
export const imporOSDouYinKFAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImporOSDouYinKFAsync', params, config) }

// dy 会话服务——列表数据
export const getOSDouYinKFList = (params, config = {}) => { return request.post(apiPrefix + 'GetOSDouYinKFList', params, config) }

// dy 会话服务——删除数据
export const deleteOSDouYinKF = (params, config = {}) => {return request.get(apiPrefix + 'DeleteOSDouYinKF', { params: params, ...config })}

// dy 会话服务——批次删除
export const deleteBatchOSDouYinKF = (params, config = {}) => {return request.get(apiPrefix + 'DeleteBatchOSDouYinKF', { params: params, ...config })}


//dy 服务数据，满意人数
export const getOSDouYinKfInquirsPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetOSDouYinKfInquirsPageList', params, config) }



//获取店铺下拉
export const getOSStoreNameList = (params, config = {}) => { if(!params) params={};  return request.post(apiPrefix + 'GetOSStoreNameList',  params, config)}