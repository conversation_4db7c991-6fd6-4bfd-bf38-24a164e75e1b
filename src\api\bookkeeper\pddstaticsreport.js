import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/pDDStaticsReport/`

 export const getFinancialStaticticsByUser = (params, config = {}) => {return request.get(apiPrefix + 'GetFinancialStaticticsByUser', { params, ...config })}
export const getFinancialPddStaticticsByUser = (params, config = {}) => {return request.get(apiPrefix + 'GetFinancialPddStaticticsByUser', { params, ...config })}
export const exportFinancialStaticticsByUser = (params, config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportFinancialStaticticsByUser', params, config)}
export const exportFinancialPddStaticticsByUser = (params, config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportFinancialPddStaticticsByUser', params, config)}
export const SetPerformanceTarget = (params, config = {}) => { return request.get(apiPrefix + 'SetPerformanceTarget', { params, ...config })}
export const getPerformanceTarget = (params, config = {}) => { return request.get(apiPrefix + 'getPerformanceTarget', { params, ...config })}
export const getPerformanceStaticticsByUser = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformanceStaticticsByUser', { params, ...config })}
export const getPerformanceStaticticsByGroup = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformanceStaticticsByGroup', { params, ...config })}
export const getDayReportStaticticsByIDUser = (params, config = {}) => { return request.get(apiPrefix + 'GetDayReportStaticticsByIDUser', { params, ...config })}
 //拼系运营人员业绩统计（店铺）
export const getPerformanceStaticticsByShop = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformanceStaticticsByShop', { params, ...config })}
//运营人员汇总趋势图
export const getPerformanceStaticticsByUserAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformanceStaticticsByUserAnalysis', { params, ...config })}
//运营组汇总趋势图
export const getPerformanceStaticticsByGroupAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformanceStaticticsByGroupAnalysis', { params, ...config })}
export const getEnergyEfficiencyYesterdayTX = (params, config = {}) => { return request.get(apiPrefix + 'GetEnergyEfficiencyYesterdayTX', { params, ...config }) }
export const getEnergyEfficiencyTX_Chart = (params, config = {}) => { return request.get(apiPrefix + 'GetEnergyEfficiencyTX_Chart', { params, ...config })}



export const getOperationPlanTx = (params, config = {}) => { return request.get(apiPrefix + 'GetOperationPlanTx', { params, ...config })}
export const editOperationPlanTx = (params, config) => { return request.post(apiPrefix + 'EditOperationPlanTx', params, config) }
export const addOperationPlanTx = (params, config) => { return request.post(apiPrefix + 'AddOperationPlanTx', params, config) }


export const deleteOperationPlanTx = (params, config = {}) => { return request.delete(apiPrefix + 'DeleteOperationPlanTxAsync', { params: params, ...config }) }
export const getLossProductWarningTX_Chart = (params, config = {}) => { return request.get(apiPrefix + 'GetLossProductWarningTX_Chart', { params, ...config })}
export const getLossProductWarningTX_Chart_GroupId = (params, config = {}) => { return request.get(apiPrefix + 'GetLossProductWarningTX_Chart_GroupId', { params, ...config })}


export const getLossProductWarningTX = (params, config = {}) => { return request.get(apiPrefix + 'GetLossProductWarningTX', { params, ...config })}

export const getOpentionPlan = (params, config = {}) => { return request.get(apiPrefix + 'GetOpentionPlanAsync', { params: params, ...config }) }

//拼系业绩导出-人员
export const exportPerformanceStaticticsByUser=(params,config = { responseType: 'blob' }) =>{return request.get(apiPrefix+'ExportPerformanceStaticticsByUser',{params:params,...config})}
export const exportPerformanceStaticticsByGroup=(params,config = { responseType: 'blob' }) =>{return request.get(apiPrefix+'ExportPerformanceStaticticsByGroup',{params:params,...config})}
export const exportPerformanceStaticticsByShop=(params,config = { responseType: 'blob' }) =>{return request.get(apiPrefix+'ExportPerformanceStaticticsByShop',{params:params,...config})}

