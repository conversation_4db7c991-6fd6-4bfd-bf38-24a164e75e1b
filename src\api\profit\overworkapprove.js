import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API}/OverWorkApprove/`

//获取所有职级
export const GetOverWorkApproveRankList = (params,config ={}) =>{
    return request.get(apiPrefix+'GetOverWorkApproveRankList', {params: params, ...config})
}
//获取所有加班审批需要配置的部门
export const GetOverWorkApproveDeptList = (params,config ={}) =>{
    return request.get(apiPrefix+'GetOverWorkApproveDeptList', {params: params, ...config})
}


//加班审批列表-导出
export const exportOverWorkApproveWorkData = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportOverWorkApproveWorkDataPageList', params, config) }

//获取加班审批列表
export const GetOverWorkApproveWorkDataPageList = (params,config ={}) =>{
    return request.post(apiPrefix + 'GetOverWorkApproveWorkDataPageList', params, config)
}
//同步加班流程
export const SyncOverWorkApproveWorkData = (params,config ={}) =>{
    return request.get(apiPrefix+'SyncOverWorkApproveWorkData', {params: params, ...config})
}
//批量通过或拒绝
export const OverWorkApproveProcessInstancesExecute = (params,config ={}) =>{
    return request.post(apiPrefix + 'OverWorkApproveProcessInstancesExecute', params, config)
}