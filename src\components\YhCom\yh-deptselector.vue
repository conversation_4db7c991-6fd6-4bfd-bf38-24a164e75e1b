<template>

<el-tooltip  :content="fullName" placement="top" :disabled="!showTip">   
    <el-select ref="selectUpResId" v-model="chooseName" :clearable="clearable" 
    style="width: 120px" size="mini"
        @clear="clear" :placeholder="placeStr">
        <el-option hidden value="一级菜单" :label="chooseName"></el-option>
        <el-tree style="width: 200px;" :data="deptList" 
            :props="defaultProps" :expand-on-click-node="false"
            :check-on-click-node="true" @node-click="handleNodeClick">
        </el-tree>
    </el-select>
    
</el-tooltip>

</template>

<script>
import { getALLDDDeptTree } from '@/api/profit/personnel'

export default {
    name: 'YhDeptSelector',
    props: {       
        placeStr: {
            type: String,
            default() {
                return "请选择部门";
            }
        },
        clearable: {
            type: Boolean,
            default() {
                return false;
            }
        },
    },
    watch: {
    },
    async mounted() {
        this.getDeptList();
    },
    computed:{
        showTip(){
            return this.ddDeptId && this.ddDeptId >0;
        }
    },
    data() {
        return {
            that: this,
            chooseName:"",
            fullName:"",
            placeholder: "",
            deptList: [],
            ddDeptId:null,
            defaultProps: {
                children: 'childDeptList',
                label: 'name'
            },
        }
    },
    methods: {
        // 获取部门列表
        async getDeptList() {
            await getALLDDDeptTree().then(res => {
                if (res.success) {
                    this.deptList = res.data.childDeptList;
                }
            })
        },
        // 节点点击事件
        handleNodeClick (data) {
            // 配置树形组件点击节点后，设置选择器的值，配置组件的数据
            this.chooseName = data.name;
            this.fullName=data.deptFullName;
            this.ddDeptId = data.dept_id;
            // 选择器执行完成后，使其失去焦点隐藏下拉框效果
            this.$refs.selectUpResId.blur();

            this.$emit('change', data);
        }, 
        clear(){
            this.ddDeptId=null;
            this.$emit('change', null);
        }      
    },

}
</script>
