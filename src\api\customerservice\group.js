import request from '@/utils/request'
const GroupPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/Group/`
export const importGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportGroupAsync', params, config) }

export const importPddGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'importPddGroupAsync', params, config) }

export const getGroupList = (params, config = {}) => { return request.post(GroupPrefix + 'GetGroupList', params, config) }

export const deleteGroupBatch = (params, config = {}) => { return request.get(GroupPrefix + 'DeleteBatchAsync', { params: params, ...config }) }

export const addgroup = (params, config = {}) => { return request.get(GroupPrefix + 'AddGroup', { params: params, ...config }) }
export const deletegroup = (params, config = {}) => { return request.get(GroupPrefix + 'DeleteGroupAsync', { params: params, ...config }) }
export const updategroupinfo = (params, config = {}) => { return request.get(GroupPrefix + 'UpdateGroup', { params: params, ...config }) }

//淘系——分组
export const GetGroupNameList = (params, config = {}) => { return request.get(GroupPrefix + 'GetGroupNameList', { params: params, ...config }) }
//获取分组修改日志
export const GetGroupLogList = (params, config = {}) => { return request.post(GroupPrefix + 'GetGroupLogList', params, config) }



export const GetKeFuHuiZongPlatformPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetKeFuHuiZongPlatformPageList', params, config) }
export const GetKeFuHuiZongGroupPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetKeFuHuiZongGroupPageList', params, config) }
export const GetKeFuHuiZongUserPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetKeFuHuiZongUserPageList', params, config) }
export const GetKeFuHuiZongDtlPageList = (params, config = {}) => { return request.post(GroupPrefix + 'GetKeFuHuiZongDtlPageList', params, config) }