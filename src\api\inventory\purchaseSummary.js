import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/purchaseSummary/`

//毛三提成比例-导入
export const importProfit3CommissionPercentage = (params, config = {}) => {
  return request.post(apiPrefix + 'ImportProfit3CommissionPercentage', params, config)
}

//毛三提成比例-查询
export const getProfit3CommissionPercentageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProfit3CommissionPercentageList', params, config)
}

//毛三提成比例-添加架构变动
export const addChangePost = (params, config = {}) => {
  return request.post(apiPrefix + 'AddChangePost', params, config)
}

//毛三提成比例-查询添加架构变动
export const getChangePost = (params, config = {}) => {
  return request.post(apiPrefix + 'GetChangePost', params, config)
}

//毛三提成比例-添加设置分值
export const addProfit3CommissionPercentageScoreSet = (params, config = {}) => {
  return request.post(apiPrefix + 'AddProfit3CommissionPercentageScoreSet', params, config)
}

//毛三提成比例-查询分值日志
export const getProfit3CommissionPercentageScoreSetLog = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProfit3CommissionPercentageScoreSetLog', params, config)
}

//毛三提成比例-查询分值设置初始值
export const getProfit3CommissionPercentageInitSetScore = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProfit3CommissionPercentageInitSetScore', params, config)
}

//毛三提成比例-计算
export const calPurchaseSumProfit3BrandRpt = (params, config = {}) => {
  return request.post(apiPrefix + 'CalPurchaseSumProfit3BrandRpt', params, config)
}

//毛三提成比例-专员测算
export const calPurchaseSumProfit3BrandStopRpt = (params, config = {}) => {
  return request.post(apiPrefix + 'CalPurchaseSumProfit3BrandStopRpt', params, config)
}

//毛三提成比例-更新时间
export const getRenewLog = (params, config = {}) => {
  return request.post(apiPrefix + 'GetRenewLog', params, config)
}


//小组明细-查询
export const getProfit3GroupDetailList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProfit3GroupDetailList', params, config)
}

//小组明细-计算
export const calPurchaseSumProfit3GroupRpt = (params, config = {}) => {
  return request.post(apiPrefix + 'CalPurchaseSumProfit3GroupRpt', params, config)
}

//毛三提成比例-小组测算
export const calPurchaseSumProfit3GroupStopRpt = (params, config = {}) => {
  return request.post(apiPrefix + 'CalPurchaseSumProfit3GroupStopRpt', params, config)
}

//小组明细-导入
export const importProfit3GroupDetail = (params, config = {}) => {
  return request.post(apiPrefix + 'ImportProfit3GroupDetail', params, config)
}

//小组明细-添加分值日志
export const addProfit3GroupDetailScoreSet = (params, config = {}) => {
  return request.post(apiPrefix + 'AddProfit3GroupDetailScoreSet', params, config)
}

//小组明细-查询分值设置初始值
export const getProfit3GroupDetailInitSetScore = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProfit3GroupDetailInitSetScore', params, config)
}

//专员明细-数据截停返回数据
export const getProfit3CommissionPercentageInitSetStop = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProfit3CommissionPercentageInitSetStop', params, config)
}

//专员明细-添加数据截停
export const addProfit3CommissionPercentageStopSet = (params, config = {}) => {
  return request.post(apiPrefix + 'AddProfit3CommissionPercentageStopSet', params, config)
}

//小组明细-数据截停返回数据
export const getProfit3GroupDetailInitSetStop = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProfit3GroupDetailInitSetStop', params, config)
}

//小组明细-添加数据截停
export const addProfit3GroupDetailStopSet = (params, config = {}) => {
  return request.post(apiPrefix + 'AddProfit3GroupDetailStopSet', params, config)
}

//小组明细-H5页面小组查询
export const getProfit3GroupStopDetailList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProfit3GroupStopDetailList', params, config)
}

//小组明细-H5页面专员查询
export const getProfit3CommissionPercentageStopList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProfit3CommissionPercentageStopList', params, config)
}

//公用
//小组明细-查询分值日志

//小组明细-更新时间


//小组明细-配置小组查询
export const getProfit3GroupConfig = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProfit3GroupConfig', params, config)
}

//小组明细-配置小组查询
export const setProfit3GroupConfig = (params, config = {}) => {
  return request.post(apiPrefix + 'SetProfit3GroupConfig', params, config)
}
