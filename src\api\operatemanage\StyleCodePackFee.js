import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/StyleCodePackFee/`

// 获取系列编码包材费
export const QueryStyleCodePackFee = (params, config = {}) => {
  return request.post(apiPrefix + 'QueryStyleCodePackFee', params, config)
}
///导出系列编码包材费
export const ExportStyleCodePackFee = (params, config = { responseType: 'blob' }) => {return request.post(apiPrefix + 'ExportStyleCodePackFee',  params, config)}
//导入系列编码包材费
export const ImportStyleCodePackFee = (params, config = {}) => { return request.post(apiPrefix + 'ImportStyleCodePackFee',  params, config ) }
// 修改系列编码包材费
export const ChangeStyleCodePackFee = (params, config = {}) => {
  return request.post(apiPrefix + 'ChangeStyleCodePackFee', params, config)
}
// 批量修改系列编码包材费
export const BulkChangeStyleCodePackFee = (params, config = {}) => {
  return request.post(apiPrefix + 'BulkChangeStyleCodePackFee', params, config)
}