import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_JAVA_API_BLADEGATEWAY}/yunhan-gis-competition/`

// export const getList = (params, config = {}) => {return request.get(apiPrefix + 'GetShopList', { params, ...config })}
// export const competitiveDataDetail = (params, config = {}) => { if(!params) params={};  return request.post(apiPrefix + 'competitiveDataDetail',  params, config)}

export const competitiveDataPage= (params, config = {}) => { return request.post(apiPrefix + 'competitiveDataPage', params, config)}

export const exportCompetitiveData=
 (params, config = { responseType: 'blob' }) => { return  request.get(apiPrefix + 'export-competitiveData', { params: params, ...config }) }

 //获取类目
export const getAllCategoryType = (params, config = {}) => {  return request.get(apiPrefix + 'getAllCategoryType', { params: params, ...config })}

export const fastgptChat = (params, config = {}) => {  return request.get(apiPrefix + 'fastgptChat', { params: params, ...config })}
