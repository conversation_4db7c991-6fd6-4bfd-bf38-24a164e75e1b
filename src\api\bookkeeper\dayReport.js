import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_BookKeeper}/dayReport/`

//日报-日报确认-查询
export const getPageDailyReportItemConfirmList = (params, config = {}) => { return request.get(apiPrefix + 'GetPageDailyReportItemConfirmList', { params, ...config }) }

//日报-日报确认-保存
export const insertDailyReportItemConfirmList = (params, config = {}) => { return request.post(apiPrefix + 'InsertDailyReportItemConfirmList', params, config) }


//日报-日报数据核对-查询
export const checkDayReportItem = (params, config = {}) => { return request.get(apiPrefix + 'CheckDayReportItem', { params, ...config }) }

//日报-未确认店铺-保存
export const saveNoConfirmShopNames = (params, config = {}) => { return request.post(apiPrefix + 'SaveNoConfirmShopNames', params, config) }

//日报-未确认店铺获取
export const getNoConfirmShopNames = (params, config = {}) => { return request.get(apiPrefix + 'GetNoConfirmShopNames', { params, ...config }) }
