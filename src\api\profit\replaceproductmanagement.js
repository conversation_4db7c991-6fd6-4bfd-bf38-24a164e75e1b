import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Profit}/replaceproductmanagement/`


//获取代拍日报数据
export const getReplaceProductManagement = (params,config ={}) =>{
    return request.get(apiPrefix+'GetReplaceProductManagementAsync', {params: params, ...config})
}



//导入代发产品管理
export const importReplaceProductManagement = (params,config ={}) =>{
    return request.post(apiPrefix + 'ImportReplaceProductManagementAsync', params, config)
}

export const editReplaceProductManagement = (params, config = {}) => {
    return request.post(apiPrefix + 'EditReplaceProductManagementAsync',  params, config )
  }