import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/businessOpportunity/`

export const getPageListAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetPageListAsync', { params, ...config })}
export const getcategoryList = (params, config = {}) => { return request.get(apiPrefix + 'GetCategoryList', { params, ...config })}

export const getPlanningTypeDescList = (params, config = {}) => { return request.get(apiPrefix + 'GetPlanningTypeDescList', { })}
export const getCategoryListAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetCategoryListAsync', { params, ...config })}

