import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Kj}/Cost/`

//财务类型下拉 获取财务费用类型(系统配置)
export const GetFinanceFeeTypes =(params,config) =>{return request.get(apiPrefix + 'GetFinanceFeeTypes',{ params: params, ...config })}

//日报类型下拉  获取日报费用类型(系统配置)
export const getDailyReportFeeNames =(params,config) =>{return request.get(apiPrefix + 'getDailyReportFeeNames',{ params: params, ...config })}

//财务类型维护分页列表 财务类型列表
export const getCostBillFinanceTypePageList =(params,config) =>{return request.post(apiPrefix + 'getCostBillFinanceTypePageList',params,config)}

//新增财务类型  新增 系统配置字典（财务类型）
export const createCofingFinanceType =(params,config) =>{return request.post(apiPrefix + 'createCofingFinanceType',params,config)}

//财务类型更新  列表：修改财务、日报类型
export const updateCostBillFinanceType =(params,config) =>{return request.post(apiPrefix + 'updateCostBillFinanceType',params,config)}

//更新财务类型 更新尾程费用列表财务类型
export const updateBillFinanceTypeItem =(params,config) =>{return request.post(apiPrefix + 'updateBillFinanceTypeItem',params,config)}

//财务类型维护  尾程费用财务类型数据更新记录日志
export const getUpdateFinanceTypeLogPageList  =(params,config) =>{return request.post(apiPrefix + 'getUpdateFinanceTypeLogPageList',params,config)}

//各仓尾程费  尾程费用财务类型变更日志
export const getUpdateBillFinanceTypeLogPageList   =(params,config) =>{return request.post(apiPrefix + 'getUpdateBillFinanceTypeLogPageList',params,config)}

//订单费用分类
// export const getFeeNames =(type,tableExtend,params,config) =>{return request.get(apiPrefix + 'GetFeeNames?thirdPlatform='+type+'&tableExtend='+tableExtend,params,config)}
// export const getFeeNames =(type,params,config) =>{return request.get(apiPrefix + 'GetFeeNames?thirdPlatform='+type,params,config)}
export const getFeeNames = (params, config = {}) => { return request.get(apiPrefix + 'GetFeeNames', { params: params, ...config })}
//订单费用账单
export const getCostBillPageList =(params,config) =>{return request.post(apiPrefix + 'getCostBillPageList',params,config)}

//导出费用账单数据
export const exportCost =(params,config={responseType: 'blob'}) =>{return request.post(apiPrefix + 'export',params,config)}


//获取订单日报汇总趋势图数据
// export const costBillSumChart = (params, config = {}) => { return request.get(apiPrefix + 'costBillSumChart', { params, ...config }) }
export const costBillSumChart =(params,config) =>{return request.post(apiPrefix + 'costBillSumChart',params,config)}


//头程费用账单
export const getShipmentLogisticsAccount =(params,config) =>{return request.post(apiPrefix + 'getShipmentLogisticsAccount',params,config)}

//头程费用分类
export const getLogisticsAccountsFeeNames = (params, config = {}) => { return request.get(apiPrefix + 'GetLogisticsAccountsFeeNames', { params: params, ...config })}
  
///头程费用导出费用账单数据
export const exportShipmentLogisticsAccount =(params,config={responseType: 'blob'}) =>{return request.post(apiPrefix + 'exportShipmentLogisticsAccount',params,config)}

///头程费用趋势图
export const logisticsAccountsSumChart =(params,config) =>{return request.post(apiPrefix + 'logisticsAccountsSumChart',params,config)} 

//订单费用分类
export const getfinanceFeeNameList =(type,params,config) =>{return request.get(apiPrefix + 'getFinanceFeeNames?thirdPlatform='+type,params,config)}

export const importCostBill = (params, config = {}) => { return request.post(apiPrefix + 'importCostBill', params, config) }

//海外仓 删除导入的尾程费用 区间删除
export const deleteImportCostBill = (params, config = {}) => {
    return request.post(apiPrefix + 'deleteImportCostBill', params, config)
  }

  //海外仓 删除导入的尾程费用  批量删除
export const deleteCostBill = (params, config = {}) => {
  return request.post(apiPrefix + 'deleteCostBill', params, config)
}

//导入更新赤道重量
export const importUpdateSogoodsellerCostBill = (params, config = {}) => {
  return request.post(apiPrefix + 'importUpdateSogoodsellerCostBill', params, config)
}

//尾程费用汇总列表查询
export const getCostBillCombinePageList = (params, config) => { return request.post(apiPrefix + 'getCostBillCombinePageList', params, config) }

//尾程费用汇总 导出
export const exportCombineList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'exportCombineList', params, config) }

//尾程费用汇总 费用；列表
export const getFinanceFeeNames = (params, config = {}) => { return request.get(apiPrefix + 'getFinanceFeeNames', { params: params, ...config }) }
