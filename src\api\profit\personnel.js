import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API}/deptmentUserInfo/`

// //导出订单
// export const exportOrderMenuManageAsync =(params,config ={responseType: 'blob'}) =>{
//     return request.get(apiPrefix + 'ExportOrderMenuManageAsync',{params: params, ...config})
// }

// //导入菜单
// export const importBaseMenuAsync = (params,config ={}) =>{
//     return request.post(apiPrefix + 'ImportBaseMenuAsync', params, config)
// }

//获取所有钉钉部门
export const getALLDDDeptTree = (params,config ={}) =>{
    return request.get(apiPrefix+'ALLDDDeptTree', {params: params, ...config})
}
//获取所有钉钉部门，仅南昌武汉
export const AllDDDeptTreeNcWh = (params,config ={}) =>{
    return request.get(apiPrefix+'AllDDDeptTreeNcWh', {params: params, ...config})
}
//获取所有列表组织
export const getALLDDDeptList = (params,config ={}) =>{
    return request.get(apiPrefix+'AllDDDeptList', {params: params, ...config})
}
//获取部门成员
export const getDeptUsers = (params,config ={}) =>{
    return request.get(apiPrefix+'GetDeptUsers', {params: params, ...config})
}
//获取所有钉角色
export const getDingRoles = (params,config ={}) =>{
    return request.get(apiPrefix+'GetDingRoles', {params: params, ...config})
}
//获取所有钉角色 树
export const getDingRolesTree = (params,config ={}) =>{
    return request.get(apiPrefix+'GetDingRolesTree', {params: params, ...config})
}


//获取跨部门bug登记数据
export const getBugRegister = (params,config ={}) =>{
    return request.get(apiPrefix+'GetBugRegisterList', {params: params, ...config})
}
//获取跨部门bug登记日志
export const getBugRegisterLog = (params,config ={}) =>{
    return request.get(apiPrefix+'GetBugRegisterLogList', {params: params, ...config})
}



