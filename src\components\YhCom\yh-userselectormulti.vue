<template>
    <el-select v-model="innerValue" multiple collapse-tags :clearable="clearable" filterable remote :style="cststyle" reserve-keyword :placeholder="placeholder" 
        :remote-method="remoteMethod" @change="valueChanged($event)"  :loading="loading">
        <el-option v-for="item in options" :key="selectKey+item.value+ item.extData.defaultDeptId" :label="item.label" :value="item.value">
            <span>{{item.label}}</span>
            <span style=" color: #8492a6; " v-show="item.extData.empStatusText">
                ({{item.extData.position}},{{item.extData.empStatusText}}{{item.extData.jstUserName ? ","+item.extData.jstUserName:""}})
            </span>           
            <span style=" color: #8492a6; "> {{item.extData.deptName}}</span>
        </el-option>
        <!-- <el-option v-if="value && !options.find(x=>x.value==value)" :key="'userSelector_dft'+(value==null?'':value)" :label="text" :value="value">
        </el-option> -->
    </el-select>

</template>

<script>     

    import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
    export default {
        name: 'YhUserelectorMulti',
        props: {
            placeholder: {
                type: String,
                default() {
                    return "请输入"
                }
            },
            rows: {
                type: Number,
                default: 50
            },
            value: {
                type: Array,
                default() {
                    return null;
                }
            },
            text: {
                type: [Array,String],
                validator(value) {   
                    return Array.isArray(value) || typeof value === 'string';  
                },  
                default() {
                    return null
                }
            },
            clearable: {
                type: Boolean,
                default() { return true; }
            },
            cststyle: {
                type: Object,
                default() {
                    return {}
                }
            },
            selectKey: {
                type: String,
                default() {
                    return ""
                }
            },
        },
        data() {
            return {
                loading: false,
                options: [],
                innerValue: "",
                innerText: "",
                orgOptions: []
            }
        },
        computed: {     
            normalizedText() {  
                return Array.isArray(this.text) ? this.text : [this.text];  
            }        
        },       
        async mounted() {
            this.orgOptions = [...this.options];
            this.$nextTick(() => {
                if (this.value) {
                    let text = this.normalizedText;
                    for (let index = 0; index < this.value.length; index++) {
                        const newOption = {
                            label: text[index],
                            value: this.value[index],
                            extData: { defaultDeptId: null, position: null, empStatusText: null, jstUserName: null, deptName: null }
                        };
                        const existingOption = this.options.find(option => option.value === newOption.value);
                        if (!existingOption) {
                            this.options.push(newOption);
                        }
                    }
                    this.innerValue = this.value;
                }  
            });
        },
        methods: {
            valueChanged(newValue) {
                let findOpts=null;
                let labelValue = newValue;
                if (newValue) {
                    findOpts = this.options.filter(item => { return item.value == labelValue; });
                    if (findOpts && findOpts.length > 0) {
                        labelValue = findOpts[0].label;
                    }
                }
                this.$emit("update:value", newValue);
                this.$emit("update:text", labelValue);
                this.$emit("change",findOpts);
            },
            async remoteMethod(query) {
                if (query && query.length > 50) return this.$message.error("输入内容过长");
                this.loading = true;
                if (query !== '') {                  
                    let rlt= await QueryAllDDUserTop100({ keywords: query });
                    if (rlt && rlt.success) {
                        this.options = rlt.data?.map(item => {
                            return { label: item.userName, value: item.ddUserId, extData:item }
                        });
                    }
                } else {
                    this.options = [...this.orgOptions];
                }
                this.loading = false;
            },
        }
    }
</script>

