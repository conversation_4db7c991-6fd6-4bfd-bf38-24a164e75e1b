import request from '@/utils/request'
const GroupPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/DamagedOrders/`

//破损订单查询
export const getDamagedOrdersStatistics = (params, config = {}) => { return request.post(GroupPrefix + 'GetDamagedOrdersStatistics', params,config  )}

//导出破损订单excel
export const exportDamagedOrdersStatistics = (params, config = {responseType: 'blob'}) => { return request.post(GroupPrefix + 'ExportDamagedOrdersStatistics', params,config  )}


//导入破损订单excel
export const importDamagedOrders = (params, config = {}) => { return request.post(GroupPrefix + 'ImportDamagedOrders', params,config  )}

//破损订单趋势图
export const getStaticticsChatData = (params, config = {}) => { return request.post(GroupPrefix + 'GetStaticticsChatData', params,config  )}

//分页获取责任扣款责任申诉数据
export const pageDeductZrAppealList = (params, config = {}) => { return request.post(GroupPrefix + 'PageDeductZrAppealList', params,config  )}

//导入破损订单(补发)
export const importDamagedOrdersBF = (params, config = {}) => { return request.post(GroupPrefix + 'ImportDamagedOrdersBF', params,config  )}

//导入导入破损订单（红包）
export const importDamagedOrdersHB = (params, config = {}) => { return request.post(GroupPrefix + 'ImportDamagedOrdersHB', params,config  )}

//导入导入破损订单（其他）
export const importDamagedOrdersQT = (params, config = {}) => { return request.post(GroupPrefix + 'ImportDamagedOrdersQT', params,config  )}

//统计查询
export const getDamagedOrdersStatisAsync = (params, config = {}) => { return request.post(GroupPrefix + 'GetDamagedOrdersStatisAsync', params,config  )}

//统计查询趋势图
export const getDamagedOrdersStatisAnlysisAsync = (params, config = {}) => { return request.post(GroupPrefix + 'GetDamagedOrdersStatisAnlysisAsync', params,config  )}

//定责数据明细
export const getDamagedOrdersWithholdListAsync = (params, config = {}) => { return request.post(GroupPrefix + 'GetDamagedOrdersWithholdListAsync', params,config  )}

//获取责任部门
export const getDamagedOrdersZrDept = (params, config = {}) => { return request.post(GroupPrefix + 'GetDamagedOrdersZrDept', params,config  )}

//获取责任类型
export const getDamagedOrdersZrType = (params, config = {}) => { return request.post(GroupPrefix + 'GetDamagedOrdersZrType?zrDeptName='+params,config  )}

//获取申诉详情
export const getDeductZrAppeal4CRUD = (params, config = {}) => { return request.post(GroupPrefix + 'GetDeductZrAppeal4CRUD?orderNo='+params.orderNo+'&goodsCode='+params.goodsCode+'&id='+params.id, config  )}

//点击保存指派
export const setZrMemberCustomize = (params, config = {}) => { return request.post(GroupPrefix + 'SetZrMemberCustomize', params,config  )}

//点击保存申诉
export const saveDeductZrAppeal = (params, config = {}) => { return request.post(GroupPrefix + 'SaveDeductZrAppeal', params,config  )}

//点击批量
export const batchDeductZrAppeal = (params, config = {}) => { return request.post(GroupPrefix + 'BatchDeductZrAppeal', params,config  )}

//审核扣款责任申诉数据
export const auditDeductZrAppeal = (params, config = {}) => { return request.post(GroupPrefix + 'AuditDeductZrAppeal', params, config  )}

//外包材数据
export const firstAuditDeductZrAppeal = (params, config = {}) => { return request.post(GroupPrefix + 'FirstAuditDeductZrAppeal?id='+params.id+'&auditState='+params.auditState+'&auditRemark='+params.auditRemark,  config  )}

//是否认可
export const firstAuditDeductZrTrans2SameDept = (params, config = {}) => { return request.post(GroupPrefix + 'FirstAuditDeductZrTrans2SameDept', params, config  )}


//批量审核扣款责任申诉数据
export const batchAuditDeductZrAppeal = (params, config = {}) => { return request.post(GroupPrefix + 'BatchAuditDeductZrAppeal', params, config  )}

//责任申诉导出
export const exportDeductZrAppealAsync = (params, config = {responseType: 'blob'}) => { return request.post(GroupPrefix + 'ExportDeductZrAppealAsync', params, config  )}

//统计查询导出
export const exportDamagedOrdersStatisAsync = (params, config = {responseType: 'blob'}) => { return request.post(GroupPrefix + 'ExportDamagedOrdersStatisAsync', params, config  )}

//定责导出
export const exportDamagedOrdersWithholdAsync = (params, config = {responseType: 'blob'}) => { return request.post(GroupPrefix + 'ExportDamagedOrdersWithholdAsync', params, config  )}

//批量删除提示
export const deleteDamagedOrderBefore = (params, config = {}) => { return request.post(GroupPrefix + 'DeleteDamagedOrderBefore', params, config  )}

//批量删除导入前
export const deleteDamagedOrder = (params, config = {}) => { return request.post(GroupPrefix + 'DeleteDamagedOrder', params, config  )}

//维度查询
export const getDamagedOrdersWithholdListGroupAsync = (params, config = {}) => { return request.post(GroupPrefix + 'GetDamagedOrdersWithholdListGroupAsync', params, config  )}

//定责数据明细导出
export const exportDamagedOrdersWithholdListGroupAsync = (params, config = {responseType: 'blob'}) => { return request.post(GroupPrefix + 'ExportDamagedOrdersWithholdListGroupAsync', params, config  )}

//编辑保存订单
export const editDamagedOrders = (params, config = {}) => { return request.post(GroupPrefix + 'EditDamagedOrders', params, config  )}

//查询订单修改记录
export const getDamagedOrderLogs = (params, config = {}) => { return request.post(GroupPrefix + 'GetDamagedOrderLogs', params, config  )}

//统计查询后,行内点击查询
export const getDamagedOrdersStatisRowPageAsync = (params, config = {}) => { return request.post(GroupPrefix + 'GetDamagedOrdersStatisRowPageAsync', params, config  )}

//点击图片
export const GetDamagedOrdersStatisRowPageDtlAsync = (params, config = {}) => { return request.post(GroupPrefix + 'GetDamagedOrdersStatisRowPageDtlAsync', params, config  )}

//点击系列编码
export const getDamagedOrdersRowDetailStatisListAsync = (params, config = {}) => { return request.post(GroupPrefix + 'GetDamagedOrdersRowDetailStatisListAsync', params, config  )}

//RPA导入
export const importDamagedOrdersRPA = (params, config = {}) => { return request.post(GroupPrefix + 'ImportDamagedOrdersRPA', params,config  )}

//得到责任人损耗通知信息数据
export const getDamagedOrderMemberNoticeData = (params, config = {}) => request.get(GroupPrefix + 'GetDamagedOrderMemberNoticeData', { params: params, ...config })

//破损订单商品编号行明细统计查询
export const getDamagedOrdersGoodsDetailStatisListAsync = (params, config = {}) => { return request.post(GroupPrefix + 'GetDamagedOrdersGoodsDetailStatisListAsync', params, config  )}

//得到损耗订单成本的情况说明信息
export const getDamagedOrderExplainAsync = (params, config = {}) => { return request.post(GroupPrefix + 'GetDamagedOrderExplainAsync', params, config  )}

//保存损耗订单成本的情况说明
export const saveDamagedOrderExplain = (params, config = {}) => { return request.post(GroupPrefix + 'SaveDamagedOrderExplain', params, config  )}

//查询损耗订单操作记录
export const queryDamagedOrderExplainRecordAsync = (params, config = {}) => { return request.post(GroupPrefix + 'QueryDamagedOrderExplainRecordAsync', params, config  )}

//查询破损聊天核查
export const getDamagedOrderChatList = (params, config = {}) => { return request.post(GroupPrefix + 'GetDamagedOrderChatList', params, config  )}

//查询破损聊天核查-获取聊天记录
export const getDamagedOrderChatRerordByOrderNo = (params, config = {}) => { return request.post(GroupPrefix + 'GetDamagedOrderChatRerordByOrderNo', params, config  )}

//查询破损聊天核查-获取问题类型
export const GetAllDamagedOrdersZrType = (params, config = {}) => { return request.post(GroupPrefix + 'GetAllDamagedOrdersZrType', params, config  )}
