<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <link rel="stylesheet" href="/approvalform/html/api/elment.css">
    <script src="/approvalform/html/api/vue.min.js"></script>
    <script src="/approvalform/html/api/elment.js"></script>
    <script src="/approvalform/html/api/jquery.min.js"></script>
    <script src="/approvalform/html/api/html2canvas.js"></script>
    <title>采购退货出库</title>
</head>

<body>
    <div id="app" style="margin:0 auto;overflow-y: hidden;">
        <div style="display: flex;gap: 10px;color: red;">
          <div>
            <span>货物总成本汇总:</span>
            <span>{{totalGoodsCost}}</span>
          </div>
          <div>
            <span>退款总成本汇总:</span>
            <span>{{totalReturnCost}}</span>
          </div>
          <div>
            <span>折扣比例:</span>
            <span>{{discountRatio}}</span>
          </div>
        </div>
        <el-container direction="vertical" style=" border: 1px #ccc solid;">
            <template>
                <el-table ref="tableBox" :data="data" align="center" style="width: 100%;" row-key="id" border
                    :max-height="tableHeight">
                    <el-table-column type="index" min-width="20" fixed></el-table-column>
                    <el-table-column prop="handlingMethod" label="处理方式"></el-table-column>
                    <el-table-column prop="styleCode" label="系列编码"></el-table-column>
                    <el-table-column prop="goodsCode" label="商品编码"></el-table-column>
                    <el-table-column prop="goodsName" label="商品名称"></el-table-column>
                    <el-table-column prop="returnQty" label="退货数量"></el-table-column>
                    <el-table-column prop="actualQty" label="厂家实收数量"></el-table-column>
                    <el-table-column prop="price" label="聚水潭成本"></el-table-column>
                    <el-table-column prop="returnPrice" label="退货单价"></el-table-column>
                    <el-table-column prop="totalCost" label="货物总成本"></el-table-column>
                    <el-table-column prop="returnCost" label="退款总成本"></el-table-column>
                </el-table>
            </template>
        </el-container>
    </div>
    <script>
        var vm = new Vue({
            el: '#app',
            data: function () {
                return {
                    totalGoodsCost: 0,//货物增成本汇总
                    totalReturnCost: 0,//退款总成本汇总
                    discountRatio: 0,//折扣比例
                    thisLonding: true,
                    data: [],
                    tableHeight: null,
                    typeId: null,
                    currentPage: 1,
                    pageSize: 20,
                    total: 0,
                    title: ''
                }
            },
            created() {

            },
            async mounted() {
                this.getStyleSheetInfo();
                this.beginShowing()
            },
            methods: {
                beginShowing() {
                    this.$nextTick(function () {
                        // 文档显示区域的高度 -
                        if (this.$refs.tableBox) {
                            this.tableHeight = 835;
                            this.$refs.tableBox.doLayout()
                        }
                    })
                },
                async getStyleSheetInfo() {
                    var me = this;
                    let searchURL = window.location.search;
                    searchURL = searchURL.substring(1, searchURL.length);
                    this.batchNo = searchURL.split("&")[0].split("=")[1];
                    me.thisLonding = true;
                    let parm = {};
                    $.ajax({
                        url: '/api/inventory/PurchaseReturnExWarehouse/GetPurchaseReturnExWarehouseH5List?batchNo=' + this.batchNo,
                        type: 'GET',
                        dataType: 'json',
                        success: function ({ data }) {
                            me.data = data.goodsList;
                            me.totalGoodsCost = data.totalGoodsCost || 0;
                            me.totalReturnCost = data.totalReturnCost || 0;
                            me.discountRatio = data.discountRatio || 0;
                        },
                        error: function (xhr, textStatus, errorThrown) {
                            console.log('Error: ', errorThrown);
                        }
                    });
                },
            }
        });
    </script>
</body>

</html>
