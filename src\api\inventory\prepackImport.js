import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_ImportInventory}/prePack/`

//第一页面导入
// export const importGoodsSaleAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ImportGoodsSaleAsync', params, config) }
export const importGoodsSaleAsync = (params, config = {}) => {return request.post(apiPrefix + 'ImportGoodsSaleAsync', params, config)}


//第二页面导入
// export const importBaseDatasAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ImportBaseDatasAsync', params, config) }
export const importBaseDatasAsync = (params, config = {}) => {return request.post(apiPrefix + 'ImportBaseDatasAsync', params, config)}

//导入重量
export const importBaseDatasUpdateAsync = (params, config = {}) => {return request.post(apiPrefix + 'ImportBaseDatasUpdateAsync', params, config)} 

//导入预包库存
export const importPreGoodsInventoryAsync = (params, config = {}) => {return request.post(apiPrefix + 'ImportPreGoodsInventoryAsync', params, config)} 
