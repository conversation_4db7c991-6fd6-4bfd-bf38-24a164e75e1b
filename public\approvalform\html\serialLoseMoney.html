<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <link rel="stylesheet" href="/approvalform/html/api/elment.css">
    <script src="/approvalform/html/api/vue.min.js"></script>
    <script src="/approvalform/html/api/elment.js"></script>
    <script src="/approvalform/html/api/jquery.min.js"></script>
    <script src="/approvalform/html/api/html2canvas.js"></script>
    <title>系列编码持续亏损</title>
    <style type="text/css">
        .linebreak {
            overflow: hidden;
            /*超出部分隐藏*/
            text-overflow: ellipsis;
            /* 超出部分显示省略号 */
            white-space: nowrap;
            /*规定段落中的文本不进行换行 */
            width: 100%;
        }
        .el-pagination__jump {
            margin-left:0px;
        }
    </style>
</head>

<body>
    <div id="app" style="margin:0 auto;overflow-y: hidden;">
        <el-container direction="vertical" style=" border: 1px #ccc solid;">
            <template>

                <el-table ref="tableBox" :data="list" align="center" style="width: 100%;" row-key="id" border
                    :max-height="tableHeight">
                    <el-table-column type="index" min-width="20" fixed></el-table-column>
                    <el-table-column prop="goodsCode" label="商品编码" min-width="100"></el-table-column>
                    <el-table-column prop="day30OrderNum" label="近30天订单量" min-width="100"></el-table-column>
                    <el-table-column prop="day30Sales" label="近30天销量" min-width="100"></el-table-column>
                    <el-table-column prop="day30SalesRevenue" label="近30天销售额" min-width="100"></el-table-column>
                    <el-table-column prop="day30Profit3" label="毛三利润" min-width="100"></el-table-column>
                    <el-table-column prop="profit3Rate" label="毛三利润率" min-width="100"></el-table-column>
                    <el-table-column prop="applyQty" label="进货量" min-width="100"></el-table-column>

                </el-table>

                <el-table ref="tableBox" :data="list1" align="center" style="width: 100%;margin-top: 20px;" row-key="id" border
                :max-height="tableHeight">
                <el-table-column type="index" min-width="20" fixed></el-table-column>
                <el-table-column prop="proCode" label="产品ID" min-width="100"></el-table-column>
                <!-- <el-table-column prop="goodsCode" label="商品编码" min-width="100">
                    <template slot-scope="scope">
                        <el-tooltip class="linebreak" effect="dark" :content="scope.row.goodsCode" placement="top-start">
                            <div class="linebreak">
                                {{ scope.row.goodsCode }}
                            </div>
                          </el-tooltip>
                    </template>
                </el-table-column> -->
                <el-table-column prop="day30OrderNum" label="近30天订单量" min-width="100"></el-table-column>
                <el-table-column prop="day30SalesRevenue" label="近30天销售额" min-width="100"></el-table-column>
                <el-table-column prop="day30Profit3" label="近30天毛三利润" min-width="100"></el-table-column>
                <el-table-column prop="profit3Rate" label="近30天毛三利率" min-width="100">
                    <template slot-scope="scope">
                        <div>
                            {{ scope.row.profit3Rate }}%
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="onTime" label="上架时间" min-width="100"></el-table-column>
                <el-table-column prop="onDays" label="上架天数" min-width="100"></el-table-column>
                <el-table-column prop="lastMonthProfit3" label="上月毛三" min-width="100"></el-table-column>
                <el-table-column prop="lastMonthProfit3Rate" label="上月毛三率" min-width="100"></el-table-column>
            </el-table>

            </template>
        </el-container>
    </div>
    <script>
        var vm = new Vue({
            el: '#app',
            data: function () {
                return {
                    thisLonding: true,
                    list: [{}],
                    list1: [],
                    tableHeight: null,
                    typeId: null,
                    currentPage: 1,
                    pageSize: 20,
                    total: 0,
                    title: ''
                }
            },
            created () {

            },
            async mounted () {
                this.getStyleSheetInfo();
                this.beginShowing()
            },
            methods: {
                handleSizeChange (val) {
                    this.pageSize = val;
                    this.getStyleSheetInfo();
                },
                handleCurrentChange (val) {
                    this.currentPage = val;
                    this.getStyleSheetInfo();
                },
                beginShowing () {
                    this.$nextTick(function () {
                        // 文档显示区域的高度 -
                        if (this.$refs.tableBox) {
                            this.tableHeight = 835;
                            this.$refs.tableBox.doLayout()
                        }
                    })
                },
                async getStyleSheetInfo () {
                    var me = this;
                    let searchURL = window.location.search;
                    searchURL = searchURL.substring(1, searchURL.length);
                    this.applyId = searchURL.split("&")[0].split("=")[1];
                    me.thisLonding = true;
                    let parm = {};
                    $.ajax({
                        url: '/api/OperateManage/ContinuLosses/GetApplyDetailData?applyId='+this.applyId,
                        type: 'POST',
                        dataType: 'json',
                        data: parm,
                        success: function (response) {
                            me.list1 = response.data.proList;
                            me.list = response.data.goodsList;
                            // me.total = response.data.total;
                            // me.title = response.data.title;
                        },
                        error: function (xhr, textStatus, errorThrown) {
                            console.log('Error: ', errorThrown);
                        }
                    });
                },
            }
        });
    </script>
</body>

</html>
