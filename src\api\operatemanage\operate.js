import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/operate/`
export const seachProCode = (params, config = {}) => {
  return request.get(apiPrefix + 'SeachProCodeAsync', { params: params, ...config })
}
export const getJpKeyWordST = (params, config = {}) => {
  return request.get(apiPrefix + 'GetJpKeyWordSTAsync', { params: params, ...config })
}
export const getAllJpProducts = (params, config = {}) => {
  return request.get(apiPrefix + 'GetAllJpProductsAsync', { params: params, ...config })
}
export const getAllKeyWord = (params, config = {}) => {
  return request.get(apiPrefix + 'GetAllKeyWordAsync', { params: params, ...config })
}
export const productMonitorAnalysis = (params, config = {}) => {
    return request.post(apiPrefix + 'ProductMonitorAnalysisAsync', params, config)
}
export const productMonitorAnalysis2 = (params, config = {}) => {
  return request.post(apiPrefix + 'ProductMonitorAnalysis2Async', params, config)
}
export const hotWordsMonitorAnalysis = (params, config = {}) => {
  return request.post(apiPrefix + 'HotWordsMonitorAnalysisAsync', params, config)
}
export const rankListProductMonitorAnalysis = (params, config = {}) => {
  return request.post(apiPrefix + 'RankListProductMonitorAnalysisAsync', params, config)
}
export const rankListProductAllMonitorAnaly = (params, config = {}) => {
  return request.post(apiPrefix + 'RankListProductAllMonitorAnalysisAsync', params, config)
}
export const rankListProductMonitor = (params, config = {}) => {
  return request.get(apiPrefix + 'RankListProductMonitorAsync', { params: params, ...config })
}
export const pageProductMonitor = (params, config = {}) => {
  return request.get(apiPrefix + 'PageProductMonitorAsync', { params: params, ...config })
}
export const productChangeList = (params, config = {}) => {
  return request.get(apiPrefix + 'ProductChangeListAsync', { params: params, ...config })
}
export const pageProductKeyWordOrder = (params, config = {}) => {
  return request.post(apiPrefix + 'PageProductKeyWordOrderAsync', params, config)
}
export const productKeyWordOrderAnalysis = (params, config = {}) => {
  return request.post(apiPrefix + 'ProductKeyWordOrderAnalysisAsync', params, config)
}
export const productKeyWordSTAnalysis = (params, config = {}) => {
    return request.post(apiPrefix + 'ProductKeyWordSTAnalysisAsync', params, config)
}
export const productKeyWordSTList = (params, config = {}) => {
    return request.get(apiPrefix + 'ProductKeyWordSTListAsync', { params: params, ...config })
}
export const productKeyWordZTCAnalysis = (params, config = {}) => {
  return request.post(apiPrefix + 'ProductKeyWordZTCAnalysisAsync', params, config)
}
export const productKeyWordZTCList = (params, config = {}) => {
  return request.get(apiPrefix + 'ProductKeyWordZTCListAsync', { params: params, ...config })
}
export const productKeyWordFancyPKAnalysis = (params, config = {}) => {
  return request.get(apiPrefix + 'ProductKeyWordFancyPKAnalysisAsync', { params: params, ...config })
}
export const pageProductKeyWordST = (params, config = {}) => {
  return request.get(apiPrefix + 'PageProductKeyWordSTAsync', { params: params, ...config })
}
export const pageProductKeyWordZTC = (params, config = {}) => {
  return request.get(apiPrefix + 'PageProductKeyWordZTCAsync', { params: params, ...config })
}
export const importProductMonitor = (params, config = {}) => {
  return request.post(apiPrefix + 'ImportProductMonitorAsync', params, config)
}
export const exportProductMonitor = (params, config = {responseType: 'blob'}) => {
  return request.get(apiPrefix + 'ExportProductMonitorAsync',  { params: params, ...config })
}
export const exportProductKeyWordST = (params, config = {responseType: 'blob'}) => {
  return request.get(apiPrefix + 'ExportProductKeyWordSTAsync',  { params: params, ...config })
}
export const exportProductKeyWordZTC = (params, config = {responseType: 'blob'}) => {
  return request.get(apiPrefix + 'ExportProductKeyWordZTCAsync',  { params: params, ...config })
}

// 工商投诉登记-查询
export const getBusinessComplaintRegister = (params, config = {}) => {
  return request.post(apiPrefix + 'GetBusinessComplaintRegister', params, config)
}

// 工商投诉登记-导出
export const exportBusinessComplaintRegister = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'ExportBusinessComplaintRegister', params, config)
}

// 工商投诉登记-删除
export const deleteBusinessComplaintRegister = (params, config = {}) => {
  return request.post(apiPrefix + 'DeleteBusinessComplaintRegister', params, config)
}

// 工商投诉登记-新增/编辑
export const addBusinessComplaintRegister = (params, config = {}) => {
  return request.post(apiPrefix + 'AddBusinessComplaintRegister', params, config)
}

// 工商投诉登记-导入
export const importBusinessComplaintRegister = (params, config = {}) => {
  return request.post(apiPrefix + 'ImportBusinessComplaintRegister', params, config)
}

// 工商投诉登记-查询修改记录
export const getEditHistoryBusinessComplaintRegister = (params, config = {}) => {
  return request.post(apiPrefix + 'GetEditHistoryBusinessComplaintRegister', params, config)
}

// 工商投诉登记-获取投诉类型
export const getComplaintType = (params, config = {}) => {
  return request.post(apiPrefix + 'GetComplaintType', params, config)
}

// 工商投诉登记-查询产品ID附带信息
export const getAttachProCodeData = (params, config = {}) => {
  return request.get(apiPrefix + 'GetAttachProCodeData', { params: params, ...config })
}
// 工商投诉登记-查询商品编码
export const getAttachGoodsCodeData = (params, config = {}) => {
  return request.get(apiPrefix + 'GetAttachGoodsCodeData', { params: params, ...config })
}
