import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_JAVA_API_XZGATEWAY}/yh-xz-salary/`

// 查询员工列表
export const employeeList = (params, config = {}) => { return request.post(apiPrefix + 'employeeList', params, config) }


//合计汇总趋势图
export const totalNumberTrendChart = (params, config = {}) => {return request.post(apiPrefix + 'totalNumberTrendChart',  params,  config )}


//字典列表
export const dictionary = (params, config = {}) => {return request.post(apiPrefix + 'dictionary?code='+params,  params,  config )}

//获取列表下拉
export const departmentRelationList = (params, config = {}) => {return request.post(apiPrefix + 'departmentRelationList',  params,  config )}

//获取所有区域 传1
export const departmentListByParentId = (params, config = {}) => {return request.post(apiPrefix + 'departmentListByParentId',  params,  config )}