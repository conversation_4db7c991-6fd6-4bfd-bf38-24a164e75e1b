import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/operate/`

//查询
export const getBrushOrderProcess = (params, config = {}) => {
    return request.post(apiPrefix + 'GetBrushOrderProcess', params, config)
}

//导出
export const exportBrushOrderProcess = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportBrushOrderProcess', params, config)
}

//批量认领
export const batchClaimOrder = (params, config = {}) => {
    return request.post(apiPrefix + 'BatchClaimOrder', params, config)
}

//创建
export const createBrushOrder = (params, config = {}) => {
    return request.post(apiPrefix + 'CreateBrushOrder', params, config)
}

//批量完成
export const batchFinishBrushOrder = (params, config = {}) => {
    return request.post(apiPrefix + 'BatchFinishBrushOrder', params, config)
}

//批量删除
export const batchDeleteBrushOrder = (params, config = {}) => {
    return request.post(apiPrefix + 'BatchDeleteBrushOrder', params, config)
}

//删除
export const deleteBrushOrder = (params, config = {}) => {
    return request.post(apiPrefix + 'DeleteBrushOrder', params, config)
}

//完成
export const finishBrushOrder = (params, config = {}) => {
    return request.post(apiPrefix + 'FinishBrushOrder', params, config)
}

//获取认领人：运营部-拼多多组-运营中台 下所有人
export const getClaimUserList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetClaimUserList', params, config)
}

//获取创建人：运营部-拼多多组 下所有人
export const getCreateUserList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetCreateUserList', params, config)
}
