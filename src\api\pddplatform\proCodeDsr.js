import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_PddPlatform}/ProCodeDsr/`

export const pageGetDsrs = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetDsrs', params, config)
}   

export const getTrendChart = (params, config = {}) => {
    return request.post(apiPrefix + 'GetTrendChart', params, config)
}

export const Export = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'Export', params, config)
}