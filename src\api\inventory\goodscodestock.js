import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/goodscodestock/`
const apiimportPrefix = `${process.env.VUE_APP_BASE_API_ImportInventory}/goodscodestock/`

//获取数据
export const pageGoodsCodeStock = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGoodsCodeStockAsync', params, config)
}

//明细数据
export const pageProCodeProfit = (params, config = {}) => {
    return request.get(apiPrefix + 'PageProCodeProfitAsync', { params: params, ...config })
}

// 获取图表
export const getPurOrderAnalysis = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPurOrderAnalysisAsync', { params: params, ...config })
}

// 获取图表 运营权限过滤
export const getPurOrderAnalysisForOperate = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPurOrderAnalysisForOperateAsync', { params: params, ...config })
}

// 导入数据
export const importGoodsCodeSellAnalyst = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportGoodsCodeSellAnalystAsync', params, config)
}

// 获取进货数据
export const getProductGoodsCode = (params, config = {}) => {
    return request.get(apiPrefix + 'GetProductGoodsCodeAsync', { params: params, ...config })
}

// 获取进货历史数据（主动申报）
export const getProductGoodsCodeHistory = (params, config = {}) => {
    return request.get(apiPrefix + 'GetProductGoodsCodeHistoryAsync', { params: params, ...config })
}

// 获取进货历史数据（认领数量）
export const getProductGoodsCodeApplyHistory = (params, config = {}) => {
    return request.get(apiPrefix + 'GetProductGoodsCodeApplyHistoryAsync', { params: params, ...config })
}

// 添加or修改申报信息
export const addOrUpdateProductGoodsCode = (params, config = {}) => {
    return request.post(apiPrefix + 'AddOrUpdateProductGoodsCodeAsync', params, config)
}

// 发起编码进货审批
export const approvalPurchaseGoods = (params, config = {}) => {
    return request.post(apiPrefix + 'ApprovalPurchaseGoodsAsync', params, config)
}

// 编码进货数据分页
export const getPurchaseGoods = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseGoodsAsync', params, config)
}


// 编码进货数据分页_运营接口
export const GetYyPurchaseGoodsAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'GetYyPurchaseGoodsAsync', params, config)
}

// 生成采购单
export const generatePurchaseOrder = (params, config = {}) => {
    return request.post(apiPrefix + 'GeneratePurchaseOrderAsync', params, config)
}

// 生成采购单
export const setPurchaseGoodsBrand = (params, config = {}) => {
    return request.post(apiPrefix + 'SetPurchaseGoodsBrandAsync', params, config)
}

export const getProfit3RateAnalysis = (params, config = {}) => {
    return request.get(apiPrefix + 'GetProfit3RateAnalysisAsync', { params: params, ...config })
}


export const getGoodsCodeRecord = (params, config = {}) => {
    return request.get(apiPrefix + 'GetGoodsCodeRecordAsync', { params: params, ...config })
}


// 生成采购单
export const purchaseNotified = (params, config = {}) => {
    return request.post(apiPrefix + 'PurchaseNotifiedAsync', params, config)
}

// 编码进货-申报 分页查询
export const PageApplyStockGoods = (params, config = {}) => {
    return request.post(apiPrefix + 'PageApplyStockGoods', params, config)
}

// 编码进货-申报
export const ApplyStockGoods = (params, config = {}) => {
    return request.post(apiPrefix + 'ApplyStockGoods', params, config)
}

// 编码进货-申报-获取更多信息
export const ApplyStockGoodsSetMoreInfo = (params, config = {}) => {
    return request.post(apiPrefix + 'ApplyStockGoodsSetMoreInfo', params, config)
}


// 分页认领数据
export const getGoodsCodeAllocation = (params, config = {}) => {
    return request.get(apiPrefix + 'GetGoodsCodeAllocationAsync', { params: params, ...config })
}


// 分页认领数据
export const getGoodsCodeAllocationList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetGoodsCodeAllocationListAsync', { params: params, ...config })
}

// 分页认领数据
export const getPurchaseOrderDetail = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPurchaseOrderDetailAsync', { params: params, ...config })
}

export const getLastPersonalInventoryTime = (params, config = {}) => {
    return request.get(apiPrefix + 'GetLastPersonalInventoryTimeAsync', { params: params, ...config })
}



// 分页查询公有可用 总表
export const PageWhStockSurplusSum = (params, config = {}) => {
    return request.post(apiPrefix + 'PageWhStockSurplusSum', params, config)
}


// 分页查询公有可用分配领取记录
export const PageWhStockSurplusFpRecord = (params, config = {}) => {
    return request.post(apiPrefix + 'PageWhStockSurplusFpRecord', params, config)
}


// 公有可用分配-通知领取
export const WhStockSurplusFpSubmit = (params, config = {}) => {
    return request.post(apiPrefix + 'WhStockSurplusFpSubmit', params, config)
}

// 公有可用分配-认领
export const WhStockSurplusFpRl = (params, config = {}) => {
    return request.post(apiPrefix + 'WhStockSurplusFpRl', params, config)
}

// 统计商品可用数变化列表
export const WhStockPersonUsableQtySumByGoodsCode = (params, config = {}) => {
    return request.get(apiPrefix + 'WhStockPersonUsableQtySumByGoodsCode', { params: params, ...config })
}

//
export const pageCollectDataDetail = (params, config = {}) => {
    return request.get(apiPrefix + 'PageCollectDataDetailAsync', { params: params, ...config })
}

// 销售主题分析列表
export const getSaleList = (params, config = {}) => {
    return request.get(apiPrefix + 'PageCodeSalesThemeAnalysisAsync', { params: params, ...config })
}
export const exportCodeSalesThemeAnalysisAsync = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportCodeSalesThemeAnalysisAsync', params, config) }

// 销售主题
export const importCodeSalesThemeAnalysis = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportCodeSalesThemeAnalysisAsync', params, config)
}

// 编码进货驳回
export const RejectPurchaseGoods = (params, config = {}) => {
    return request.post(apiPrefix + 'RejectPurchaseGoods', params, config)
}

// 记录日志
export const writeApplyStockGoodsLog = (params, config = {}) => {
    return request.get(apiPrefix + 'WriteApplyStockGoodsLog', { params: params, ...config })
}

//编码进货批量提交生成采购单 ExistsButchGeneratePurchaseOrderAsync
export const existsButchGeneratePurchaseOrder = (params, config = {}) => {
    return request.post(apiPrefix + 'ExistsButchGeneratePurchaseOrderAsync', params, config)
}

//编码下架-汇总导入 ImportBanGoodsCodeAsync
export const importBanGoodsCodeAsync = (params, config = {}) => {
    return request.post(apiimportPrefix + 'ImportBanGoodsCodeAsync', params, config)
}

//商品资料库-资质证书、合格证查询
export const checkQualificationTypeOrQualifiedImgs = (params, config = {}) => {
    return request.post(apiPrefix + 'CheckQualificationTypeOrQualifiedImgs', params, config)
}

export const IsCrossBorderDept  = (params, config = {}) => {
    return request.get(apiPrefix + 'IsCrossBorderDept', { params: params, ...config })
}
