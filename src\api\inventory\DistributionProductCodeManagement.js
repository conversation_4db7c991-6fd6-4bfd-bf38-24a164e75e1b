import request from '@/utils/request'
// import { config } from 'vue/types/umd'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/Warehouse/`

//获取状态采集器信息
export const getDistributionProductCodeStatusList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDistributionProductCodeStatusList', params, config)
}

//查询分销商品编码与我们商品编码的关系映射信息
export const getDistributionProductCodePageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDistributionProductCodePageList', params, config)
}

//导入分销商品编码商品编码与我们商品编码的关系映射信息
export const importDistributionProductCode = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportDistributionProductCode', params, config)
}

//导出分销商品编码与商品编码数据
export const exportDistributionProductCode = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportDistributionProductCode', params, config)
}

//新增分销商品编码商品编码与我们商品编码的关系映射信息
export const addDistributionProductCode = (params, config = {}) => {
    return request.post(apiPrefix + 'AddDistributionProductCode', params, config)
}


//编辑分销商品编码商品编码与我们商品编码的关系映射信息
export const updateDistributionProductCode = (params, config = {}) => {
    return request.post(apiPrefix + 'UpdateDistributionProductCode', params, config)
}

//删除分销商品编码商品编码与我们商品编码的关系映射信息
export const deleteDistributionProductCode = (params, config = {}) => {
    return request.post(apiPrefix + 'DeleteDistributionProductCode', params, config)
}

