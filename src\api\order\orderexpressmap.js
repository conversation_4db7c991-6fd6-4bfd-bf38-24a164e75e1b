import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/orderexpressmap/`

//计算
export const calcOrderExpressMap = (params,config ={}) =>{
    return request.post(apiPrefix + 'CalcOrderExpressMapAsync', params, config)
}

//分页查询
export const pageOrderExpressMap = (params,config ={}) =>{
    return request.post(apiPrefix+'PageOrderExpressMapAsync', params, config)
}

//导出
export const exportOrderExpressMap =(params,config ={responseType: 'blob'}) =>{
    return request.get(apiPrefix + 'ExportOrderExpressMapAsync',{params: params, ...config})
}

//查询汇总
export const getOrderExpressMapSummary = (params,config ={}) =>{
    return request.post(apiPrefix+'GetOrderExpressMapSummaryAsync', params, config)
}

//查询总数
export const getOrderExpressMapCount = (params,config ={}) =>{
    return request.post(apiPrefix+'GetOrderExpressMapCountAsync', params, config)
}

//删除
export const delOrderExpressMap = (params,config ={}) =>{
    return request.post(apiPrefix+'DelOrderExpressMapAsync', params, config)
}

//是否计算完成
export const isCalcOrderExpressMapFinish = (params,config ={}) =>{
    return request.post(apiPrefix+'IsCalcOrderExpressMapFinishAsync', params, config)
}
