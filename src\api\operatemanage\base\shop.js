import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/shopmanager/`

export const getList = (params, config = {}) => {return request.get(apiPrefix + 'GetShopList', { params, ...config })}

//获取店铺负责人
export const GetAllShopManager = (params, config = {}) => {return request.get(apiPrefix + 'GetAllShopManager', { params, ...config })}

export const getAllList = (params, config = {}) => { if(!params) params={};  return request.post(apiPrefix + 'GetAllShopList',  params, config)}
export const getAllListInCalc = (params, config = {}) => { return request.get(apiPrefix + 'GetAllShopListInCalc', {params, ...config})}
export const getbyid  = (id, config = {}) => { return request.get(apiPrefix + `GetModel?id=${id}`, {...config })}
export const getbycode  = (code, config = {}) => { return request.get(apiPrefix + `GetByShopCode?shopcode=${code}`, {...config })}
export const deletebyid = (id, config = {}) => { return request.delete(apiPrefix + 'DeleteShop', { params: id, ...config })}
export const addoredit = (params, config = {}) => { return request.post(apiPrefix + 'EditShop', params, config)}
export const addoreditDirector = (params, config = {}) => { return request.post(apiPrefix + 'AddOrUpdateDirector', params, config)}
export const addoreditDirectorGroup = (params, config = {}) => {return request.post(apiPrefix + 'AddOrUpdateDirectorGroup', params, config)}
export const updateDirectorGroupGzzb = (params, config = {}) => {return request.post(apiPrefix + 'UpdateDirectorGroupGzzb', params, config)}
export const getDirectorGroupAllEnabledList = (params, config = {}) => { return request.get(apiPrefix + 'GetDirectorGroupAllEnabledList', { params, ...config })}
export const getDirectorById = (id, config = {}) => {return request.get(apiPrefix + `GetDirectorById?id=${id}`, {}, config) }
export const getDirectorGroupById = (id, config = {}) => { return request.get(apiPrefix + `GetDirectorGroupById?id=${id}`, {}, config)}

export const getDirectorList = (params, config = {}) => { return request.get(apiPrefix + 'GetDirectorList', { params, ...config })}
export const getDirectorListWithDdInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetDirectorListWithDdInfo', { params, ...config })}
export const GetDirectorAllList = (params, config = {}) => { return request.get(apiPrefix + 'GetDirectorAllList', { params, ...config })}
export const getDirectorGroupList = (params, config = {}) => { return request.get(apiPrefix + 'GetDirectorGroupList', { params, ...config })}
export const getDirectorGroupList2 = (params, config = {}) => { return request.get(apiPrefix + 'GetDirectorGroupList2', { params, ...config })}

//获取主体公司
export const getMainWarehouseList = (params, config = {}) => { return request.get(apiPrefix + 'GetMainWarehouseList', { params, ...config }) }

//新增主体公司
export const addNewMainWarehouseList = (params, config = {}) => { return request.post(apiPrefix + 'AddNewMainWarehouseList', params, config) }

export const GetDirectorGroupAllList = (params, config = {}) => { return request.get(apiPrefix + 'GetDirectorGroupAllList', { params, ...config })}
export const GetDirectorGroupAllListV2 = (params, config = {}) => { return request.get(apiPrefix + 'GetDirectorGroupAllListV2', { params, ...config })}
export const getDirectorUserList = (params, config = {}) => {         return request.get(apiPrefix + 'GetDirectorUserList', { params, ...config })  }
export const getDirectorGroupUserList = (params, config = {}) => {    return request.get(apiPrefix + 'GetDirectorGroupUserList', { params, ...config })  }
export const getDirectorAndGroupUserList = (params, config = {}) => { return request.get(apiPrefix + 'GetDirectorAndGroupUserList', { params, ...config })  }
export const getDirectorAndGroupList = (params, config = {}) => {     return request.get(apiPrefix + 'GetDirectorAndGroupList', { params, ...config })  }
export const getShopKeyValue = (params, config = {}) => {             return request.get(apiPrefix+'GetShopKeyValue',{params,config})  }
export const GetDirectorById = (params, config = {}) => {             return request.get(apiPrefix+'GetDirectorById',{params,config})  }
export const GetDirectorGroupById = (params, config = {}) => {             return request.get(apiPrefix+'GetDirectorGroupById',{params,config})  }
export const addOrUpdateDirector = (params, config = {}) => {       return request.post(apiPrefix+'AddOrUpdateDirector', params, config)}
export const addOrUpdateBand = (params, config = {}) => {    return request.post(apiPrefix + 'AddOrUpdateBand', params, config)}


export const deleteBand = (params, config = {}) => {    return request.delete(apiPrefix + 'DeleteBand',{ params, ...config}) }
export const getProductBrandById = (id, config = {}) => {  return request.get(apiPrefix + `GetProductBrandById?id=${id}`, {}, config) }
export const getProductBrandPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetProductBrandPageList',{ params, ...config })}

// 商品编码类别
export const addOrUpdateProductBianMaCategory = (params, config = {}) => {return request.post(apiPrefix + 'AddOrUpdateProductBianMaCategory', params, config)}
export const deleteProductBianMaCategory = (params, config = {}) => { return request.delete(apiPrefix + 'DeleteProductBianMaCategory',{ params, ...config}) }
export const getProductBianMaCategoryById = (id, config = {}) => {return request.get(apiPrefix + `GetProductBianMaCategoryById?id=${id}`, {}, config)}
export const getProductBianMaCategoryPageList = (params, config = {}) => {return request.get(apiPrefix + 'GetProductBianMaCategoryPageList', { params, ...config })}

//商铺服务费管理
export const addOrUpdateShopServiceFee = (params, config = {}) => { return request.post(apiPrefix + 'AddOrUpdateShopServiceFee', params, config)}
export const deleteShopServiceFee = (params, config = {}) => {return request.delete(apiPrefix + 'DeleteShopServiceFee',{ params, ...config})}
export const getShopServiceFeeById = (id, config = {}) => {return request.get(apiPrefix + `GetShopServiceFeeById?id=${id}`, {}, config)}
export const getShopServiceFeePageList = (params, config = {}) => {return request.get(apiPrefix + 'GetShopServiceFeePageList', { params, ...config })}

//获取费用参数设置
export const updateSellerConfig = (params, config = {}) => {return request.post(apiPrefix + 'UpdateSellerConfig',  params, config)}
export const getSellerConfig = (params, config = {}) => { return request.get(apiPrefix + 'GetSellerConfig', { params, ...config }) }

//京东日报配置仓储物流费占比
export const getJdShop = (params, config = {}) => { return request.get(apiPrefix + 'GetJdShop', { params, ...config }) }
export const updateJdShopLogisticsFeeRatio = (params, config = {}) => { return request.get(apiPrefix + 'UpdateJdShopLogisticsFeeRatio', { params, ...config }) }

//根据当前登录人运营组获取店铺
export const getShopListByCurUserGroup = (params, config = {}) => { return request.get(apiPrefix + 'GetShopListByCurUserGroup', { params, ...config }) }

// 获取dpp店铺(铺货3.0)
export const getShopByCurUserGroup = (params, config = {}) => { return request.get(apiPrefix + 'GetShopByCurUserGroup', { params, ...config }) }

//修改平台店铺id  UpdateShopPlatformId
export const updateShopPlatformId = (params, config = {}) => { return request.post(apiPrefix + 'UpdateShopPlatformId',  params, config ) }

export const updateShopEmail = (params, config = {}) => { return request.post(apiPrefix + 'UpdateShopEmail',  params, config ) }

//修改店铺状态
export const updateShopStatus = (params, config = {}) => { return request.post(apiPrefix + 'UpdateShopStatus',  params, config ) }

// 获取所有店铺细分平台
export const getAllShopXiFenPlatform = (params, config = {}) => { return request.get(apiPrefix + 'GetAllShopXiFenPlatform', { params: params, ...config })}

//设置京东商家编码
export const UpdateShopCustomCode = (params, config = {}) => { return request.post(apiPrefix + 'UpdateShopCustomCode', params, config) }
