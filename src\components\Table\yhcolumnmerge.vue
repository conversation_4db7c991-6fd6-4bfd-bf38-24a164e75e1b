 <template>
    <el-table-column :label="column.label" header-align="center" :show-overflow-tooltip="showoverflowtooltip">
      <template #header v-if="column.tipmesg">
           <span class="grid-header">
             <span>
              <el-tooltip class="item" effect="dark" :content="column.tipmesg" placement="top-end">
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
             </span>
             <span>{{column.label}}</span>
           </span>
        </template>
        <template v-for="(col, colindex) in column.cols">
          <cescolumn :that="that" :key="'merge'+colindex" :column="col" :size="size" :type="type" :descData="descData"/>
        </template>
    </el-table-column>
</template>
<script>
import cescolumn from "@/components/Table/yhcolumn.vue";
export default {
  components: {cescolumn},
  props:{
    that: { type: Object, default: this },
    column:{ type:Object,default:()=>{}},   
    // 表格型号：mini,medium,small
    size:{type:String,default:'mini'},
    type:{type:String,default:'primary'},
    descData:{ type:Array,default:()=>[]},
    showoverflowtooltip: {type: Boolean, default: true},
  },
  created() {
    //console.log(this.column.label+":"+this.column.istrue,this.column)
	},
  updated(){
    // console.log(this.column.label+":"+this.column.istrue,this.column)
  }
}
</script>
<style>