<template>
  <div class="ChatPage" :style="faceSize">
    <WinBar v-if="winBarConfig.list != null" class="winBar" :config="winBarConfig" @click="winBarClick" />
    <div class="header">
      <JwChatitem :config="config" @click="bindClick" />
      <slot name="header" />
    </div>
    <div class="main">
      <div class="chatBox">
        <JwChat ref="jwChat" :taleList="taleList" @enter="$emit('enter', $event)" v-model="msg" :toolConfig="toolConfig"
          :scrollType="scrollType" :width="realWidth" :height="chatHeight" :config="chatConfig"
          @clickTalk="$emit('clickTalk', $event)">
          <slot name="tools" slot="tools" />
        </JwChat>
      </div>
    </div>
  </div>
</template>

<script>
import WinBar from './windowBar'
import JwChat from '../Chat/index.vue'
import JwChatitem from '../UserItem/index.vue'
export default {
  name: "JwChat-index",
  components: {
    WinBar, JwChat, JwChatitem
  },
  props: {
    config: {
      type: Object,
      default: () => ({
        img: '',
        name: '',
        dept: '昀含贸易',
        callback: () => { }
      })
    },
    taleList: {
      type: Array,
      default: () => {
        return []
      }
    },
    height: {
      type: String,
      default: "670"
    },
    width: {
      default: "750"
    },
    value: {
      default: ''
    },
    toolConfig: {
      type: Object
    },
    winBarConfig: {
      type: Object,
      default: () => ({})
    },
    scrollType: {
      default: "noroll"
    },
    size: {
      default: 35
    }
  },
  data() {
    return {
      chatHeight: '',
      msg: '',
      switchBox: true,
    }
  },
  computed: {
    faceSize() {
      let height = this.height
      let width = this.width
      if (height.match(/\d$/)) {
        height += 'px'
      }
      if (width.match(/\d$/)) {
        width += 'px'
      }
      const style = { height, width } 
      if (this.winBarConfig.list != null) {
        style.left = '100px'
      }
      return style
    },
    chatConfig() {
      const { historyConfig = {} } = this.config || {}
      return { historyConfig }
    },
    switchIcon() {
      let result = 'icon-jiantou-xiangzuo'
      if (this.switchBox) result = 'icon-jiantou-xiangyou'
      return result
    },
    realWidth() {
      const width = this.width
      let ratio = 1
      return width * ratio + ''
    },
    coverSize() {
      let size = this.size
      if (`${size}`.match(/\d$/)) {
        size += 'px'
      }
      return {
        width: size,
        height: size,
      }
    }
  },
  watch: {
    height: {
      handler() {
        this.chatHeight = this.height - 60 + ''
      },
      immediate: true
    },
    value: {
      handler() {
        this.msg = this.value;
      },
      immediate: true
    },
    msg: {
      handler() {
        this.$emit('input', this.msg);
      },
      immediate: true
    },
    showRightBox: {
      handler(newval) {
        if (typeof newval === 'boolean')
          this.switchBox = newval
      },
      immediate: true
    }
  },
  methods: {
    bindClick(type) {
      const { callback } = this.config || {}
      if (callback) {
        callback(type)
      }
    },
    winBarClick(play) {
      const { callback = null } = this.winBarConfig
      if (callback) {
        callback(play)
      }
    },
    finishPullDown() {
      this.$refs.jwChat.finishPullDown()
    },
    scrollToEle(el) {
      this.$refs.jwChat.scrollToEle(el)
    },
    scrollBottom(){
      this.$refs.jwChat.scrollBottom()
    },
    bindTopClick(type) {
      const target = type
      this.$emit('click', target)
    }
  }
}
</script>

<style  scoped lang="scss">
.ChatPage {
  margin: 0 auto;
  background: #F5F5F5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;

  .winBar {
    position: absolute;
    transform: translateX(-100%);
  }

  .header {
    background-color: #409eff;
    display: flex;
    margin: 0 auto;
    padding-left: 12px;
    align-items: center;
    height: 60px;
    position: relative;
    color: #F5F5F5;
  }

  .main {
    display: flex;
    height: calc(100% - 60px);

    .rightBox {
      box-shadow: 0 -3px 3px 0 rgba(0, 0, 0, 0.1);
      width: 45%;
      position: relative;

      .switch {
        position: absolute;
        left: -1.2rem;
        top: 20%;
        background: rgba(204, 204, 204, 0.5);
        padding: 0.3rem 0 0.3rem 0.1rem;
        border-radius: 100% 0 0 100%;
        color: #fff;
        cursor: pointer;

        &:hover {
          background: #409eff;
        }
      }

    }
  }
}

.item {
  display: flex;
  align-items: center;
  position: relative;

  .cover {
    border-radius: 50%;
    margin-right: 12px;
    box-shadow: 0 3px 3px 0 rgba(0, 0, 0, 0.1);
    cursor: pointer;
  }

  .info {
    display: flex;
    flex-direction: column;
    justify-content: center;

    p {
      margin: 0;
      margin: 0;
      padding: 0;
      /* width: 175px; */
      text-overflow: ellipsis;
      overflow: hidden;
      text-align: left;
      white-space: nowrap;
      font-size: 13px;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }

      &:last-child {
        font-size: 12px;
      }
    }
  }
}
</style>
