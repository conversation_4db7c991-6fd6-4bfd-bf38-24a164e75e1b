<template>
    <el-select v-model="innerValue" :clearable="clearable" filterable remote :style="cststyle" reserve-keyword placeholder="" :remote-method="remoteMethod" @change="valueChanged($event)" :loading="loading">
        <el-option v-for="item in options" :key="item.id" :label="item.label" :value="item.value">
        </el-option>
        <el-option :key="value" :label="text" :value="value">
        </el-option>
    </el-select>

</template>

<script>
    import { queryCompanyByKeyword } from "@/api/express/express.js"

    export default {
        name: 'YhExpressselector',
        props: {
            rows: {
                type: Number,
                default: 20
            },
            value: {
                type: String,
                default() {
                    return ""
                }
            },
            text: {
                type: String,
                default() {
                    return ""
                }
            },
            clearable: {
                type: Boolean,
                default() { return true; }
            },
            cststyle: {
                type: Object,
                default() {
                    return {}
                }
            }
        },
        data() {
            return {
                loading: false,
                options: [],
                innerValue: "",
                innerText: "",
                orgOptions: []
            }
        },
        computed: {
            // innerValue() {
            //     return this.value;
            // }
        },
        watch: {
            value(newVal, oldVal) {
                //如果选项值在options中没有，

                this.innerValue = newVal;

                // this.$emit("update:value", newVal);
                // this.$emit("update:text", newVal);
            }
        },
        async mounted() {
            let rlt = await queryCompanyByKeyword({ rows: this.rows, kw: "" });
            if (rlt && rlt.success) {
                this.options = rlt.data.map(item => {
                    return { label: item.companyName, value: item.comanyCode, id: item.id }
                });
            }

            //如果value有值追加显示到下拉列表

            // if (this.value) {
            //     let tempV = this.value;
            //     let findOpts = this.options.filter(item => { return item.value == tempV; });
            //     if (!(findOpts && findOpts.length > 0)) {
            //         let opt = { value: this.value, label: this.label };
            //         if (!!this.label) {
            //             opt.label = this.value;
            //         }
            //         this.options.push(opt);
            //     }
            // }

            this.orgOptions = [...this.options];
        },
        methods: {
            valueChanged(newValue) {

                let labelValue = newValue;
                if (newValue) {

                    let findOpts = this.options.filter(item => { return item.value == labelValue; });
                    if (findOpts && findOpts.length > 0) {
                        labelValue = findOpts[0].label;
                    }
                }
                this.$emit("update:value", newValue);
                this.$emit("update:text", labelValue);
            },
            async remoteMethod(query) {
                this.loading = true;
                if (query !== '') {
                    let rlt = await queryCompanyByKeyword({ rows: this.rows, kw: query });
                    if (rlt && rlt.success) {
                        this.options = rlt.data.map(item => {
                            return { label: item.companyName, value: item.comanyCode, id: item.id }
                        });
                    }
                } else {
                    this.options = [...this.orgOptions];
                }
                this.loading = false;
            }
        }
    }
</script>

