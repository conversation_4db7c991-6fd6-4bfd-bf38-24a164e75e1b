import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Media}/shootingset/`
//获取数据-ById
export const getShootingSetDataById = (params, config = {}) => {
    return request.get(apiPrefix + 'GetShootingSetDataById', { params, ...config })
}
//获取数据
export const getShootingSetData = (params, config = {}) => {
    return request.get(apiPrefix + 'GetShootingSetData', { params, ...config })
}
//保存
export const saveShootingSet = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveShootingSet', params, config)
}
//删除
export const deleteShootingSet = (params, config = {}) => {
    return request.get(apiPrefix + 'DeleteShootingSet', { params, ...config })
}

//保存
export const saveShootingSetList = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveShootingSetList', params, config)
}


//保存
export const moveShootingSetIndex = (params, config = {}) => {
    return request.get(apiPrefix + 'MoveShootingSetIndex', params, config)
}

//新增或更改
export const addOrUpdatePersonnelPositionAsync = (params, config = {}) => { return request.post(apiPrefix + 'AddOrUpdatePersonnelPositionAsync', params, config) }
//列表查询
export const getPersonnelPositionAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetPersonnelPositionAsync', params, config) }

//排序
export const sortPersonnelPosition = (params, config = {}) => {
    return request.post(apiPrefix + 'SortPersonnelPosition', params, config)
}

//排序
export const saveDataOrderListDataAsync = (params, config = {}) => { return request.post(apiPrefix + 'SaveDataOrderListDataAsync', params, config) }


//列表查询
export const delPersonnelPositionAsync = (params, config = {}) => { return request.get(apiPrefix + 'DelPersonnelPosition', { params: params, ...config }) }


//获取配置提成表
export const getCommissionPositionAsync = (params, config = {}) => {
    return request.get(apiPrefix + 'GetCommissionPositionAsync', { params: params, ...config })
}

//保存配置提成表
export const saveCommissionPositionAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveCommissionPositionAsync', params, config)
}

//获取配置提成表
export const getPersonnelPositionWorkInfo = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPersonnelPositionWorkInfo', { params: params, ...config })
}
//导入薪资
export const importShootingSalaryAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportShootingSalary', params, config) }

//列表查询
export const setPersonnelIsHsAsync = (params, config = {}) => { return request.get(apiPrefix + 'SetPersonnelIsHsAsync', { params: params, ...config }) }

// 111
export const pageShootingCaclTaskAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageShootingCaclTaskAsync', params, config) }

//存档
export const archiveCalculateCheck = (params, config = {}) => { return request.get(apiPrefix + 'RunCalculatePersonnelWorkInfo', { params: params, ...config }) }

//工作核算历史信息查询
export const getHistoryVersionInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetHistoryVersionInfo', { params: params, ...config }) }

export const getHistoryShootingTaskInfo = (params, config = {}) => { return request.post(apiPrefix + 'GetHistoryShootingTaskInfo', params, config) }

export const getHistorytPersonnelPositionInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetHistorytPersonnelPositionInfo', { params: params, ...config }) }

export const getHistoryCommissionPositionInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetHistoryCommissionPositionInfo', { params: params, ...config }) }

export const getHistoryPersonnelPositionWorkInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetHistoryPersonnelPositionWorkInfo', { params: params, ...config }) }

export const sendSalery = (params, config = {}) => { return request.post(apiPrefix + 'SendSalery', params, config) }

export const delHistoryVersionInfo = (params, config = {}) => { return request.get(apiPrefix + 'DelHistoryVersionInfo', { params: params, ...config }) }

export const pageShortVideoCaclTaskAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageShortVideoCaclTaskAsync', params, config) }

export const pageChangeImgCaclTaskAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageChangeImgCaclTaskAsync', params, config) }

export const pageDirectImgCaclTaskAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageDirectImgCaclTaskAsync', params, config) }

export const pageMicroVedioCaclTaskAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageMicroVedioCaclTaskAsync', params, config) }

export const pageShopDecorationCaclTaskAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageShopDecorationCaclTaskAsync', params, config) }


export const pagePackDesginCaclTaskAsync = (params, config = {}) => { return request.post(apiPrefix + 'PagePackDesginCaclTaskAsync', params, config) }


//导入薪资
export const importShootCommonSalary = (params, config = {}) => { return request.post(apiPrefix + 'ImportShootCommonSalary', params, config) }

export const exportCommissionTaskReport = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportCommissionTaskReport', { params: params, ...config }) }

export const getHistoryShortVideoTaskInfo = (params, config = {}) => { return request.post(apiPrefix + 'GetHistoryShortVideoTaskInfo', params, config) }

export const getHistoryMicroVedioTaskInfo = (params, config = {}) => { return request.post(apiPrefix + 'GetHistoryMicroVedioTaskInfo', params, config) }

export const getHistoryChangeImgTaskInfo = (params, config = {}) => { return request.post(apiPrefix + 'GetHistoryChangeImgTaskInfo', params, config) }

export const getHistoryDirectImgTaskInfo = (params, config = {}) => { return request.post(apiPrefix + 'GetHistoryDirectImgTaskInfo', params, config) }

export const getHistoryShopDecorationTaskInfo = (params, config = {}) => { return request.post(apiPrefix + 'GetHistoryShopDecorationTaskInfo', params, config) }

export const getHistoryPackageDesignTaskInfo = (params, config = {}) => { return request.post(apiPrefix + 'GetHistoryPackageDesignTaskInfo', params, config) }

export const addShootingFpSet = (params, config = {}) => { return request.post(apiPrefix + 'AddShootingFpSet', params, config) }

export const editShootingFpSet = (params, config = {}) => { return request.post(apiPrefix + 'EditShootingFpSet', params, config) }

export const deleteShootingFpSet = (params, config = {}) => { return request.post(apiPrefix + 'DeleteShootingFpSet',params, config) }

export const getShootingFpInfo = (params, config = {}) => { return request.post(apiPrefix + 'GetShootingFpInfo',params, config) }

export const sortShootingFpSet = (params, config = {}) => { return request.post(apiPrefix + 'SortShootingFpSet',params, config) }

export const pagePddChangeImgCaclTaskAsync = (params, config = {}) => { return request.post(apiPrefix + 'PagePddChangeImgCaclTaskAsync', params, config) }

//获取员工个人资料
export const getEmployeeInfoList = (params, config = {}) => { return request.post(apiPrefix + 'GetEmployeeInfoList', params, config) }
//编辑员工个人资料
export const editEmployeeInfo = (params, config = {}) => { return request.post(apiPrefix + 'EditEmployeeInfo', params, config) }
//获取员工个人资料详情
export const getEmployeeInfoDetial = (params, config = {}) => { return request.get(apiPrefix + 'GetEmployeeInfoDetial?uid=' + params, config) }

//新增员工资料类别
export const editEmployeeInfoCategory = (params, config = {}) => { return request.post(apiPrefix + 'EditEmployeeInfoCategory', params, config) }

//删除员工资料类别
export const delEmployeeInfoCategory = (params, config = {}) => { return request.post(apiPrefix + 'DelEmployeeInfoCategory', params, config) }

//员工资料评分统计
export const getEmployeeStatList = (params, config = {}) => { return request.get(apiPrefix + 'GetEmployeeStatList', { params: params, ...config }) }

//绩效模板查看
export const getPerformTempInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformTempInfo', { params: params, ...config }) }

//编辑绩效模板
export const editPackProcessPerformanceTemplate =(params,config) =>{return request.post(apiPrefix + 'EditPackProcessPerformanceTemplate', params,config)}

//绩效考核查看
export const getPerformAssessInfo =(params,config) =>{return request.post(apiPrefix + 'GetPerformAssessInfo', params,config)}

//保存绩效打分
export const savePackProcessPerformanceAssess =(params,config) =>{return request.post(apiPrefix + 'SavePackProcessPerformanceAssess', params,config)}

//考核确认
export const confirmPerformAssess =(params,config) =>{return request.post(apiPrefix + 'ConfirmPerformAssess', params,config)}

//考核取消确认
export const unConfirmPerformAssess =(params,config) =>{return request.post(apiPrefix + 'UnConfirmPerformAssess', params,config)}

//考核取消确认
export const getHistoryPerformAssessInfo =(params,config) =>{return request.post(apiPrefix + 'GetHistoryPerformAssessInfo', params,config)}

//新增编辑绩效模板
export const addOrUpdatePackProcessPerformanceTemplate =(params,config) =>{return request.post(apiPrefix + 'AddOrUpdatePackProcessPerformanceTemplate', params,config)}

//删除绩效模板
export const delPackProcessPerformanceTemplate =(params,config) =>{return request.get(apiPrefix + 'DelPackProcessPerformanceTemplate', { params: params, ...config })}

//薪资核算导出
export const exportPersonnelSalary = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPersonnelSalary',  params, config) }

//薪资核算历史版本导出
export const exportPersonnelSalaryHistory = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportPersonnelSalaryHistory', { params: params, ...config }) }

//新增编辑绩效模板
export const confirmPerformAssessAll = (params,config) =>{return request.post(apiPrefix + 'ConfirmPerformAssessAll', params,config)}

//新增编辑绩效模板
export const resetSalaryData = (params,config) =>{return request.post(apiPrefix + 'ResetSalaryData', params,config)}


//批量打印
export const printPerformAssessAllList =(params,config) =>{return request.post(apiPrefix + 'PrintPerformAssessAllList', params,config)}

//绩效考核模板保存排序
export const sortPerformTempList =(params,config) =>{return request.post(apiPrefix + 'SortPerformTempList', params,config)}

//新增模板备注
export const addPerformTempRemark = (params,config) =>{return request.post(apiPrefix + 'AddPerformTempRemark', params,config)}

//删除模板备注
export const delPerformTempRemark = (params, config = {}) => { return request.delete(apiPrefix + 'DelPerformTempRemark', { params: params, ...config })}

//备注编辑回显数据
export const getPerformTempRemark = (params, config = {}) => { return request.get(apiPrefix + 'GetPerformTempRemark', { params: params, ...config }) }

//拼多多改图设置新增员工
export const addShootingFpSetTemp = (params,config) =>{return request.post(apiPrefix + 'AddShootingFpSetTemp', params,config)}

//获取出款时效列表
export const getTimeLimitDrawingList = (params, config = {}) => { return request.get(apiPrefix + 'GetTimeLimitDrawingList', { params: params, ...config }) }

//编辑保存
export const addOrUpdateTimeLimitDrawingList = (params,config) =>{return request.post(apiPrefix + 'AddOrUpdateTimeLimitDrawingList', params,config)}

//删除
export const delTimeLimitDrawingList = (params, config = {}) => { return request.delete(apiPrefix + 'DelTimeLimitDrawingList', { params: params, ...config })}

//获取确定版本信息
export const getMainStatVersionInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetMainStatVersionInfo', { params: params, ...config }) }

//获取版本信息
export const getMainStatVersionList = (params, config = {}) => { return request.get(apiPrefix + 'GetMainStatVersionList', { params: params, ...config }) }

//新增版本信息
export const addainStatVersionList = (params,config) =>{return request.post(apiPrefix + 'AddainStatVersionList', params,config)}

//版本存档
export const saveMainStatVersionList = (params,config) =>{return request.post(apiPrefix + 'SaveMainStatVersionList', params,config)}

//确定版本
export const confirmMainStatVersionList = (params,config) =>{return request.post(apiPrefix + 'ConfirmMainStatVersionList', params,config)}

//获取信息
export const getMainStatVersionInfoById = (params, config = {}) => { return request.get(apiPrefix + 'GetMainStatVersionInfoById', { params: params, ...config }) }

//删除版本
export const delMainStatVersionInfoById = (params, config = {}) => { return request.delete(apiPrefix + 'DelMainStatVersionInfoById', { params: params, ...config })}

//获取及时完成率列表
export const getShootingInTimeRateList = (params, config = {}) => { return request.get(apiPrefix + 'GetShootingInTimeRateList', { params: params, ...config }) }

//编辑保存
export const addOrUpdateInTimeRate = (params,config) =>{return request.post(apiPrefix + 'AddOrUpdateInTimeRate', params,config)}

//删除
export const delInTimeRate = (params, config = {}) => { return request.delete(apiPrefix + 'DelInTimeRate', { params: params, ...config })}

//获取质量+配合度列表
export const getShootingAualityAdaptabilityDeductList = (params, config = {}) => { return request.get(apiPrefix + 'GetShootingAualityAdaptabilityDeductList', { params: params, ...config }) }

//编辑保存
export const addOrUpdateAualityAdaptabilityDeduct = (params,config) =>{return request.post(apiPrefix + 'AddOrUpdateAualityAdaptabilityDeduct', params,config)}

//删除
export const delAualityAdaptabilityDeduct = (params, config = {}) => { return request.delete(apiPrefix + 'DelAualityAdaptabilityDeduct', { params: params, ...config })}

//考核设置
export const assessTypeSet = (params,config) =>{return request.post(apiPrefix + 'AssessTypeSet', params,config)}

//获取提成考核
export const getCommCommissionAssess = (params, config = {}) => { return request.get(apiPrefix + 'GetCommCommissionAssess', { params: params, ...config }) }

//部门经理绩效保存
export const saveDeptManagerAssess = (params,config) =>{return request.post(apiPrefix + 'SaveDeptManagerAssess', params,config)}

//获取部门经理绩效分
export const getDeptManagerCommCommissionAssess = (params, config = {}) => { return request.get(apiPrefix + 'GetDeptManagerCommCommissionAssess', { params: params, ...config }) }

//获取提成考核历史
export const getCommCommissionAssessHistory = (params, config = {}) => { return request.get(apiPrefix + 'GetCommCommissionAssessHistory', { params: params, ...config }) }
