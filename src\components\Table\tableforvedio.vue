<template>
    <div id="mainmidle"
        :style="[{ height: (isSelectColumn == true ? '95%' : '96%') }, { width: '100%' }, { 'margin': ' 0' }]">
        <div ref="btns" :style="{height: selectColumnHeight}" v-if="isSelectColumn">
            <el-popover placement="right" title="列隐藏/显示" trigger="click" width="420">
                <el-checkbox-group v-model="checkedColumns" size="mini">
                    <el-checkbox v-for="item in checkBoxGroup" :key="item" :label="item" :value="item"></el-checkbox>
                </el-checkbox-group>
                <el-button slot="reference" type="primary" size="small" plain><i
                        class="el-icon-arrow-down el-icon-menu" />列隐藏/显示</el-button>
            </el-popover>
            <el-button-group>
                <template v-for='item in tableHandles'>
                    <el-button v-if="(!item.permission || (item.permission && checkPermission(item.permission)))"
                        :key='item.label' :size="item.size || size" :type="item.type || type" :icon='item.icon || ""'
                        @click="item.handle(that)">{{ item.label }}</el-button>
                </template>
                <slot name="extentbtn" />
            </el-button-group>
        </div>
        <!-- row-key="id" -->
        <el-table header-align="center" :data='tableData' class="table-wrapper" :height="tableHeight" :border='isBorder' @select='select' @select-all='selectAll' @sort-change='sortchange' @cell-click='cellclick' @expand-change='expandchange' v-loading='loading' :tree-props='treeprops' :show-summary='showsummary' :summary-method="getSummaries" :defaultSelections='defaultSelections' :row-style='customRowStyle' highlight-current-row :stripe="stripe.stripe" :header-cell-style='headerCellStyle||heardcolumnbackgroundStyle' ref="cesTable" :row-key="rowkey" :cell-style="columnbackgroundStyle" v-bind:name="tablekey">
            <template v-if="hasexpand">
                <slot />
            </template>
            <el-table-column v-if="isSelection" type="selection"  width="38" :fixed="tablefixed"></el-table-column>
            <el-table-column v-if="isIndex" type="index" :label="indexLabel" align="center" width="40" :fixed="tablefixed"></el-table-column>
            <template v-for="(item, index) in childtableCols">
                <template v-if="item.istrue && item.type !='color'">
                    <cescolumnmerge v-if="item.merge &&(!items.permission||(item.permission&&checkPermission(item.permission)))" :that="that" :key="index" :column="item" :size="size" :type="type" :descData="descData" />
                    <cescolumn v-else :that="that" :key="item.prop" :column="item" :size="size" :type="type" :descData="descData" @previewImageGoodsCode="previewImageGoodsCode" @showImg="showImg" @preview="preview" @playerVideo="playerVideo" />
                </template>
                <template v-if="item.type=='color'">
                    <el-table-column type="color" label="|" :property="item.backgroudColor" align="left" width="28"  >|</el-table-column>
                </template>
            </template>


            <template v-if="hasexpandRight">
                <slot name="right" />
            </template>
        </el-table>
        <el-dialog :visible.sync="showImage" :modal="false" :show-close="false" :width="ImgWith" v-dialogDrag>
            <img ref="imgdialog" :src="imgList[0]" />
        </el-dialog>
        <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeFunc" style="z-index:9999;" />
        <div class="imgDolg" v-show="imgPreview.show" @click.stop="imgPreview.show = false">
            <i class="el-icon-close" id="imgDolgClose" @click.stop="imgPreview.show = false"></i>
            <img @click.stop="imgPreview.show = true" :src="imgPreview.img" />
        </div>

        <!--视频播放-->
        <el-dialog title="视频播放" :visible.sync="videoDialogVisible" width="50%" @close="closeVideoPlyer">
            <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
            <span slot="footer" class="dialog-footer">
                <el-button @click="videoDialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

    </div>
</template>
<script>
//播放器
import videoplayer from '@/views/media/video/videoplayer'
import cescolumnmerge from "@/components/Table/yhcolumnmerge.vue";
import cescolumn from "@/components/Table/yhcolumn.vue";
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { addOrUpdate as addRephotography } from "@/api/inventory/goodsimagerephotography";
export default {
    components: { cescolumnmerge, cescolumn, ElImageViewer, videoplayer },
    props: {
        that: { type: Object, default: this },
        hasexpand: { type: Boolean, default: false },
        hasexpandRight: { type: Boolean, default: false },
        // 表格型号：mini,medium,small
        size: { type: String, default: 'mini' },
        type: { type: String, default: 'primary' },
        isBorder: { type: Boolean, default: true },
        loading: { type: Boolean, default: false },
        tableHandles: { type: Array, default: () => [] },
        // 表格数据
        tableData: { type: Array, default: () => [] },
        // 表格列配置
        tableCols: { type: Array, default: () => [] },
        tablefixed: { type: Boolean, default: false },
        // 是否显示表格复选框
        isSelection: { type: Boolean, default: false },
        defaultSelections: { type: [Array, Object], default: () => null },
        // 是否显示表格索引
        isIndex: { type: Boolean, default: false },
        indexLabel: { type: String, default: '#' },
        //排序
        orderby: { type: Object, default: () => ({ order: "ascending", name: 'id' }) },
        filter: {},
        treeprops: {},
        showsummary: { type: Boolean, default: true },
        summaryarry: { type: Object, default: () => { } },
        descData: { type: Array, default: () => [] },
        isSelectColumn: { type: Boolean, default: true },//是否显示列筛选按钮
        customRowStyle: { type: Function, default: null },//行style样式
        headerCellStyle: { type: Function, default: null },//表头单元格style样式
        tablekey: { type: String, default: '' },//表格key
        rowkey: { type: String, default: '' },//表格key
        selectColumnHeight: { type: String, default: '30px' },//表格key
        stripe: {type: Boolean, default: true}//斑马线显示
    },
    data() {
        return {
            childtableCols: { type: Array, default: () => [] },
            checkBoxGroup: [],
            checkedColumns: [],
            tableHeight: this.gettableHeight(),
            imgPreview: { img: "", show: false },
            showImage: false,
            showGoodsImage: false,
            imgList: [],
            ImgWith: null,
            CansetTableColumn: false,
            summarycolumns: [],
            videoDialogVisible: false,
            videoplayerReload: false,
            videoUrl: '',
            bgcolorlist: [],
            arraynewone: [],
            arraynewtwo: [],
            colortime: 0,
        }
    },
    mounted() {
        this.initCheckedColumns(this.tableCols)
    },
    updated(){
    },
    watch: {
        async checkedColumns(val, value) {
            if (!this.isSelectColumn) return;
            let checked = [];
            let unchecked = [];
            var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
            this.childtableCols.forEach((item, index) => {
                if (val.includes(item.label)) {
                    checked.push(item.label)
                    this.showhiddenColumn(item, true)
                    this.$nextTick(() => { this.$refs.cesTable.doLayout(); });
                }
                else {
                    unchecked.push(item.label)
                    this.showhiddenColumn(item, false)
                    this.$nextTick(() => { this.$refs.cesTable.doLayout(); });
                }
            })
        },
        tableHeight(val, value) { },
        async tableCols(val, value) {
            this.initCheckedColumns(val)
        },
        tableData(val, value) {
            this.$nextTick(() => { this.$refs.cesTable.doLayout(); });
        },
    },
    methods: {
        async checkedColumnsShow(val, value) {
            if (!this.isSelectColumn) return;
            let checked = [];
            let unchecked = [];
            var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
            this.childtableCols.forEach((item, index) => {
                if (val.includes(item.label)) {
                    checked.push(item.label)
                    this.showhiddenColumn(item, true)
                    this.$nextTick(() => { this.$refs.cesTable.doLayout(); });
                }
                else {
                    unchecked.push(item.label)
                    this.showhiddenColumn(item, false)
                    this.$nextTick(() => { this.$refs.cesTable.doLayout(); });
                }
            })
        },
        async initCheckedColumns(_tableCols) {
            _tableCols.forEach((item, index) => {
                this.checkBoxGroup.push(item.label);
            })
            await this.$nextTick(async function () {
                this.CansetTableColumn = false
                this.childtableCols = this.tableCols;
                if (!this.isSelectColumn) return;
                else this.checkedColumns = this.checkBoxGroup
            });
        },
        showhiddenColumn(column, ishow) {
            if (!!column.merge && !!column.cols && column.cols.length > 0) {
            }
            else if (!column.label)
                column.istrue = true;
            else if (!column.merge)
                column.istrue = ishow;
        },
        updateData() {
            if (this.tableData) {
                this.tableData.forEach(element => {
                    if (!element.id) {
                        element.id = this.randrom()
                    }
                });
            }
        },
        randrom() {
            var e = 10;
            var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
                a = t.length,
                n = "";
            for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
            return n;
        },
        gettableHeight() {
            return this.isSelectColumn ? '95%' : '100%';
        },
        select(rows, row) {
            this.$emit('select', rows, row);
        },
        // 全选
        selectAll(rows) {
            this.$emit('select', rows)
        },
        sortchange(column) {
            this.$emit('sortchange', column)
        },
        cellclick(row, column, cell, event) {
            this.$emit('cellclick', row, column, cell, event)
        },
        expandchange(row, args) {
            this.$emit('expandchange', row, args)
        },
        clearSort() {
            this.$refs.cesTable.clearSort();
        },
        btnHand(hand) {
            console.log("hand", hand)
            this.$emit(hand)
        },
        getSummaries(param) {
            const sums = [];
            if (!this.summaryarry)
                return sums
            var arr = Object.keys(this.summaryarry);
            if (arr.length == 0)
                return sums
            const { columns, data } = param;
            var hashj = false;
            columns.forEach((column, index) => {
                if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
                    var sum = this.summaryarry[column.property + '_sum'];
                    if (sum == null) return;
                    else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
                    else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2)
                    else sums[index] = sum.toFixed(0)
                }
                else sums[index] = ''
            });
            if (this.summarycolumns.length == 0) {
                this.summarycolumns = columns;
                this.initsummaryEvent();
            }
            return sums
        },
        initsummaryEvent() {
            let self = this;
            let table;
            if(this.tablekey) table = document.querySelector('[name='+this.tablekey+'] .el-table__footer-wrapper>table');
            else table = document.querySelectorAll('.el-table__footer-wrapper>table');
            if(table?.length>0) table=table[0]
            this.$nextTick(() => {
                self.summarycolumns.forEach((column, index) => {
                    if (column.property) {
                        var col = findcol(self.tableCols, column.property);
                        table.rows[0].cells[index].style.color = "red";
                        if (col && col.summaryEvent) {

                            table.rows[0].cells[index].onclick = function () {
                                self.$emit('summaryClick', column.property)
                            }
                        }
                    }
                })
            })

            function findcol(cols, property) {
                let column;
                for (var i = 0; i < cols.length; i++) {
                    var c = cols[i];
                    if (column) break
                    else if (c.prop && c.prop.toLowerCase() == property.toLowerCase()) {
                        column = c;
                        break
                    }
                    else if (c.cols && c.cols.length > 0) column = findcol(c.cols, property)
                }
                return column
            }
        },

        initsummaryEventNew(parmsarray,summaryClickfun) {
            let self = this;
            let table;
            if(this.tablekey) table = document.querySelector('[name='+this.tablekey+'] .el-table__footer-wrapper>table');
            else table = document.querySelectorAll('.el-table__footer-wrapper>table');
            if(table?.length>0) table=table[0]
            this.$nextTick(() => {
                parmsarray.forEach((item, index) => {
                    table.rows[0].cells[item].style.color = "red";
                    table.rows[0].cells[item].onclick = function () {
                        self.$emit('summaryClick', summaryClickfun)
                    }
                })
            })


        },
        async closeVideoPlyer() {
            this.videoplayerReload = false;
        },
        async playerVideo(videoUrl) {
            this.videoplayerReload = false;
            this.videoplayerReload = true;
            this.videoDialogVisible = true;
            this.videoUrl = videoUrl;
        },
        async closeFunc() {
            this.showGoodsImage = false;
        },
        async preview(imgUrl) {
            this.showGoodsImage = true;
            this.imgList = [];
            this.imgList.push(imgUrl);
        },
        // 图片点击放大
        async showImg(e) {
            this.showGoodsImage = true;
            this.imgList = [];
            this.imgList.push(e.target.src);
        },
        //商品编码大图预览 增加自定义相机按钮code商品编码，name商品名称
        async previewImageGoodsCode(imgUrl, code, name) {
            this.showGoodsImage = true;
            this.imgList = [];
            this.imgList.push(imgUrl);
            var that = this;
            //增加自定义相机按钮，记录重拍
            setTimeout(() => {
                var container = document.getElementsByClassName('el-image-viewer__actions__inner');
                if (container && container.length > 0) {
                    container = container[0];
                    var child = document.createElement('li');
                    child.title = "重拍";
                    child.className = "el-icon-camera";
                    child.onclick = async function () {
                        //that.$confirm('确定重拍吗？','提示').then(async ()=>{
                        if (confirm("确定重拍吗？")) {
                            var params = {
                                goodsCode: code,
                                goodsName: name,
                                goodsImage: imgUrl
                            };
                            var res = await addRephotography(params);
                            if (!res?.success) {
                                return;
                            }
                            else {
                                that.$message({ message: "重拍登记成功", type: "success" });
                            }
                        }
                        //}).catch(()=>{});

                    };
                    container.appendChild(child);
                }
            }, 100);
        },
        //清空选择
        clearSelection() {
            this.$refs.cesTable.clearSelection();
        },
        //取消/选择某一行
        toggleRowSelection(row, selected) {
            this.$refs.cesTable.toggleRowSelection(row, selected);
        },
        //取消/选择所有
        toggleAllSelection(selected) {
            this.$refs.cesTable.toggleAllSelection(selected);
        },
        doLayout() {
            this.$nextTick(() => { this.$refs.cesTable.doLayout(); });
        },
        columnbackgroundStyle({ row, column, rowIndex, columnIndex }) {
            let _this = this;
            if(column.type=='color'){
                return'background:'+ column.property;
            }
        },
        sendApi(val){
            if(val.length>0&&newcolorindex!=val.length-1){
                newcolorindex = newcolorindex+1
                return val[newcolorindex]
            }else if(newcolorindex==val.length-1){
                newcolorindex = 0;
                return val[0]
            }
        },
        heardcolumnbackgroundStyle({ row, column, rowIndex, columnIndex }) {
            let _this = this;
            if(column.type=='color'){
                return'background:'+ column.property;
            }
        },
        // sumcolor(){
        //     let demo = document.getElementById("el-tableid");
        //     console.log("打印demio",demo)
        //     debugger
        // },
    },
}
</script>
<style lang="scss" scoped>
.el-table th>.cell {
    padding-left: 8px;
}

.el-table .caret-wrapper {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    height: 34px;
    width: 0;
    vertical-align: middle;
    cursor: pointer;
    overflow: initial;
    position: relative;
}

.el-table__footer-wrapper {
    margin-top: -2px;
    font-size: 9px;
    padding: 0px;
}

.el-table .cell {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    word-break: break-all;
    line-height: 23px;
    padding-right: 1px;
}

.el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
    background-color: #f1f1f1;
    color: #0756f5;
}

.ces-table-require::before {
    content: "*";
    color: red;
}

.table-wrapper {
    width: 100%;
    height: calc(100% - 35px);
    margin: 0;
}

.el-table {
    overflow: visible !important;
    height: 99.9% !important;
    width: 99.9% !important;
}

.el-table__empty-block {
    height: 550px;
    border: none;
}

/* .el-table {
                                                                                                  width: 99.9% !important;
                                                                                                } */
/* .wendang p img{max-height: 60px;} */
.wendang img {
    max-height: 50px;
}

.table_column_show {
    display: block;
}

.table_column_hidden {
    display: none;
}
</style>
<style lang="scss" scoped>
.imgDolg {
    width: 100vw;
    height: 100vh;
    position: fixed;
    z-index: 99999;
    background-color: rgba(140, 134, 134, 0.6);
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: auto;

    #imgDolgClose {
        position: fixed;
        top: 35px;
        cursor: pointer;
        right: 7%;
        font-size: 50px;
        color: white;
    }

    // img{
    //    width: 80%;
    // }
}
// ::v-deep .tr{
//         pointer-events:none;
//     }
</style>
