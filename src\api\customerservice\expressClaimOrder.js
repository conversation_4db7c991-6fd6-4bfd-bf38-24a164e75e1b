import request from '@/utils/request'
const GroupPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/ExpressClaimOrder/`

//快递理赔-快递理赔列表
export const getExpressClaimOrderListAsync = (params, config = {}) => { return request.post(GroupPrefix + 'GetExpressClaimOrderListAsync', params, config) }

//快递理赔-快递理赔记录列表
export const getExpressClaimOrderHisListAsync = (params, config = {}) => { return request.post(GroupPrefix + 'GetExpressClaimOrderHisListAsync', params, config) }

//快递理赔-快递理赔导出
export const exportExpressClaimOrderListAsync = (params, config = { responseType: 'blob' }) => request.post(`${GroupPrefix}ExportExpressClaimOrderListAsync`, params, config)

//快递理赔-获取设置
export const getExpressClaimReasonRelations = (params, config = {}) => { return request.post(GroupPrefix + 'GetExpressClaimReasonRelations', params, config) }

//快递理赔-保存设置
export const saveExpressClaimReasonRelations = (params, config = {}) => { return request.post(GroupPrefix + 'SaveExpressClaimReasonRelations', params, config) }

//快递理赔-批量更新归档状态
export const batchChangeExpressClaimOrderClose = (params, config = {}) => { return request.post(GroupPrefix + 'BatchChangeExpressClaimOrderClose', params, config) }

//快递理赔-批量手动操作待抓取
export const batchChangeExpressClaimOrderWaitCatch = (params, config = {}) => { return request.post(GroupPrefix + 'BatchChangeExpressClaimOrderWaitCatch', params, config) }

//快递理赔-客服导入列表
export const getExpressClaimCusImportRecordListAsync = (params, config = {}) => { return request.post(GroupPrefix + 'GetExpressClaimCusImportRecordListAsync', params, config) }

//快递理赔-导出客服导入列表
export const exportExpressClaimCusImportRecordListAsync = (params, config = { responseType: 'blob' }) => { return request.post(GroupPrefix + 'ExportExpressClaimCusImportRecordListAsync', params, config) }

//快递理赔-首页统计-退货数趋势图
export const expressClaimOrderStatisticCountAnalysis = (params, config = {}) => { return request.post(GroupPrefix + 'ExpressClaimOrderStatisticCountAnalysis', params, config) }

//快递理赔-首页统计-退货金额趋势图
export const expressClaimOrderStatisticAmountAnalysis = (params, config = {}) => { return request.post(GroupPrefix + 'ExpressClaimOrderStatisticAmountAnalysis', params, config) }

//快递理赔-首页统计-物流公司占比
export const expressClaimOrderStatisticExpressCompanyRate = (params, config = {}) => { return request.post(GroupPrefix + 'ExpressClaimOrderStatisticExpressCompanyRate', params, config) }

//快递理赔-首页统计-理赔原因占比
export const expressClaimOrderStatisticClaimReasonRate = (params, config = {}) => { return request.post(GroupPrefix + 'ExpressClaimOrderStatisticClaimReasonRate', params, config) }

//快递理赔-快递理赔-导入归档数据
export const importBatchCloseExpressClaimOrderAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportBatchCloseExpressClaimOrderAsync', params, config) }

//快递理赔-客服导入列表-导入
export const importExpressClaimCusImportRecordAsync = (params, config = {}) => { return request.post(GroupPrefix + 'ImportExpressClaimCusImportRecordAsync', params, config) }
