import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/companyGoods/`

//分页获取序列编码数据
export const pageGetSeriesData = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetSeriesData', params, config)
}

//获取趋势图系列编码、开始创建、截止创建
export const getSeriesDataAnalysis = (params, config = {}) => {
    return request.post(apiPrefix + 'GetSeriesDataAnalysis', params, config)
}

//获取商品编码
export const pageGetSeriesGoodsData = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetSeriesGoodsData', params, config)
}

//分页获取商品编码销售数据
export const getGoodsSalesByGoodsCode = (params, config = {}) => {
    return request.post(apiPrefix + 'GetGoodsSalesByGoodsCode', params, config)
}

//获取商品编码计划建议库存数据
export const pageGetPurchaseDataByGoodsCode = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetPurchaseDataByGoodsCode', params, config)
}

//获取用户平台jd、pdd、tx、al、fx、dy
export const getUserPlatform = (params, config = {}) => {
    return request.post(apiPrefix + 'GetUserPlatform', params, config)
}

//分页获取公司商品历史进货数据
export const pageGetCompanyGoodsForHistory = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetCompanyGoodsForHistory', params, config)
}

//进货列表-确认申请
export const createCompanyGoods = (params, config = {}) => {
    return request.post(apiPrefix + 'CreateCompanyGoods', params, config)
}

//分页获取公司商品数据
export const pageGetCompanyGoods = (params, config = {}) => {
    return request.post(apiPrefix + 'PageGetCompanyGoods', params, config)
}

//进货列表-分配采购员
export const companyGoodsPoBrand = (params, config = {}) => {
    return request.post(apiPrefix + 'CompanyGoodsPoBrand', params, config)
}

//进货列表-生成采购单
export const generatePurchaseOrder = (params, config = {}) => {
    return request.post(apiPrefix + 'GeneratePurchaseOrder', params, config)
}

//多公司商品成成采购单 ButchCreatedPurchaseOrder
export const butchCreatedPurchaseOrder = (params, config = {}) => {
    return request.post(apiPrefix + 'ButchCreatedPurchaseOrder', params, config)
}