<template>
  <div>
   <vxe-column v-if="!col.type&&!col.merge"
      show-header-overflow :field="col.prop? col.prop : ('col'+ colIndex)"
     :key="col.field"
     :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
     :title-help="col.tipmesg?{message: col.tipmesg}:null"
     :tree-node="!!col.treeNode? true:false"
     :width="col.width == 'auto' ? '' : col.width"
     :min-width="col.minwidth?col.minwidth:null"
     :sortable="!!col.sortable"
     :fixed="col.fixed?col.fixed:''"
     :align="col.align?col.align:'center'"
     >
         <template #default="{ row }" v-if="col.formatter">
             {{col.formatter? tonumfuc(col.formatter(row), col.label): tonumfuc(row[col.prop], col.label)}}
         </template>
         <template #default="{ row }" v-else>
             {{tonumfuc(row[col.prop], col.label)}}
         </template>
         <template  #footer="{ items, _columnIndex }">
             <span :style="col.summaryEvent?'color: red;cursor:pointer;':'' ">{{ items[_columnIndex] }}</span>
         </template>
   </vxe-column>

   <vxe-column show-header-overflow type="checkbox" width="60" v-else-if="col.type=='checkbox'"  fixed="left" :key="colIndex"></vxe-column>

   <vxe-column show-header-overflow type="seq" width="60" v-else-if="col.type=='seqright'"  fixed="left" :key="colIndex"></vxe-column>

   <vxe-column v-else-if="col.type=='colslot'" show-header-overflow
     :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
     :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
     show-overflow  :tree-node="!!col.treeNode? true:false"
     :width="col.width == 'auto' ? '' : col.width"
     :title-help="col.tipmesg?{message: col.tipmesg}:null"
     :min-width="col.minwidth?col.minwidth:null"
     :sortable="!!col.sortable"
     :fixed="col.fixed?col.fixed:''"
     :align="col.align?col.align:'center'"
     >
         <template #default="{ row }">
             <slot name="colslot" :col="row"></slot>
         </template>
   </vxe-column>

   <vxe-column v-else-if="col.type=='images'" show-header-overflow
     :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
     :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
     show-overflow  :tree-node="!!col.treeNode? true:false"
     :width="col.width == 'auto' ? '' : col.width"
     :title-help="col.tipmesg?{message: col.tipmesg}:null"
     :min-width="col.minwidth?col.minwidth:null"
     :sortable="!!col.sortable"
     :fixed="col.fixed?col.fixed:''"
     :align="col.align?col.align:'center'"
     >
         <template #default="{ row }">

             <template v-if=" row && !!row[col.prop] && row[col.prop].length>2">

                 <template v-if="row[col.prop][0]=='[' && JSON.parse(row[col.prop]).length>1">
                     <el-badge
                         class="badgeimage20221212"
                         :value="JSON.parse(row[col.prop]).length" style="margin-top:0px;margin-right:40px;">
                         <el-image  :src="(row[col.prop][0]=='['?(formatImg(row[col.prop])[0].url):(formatImg(row[col.prop]) ? formatImg(row[col.prop])[0] : '' ) )"
                         class="images20221212"
                         :preview-src-list="(row[col.prop][0]=='['
                 ?(()=>{
                     let tempArray=JSON.parse(row[col.prop]);
                     let tempRltArr=[];
                     tempArray.forEach(x=>tempRltArr.push(x.url));
                     return tempRltArr
                 })()
                 :(()=>{
                     return [row[col.prop]]
                 })()  )">
                         </el-image>
                     </el-badge>
                 </template>
                 <template v-else>
                     <el-image  :src="formatImg(row[col.prop])[0].url ? formatImg(row[col.prop])[0].url : formatImg(row[col.prop])[0]"

                     class="images20221212"
                     :preview-src-list="(row[col.prop][0]=='['
                 ?(()=>{
                     let tempArray=JSON.parse(row[col.prop]);
                     let tempRltArr=[];
                     tempArray.forEach(x=>tempRltArr.push(x.url));
                     return tempRltArr
                 })()
                 :(()=>{
                     return [row[col.prop]]
                 })()  )">
                     <!-- <el-image  class="imgstyle" :src="row[col.prop]"> -->
                         <div slot="error" class="image-slot">
                             <el-image></el-image>
                         </div>
                     <!-- </el-image> -->
                     </el-image>
                 </template>
             </template>


         </template>
   </vxe-column>

   <vxe-column  v-else-if="col.type=='treeimages'"
     :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
     :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
     show-overflow  :tree-node="!!col.treeNode? true:false"
     :width="col.width == 'auto' ? '' : col.width"
     :min-width="col.minwidth?col.minwidth:null"
     :sortable="!!col.sortable"
     :fixed="col.fixed?col.fixed:''"
     :align="col.align?col.align:'center'"
     >
         <template #default="{ row }">
                 <el-image  v-if="col.formatter"  :src="col.formatter? (formatImg(col.formatter(row)) ? formatImg(col.formatter(row))[0] : '') : formatImg(row[col.prop])  :preview-src-list="[col.formatter? col.formatter(row): row[col.prop]]"></el-image>
             </template>
   </vxe-column>

   <vxe-column show-header-overflow  v-else-if="col.type=='danimages'"
     :field="col.prop? col.prop : ('col'+ colIndex)"
     :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
     show-overflow  :tree-node="!!col.treeNode? true:false"
     :width="col.width == 'auto' ? '' : col.width"
     :title-help="col.tipmesg?{message: col.tipmesg}:null"
     :min-width="col.minwidth?col.minwidth:null"
     :sortable="!!col.sortable"
     :fixed="col.fixed?col.fixed:''"
     :align="col.align?col.align:'center'"
     >
         <template #default="{ row }">



                 <template>

                     <el-image  :src="formatImg(row[col.chiprop]) ? (formatImg(row[col.chiprop]) ? formatImg(row[col.chiprop])[0] : '') : (formatImg(row[col.prop]) ? formatImg(row[col.prop])[0] : '')"

                     class="images20221212"
                     :preview-src-list="[row[col.chiprop]?row[col.chiprop]:row[col.prop]]">
                         <div slot="error" class="image-slot">
                             <el-image></el-image>
                         </div>
                     </el-image>
                 </template>


         </template>
   </vxe-column>

   <vxe-column show-header-overflow  v-else-if="col.type=='imagess'"
     :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
     :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
     show-overflow  :tree-node="!!col.treeNode? true:false"
     :width="col.width == 'auto' ? '' : col.width"
     :title-help="col.tipmesg?{message: col.tipmesg}:null"
     :min-width="col.minwidth?col.minwidth:null"
     :sortable="!!col.sortable"
     :fixed="col.fixed?col.fixed:''"
     :align="col.align?col.align:'center'"
     >
         <template #default="{ row }">
             <template v-if="col.type==='imagess'">
                 <template>
                     <div class="alicenter">
                         <el-badge :value="row[col.prop].length" style="margin-top:10px;">
                             <el-image  class="imgstyle" :src="formatImg(row[col.prop])[0]?formatImg(row[col.prop])[0]:imagedefault" fit="fill" :preview-src-list="row[col.prop]">
                             </el-image>
                         </el-badge>
                         <el-button v-if="row[col.props]"  type="text" @click="col.handle&&col.handle(that,row)">{{col.labels}}</el-button>
                     </div>
                 </template>
             </template>

         </template>
   </vxe-column>

   <template v-else-if="col.merge" :width="col.width == 'auto' ? '' : col.width" >
       <vxe-colgroup :title="col.label" show-header-overflow :key="col.label" :field="col.label" :fixed="col.fixed?col.fixed:''">
           <vxe-column show-header-overflow :field="coll.prop? coll.prop : ('col'+ colIndex)"
           :title="coll.label?coll.label:((coll.type && coll.type=='color' || coll.type=='split')?'|':'')"
           show-overflow :tree-node="!!coll.treeNode? true:false"
           :width="col.width == 'auto' ? '' : col.width"
           :title-help="coll.tipmesg?{message: coll.tipmesg}:null"
           v-for="(coll, colindex) in col.cols" :key="colindex"
           :min-width="coll.minwidth?coll.minwidth:null"
           :sortable="!!coll.sortable"

           :align="coll.align?coll.align:'center'"
           >
               <template #default="{ row }" v-if="coll.formatter">
                   {{coll.formatter? tonumfuc(coll.formatter(row), coll.label): tonumfuc(row[coll.prop], coll.label)}}
               </template>
               <template #default="scope">
               <span v-if="coll.type=='color' || coll.type=='split'" >
                   |
               </span>
               <template  v-if="coll.type==='button'">
                   <template v-for="(btn,btnIndex) in coll.btnList" >
                       <el-link :key="btn.label" :style="''+ (btnIndex>0?'margin-left:5px;':'')"
                       v-if="(!btn.permission||(btn.permission&&checkPermission(btn.permission)))&&!(btn.hasOwnProperty('ishide')&&((typeof btn.ishide=='function'&& btn.ishide(that,scope.row)==true)||btn.ishide==true))"
                       :disabled="(btn.hasOwnProperty('display')&&((typeof btn.display=='function'&& btn.display(scope.row)==true)||btn.display==true))"
                       :type="((!!btn.type)? btn.type: 'primary')" :size="btn.size || size " :icon="btn.icon" @click="btn.handle(that,scope.row)">
                           {{btn.label}}
                       </el-link>
                   </template>
               </template>
               <el-button v-else-if="coll.type=='click'"  type="text" @click="coll.handle&&coll.handle(that,scope.row,coll,scope.row[coll.prop])">
                   {{(coll.formatter && tonumfuc(coll.formatter(scope.row), coll.label)) || tonumfuc(scope.row[coll.prop], coll.label)}}
               </el-button>
               <div v-else-if="coll.type=='html'"  v-html="coll.formatter? coll.formatter(scope.row): scope.row[coll.prop]  "></div>
               <el-switch v-else-if="coll.type=='switch'" v-model="scope.row[coll.prop]" @change='coll.change && coll.change(scope.row,that)'></el-switch>

               <span v-if="coll.type=='custom'||!coll.type" :style="coll.itemStyle && coll.itemStyle(scope.row)" :size="size || btn.size" :class="coll.itemClass && coll.column.itemClass(scope.row)">
               {{ (()=>{
                   if(coll.formatter)
                       return tonumfuc(coll.formatter(scope.row), coll.label);
                       return tonumfuc(scope.row[coll.prop], coll.label);
               })() }}</span>
               <el-progress v-if="coll.type==='progress'" :text-inside="true" :stroke-width="20" :percentage="Number(scope.row[coll.prop])" :status="scope.row[coll.prop]==100?'success':null"></el-progress>
               <div v-if="coll.type == 'before'" class="beforeBox">
                 <div class="beforeBox_item1">{{ scope.row[coll.prop][0] }}</div>
                 <div class="beforeBox_item2" :style="{color: (scope.row[coll.prop][1] > 0 ) ? 'red' : (scope.row[coll.prop][1] < 0 ) ? 'green' : 'gray'}">{{ scope.row[coll.prop][1] }}</div>
               </div>

           </template>
           <template  #footer="{ items, _columnIndex }">
               <span :style="col.summaryEvent?'color: red;cursor:pointer;':'' ">{{ items[_columnIndex] }}</span>
           </template>
           </vxe-column>
       </vxe-colgroup>
   </template>


   <vxe-column  v-else
     show-header-overflow
     :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.prop"
     :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
     show-overflow  :tree-node="!!col.treeNode? true:false"
     :width="col.width == 'auto' ? '' : col.width"
     :title-help="col.tipmesg?{message: col.tipmesg}:null"
     :min-width="col.minwidth?col.minwidth:null"
     :sortable="!!col.sortable"
     :fixed="col.fixed?col.fixed:''"
     :align="(col.type && (col.type=='color' || col.type=='split' || col.type=='images' || col.type=='image'))?'center':  (col.align?col.align:'left')"
     >
         <template #default="scope">

             <span v-if="col.type=='color' || col.type=='split'" >
                 |
             </span>
            <template v-if="col.type==='newstar'">
             <i v-if="scope.row.star==null"></i>
             <i v-else-if="scope.row.star==0"></i>
             <i v-else-if="scope.row.star==1" class="el-icon-star-on" style="color:red"></i>
             <i v-else-if="scope.row.star==2" class="el-icon-star-on" style="color:orange"></i>
             <i v-else-if="scope.row.star==3" class="el-icon-star-on" style="color:yellow"></i>
             <i v-else-if="scope.row.star==4" class="el-icon-star-on" style="color:green"></i>
             <i v-else-if="scope.row.star==5" class="el-icon-star-on" style="color:blue"></i>
             <i v-else-if="scope.row.star==6" class="el-icon-star-on" style="color:indigo"></i>
             <i v-else-if="scope.row.star==7" class="el-icon-star-on" style="color:purple"></i>
             <i v-else style="color:gray" class="el-icon-star-on"></i>
            </template>
            <template v-if="col.type==='flag'">
             <i v-if="scope.row.flag==null"></i>
             <i v-else-if="scope.row.flag==0"></i>
             <i v-else-if="scope.row.flag==1" class="el-icon-s-flag
             " style="color:red"></i>
             <i v-else-if="scope.row.flag==2" class="el-icon-s-flag
             " style="color:orange"></i>
             <i v-else-if="scope.row.flag==3" class="el-icon-s-flag
             " style="color:yellow"></i>
             <i v-else-if="scope.row.flag==4" class="el-icon-s-flag
             " style="color:green"></i>
             <i v-else-if="scope.row.flag==5" class="el-icon-s-flag
             " style="color:blue"></i>
             <i v-else-if="scope.row.flag==6" class="el-icon-s-flag
             " style="color:indigo"></i>
             <i v-else-if="scope.row.flag==7" class="el-icon-s-flag
             " style="color:purple"></i>
             <i v-else style="color:gray" class="el-icon-s-flag
             "></i>
            </template>

            <template v-else-if="col.type==='copy'">
                 <div class="relativebox">
                     <el-tooltip effect="dark" :content="scope.row[col.prop]" placement="top-start">
                     <div class="textover" style="width: 80%;">{{ scope.row[col.prop] }}</div>
                     </el-tooltip>

                     <div class="copyhover" @click="copytext(scope.row[col.prop])">
                         <i class="el-icon-document-copy"></i>
                     </div>
                 </div>
             </template>
             <template v-else-if="col.type==='echarts'">
                 <!--
                     1、行图表目前比较单一
                     2、行图表里col定义prop是用来指定排序的字段名，chartProp是用来指定图表数据的字段名
                     3、chartProp字段用后台类：Row7DayEchartsDtoSingle来进行输出
                  -->
                 <div  v-loading="echartsLoading" style="height: 40px;width:100%;" >
                     <div style="height: 40px;width:100%;"
                     :id="'rptIdecharts'+id+'_'+scope.columnIndex+'_'+scope.rowIndex"
                     :ref="'rptIdecharts'+id+'_'+scope.columnIndex+'_'+scope.rowIndex"
                     v-bind="{chartData:JSON.stringify(scope.row[col.chartProp])}"
                     ></div>
                 </div>
             </template>

             <template  v-if="col.type==='button'">
                 <template v-for="(btn,btnIndex) in col.btnList" >
                     <el-link :key="btn.label" :style="''+ (btnIndex>0?'margin-left:5px;':'')"
                     v-if="(!btn.permission||(btn.permission&&checkPermission(btn.permission)))&&!(btn.hasOwnProperty('ishide')&&((typeof btn.ishide=='function'&& btn.ishide(that,scope.row)==true)||btn.ishide==true))"
                     :disabled="(btn.hasOwnProperty('display')&&((typeof btn.display=='function'&& btn.display(scope.row)==true)||btn.display==true))"
                     :type="((!!btn.type)? btn.type: 'primary')" :size="btn.size || size " :icon="btn.icon" @click="btn.handle(that,scope.row)">
                         {{btn.label}}
                         <span v-if="btn.htmlformatter" v-html="btn.htmlformatter(scope.row)"></span>
                     </el-link>
                 </template>
             </template>
             <template  v-if="col.type==='treeButton'">
                 <template v-for="(btn,btnIndex) in col.btnList" >
                     <el-link :key="btn.label" :style="''+ (btnIndex>0?'margin-left:5px;':'')"
                     v-if="(!btn.permission||(btn.permission&&checkPermission(btn.permission)))&&!(btn.hasOwnProperty('ishide')&&((typeof btn.ishide=='function'&& btn.ishide(that,scope.row)==true)||btn.ishide==true))&&!(btn.hasOwnProperty('display')&&((typeof btn.display=='function'&& btn.display(that,scope.row)==true)||btn.display==true))"
                     :disabled="(btn.hasOwnProperty('display')&&((typeof btn.display=='function'&& btn.display(scope.row)==true)||btn.display==true))"
                     :type="((!!btn.type)? btn.type: 'primary')" :size="btn.size || size " :icon="btn.icon" @click="btn.handle(that,scope.row)">
                         {{btn.label}}
                     </el-link>
                 </template>
             </template>
             <span v-if="col.type==='clickLink'" :style="col.style==null?'color:blue;cursor:pointer;':typeof(col.style)=='function'?col.style(that,scope.row,col,scope.row[col.prop]):column.style" @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">{{(col.formatter && tonumfuc(col.formatter(scope.row), col.label)) || tonumfuc(scope.row[col.prop], col.label)}}</span>
             <!-- "class="custom-button text-like-button"" 原: "type="text""-->
             <el-button v-else-if="col.type=='click'" :style="{color: col.color?col.color:''}" class="custom-button text-like-button"
             @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">
                 {{(col.formatter && tonumfuc(col.formatter(scope.row), col.label)) || tonumfuc(scope.row[col.prop], col.label)}}
             </el-button>
             <el-button v-else-if="col.type=='orderLogInfo'" :style="{color: col.color?col.color:''}" class="custom-button text-like-button"
             @click="showLogDetail(scope.row[col.prop],col.orderType,scope.row)">
                 {{(col.formatter && tonumfuc(col.formatter(scope.row), col.label)) || tonumfuc(scope.row[col.prop], col.label)}}
             </el-button>
             <div v-else-if="col.type=='ellips'"  type="text" @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">
                 <i class="vxe-icon-ellipsis-h"></i>
             </div>
             <div v-else-if="col.type=='html'"  v-html="col.formatter? col.formatter(scope.row): scope.row[col.prop]  "></div>
             <el-switch v-else-if="col.type=='switch'" v-model="scope.row[col.prop]" @change='col.change && col.change(scope.row,that)'></el-switch>
             <div class="wendang" v-else-if="col.type==='editor'" v-html="scope.row[col.prop]" @click="showImg($event)">{{scope.row[col.prop]}}</div>
             <span v-if="col.type=='custom'||!col.type" :style="col.itemStyle && col.itemStyle(scope.row)" :size="size || btn.size" :class="col.itemClass && col.column.itemClass(scope.row)">
             {{ (()=>{
                 if(col.formatter)
                     return tonumfuc(col.formatter(scope.row), col.label);
                 else
                     return tonumfuc(scope.row[col.prop], col.label);
             })() }}</span>
             <el-progress v-if="col.type==='progress'" :text-inside="true" :stroke-width="20" :percentage="Number(scope.row[col.prop])" :status="scope.row[col.prop]==100?'success':null"></el-progress>
             <div v-if="col.type=='treeStar'" >
                 <el-badge value="重" class="item" v-if="scope.row.isExitProvider==1" type="warning">
                     <span>{{ scope.row.providerName}}</span>
                 </el-badge>
                     <span v-else>{{ scope.row.providerName}}</span>
             </div>
             <div v-if="col.type=='treeStar1'" @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])" :style="col.style" >
                 <el-badge value="重" class="item" v-if="scope.row.isExitProvider==1" type="warning">
                     <span>{{ scope.row.providerName}}</span>
                 </el-badge>
                     <span v-else>{{ scope.row.providerName}}</span>
             </div>
             <div v-if="col.type=='changeColor'"  :style="{color: col.formatter(scope.row) ? 'red' : 'green'}" >
               <span>{{ scope.row[col.prop] }}</span>
             </div>
             <div v-if="col.type=='threeColor'"  :style="{color: col.formatter(scope.row)==2 ? 'red' : col.formatter(scope.row)==1 ? 'green' : '#787776'}" >
               <span>{{ scope.row[col.propstring] }}</span>
             </div>
             <template v-if="col.type==='files'  && scope.row[col.prop]&& scope.row[col.prop].length>2">
                     <template v-if=" JSON.parse(scope.row[col.prop]).length>1">
                         <span style="color:blue;cursor:pointer;"  @click="downloadFiles(scope.row[col.prop])">{{JSON.parse(scope.row[col.prop]).length}}个文件</span>
                     </template>
                     <template v-else>
                         <span style="color:blue;cursor:pointer;"  @click="downloadFile(scope.row[col.prop])">{{JSON.parse(scope.row[col.prop])[0].name}}</span>
                     </template>
             </template>

             <template v-if="col.type==='files'  && scope.row[col.prop]&& scope.row[col.prop].length>2">
                     <template v-if=" JSON.parse(scope.row[col.prop]).length>1">
                         <span style="color:blue;cursor:pointer;"  @click="downloadFiles(scope.row[col.prop])">{{JSON.parse(scope.row[col.prop]).length}}个文件</span>
                     </template>
                     <template v-else>
                         <span style="color:blue;cursor:pointer;"  @click="downloadFile(scope.row[col.prop])">{{JSON.parse(scope.row[col.prop])[0].name}}</span>
                     </template>
             </template>

             <div v-if="col.type=='xptooltip'">
                 <el-tooltip effect="dark"  placement="top-start">
                     <div slot="content">
                         <div  v-for="(item,i) in scope.row[col.props]" :key="i">{{item.platformName}}:
                             {{col.prop=='totalLastMonthSaleCount'?item.lastMonthSaleCount:
                             col.prop=='totalLastMonthSaleAmount'?item.lastMonthSaleAmount:
                             col.prop=='totalLastMonthProfitAmount'?item.lastMonthProfitAmount:
                             col.prop=='totalLastMonthProfitRate'?item.lastMonthProfitRate:'' }}
                             <br/></div>
                     </div>
                     <div class="textover" style="width: 80%;">{{ (col.formatter && tonumfuc(col.formatter(scope.row), col.label)) || tonumfuc(scope.row[col.prop], col.label) }}</div>
                 </el-tooltip>
             </div>
         </template>
         <template  #footer="{ items, _columnIndex }">
             <span :style="col.summaryEvent?'color: red;cursor:pointer;':'' ">{{ items[_columnIndex] }}</span>
         </template>
   </vxe-column>
  </div>


</template>

<script>
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
import tablejs from "@/utils/tablejs.js";
import { tonumfuc } from '@/utils/tonumqian.js'
export default {
  mixins: [tablejs],
  components: { OrderActionsByInnerNos },
  props: {
    that: { type: Object, default: this },
    col: { type: Object, default: () => {} },
    colIndex: { type: Number, default: 0 },
    size: { type: String, default: "mini" },
    type: { type: String, default: "primary" },
  },
  data() {
    return {
      imgPreview: { img: "", show: false },
      showImage: false,
      imgList: [],
      ImgWith: null,
      imagedefault: require("@/assets/images/detault.jpeg"),
    };
  },
  created() {},
  async mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.relativecss {
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0;
}
.zlevelTop {
  z-index: 2000;
}
.more-tran-animate {
  transition: 0.5s;
}
.moreModal {
  /* 如果碰到滑动问题，1.3 请检查 z-index。z-index需比web大一级*/
  z-index: 2000;
  position: fixed;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  // background-color: #337AB7;
  // line-height: 40px;
  text-align: center;
  // color: #fff;
  // opacity: 0.6;
}
.moreModal:hover {
  opacity: 1;
}
.six-more-modal-btn {
  position: fixed;
  z-index: 2000;
  width: 14rem;
  height: 14rem;
  border-radius: 5px;
  // background: #1A1A1A;
  // color: #fff;
}
.imgMore {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

/////////////

/*滚动条整体部分*/
.mytable-scrollbar20221212 ::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
/*滚动条的轨道*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-track {
  background-color: #ffffff;
}
/*滚动条里面的小方块，能向上向下移动*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
  background-color: #bfbfbf;
  border-radius: 5px;
  border: 1px solid #f1f1f1;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
  background-color: #787878;
}
/*边角，即两个滚动条的交汇处*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
  background-color: #ffffff;
}

// 图片大小
.mytable-scrollbar20221212 .images20221212 {
  max-width: 150px;
  max-height: 150px;
  width: 40px !important;
  height: 40px !important;
}

// 图片张数标记
.mytable-scrollbar20221212 .badgeimage20221212 .el-badge__content.is-fixed {
  top: 10px;
}

/*  工具箱位置  */
.vxetoolbar20221212 {
  position: absolute;
  top: 30px;
  right: 0px;
  padding-top: 0;
  padding-bottom: 0;
  z-index: 999;
  background-color: rgb(255 255 255 / 0%);
}

.vxetoolbar20221212 ::v-deep .vxe-custom--wrapper {
  margin-left: 0px !important;
}

.vxetableheadercell-left-20221216 {
  text-align: left;
}

.vxetableheadercell-center-20221216 {
  text-align: center;
}

.vxetableheadercell-right-20221216 {
  text-align: right;
}

::v-deep .vxe-table .vxe-cell--sort {
  width: 10px !important;
}
::v-deep .vxe-table--render-default .vxe-cell {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

.copyhover {
  display: none;
}
.relativebox {
  width: 80%;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}
.relativebox:hover {
  width: 80%;
}
.textover {
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}
.relativebox:hover .textover {
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.relativebox:hover .copyhover {
  display: block;
  position: absolute;
  top: 50%;
  left: 75%;
  margin: 0 10px;
  z-index: 99;
  transform: translate(-50%, -50%);
  color: #409eff;
  font-weight: 600;
}

.vxe-icon-ellipsis-h:hover {
  color: #409eff;
  margin-left: 2px;
  background-color: #f1f1f1;
}

.vxe-icon-ellipsis-h {
  color: #999;
  font-size: 15px;
}
.custom-button.text-like-button {
  background-color: transparent;
  border: none;
  padding: 0;
  cursor: pointer;
  user-select: text;
  color: #409eff;
}

.beforeBox {
  position: relative;
  height: 40px;
  .beforeBox_item1 {
    position: absolute;
    bottom: 0;
    left: 5px;
  }
  .beforeBox_item2 {
    position: absolute;
    top: 0;
    right: 5px;
  }
}
.alicenter {
  display: flex;
  align-items: center;
  justify-content: center;
}
.imgstyle {
  max-height: 50px !important;
}
</style>
