<template>
     <div :class="xTableIsFull ? 'xTableFull' : 'xTableMin' " style="background-color: #fff;">
<div :style="[{ height: (tableHandles != null && tableHandles.length>0 ? '95%' : '100%') }, { width: '100%' }, { 'margin': ' 0' }]">
    <el-button-group v-if="tableHandles.length>0">
        <template v-for='item in tableHandles'>
            <el-button v-if="(!item.permission || (item.permission && checkPermission(item.permission)))"
                :key='item.label' :size="item.size || size" :type="item.type || type" :icon='item.icon || ""'
                v-throttle="item.throttle || 1000" @click="item.handle(that)" :disabled="item.isDisabled ? item.isDisabled(that) : false">{{ item.label }}</el-button>
        </template>
        <slot name="extentbtn" ></slot>
    </el-button-group>
    <div class="vxetoolbar20221212 " v-if="isNeedExpend">
        <div style="position: relative;">
            <i class="el-icon-s-grid" @click="expendIsShow = !expendIsShow"></i>
            <div class="expendBtn" v-show="expendIsShow">
                <div style="display: flex;justify-content: end;align-items: center;">
                    <div class="fullClass" v-if="screenShow">
                        <i class="el-icon-full-screen" @click="xTableIsFull = !xTableIsFull"></i>
                    </div>
                    <vxe-toolbar ref="xToolbar" custom  v-if="toolbarshow">
                        <template #buttons>
                            <slot name="tbHeader" ></slot>
                        </template>
                    </vxe-toolbar>
                </div>
            </div>
        </div>
     </div>
    <!-- <vxe-toolbar ref="xToolbar" custom class="vxetoolbar20221212" v-if="toolbarshow">
          <template #buttons>
            <slot name="tbHeader" ></slot>
          </template>
    </vxe-toolbar> -->
    <vxe-table
        :row-config="{isHover: true, keyField:keyField}"
        :export-config="{}"
        :size="size"
        :column-config="{resizable: resizable,maxFixedSize: 300, maxFixedSize: 20,
	resizable: true,}"
        @custom="tooclick"
        :tree-config="treeProp"
        :show-header-overflow="showheaderoverflow"
        :align="align"
        resizable ref="xTable" :loading="loading"
        :data="tableData"
        :sort-config="{sortMethod: customSortMethod}"
        :custom-config="{storage: isstorage,restoreStore:restoreStore,updateStore:updateStore}"
        :scroll-y="{enabled: true, gt: 0}"
        :scroll-x="{enabled: enabledx, gt: 100, oSize: oSizex}"
        :show-overflow="showoverflow"
        class="vxetable202212161323 mytable-scrollbar20221212"
        :cell-style="cellStyleFun"
        :footer-cell-style="footerCellStyle"
        height="100%"
        width="100%"
        :stripe="true"
        :border="border"
        :id="id"
        :edit-config="editconfig"
        :header-cell-class-name="headerCellClassName"
        :show-footer="showsummary"
        :footer-method="footerMethod"
        :header-cell-style="headerCellStyle"
        v-bind:name="tablekey"
        :checkbox-config="{labelField: '', highlight: true, range: enableCheckRange, checkMethod: checCheckboxkMethod2}"
        @checkbox-range-end="selectAllEvent"
        @cell-click="selectAllEvent"
        @checkbox-all="checkboxall"
        @footer-cell-click="footercellclick"
        @checkbox-change="selectChangeEvent"
        :cell-class-name="cellClassName"
        @toggle-tree-expand="toggleTreeExpand"
    >
    <!--  -->
    <!-- border show-overflow-->

        <vxe-column :show-header-overflow="showheaderoverflow" v-if="hasSeq" type="seq" title="#" :width="indexWidth" :fixed="isIndexFixed?'left':''" :align="seqAlign">
            <template v-if="isSelectLvl" #header>
                <el-select v-model="currentLvl" placeholder="超小尺寸" size="mini" @change="lvlChang" style="width:50px;">
                    <el-option v-for="num in 9" :key="num" :value="num" :label="`+${num}`"></el-option>
                </el-select>
            </template>
            <template #footer="{ items, _columnIndex }">
                <span ></span>
            </template>
        </vxe-column>



        <slot name="left" ></slot>

        <template v-for="(col,colIndex) in tableCols">
                <template v-if="!col.type&&!col.merge">
                    <vxe-column :show-header-overflow="showheaderoverflow" :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                    :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                    :title-help="col.tipmesg?{message: col.tipmesg}:null"
                    :tree-node="!!col.treeNode? true:false"
                    :width="col.width == 'auto' ? '' : col.width"
                    :min-width="col.minwidth?col.minwidth:null"
                    :sortable="!!col.sortable"
                    :fixed="col.fixed?col.fixed:''"
                    :align="col.align?col.align:'center'"
                    :visible="typeof col.visible === 'undefined' || col.visible === true"
                    >
                        <template #default="{ row,rowIndex }">
                            <slot :name="col.prop" v-bind:row="row" v-bind:index="rowIndex">
                                <template v-if="col.isSeriesField">
                                    <el-button type="text" @click="onTrendChart(row,col.prop)">
                                        <span :style="{color: col.color ? col.color(row) : ''}">{{col.formatter? col.formatter(row,that):format(row,col)}}</span>
                                    </el-button>
                                </template>
                                <template v-else>
                                  <span v-if="canJump(row, col)">
                                    <a @click="handleLinkClick(row, col)" style="color: blue; cursor: pointer;"> {{ (() => {
                                        if (col.formatter)
                                          return tonumfuc(col.formatter(row,that), col.label);
                                        else
                                          return tonumfuc(row[col.prop], col.label); })() }} </a>
                                  </span>
                                  <span v-else :style="{color: col.color ? col.color(row) : ''}">{{col.formatter? col.formatter(row,that):format(row,col)}}</span>
                                </template>
                            </slot>
                        </template>
                        <template  #footer="{ items, _columnIndex }">
                            <span :style="col.summaryEvent?'color: red;cursor:pointer;':'' ">{{ items[_columnIndex] }}</span>
                        </template>
                    </vxe-column>

                </template>

                <vxe-column ::show-header-overflow="showheaderoverflow" :width="col.width ? col.width : 60" v-else-if="col.type=='Tags'" :key="colIndex" :title="col.label" :align="col.align?col.align:'center'">
                    <template #default="{ row,rowIndex }">
                            <slot :name="col.prop" v-bind:row="row" v-bind:index="rowIndex">
                                <template v-for="tag in row[col.prop]" >
                                    <el-tooltip effect="dark" :key="tag.id" v-if="tag.toolTip" placement="right">
                                        <div slot="content" v-html="tag.toolTip"/>
                                            <el-tag  class="tag">
                                            {{ tag.propFormatVal }}
                                            </el-tag>
                                    </el-tooltip>
                                    <el-tag class="tag" :key="tag.id" v-else>
                                        {{ tag.propFormatVal }}
                                    </el-tag>

                                </template>
                            </slot>
                        </template>
                </vxe-column>

                <vxe-column  v-else-if="col.type=='Boolean'"
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow
                :width="col.width == 'auto' ? '' : col.width"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="col.align?col.align:'center'"
                >
                    <template #default="{ row }">
                        {{ row[col.prop]?"是":"否" }}
                        </template>
                </vxe-column>

                <vxe-column :show-header-overflow="showheaderoverflow" type="checkbox" :width="col.width ? col.width : 60" v-else-if="col.type=='checkbox'"  fixed="left" :key="colIndex"></vxe-column>

                <vxe-column :show-header-overflow="showheaderoverflow" type="seq" width="60" v-else-if="col.type=='seqright'"  fixed="left" :key="colIndex"></vxe-column>

                <vxe-column :show-header-overflow="showheaderoverflow"  v-else-if="col.type=='colslot'"
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow  :tree-node="!!col.treeNode? true:false"
                :width="col.width == 'auto' ? '' : col.width"
                :title-help="col.tipmesg?{message: col.tipmesg}:null"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="col.align?col.align:'center'"
                >
                    <template #default="{ row }">
                        <slot name="colslot" :col="row"></slot>
                    </template>
                </vxe-column>

                <vxe-column :show-header-overflow="showheaderoverflow"  v-else-if="col.type=='images'"
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow  :tree-node="!!col.treeNode? true:false"
                :width="col.width == 'auto' ? '' : col.width"
                :title-help="col.tipmesg?{message: col.tipmesg}:null"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="col.align?col.align:'center'"
                >
                    <template #default="{ row }">

                        <template v-if=" row && !!row[col.prop] && row[col.prop].length>2">

                            <template v-if="row[col.prop][0]=='[' && JSON.parse(row[col.prop]).length>1">
                                <el-badge
                                    class="badgeimage20221212"
                                    :value="JSON.parse(row[col.prop]).length" style="margin-top:0px;margin-right:40px;">
                                    <el-image  :src="(row[col.prop][0]=='['?(formatImg(row[col.prop])[0].url):(formatImg(row[col.prop]) ? formatImg(row[col.prop])[0] : '' ) )"
                                    class="images20221212"
                                    :preview-src-list="(row[col.prop][0]=='['
                            ?(()=>{
                                let tempArray=JSON.parse(row[col.prop]);
                                let tempRltArr=[];
                                tempArray.forEach(x=>tempRltArr.push(x.url));
                                return tempRltArr
                            })()
                            :(()=>{
                                return [row[col.prop]]
                            })()  )">
                                    </el-image>
                                </el-badge>
                            </template>
                            <template v-else>
                                <el-image :src="formatImg(row[col.prop])[0].url ? formatImg(row[col.prop])[0].url : formatImg(row[col.prop])[0]"
                                class="images20221212"
                                :preview-src-list="(row[col.prop][0]=='['
                            ?(()=>{
                                let tempArray=JSON.parse(row[col.prop]);
                                let tempRltArr=[];
                                tempArray.forEach(x=>tempRltArr.push(x.url));
                                return tempRltArr
                            })()
                            :(()=>{
                                return [row[col.prop]]
                            })()  )">
                                <!-- <el-image  class="imgstyle" :src="row[col.prop]"> -->
                                    <div slot="error" class="image-slot">
                                        <el-image></el-image>
                                    </div>
                                <!-- </el-image> -->
                                </el-image>
                            </template>
                        </template>


                    </template>
                </vxe-column>

                <vxe-column  v-else-if="col.type=='ddAvatar'"
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow  :tree-node="!!col.treeNode? true:false"
                :width="col.width == 'auto' ? '' : col.width"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="col.align?col.align:'center'"
                >
                    <template #default="{ row }" v-if="col.ddInfo">
                        <!-- {{ col.ddInfo.type }} -->
                        <!-- 1运营组  2运营  3采购  4Userid  5dduserId -->
                            <el-image class="userAvatar"  :src="`http://192.168.16.240:8001/api/Admin/User/GetUserAvatar?type=${col.ddInfo.type}&id=${row[col.ddInfo.prop]}`"
                            :preview-src-list="[`http://192.168.16.240:8001/api/Admin/User/GetUserAvatar?type=${col.ddInfo.type}&id=${row[col.ddInfo.prop]}`]"/>
                        </template>
                </vxe-column>

                <vxe-column  v-else-if="col.type=='ddTalk'"
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow  :tree-node="!!col.treeNode? true:false"
                :width="col.width == 'auto' ? '' : col.width"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="col.align?col.align:'center'"
                >
                    <template #default="{ row }">
                        <div class="nameBox" v-if="row[col.ddInfo.prop] && row[col.ddInfo.name] && row[col.ddInfo.name]!= ' '">
                            <el-image :src="ddLogo" style="border-radius:5px;" class="ddTalk"
                                @click="startSession(row[col.ddInfo.prop],col.ddInfo.type)" />
                                <el-button type="text" style="cursor: pointer;max-width: 40px;" v-if="col.handle && typeof col.handle == 'function'" @click="col.handle&&col.handle(that,row)">{{ col.formatter ? col.formatter(row) : row[col.ddInfo.name] }}</el-button>
                                <div v-else class="name">{{ col.formatter ? col.formatter(row) : row[col.ddInfo.name] }} </div>
                        </div>
                        <div v-else></div>
                    </template>
                </vxe-column>

                <vxe-column  v-else-if="col.type=='treeimages'"
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow  :tree-node="!!col.treeNode? true:false"
                :width="col.width == 'auto' ? '' : col.width"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="col.align?col.align:'center'"
                >
                    <template #default="{ row }">
                            <el-image  v-if="col.formatter"  :src="col.formatter? (formatImg(col.formatter(row)) ? formatImg(col.formatter(row))[0] : '') : formatImg(row[col.prop]) " :preview-src-list="[col.formatter? col.formatter(row): row[col.prop]]"></el-image>
                        </template>
                </vxe-column>

                <vxe-column  v-else-if="col.type=='carouselimages'"
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow  :tree-node="!!col.treeNode? true:false"
                :width="col.width == 'auto' ? '' : col.width"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="col.align?col.align:'center'"
                >
                  <template #default="{ row }">
                      <el-image  v-if="col.formatter"  :src="col.formatter? (formatImg(col.formatter(row)) ? formatImg(col.formatter(row))[0] : '') : formatImg(row[col.prop]) " @click="col.handle&&col.handle(that,row,col,row[col.prop])"></el-image>
                  </template>
                </vxe-column>

                <vxe-column :show-header-overflow="showheaderoverflow"  v-else-if="col.type=='danimages'"
                :field="col.prop? col.prop : ('col'+ colIndex)"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow  :tree-node="!!col.treeNode? true:false"
                :width="col.width == 'auto' ? '' : col.width"
                :title-help="col.tipmesg?{message: col.tipmesg}:null"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="col.align?col.align:'center'"
                >
                    <template #default="{ row }">



                            <template>

                                <el-image  :src="formatImg(row[col.chiprop]) ? (formatImg(row[col.chiprop]) ? formatImg(row[col.chiprop])[0] : '') : (formatImg(row[col.prop]) ? formatImg(row[col.prop])[0] : '')"
                                class="images20221212"
                                :preview-src-list="[row[col.chiprop]?row[col.chiprop]:row[col.prop]]">
                                    <div slot="error" class="image-slot">
                                        <el-image></el-image>
                                    </div>
                                </el-image>
                            </template>


                    </template>
                </vxe-column>

                <vxe-column :show-header-overflow="showheaderoverflow"  v-else-if="col.type=='imagess'"
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                show-overflow  :tree-node="!!col.treeNode? true:false"
                :width="col.width == 'auto' ? '' : col.width"
                :title-help="col.tipmesg?{message: col.tipmesg}:null"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="col.align?col.align:'center'"
                >
                    <template #default="{ row }">
                        <template v-if="col.type==='imagess'">
                            <template>
                                <div class="alicenter">
                                    <el-badge :value="row[col.prop].length" style="margin-top:10px;">
                                        <el-image  class="imgstyle" :src="formatImg(row[col.prop])?formatImg(row[col.prop])[0]:imagedefault" fit="fill" :preview-src-list="row[col.prop]">
                                        </el-image>
                                    </el-badge>
                                    <el-button v-if="row[col.props]"  type="text" @click="col.handle&&col.handle(that,row)">{{col.labels}}</el-button>
                                </div>
                            </template>
                        </template>

                    </template>
                </vxe-column>

                <template v-else-if="col.merge" :width="col.width == 'auto' ? '' : col.width" >
                    <vxe-colgroup :title="col.label" :show-header-overflow="showheaderoverflow" :key="col.label" :field="col.label" :fixed="col.fixed?col.fixed:''" :title-help="col.tipmesg?{message: col.tipmesg}:null" :align="col.align?col.align:'center'">
                        <vxe-column :show-header-overflow="showheaderoverflow" :field="coll.prop? coll.prop : ('col'+ colIndex)"
                        :title="coll.label?coll.label:((coll.type && coll.type=='color' || coll.type=='split')?'|':'')"
                        show-overflow :tree-node="!!coll.treeNode? true:false"
                        :width="col.width == 'auto' ? '' : col.width"
                        :title-help="coll.tipmesg?{message: coll.tipmesg}:null"
                        v-for="(coll, colindex) in col.cols" :key="colindex"
                        :min-width="coll.minwidth?coll.minwidth:null"
                        :sortable="!!coll.sortable"

                        :align="coll.align?coll.align:'center'"
                        >
                            <template #default="{ row }" v-if="coll.formatter">
                                {{coll.formatter? coll.formatter(row): row[coll.prop]}}
                            </template>
                            <template #default="scope">
                            <span v-if="coll.type=='color' || coll.type=='split'" >
                                |
                            </span>
                            <template  v-if="coll.type==='button'">
                                <template v-for="(btn,btnIndex) in coll.btnList" >
                                    <el-link :key="btn.label" :style="''+ (btnIndex>0?'margin-left:5px;':'')"
                                    v-if="(!btn.permission||(btn.permission&&checkPermission(btn.permission)))&&!(btn.hasOwnProperty('ishide')&&((typeof btn.ishide=='function'&& btn.ishide(that,scope.row)==true)||btn.ishide==true))"
                                    :disabled="(btn.hasOwnProperty('display')&&((typeof btn.display=='function'&& btn.display(scope.row)==true)||btn.display==true))"
                                    :type="((!!btn.type)? btn.type: 'primary')" :size="btn.size || size " :icon="btn.icon" @click="btn.handle(that,scope.row)">
                                    {{btn.label}}
                                    </el-link>
                                </template>
                            </template>
                            <span v-else-if="coll.type=='click'" style="cursor: pointer;" :style="{color: coll.color?coll.color:'#409EFF'}" @click="coll.handle&&coll.handle(that,scope.row,coll,scope.row[coll.prop])">
                                {{(coll.formatter && coll.formatter(scope.row)) || scope.row[coll.prop]}}
                            </span>
                            <div v-else-if="coll.type=='html'"  v-html="coll.formatter? coll.formatter(scope.row): scope.row[coll.prop]  " @click="handleClick($event,scope.row[coll.prop])"></div>
                            <el-switch v-else-if="coll.type=='switch'" v-model="scope.row[coll.prop]" @change='coll.change && coll.change(scope.row,that)'></el-switch>
                            <div v-if="coll.type=='image'">
                                <template v-if=" scope.row && !!scope.row[coll.prop] && scope.row[coll.prop].length>2">
                            <template v-if="scope.row[coll.prop][0]=='[' && JSON.parse(scope.row[coll.prop]).length>1">
                                <el-badge
                                    class="badgeimage20221212"
                                    :value="JSON.parse(scope.row[coll.prop]).length" style="margin-top:0px;margin-right:40px;">
                                    <el-image  :src="formatImg(scope.row[coll.prop]) ? formatImg(scope.row[coll.prop])[0].url : formatImg(scope.row[coll.prop])"
                                    class="images20221212"
                                    :preview-src-list="(scope.row[coll.prop][0]=='['
                            ?(()=>{
                                let tempArray=JSON.parse(scope.row[coll.prop]);
                                let tempRltArr=[];
                                tempArray.forEach(x=>tempRltArr.push(x.url));
                                return tempRltArr
                            })()
                            :(()=>{
                                return [scope.row[coll.prop]]
                            })()  )">
                                    </el-image>
                                </el-badge>
                            </template>
                            <template v-else>
                                <el-image  :src="formatImg(scope.row[coll.prop]) ? formatImg(scope.row[coll.prop])[0].url : formatImg(scope.row[coll.prop])"

                                class="images20221212"
                                :preview-src-list="(scope.row[coll.prop][0]=='['
                            ?(()=>{
                                let tempArray=JSON.parse(scope.row[coll.prop]);
                                let tempRltArr=[];
                                tempArray.forEach(x=>tempRltArr.push(x.url));
                                return tempRltArr
                            })()
                            :(()=>{
                                return [scope.row[coll.prop]]
                            })()  )">
                                <!-- <el-image  class="imgstyle" :src="scope.row[coll.prop]"> -->
                                    <div slot="error" class="image-slot">
                                        <el-image></el-image>
                                    </div>
                                <!-- </el-image> -->
                                </el-image>
                            </template>
                            </template>
                            </div>
                                <span v-if="coll.type==='clickLink'" :style="coll.style==null?'color:blue;cursor:pointer;':typeof(coll.style)=='function'?coll.style(that,scope.row,coll,scope.row[coll.prop]):column.style" @click="coll.handle&&coll.handle(that,scope.row,coll,scope.row[coll.prop])">{{(coll.formatter && coll.formatter(scope.row)) || scope.row[coll.prop]}}</span>
                            <span v-if="coll.type=='custom'||!coll.type" :style="coll.itemStyle && coll.itemStyle(scope.row)" :size="size || btn.size" :class="coll.itemClass && coll.column.itemClass(scope.row)">
                            {{ (()=>{
                                if(coll.formatter)
                                    return coll.formatter(scope.row);
                                    return scope.row[coll.prop];
                            })() }}</span>
                            <el-progress v-if="coll.type==='progress'" :text-inside="true" :stroke-width="20" :percentage="Number(scope.row[coll.prop])" :status="scope.row[coll.prop]==100?'success':null"></el-progress>
                            <div v-if="coll.type == 'before'" class="beforeBox">
                              <div class="beforeBox_item1">{{ scope.row[coll.prop][0] }}</div>
                              <div class="beforeBox_item2" :style="{color: (scope.row[coll.prop][1] > 0 ) ? 'red' : (scope.row[coll.prop][1] < 0 ) ? 'green' : 'gray'}">{{ scope.row[coll.prop][1] }}</div>
                            </div>

                        </template>
                        <template  #footer="{ items, _columnIndex }">
                            <span :style="col.summaryEvent?'color: red;cursor:pointer;':'' ">{{ items[_columnIndex] }}</span>
                        </template>
                        </vxe-column>
                    </vxe-colgroup>
                </template>


                <vxe-column  v-else
                :show-header-overflow="showheaderoverflow"
                :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.prop"
                :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                :show-overflow="col.showoverflow?col.showoverflow:true"  :tree-node="!!col.treeNode? true:false"
                :width="col.width == 'auto' ? '' : col.width"
                :title-help="col.tipmesg?{message: col.tipmesg}:null"
                :min-width="col.minwidth?col.minwidth:null"
                :sortable="!!col.sortable"
                :fixed="col.fixed?col.fixed:''"
                :align="(col.type && (col.type=='color' || col.type=='split' || col.type=='images' || col.type=='image'))?'center':  (col.align?col.align:'left')"
                >
                    <template #default="scope">
                        <span v-if="col.type=='color' || col.type=='split'" >
                            |
                        </span>
                        <template v-if="col.type==='star'">
                            <span v-for="(item,i) in scope.row.star" :key="i">
                            <i v-if="item[col.starProp]>0&&item[col.starProp]<10" style="color:red">个</i>
                            <i v-else-if="item[col.starProp]>=10&&item[col.starProp]<100" style="color:red">十</i>
                            <i v-else-if="item[col.starProp]>=100&&item[col.starProp]<1000" style="color:red">百</i>
                            <i v-else-if="item[col.starProp]>=1000&&item[col.starProp]<10000" style="color:red">千</i>
                            <i v-else-if="item[col.starProp]>=10000&&item[col.starProp]<100000" style="color:red">万</i>
                            <i v-else-if="item[col.starProp]>=100000" style="color:red">萬</i>
                            <i v-else-if="item[col.starProp]>-10&&item[col.starProp]<0" style="color:rgb(8, 108, 11)">个</i>
                            <i v-else-if="item[col.starProp]>-100&&item[col.starProp]<=-10" style="color:rgb(8, 108, 11)">十</i>
                            <i v-else-if="item[col.starProp]>-1000&&item[col.starProp]<=-100" style="color:rgb(8, 108, 11)">百</i>
                            <i v-else-if="item[col.starProp]>-10000&&item[col.starProp]<=-1000" style="color:rgb(8, 108, 11)">千</i>
                            <i v-else-if="item[col.starProp]>-100000&&item[col.starProp]<=-10000" style="color:rgb(8, 108, 11)">万</i>
                            <i v-else-if="item[col.starProp]<=-100000" style="color:rgb(8, 108, 11)">萬</i>
                            <i v-else style="color:gray">X</i>
                        </span>
                        </template>
                       <template v-if="col.type==='newstar'">
                        <i v-if="scope.row.star==null"></i>
                        <i v-else-if="scope.row.star==0"></i>
                        <i v-else-if="scope.row.star==1" class="el-icon-star-on" style="color:red"></i>
                        <i v-else-if="scope.row.star==2" class="el-icon-star-on" style="color:orange"></i>
                        <i v-else-if="scope.row.star==3" class="el-icon-star-on" style="color:yellow"></i>
                        <i v-else-if="scope.row.star==4" class="el-icon-star-on" style="color:green"></i>
                        <i v-else-if="scope.row.star==5" class="el-icon-star-on" style="color:blue"></i>
                        <i v-else-if="scope.row.star==6" class="el-icon-star-on" style="color:indigo"></i>
                        <i v-else-if="scope.row.star==7" class="el-icon-star-on" style="color:purple"></i>
                        <i v-else style="color:gray" class="el-icon-star-on"></i>
                       </template>
                       <template v-if="col.type==='flag'">
                        <i v-if="scope.row.flag==null"></i>
                        <i v-else-if="scope.row.flag==0"></i>
                        <i v-else-if="scope.row.flag==1" class="el-icon-s-flag
                        " style="color:red"></i>
                        <i v-else-if="scope.row.flag==2" class="el-icon-s-flag
                        " style="color:orange"></i>
                        <i v-else-if="scope.row.flag==3" class="el-icon-s-flag
                        " style="color:yellow"></i>
                        <i v-else-if="scope.row.flag==4" class="el-icon-s-flag
                        " style="color:green"></i>
                        <i v-else-if="scope.row.flag==5" class="el-icon-s-flag
                        " style="color:blue"></i>
                        <i v-else-if="scope.row.flag==6" class="el-icon-s-flag
                        " style="color:indigo"></i>
                        <i v-else-if="scope.row.flag==7" class="el-icon-s-flag
                        " style="color:purple"></i>
                        <i v-else style="color:gray" class="el-icon-s-flag
                        "></i>
                       </template>

                       <template v-else-if="col.type==='copy'">
                            <div class="relativebox">
                                <el-tooltip effect="dark" :content="scope.row[col.prop]" placement="top-start">
                                <div class="textover" style="width: 80%;">{{ scope.row[col.prop] }}</div>
                                </el-tooltip>

                                <div class="copyhover" @click="copytext(scope.row[col.prop])">
                                    <i class="el-icon-document-copy"></i>
                                </div>
                            </div>
                        </template>
                        <template v-else-if="col.type==='echarts'">
                            <!--
                                1、行图表目前比较单一
                                2、行图表里col定义prop是用来指定排序的字段名，chartProp是用来指定图表数据的字段名
                                3、chartProp字段用后台类：Row7DayEchartsDtoSingle来进行输出
                             -->
                            <div  v-loading="echartsLoading" style="height: 40px;width:100%;">
                                <div style="height: 40px;width:100%;"
                                :id="'rptIdecharts'+id+'_'+scope.columnIndex+'_'+scope.rowIndex+'_'+(col.sign ? scope.row[col.sign] : '')"
                                :ref="'rptIdecharts'+id+'_'+scope.columnIndex+'_'+scope.rowIndex+'_'+(col.sign ? scope.row[col.sign] : '')"
                                v-bind="{ chartData:JSON.stringify(scope.row[col.chartProp])}"
                                ></div>
                            </div>
                        </template>

                        <template  v-if="col.type==='button'">
                            <template v-for="(btn,btnIndex) in col.btnList" >
                                  <el-link :key="btn.label" :style="''+ (btnIndex>0?'margin-left:5px;':'')" v-show="!btn.color?true:false"
                                  v-if="(!btn.permission||(btn.permission&&checkPermission(btn.permission)))&&!(btn.hasOwnProperty('ishide')&&((typeof btn.ishide=='function'&& btn.ishide(that,scope.row)==true)||btn.ishide==true))"
                                  :disabled="(btn.hasOwnProperty('display')&&((typeof btn.display=='function'&& btn.display(scope.row)==true)||btn.display==true))"
                                  :type="((!!btn.type)? btn.type: 'primary')" :size="btn.size || size " :icon="btn.icon" @click="btn.handle(that,scope.row)">
                                      {{btn.label}}
                                      <span v-if="btn.htmlformatter" v-html="btn.htmlformatter(scope.row)"></span>
                                  </el-link>
                                  <span v-show="!btn.color?false:true" :style="btn.itemStyle??{}" @click="btn.handle?btn.handle(that,scope.row):()=>{}"> {{scope.row[btn.prop]??btn.label}}</span>
                            </template>
                        </template>
                        <template  v-if="col.type==='treeButton'">
                            <template v-for="(btn,btnIndex) in col.btnList" >
                                <el-link :key="btn.label" :style="''+ (btnIndex>0?'margin-left:5px;':'')"
                                v-if="(!btn.permission||(btn.permission&&checkPermission(btn.permission)))&&!(btn.hasOwnProperty('ishide')&&((typeof btn.ishide=='function'&& btn.ishide(that,scope.row)==true)||btn.ishide==true))&&!(btn.hasOwnProperty('display')&&((typeof btn.display=='function'&& btn.display(that,scope.row)==true)||btn.display==true))"
                                :disabled="(btn.hasOwnProperty('display')&&((typeof btn.display=='function'&& btn.display(scope.row)==true)||btn.display==true))"
                                :type="((!!btn.type)? btn.type: 'primary')" :size="btn.size || size " :icon="btn.icon" @click="btn.handle(that,scope.row)">
                                    {{btn.label}}
                                </el-link>
                            </template>
                        </template>
                        <span v-if="col.type==='clickLink'" :style="col.style==null?'color:blue;cursor:pointer;':typeof(col.style)=='function'?col.style(that,scope.row,col,scope.row[col.prop]):column.style" @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">{{(col.formatter && col.formatter(scope.row)) || scope.row[col.prop]}}</span>
                        <!-- "class="custom-button text-like-button"" 原: "type="text""-->
                        <el-button v-else-if="col.type=='click'" :style="{color: col.color?col.color:''}" class="custom-button text-like-button"
                        @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">
                            {{(col.formatter && col.formatter(scope.row)) || scope.row[col.prop]}}
                        </el-button>

                        <el-button v-else-if="col.type=='colorClick'" :style="col.style && col.style(that ,scope.row)" class="custom-button text-like-button"
                        @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">
                            {{(col.formatter && col.formatter(scope.row)) || scope.row[col.prop]}}
                        </el-button>

                        <el-button v-else-if="col.type=='orderLogInfo'" :style="{color: col.color?col.color:''}" class="custom-button text-like-button"
                        @click="showLogDetail(scope.row[col.prop],col.orderType,scope.row)">
                            {{(col.formatter && col.formatter(scope.row)) || scope.row[col.prop]}}
                        </el-button>
                        <div v-else-if="col.type=='ellips'"  type="text" @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">
                            <i class="vxe-icon-ellipsis-h"></i>
                        </div>
                        <div v-else-if="col.type=='html'"  v-html="col.formatter? col.formatter(scope.row): scope.row[col.prop]  " @click="handleClick($event,scope.row[col.prop])"></div>
                        <div v-else-if="col.type=='htmlOther'" style="display: flex;align-items: center;" :style="{color: col.handle?'#409EFF':''}" @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">
                            <div v-html="col.formatter? col.formatter(scope.row): scope.row[col.prop]"></div>
                            <el-tooltip class="item" effect="dark" :content="col.tootip" placement="top-start">
                                <i class="el-icon-warning-outline" v-if="col.isBrage&&typeof(col.isBrage)=='function'&&col.isBrage(scope.row)"></i>
                            </el-tooltip>
                        </div>
                        <el-switch v-else-if="col.type=='switch'" v-model="scope.row[col.prop]" @change='col.change && col.change(scope.row,that)'></el-switch>
                        <div class="wendang" v-else-if="col.type==='editor'" v-html="scope.row[col.prop]" @click="showImg($event)">{{scope.row[col.prop]}}</div>
                        <span v-if="col.type=='custom'||!col.type" :style="col.itemStyle && col.itemStyle(scope.row)" :size="size || btn.size" :class="col.itemClass && col.column.itemClass(scope.row)">
                          <template v-if="canJump(scope.row, col)">
                            <a @click="handleLinkClick(scope.row, col)" style="color: blue; cursor: pointer;">{{ (() => {
                              if (col.formatter)
                                return col.formatter(scope.row);
                              else
                                return scope.row[col.prop]; })() }}</a>
                          </template>
                          <template v-else>
                        {{ (()=>{
                            if(col.formatter)
                                return col.formatter(scope.row);
                            else
                                return scope.row[col.prop];
                            })() }}
                          </template>
                        </span>
                        <el-progress v-if="col.type==='progress'" :text-inside="true" :stroke-width="20" :percentage="Number(scope.row[col.prop])" :status="scope.row[col.prop]==100?'success':null"></el-progress>
                        <div v-if="col.type=='treeStar'" >
                            <el-badge value="重" class="item" v-if="scope.row.isExitProvider==1" type="warning">
                                <span>{{ scope.row.providerName}}</span>
                            </el-badge>
                                <span v-else>{{ scope.row.providerName}}</span>
                        </div>
                        <div v-if="col.type=='treeStar1'" @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])" :style="col.style" >
                            <el-badge value="重" class="item" v-if="scope.row.isExitProvider==1" type="warning">
                                <span>{{ scope.row.providerName}}</span>
                            </el-badge>
                                <span v-else>{{ scope.row.providerName}}</span>
                        </div>
                        <div v-if="col.type == 'levelbefore'">
                          <div>
                            <span style="margin-right: 3px;">{{ scope.row[col.prop][0] ? scope.row[col.prop][0] : 0}}</span>
                            <span :style="{color: (scope.row[col.prop][1] >= 0 ) ? '#ff3c31' : '#36c85b' , backgroundColor: (scope.row[col.prop][1] >= 0 ) ? '#ffebea' : '#eaf9ee' }">
                                <i v-if="col.icon" :class="(scope.row[col.prop][1] >= 0 ) ? 'el-icon-top' : 'el-icon-bottom' "></i>
                                {{ scope.row[col.prop][1] ? Math.abs(scope.row[col.prop][1]) : 0 }}%
                            </span>
                          </div>
                        </div>
                        <div v-if="col.type=='tagline'">
                          <div :style="{ width: col.width + 'px'}" class="tagpattern">
                            <el-tooltip class="box-card" effect="light" placement="top">
                                <div slot="content" style="display: flex; flex-direction: column;">
                                <el-tag v-for="(tag, index) in scope.row[col.prop].split(',')" :key="index" type="info" class="tagpattern_item tag-with-border" style="margin-bottom: 5px;">{{ tag }}</el-tag>
                                </div>
                                <el-tag v-for="(tag, index) in scope.row[col.prop].split(',')" :key="index" type="info" class="tagpattern_item">{{ tag }}</el-tag>
                            </el-tooltip>
                            <span v-if="scope.row[col.prop].split(',').length > 1">...</span>
                          </div>
                        </div>
                        <div v-if="col.type=='changeColor'"  :style="{color: col.formatter(scope.row) ? 'red' : 'green'}" >
                          <span>{{ scope.row[col.prop] }}</span>
                        </div>
                        <div v-if="col.type=='threeColor'"  :style="{color: col.formatter(scope.row)==2 ? 'red' : col.formatter(scope.row)==1 ? 'green' : '#787776'}" >
                          <span>{{ scope.row[col.propstring] }}</span>
                        </div>
                        <template v-if="col.type==='files'  && scope.row[col.prop]&& scope.row[col.prop].length>2">
                                <template v-if=" JSON.parse(scope.row[col.prop]).length>1">
                                    <span style="color:blue;cursor:pointer;"  @click="downloadFiles(scope.row[col.prop])">{{JSON.parse(scope.row[col.prop]).length}}个文件</span>
                                </template>
                                <template v-else>
                                    <span style="color:blue;cursor:pointer;"  @click="downloadFile(scope.row[col.prop])">{{JSON.parse(scope.row[col.prop])[0].name}}</span>
                                </template>
                        </template>
                        <div v-if="col.type=='xptooltip'">
                            <el-tooltip effect="dark"  placement="top-start">
                                <div slot="content">
                                    <div  v-for="(item,i) in scope.row[col.props]" :key="i">{{item.platformName}}:
                                        {{col.prop=='totalLastMonthSaleCount'?item.lastMonthSaleCount:
                                        col.prop=='totalLastMonthSaleAmount'?item.lastMonthSaleAmount:
                                        col.prop=='totalLastMonthProfitAmount'?item.lastMonthProfitAmount:
                                        col.prop=='totalLastMonthProfitRate'?item.lastMonthProfitRate:'' }}
                                        <br/></div>
                                </div>
                                <div class="textover" style="width: 80%;" :style="{ color: col.trendChart ? '#409EFF' : 'black' }" :class="{ 'cursor-pointer': col.trendChart }" @click="col.trendChart && col.handle && col.handle(that,scope.row,col,scope.row[col.prop])" >
                                  {{ (col.formatter && col.formatter(scope.row)) || scope.row[col.prop] }}
                                </div>
                            </el-tooltip>
                        </div>
                    </template>
                    <template  #footer="{ items, _columnIndex }">
                        <span :style="col.summaryEvent?'color: red;cursor:pointer;':'' ">{{ items[_columnIndex] }}</span>
                    </template>
                </vxe-column>

        </template>


        <slot name="right" ></slot>

    </vxe-table>

    <el-dialog :visible.sync="exportColumnsDialogProp.visible" width="300" draggable="true" title="导出选项"  v-dialogDrag>
        <el-row>
            <el-col :span="24" style="height:400px;overflow: auto;">
                <el-tree
                ref="exportColTree"
                :data="exportColumnsDialogProp.data"
                show-checkbox
                node-key="id"
                default-expand-all
                :default-checked-keys="exportColumnsDialogProp.colIds"
                :props="{
                    'label':'title'
                }"
                >
                </el-tree>

            </el-col>
            <el-col :span="24" style="text-align:right">
                <el-button type="primary" @click="setExportCols">确定</el-button>
                <el-button @click="exportColumnsDialogProp.visible=false;" >取消</el-button>
            </el-col>
        </el-row>

    </el-dialog>

    <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            v-dialogDrag>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNoInner="orderNoInner" :orderNo="orderNo" :isTx="isTx"
                style="z-index:10000;height:600px" />
        </el-dialog>
</div>
</div>
</template>

<script>
import * as echarts from 'echarts'
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
import { getTableColumnCache, setTableColumnCache, GetVxeTableColumnCacheAsync, SetVxeTableColumnCacheAsync } from '@/api/admin/business'
import { getUserDingCode } from '@/api/admin/user'
import { matchImg } from '@/utils/getCols'
import { SaveProductIdViewLog } from '@/api/operatemanage/PddChart'
import dayjs from 'dayjs'
import { canJump, onJumpLink } from '@/utils/tools';
import { tonumfuc } from '@/utils/tonumqian.js'

    export default {
        name:"vxetablebase",
        components:{
            OrderActionsByInnerNos
        },
        props: {
            customExportColumn:{type: Object, default: () => { return null }},
            isNeedExpend:{ type: Boolean, default: () => { return true } },
            keyField: { type: String, default: () => { return '' } },
            cstmExportFunc:{type:Object,default:()=>{
                return null;
            }},
            indexWidth:{type:Number,default:()=>{return 30;}},
            dftLvl:{type:Number,default:()=>{return 1;}},
            isSelectLvl:{ type: Boolean, default: () => { return false } },
            editconfig:{ type: Object, default: () => { return {  } } },
            hasSeq: { type: Boolean, default: () => { return true } },
            // 表格数据
            tableData: { type: Array, default: () => [] },
            // 表格型号：mini,medium,small
            size: { type: String, default: 'small' },
            type: { type: String, default: 'primary' },
            isBorder: { type: Boolean, default: true },
            // 表格列配置
            tableCols: { type: Array, default: () => [] },
            isRemoteSort:{ type: Boolean, default: () => { return true } },
            id:{type:String,default:()=>{ return new Date().valueOf().toString()}},
            that:{type:Object,default:()=>{return null}},
            border:{type:Boolean | Object ,default:()=>{return 'default'}},
            tableHandles: { type: Array, default: () => [] },
            showsummary: { type: Boolean, default: false },
            align: { type: String, default: 'center' }, //对齐方式
            summaryarry: { type: Object, default: () => { } },
            tablekey: { type: String, default: '' },//表格key
            treeProp: { type: Object, default: () => { return null } },
            // isstorage:  { type: Boolean, default: true }, //true为本地缓存，false为后端缓存
            isstorage:  { type: Object, default: function () {
                    return {fixed: true, sort: true, resizable: true, visible: true};
                }
            },
            oSizex: { type: Number, default: 0 },
            oSizey: { type: Number, default: 0 },
            enabledy:  { type: Boolean, default: true },
            enabledx:  { type: Boolean, default: true },
            enabled:  { type: Boolean, default: true },
            height: { type: String, default: '100%' },
            ygt: { type: Number, default: 100 },
            xgt: { type: Number, default: 100 },
            somerow: { type: String, default: '' },
            cellClassName: { type: Function, default: null },
            toolbarshow: { type: Boolean, default: () => { return true } },
            screenShow: { type: Boolean, default: () => { return true } },
            loading: { type: Boolean, default: () => { return false } },
            showheaderoverflow: { type: String | Boolean, default: 'tooltip' },//ellipsis（只显示省略号）,title（并且显示为原生 title）,tooltip（并且显示为 tooltip 提示）
            showoverflow: { type: String, default: 'title' },//ellipsis（只显示省略号）,title（并且显示为原生 title）,tooltip（并且显示为 tooltip 提示）
            isDisableCheckBox:{ type: Boolean, default: () => { return false } },
            isIndexFixed:{type: Boolean, default: () => { return true } },
            enableCheckRange:{type:Boolean,default:()=>{return true}},
            noToFixed:{type:Boolean,default:()=>{return false}},
            resizable:{type:Boolean,default:()=>{return true}},
            rowStyle:{type:Function,default:()=>{return null}},
            visibleMethod:{type:Function,default:()=>{return null}},
            seqAlign:{type:String,default:()=>{return 'left'}},
            virtualScroll:{type:Boolean,default:()=>{return false}},
        },
        data() {
            return {
                tonumfuc,
                ddLogo: require('@/static/images/dingding.png'),
                expendIsShow:false,
                xTableIsFull:false,
                currentLvl:1,
                lastSortArgs:{
                    field:"",
                    order:"",
                },
                orderNo:null,
                dialogHisVisible: false,
                orderNoInner: null,
                isTx: false,
                summarycolumns: [],
                checkBoxGroup: [],
                echartsLoading:false,
                echarts:echarts,
                exportColumnsDialogProp:{
                    visible:false,
                    data:[],
                    colIds:[],
                    colFields:[],
                    colNames:[],
                    mapFields:{},
                    mapTitles:{},
                    isLoadMapFields:false
                },
                imagedefault: require("@/assets/images/detault.jpeg"),
                path: '',
                storeFiex: {},
                isnext: true,
                newobj: {},
                allobj: {},
                fullColumnFieldData: {}
            }
        },
        created(){
            this.$nextTick(() => {
              // 手动将表格和工具栏进行关联
              this.$refs.xTable.connect(this.$refs.xToolbar)
            })
        },
        async mounted(){
                let _this = this;
                this.currentLvl=this.dftLvl;
                this.$nextTick(()=>{
                    this.columns = this.$refs.xTable.getColumns()
                })
                var arrlist = [];
                this.tableCols.map((item)=>{
                    if(item.fixed){
                        arrlist.push(item.prop)
                    }
                })
                this.arrlist = arrlist;

                // 获取数据
                if(!this.isstorage){
                    let key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
                    let res = await getTableColumnCache({ key: key })
                    await this.changecolumn(res.data.hideContent);
                }
                // this.rowStyle()
                //window.addEventListener('resize', this.loadRowEcharts);

                // setTimeout(()=>{
                //     _this.initfiex();
                // },0)

        },
        methods:{
            canJump,
            async handleLinkClick(row, column) {
              if (!this.canJump(row, column)) return;
              try {
                await onJumpLink(row[column.prop], column.prop);
              } catch (err) {
                this.$message.error('小昀工具箱不在线，请开启后使用！');
              }
            },
            handleClick(e, prop) {
                if (!prop) return
                if (!e.target.parentNode.innerHTML.includes('复') && !e.target.parentNode.innerHTML.includes('查 ')) return
                let res = JSON.parse(JSON.stringify(prop));
                if (res.length > 6) {
                    res = res.substring(0, 2) + '**' + res.substring(res.length - 2, res.length);
                }
                if (e.target.innerHTML == '复') {
                    var _this = this;
                    this.$copyText(prop).then(function (e) {
                        _this.$message({ message: "内容已复制到剪切板！", type: "success" });
                    }, function (e) {
                        _this.$message({ message: "抱歉，复制失败！", type: "warning" });
                    })
                    this.sendLog(prop, '复制宝贝ID', 'ERP')
                } else if (e.target.innerHTML == '查 ') {
                    if (e.target.parentNode.innerHTML.includes(res)) {
                        e.target.parentNode.innerHTML = e.target.parentNode.innerHTML.replace(res, prop)
                    }
                    this.sendLog(prop, '查看宝贝ID', 'ERP')
                } else {
                    if (res == e.target.innerHTML || prop == e.target.innerHTML) {
                        this.sendLog(prop, '打开链接', 'ERP')
                    }
                }
            },
            async sendLog(proCode, action, source) {
                await SaveProductIdViewLog({ proCode, action, source })
            },
            footerCellStyle(row){
                let styleitem = null;
                this.$emit('footerCellStyle', row, val=>{
                    styleitem = val
                });
                return styleitem;
            },
            formatImg(img){
                return matchImg(img)
            },
            async startSession(id, type) {
                const { data, success } = await getUserDingCode({ type, id })
                if (success) {
                    if (!data) return this.$message.error('未获取到钉钉id')
                    window.open(`dingtalk://dingtalkclient/action/sendmsg?spm=dingtalk_id=${data}`, '_self')
                }
            },
            expandAllTree(val){
                if (val) {
                    this.$refs.xTable.setTreeExpand(this.tableData, true);
                }else{
                    this.$refs.xTable.clearTreeExpand();
                }
                this.$nextTick(()=>{
                    this.loadRowEcharts()
                })
            },
            toggleTreeExpand({row, column, cell, $event}){
                console.log(row, column, cell, $event,'2222');
                this.$nextTick(()=>{
                  this.loadRowEcharts()
                })
            },
            format(row,col){
                if(col.format && col.propType && row[col.prop]){
                    if(col.propType=="DateTime"){
                        return dayjs(row[col.prop]).format(col.format.replace(/yyyy/,"YYYY").replace(/dd/g,"DD"))
                    }
                }else if(col.mappingValues!=null){
                    for (const key in col.mappingValues) {
                        if (Object.hasOwnProperty.call(col.mappingValues, key)) {
                            const value = col.mappingValues[key];
                            if(value==row[col.prop]){
                                return key;
                            }
                        }
                    }
                }
                return row[col.prop];
            },
            onTrendChart(row,prop){
                var dateCol= this.tableCols.find(a=>a.isSeriesDate);
                var keys = this.tableCols.filter(a=>a.isSeriesKey).map(a=>a.prop);
                var fields = this.tableCols.filter(a=>a.isSeriesField).map(a=>{return {field: a.prop,label:a.label};});
                let filter={logic:'And',filters:[]};
                for (const p in row) {
                 if(keys.indexOf(p)>-1)   {
                    filter.filters.push({field:p,operator:'Eq',value:row[p]})
                 }
                }
                var date=new Date(row[dateCol.prop]);
                var option={key:keys,dateField:dateCol.prop,fields:fields,filter:filter,date:date}

                this.$emit('onTrendChart',option)
            },
            headerCellStyle({ column }){

                var col=this.tableCols.find(x=>x.prop==column.field);
                if (col) {
                    return {
                    backgroundColor: col.headerBgColor,
                    color: col.fontColor,
                    fontSize:col.fontSize
                    }
                }
            },
            showLogDetail(val,orderType,row) {
            this.orderNoInner = orderType == 'orderNoInner' ? val : null
            this.orderNo = orderType == 'orderNo' ? val : null
            //将row对象的key转换为小写,有platForm的情况
            let res = Object.keys(row).reduce((newObj, key) => {
                newObj[key.toLowerCase()] = row[key];
                return newObj;
            }, {});
            if (this.orderNo && res.platform && (res.platform == 1 || res.platform == 4 || res.platform == 8 || res.platform == 9)) {
                this.isTx = true
            }else{
                this.isTx = false
            }
            this.dialogHisVisible = true;
            },
            rowStyleTable(row){
                let styleitem = null;
                this.$emit('rowStyle', row, val=>{
                    styleitem = val
                });
                return styleitem;
            },
            downloadFile(files){
                if(files){
                    let jsonF=JSON.parse(files);
                    if(jsonF && jsonF.length>0)
                        window.open(jsonF[0].url);
                }
            },
            downloadFiles(files){
                if(files){
                    let jsonF=JSON.parse(files);

                    this.$showDialogform({
                        path: `@/views/base/DownloadFilesForm.vue`,
                        title: '文件列表',
                        autoTitle: false,
                        args: { files:jsonF, mode: 3 },
                        height: 300,
                        width: '600px',
                        callOk:null
                    })
                }

            },
            setExportCols(){
                if(!this.exportColumnsDialogProp.visible){
                    this.exportColumnsDialogProp.visible=true;
                    let allcolumns = []
                    if (this.customExportColumn && this.customExportColumn?.collectColumn?.length > 0) {
                        allcolumns = this.customExportColumn
                    }else{
                        allcolumns=this.$refs.xTable.getTableColumn();
                    }
                    let tempCols=[...allcolumns.collectColumn];
                    if(tempCols.length>0 && tempCols[0].title==null){
                        tempCols[0].title="#";
                    }
                    let tempRoot={
                        id:'_root',
                        parentId:"_root",
                        children:tempCols,
                        title:'全部'
                    };


                    if(this.exportColumnsDialogProp.data.length==0){
                        let visibleColumn =[]
                        if (this.customExportColumn && this.customExportColumn?.collectColumn?.length > 0) {
                            visibleColumn = this.customExportColumn.collectColumn
                        } else {
                            visibleColumn = this.$refs.xTable.getColumns();
                        }
                        visibleColumn.forEach(item=>{
                            this.exportColumnsDialogProp.colIds.push(item.id);
                        });
                    }

                    if(this.exportColumnsDialogProp.isLoadMapFields==false){
                        //如果还没加载字段与导出的映射关系，先加载
                        this.tableCols.forEach(col=>{
                            if(col.prop && col.exportField){
                                this.exportColumnsDialogProp.mapFields[col.prop]=col.exportField;
                            }
                            if(col.cols && col.cols.length>0){
                                col.cols.forEach(col1=>{
                                    if(col1.prop && col1.exportField){
                                        this.exportColumnsDialogProp.mapFields[col1.prop]=col1.exportField;
                                    }
                                    if(col1.cols && col1.cols.length>0){
                                        col1.cols.forEach(col2=>{
                                            if(col2.prop && col2.exportField){
                                                this.exportColumnsDialogProp.mapFields[col2.prop]=col2.exportField;
                                            }
                                        });
                                    }
                                });

                            }
                        });

                        this.exportColumnsDialogProp.isLoadMapFields=true;
                    }

                    this.exportColumnsDialogProp.data=[tempRoot];


                    return;
                }

                let selNodes=this.$refs.exportColTree.getCheckedNodes(true);
                this.exportColumnsDialogProp.colIds=selNodes.map(x=>x.id);

                this.exportColumnsDialogProp.colFields=selNodes.map(x=>{
                    return x.field  &&  this.exportColumnsDialogProp.mapFields[x.field] ? this.exportColumnsDialogProp.mapFields[x.field] :x.field;
                });

                this.exportColumnsDialogProp.colTitles=selNodes.map(x=>{
                    return x.title  &&  this.exportColumnsDialogProp.mapTitles[x.title] ? this.exportColumnsDialogProp.mapTitles[x.title] :x.title;
                });


                if(this.cstmExportFunc &&  typeof(this.cstmExportFunc)=='function' ){
                    console.log('开始调用导出');

                    var colFields=[...this.exportColumnsDialogProp.colFields];
                    var colTitles=[...this.exportColumnsDialogProp.colTitles];

                       var cols=[];
                        this.tableCols.forEach(item=>{
                            if(item.cols&&item.cols.length>0){
                                item.cols.forEach(item1=>{
                                    cols.push(item1);
                                });
                            }else{
                                cols.push(item);
                            }
                        });

                    if (cols.filter(a=>a.mergeName).length>0) {
                        colTitles=[];
                        colFields.forEach(field => {
                          const res =  cols.find(item=>item.prop==field)
                            if(res && res.mergeName){
                                colTitles.push(res.mergeName ? res.mergeName+" - "+res.label : res.label);
                            }else{
                                colTitles.push(res ? res.label :'');
                            }
                        });
                    }
                    this.cstmExportFunc({"YH_EXT_ExportColumns":colFields,"YH_EXT_ExportCnColumns":colTitles});

                    console.log('导出数据结束');
                }



                this.exportColumnsDialogProp.visible=false;
            },
            // 图片点击放大
            showImg(e) {
                if (e.target.tagName == 'IMG') {
                    this.$emit('showImg', e)
                }
            },
            toggleTreeMethod   ({ expanded, column, columnIndex, row, rowIndex }) {
              if (!expanded) return;
              return new Promise(resolve => {
                this.$emit('toggleTreeMethod',row)
                return true;
              })
            },
            copytext(e) {
                let textarea = document.createElement("textarea")
                textarea.value = e
                textarea.readOnly = "readOnly"
                document.body.appendChild(textarea)
                textarea.select()
                let result = document.execCommand("copy")
                if (result) {
                    this.$message({
                    message: '复制成功',
                    type: 'success'
                    })
                }
                textarea.remove()
            },
            //层级切换
            lvlChang(v1){
                if(v1==9){
                    this.$refs.xTable.setAllTreeExpand(true);
                }
                else if(v1==1){
                    this.$refs.xTable.setAllTreeExpand(false)
                }
                else{
                    let rows=this.$refs.xTable.getTableData().fullData;
                    this.lvlShow(rows,1,v1);
                }
            },
            lvlShow(rows,curI,maxLvl){
                if(!rows || rows==undefined || rows==null)
                    return;

                if(curI>=maxLvl){
                    rows.forEach(r=>{
                        this.$refs.xTable.setTreeExpand(r,false)
                    })
                    return;
                }else{
                    rows.forEach(r=>{
                        this.$refs.xTable.setTreeExpand(r,true);
                        this.lvlShow(r.children,curI+1,maxLvl);
                    });
                }

            },
            // 通用行合并函数（将相同多列数据合并为一行）
            mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
              const fields = this.somerow.split(',')
              const cellValue = row[column.property]
              if (cellValue && fields.includes(column.property)) {
                const prevRow = visibleData[_rowIndex - 1]
                let nextRow = visibleData[_rowIndex + 1]
                if (prevRow && prevRow[column.property] === cellValue) {
                  return { rowspan: 0, colspan: 0 }
                } else {
                  let countRowspan = 1
                  while (nextRow && nextRow[column.property] === cellValue) {
                    nextRow = visibleData[++countRowspan + _rowIndex]
                  }
                  if (countRowspan > 1) {
                    return { rowspan: countRowspan, colspan: 1 }
                  }
                }
              }
            },
            //默认列隐藏 批量控制列的显影
            changecolumn(val){
                setTimeout(() => {
                // this.columns.forEach(column => {
                    this.$refs.xTable.getTableColumn().collectColumn.forEach(column => {
                    if(val!=null){
                        if (val.includes(column.property)) {
                            column.visible = false
                        }
                    }
                })
                if (this.$refs.xTable) {
                    this.$refs.xTable.refreshColumn()
                }
                }, 800)
            },
            changecolumn_setTrue(val){
                setTimeout(() => {
                // this.columns.forEach(column => {
                  this.$refs.xTable.getTableColumn().collectColumn.forEach(column => {
                    if(val!=null){
                        if (val.includes(column.property)) {
                            column.visible = true
                        }
                    }
                })
                if (this.$refs.xTable) {
                    this.$refs.xTable.refreshColumn()
                }
                }, 800)
            },

            isChinese(str) {
                const chinesePattern = /^[\u4e00-\u9fa5]+$/;
                return chinesePattern.test(str);
            },
            async initfiex(){
                let _ = this;
                _.path = _.$route.path;

                let resp=  await GetVxeTableColumnCacheAsync({tableId:_.id});

                let store =null;
                if(resp && resp.success && resp.data){
                    store = JSON.parse(resp.data);
                }
                else{
                    store = JSON.parse(localStorage.getItem('tableFixd'));
                }

                _.allobj = store?store:{};

                if(!store){
                    return;
                }

                ////////////////
                let storeFiex = {};
                for (var key in store) {
                    if (key === _.path + _.id) {
                        storeFiex = store[key]
                    }
                }

                if(storeFiex){



                    let aaa = storeFiex;

                    for(var key in aaa){
                        if(aaa.hasOwnProperty(key)){
                            _.$refs.xTable.setColumnFixed(key, aaa[key])
                        }
                    }


                    // this.$refs.xTable.refreshColumn()
                }

            },
            async runAsyncThr() {
                var _ = this;
                for (var key in _.$refs.xTable.fullColumnFieldData) {
                    if (_.$refs.xTable.fullColumnFieldData.hasOwnProperty(key)) { // 判断key
                        _.newobj[key] = _.$refs.xTable.fullColumnFieldData[key]['column']['fixed'];
                    }
                }
            },
            async runAsyncFour() {
                let _ = this;
                _.allobj[_.path+_.id] = _.newobj;

                // _.$store.commit('tableView/set_table_fiex', _.allobj)

                localStorage.setItem('tableFixd', JSON.stringify(_.allobj));

                await SetVxeTableColumnCacheAsync({tableId:_.id,ColumnConfig:JSON.stringify(_.allobj)});

            },
            async restoreStore({id, type, storeData}){

                let resp=  await GetVxeTableColumnCacheAsync({tableId:id});

                let store =null;
                if(resp && resp.success && resp.data){
                    store = JSON.parse(resp.data);
                }
                if(store.fixedData){
                    this.tableCols.map((item)=>{
                        item.fixed = store.fixedData[item.prop]
                    })
                }

                return store??storeData;

            },
            async updateStore({id, type, storeData}){
                let newobj = {};
                let mergearr = [];

                this.tableCols.map((item)=>{
                    if(item.merge){
                        mergearr.push({
                            name:  item.prop,
                            value: item.cols[0]['prop']
                        });
                    }
                })

                this.$refs.xTable.getColumns().map((item)=>{

                    if(item.type){
                        return;
                    }
                    mergearr.map((itemm)=>{
                        if(item.field === itemm.value){
                            // item.fixed = itemm.value
                            newobj[itemm.name] = item.fixed
                        }
                    })
                    newobj[item.field]  = item.fixed;
                })
                storeData.fixedData = newobj;

                await SetVxeTableColumnCacheAsync({tableId:id,ColumnConfig:JSON.stringify(storeData)});
            },
            async tooclick(params){
                return;

                var _ = this;

                _.path = _.$route.path;

                switch (params.type) {
                    case 'confirm': {
                        await _.runAsyncThr();
                        await _.runAsyncFour();
                    }
                    case 'reset': {
                        await _.runAsyncThr();
                        await _.runAsyncFour();
                    }
                }

                return
                //行数据
                const visibleColumn = this.$refs.xTable.getColumns()
                const Columnevn = this.$refs.xTable;

                switch (params.type) {
                    case 'confirm': {
                        var checked = [];
                        var nochecked = [];
                        var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
                        visibleColumn.forEach(item=>{
                            if(item?.property)
                            checked.push(item.property)
                        })

                        var arrList = Columnevn.tableFullColumn;
                        var newList = visibleColumn;
                        arrList = arrList.filter((item) => {
                            return newList.every((item2) => {
                                return item.property != item2.property;
                            });
                        });
                        arrList.forEach(item=>{
                            nochecked.push(item.property)
                        })
                        if(!this.isstorage)
                            await setTableColumnCache({ key: key, displays: checked, hides: nochecked });

                        //获取label未选中的值
                        // setTimeout(async() => {
                        //     var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
                        //     let res = await getTableColumnCache({ key: key })
                        // }, 200);
                        break
                    }
                    case 'reset': {
                        var checked = [];
                        var nochecked = [];
                        var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
                        visibleColumn.forEach(item=>{
                            if(item?.property)
                            checked.push(item.property)
                        })
                        if(!this.isstorage)
                            await setTableColumnCache({ key: key, displays: checked, hides: [] });

                        break
                    }
                    case 'close': {
                        var checked = [];
                        var nochecked = [];
                        var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
                        visibleColumn.forEach(item=>{
                            if(item?.property)
                            checked.push(item.property)
                        })

                        var arrList = Columnevn.tableFullColumn;
                        var newList = visibleColumn;
                        arrList = arrList.filter((item) => {
                            return newList.every((item2) => {
                                return item.property != item2.property;
                            });
                        });
                        arrList.forEach(item=>{
                            nochecked.push(item.property)
                        })
                        if(!this.isstorage)
                            await setTableColumnCache({ key: key, displays: checked, hides: nochecked });

                        // VXETable.modal.message({ content: `关闭了面板，显示为 ${visibleColumn.length} 列`, status: 'info' })
                        break
                    }
                    case 'open': {
                        break
                    }
                }

                if(params.type && params.type!='open')
                    this.loadRowEcharts();
            },
            // 清空全选
            clearSelection(){
                this.$refs.xTable.clearCheckboxRow()
            },
            async checkboxall(){
                const records = this.$refs.xTable.getCheckboxRecords()
                this.$emit('checkbox-range-end', records);
                this.$emit('select', records);
            },
            async toggleRowSelection(val){
                await this.$refs.xTable.clearCheckboxRow()
                await this.$refs.xTable.setCheckboxRow(val, true)
            },
            selectAllEvent ({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {
              const records = this.$refs.xTable.getCheckboxRecords()
              this.$emit('checkbox-range-end', records);
              this.$emit("cellClick",{ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event });
            },
            cellClickEvent({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }){
                this.$emit("cellClick",{ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event });

            },
            selectChangeEvent ({ checked }) {
              const records = this.$refs.xTable.getCheckboxRecords()
            this.$nextTick(()=>{
                this.$emit('select', records);
            })


            },
            cellStyleFun({ row, rowIndex, column }){
                let rltStyle={};
                let col=column;
                var colArg=this.tableCols.find(x=>x.prop==col.property );

                if(colArg ){
                    if(colArg.type && (colArg.type=="images" ||colArg.type=="image"))
                        rltStyle={
                            ...rltStyle,
                            ...{
                                textAlign:"center"
                            }
                        };

                    if(colArg.align)
                        rltStyle={
                            ...rltStyle,
                            ...{
                                textAlign:colArg.align
                            }
                        };

                }
                return rltStyle;
            },
            customSortMethod({ data, sortList }){
                if(this.isRemoteSort){
                    if(sortList && sortList.length>0){
                        var prop=sortList[0].field;
                        if(sortList[0].field != this.lastSortArgs.field || sortList[0].order!=this.lastSortArgs.order){
                            this.lastSortArgs={...sortList[0]};
                            var temp = this.tableCols.filter(a=>a.prop==prop);
                            var sortBy = this.lastSortArgs.field;
                            if(temp.length>0 && temp[0]["sort-by"]){
                              sortBy = temp[0]["sort-by"]
                            }
                            this.$emit('sortchange',{
                                order:(this.lastSortArgs.order.indexOf('desc')>-1?'descending':'asc'),
                                prop: sortBy
                            });
                        }
                    }
                }else{
                    if(sortList && sortList.length>0){
                        data.sort((a, b) => {
                            const valueA = a[sortList[0].field]
                            const valueB = b[sortList[0].field]
                            if (sortList[0].order === 'asc') {
                                return valueA > valueB ? 1 : -1
                            } else {
                                return valueA < valueB ? 1 : -1
                            }
                        })
                    }
                }
            },
            headerCellClassName ({ column, columnIndex }) {
                let className='';
                var col=this.tableCols.find(x=>x.prop==column.property );

                if (col && col.align ) {
                    className=' '+`vxetableheadercell-${column.align}-20221216`;
                }else if(col && col.type && (col.type=="images" || col.type=="image")){
                    className=' '+`vxetableheadercell-center-20221216`;
                }

                return className;
            },
                            // 格式化函数，处理千位分隔符和小数位
                formatNumber(number){
                    const absNumber = Math.abs(number);
                    const options = {
                        minimumFractionDigits: absNumber >= 100 ? 0 : 2,
                        maximumFractionDigits: absNumber >= 100 ? 0 : 2,
                    };
                    return new Intl.NumberFormat('zh-CN', options).format(number);
                },


            footerMethod ({ columns, data }) {
                const sums = [];
                if (!this.summaryarry)
                    return sums
                var arr = Object.keys(this.summaryarry);
                if (arr.length == 0)
                    return sums
                //const { columns, data } = param;
                var hashj = false;
                columns.forEach((column, index) => {

                    if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
                        var sum = this.summaryarry[column.property + '_sum'];
                        if (sum == null) return;
                        else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
                        else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2);
                        else if (Math.abs(parseInt(sum)) && this.noToFixed) sums[index] = this.formatNumber(sum);
                        // else sums[index] = sum.toFixed(0);
                           else sums[index] = this.formatNumber(sum);
                    }
                    else if (column.property == 'buchar') sums[index] = '汇总趋势图'
                    else if (index == '0'||column.type == 'seq'||index == 0){ sums[0] = '合计';}


                    else sums[index] = ''
                });
                if (this.summarycolumns.length == 0) {
                    this.summarycolumns = columns;
                    //this.initsummaryEvent();
                }
                return [sums]
            },
            initsummaryEvent() {
                let self = this;
                let table;
                this.$nextTick(() => {
                    if(this.tablekey) table = document.querySelector('[name='+this.tablekey+'] .vxe-table--footer-wrapper>table');
                    else table = document.querySelectorAll('.vxe-table--footer-wrapper>table');
                    if(table?.length>0) table = table[0]
                    this.$nextTick(() => {
                        self.summarycolumns.forEach((column, index) => {
                            if (column.property) {
                                var col = findcol(self.tableCols, column.property);
                                if (col && col.summaryEvent) {
                                    table.rows[0].cells[index].style.color = "red";
                                    table.rows[0].cells[index].style.cursor= "pointer";
                                }
                            }
                        })
                    })

                })

                function findcol(cols, property) {
                    let column;
                    for (var i = 0; i < cols.length; i++) {
                        var c = cols[i];
                        if (column) break
                        else if (c.prop && c.prop.toLowerCase() == property.toLowerCase()) {
                            column = c;
                            break
                        }
                        else if (c.cols && c.cols.length > 0) column = findcol(c.cols, property)
                    }
                    return column
                }
            },
            footercellclick({ items, $rowIndex, column, columnIndex, $columnIndex, $event }){
                let self = this;
                var  col = findcol(self.tableCols, column.property);
                if (col && col.summaryEvent)
                    self.$emit('summaryClick', column.property)

                function findcol(cols, property) {
                    let column;
                    for (var i = 0; i < cols.length; i++) {
                        var c = cols[i];
                        if (column) break
                        else if (c.prop && c.prop.toLowerCase() == property.toLowerCase()) {
                            column = c;
                            break
                        }
                        else if (c.cols && c.cols.length > 0) column = findcol(c.cols, property)
                    }
                    return column
                }
            },
            checCheckboxkMethod2({ row }) {
                if(this.isDisableCheckBox){
                    let result;
                    this.$emit('checCheckboxkMethod', row, val=>{result = val})
                    return result;
                }else {
                    return true;
                }
            },
            /* 加载行图表 */
            loadRowEcharts(){
                let self=this;
                let echarts=self.echarts;
                self.echartsLoading = true;
                setTimeout(_ => {

                    let idPrex='rptIdecharts'+self.id;
                    let ids=[];

                    for(let p in self.$refs){
                        if(p.startsWith(idPrex))
                            ids.push(p);
                    }

                    ids.forEach(e => {
                        var idNode=self.$refs[e];

                        if(!idNode || idNode.length==0 || !idNode[0].id ||  !(idNode[0]?.attributes?.chartdata?.value))
                        {
                            return;
                        }


                        //检测是否已经存在echarts实例，如果不存在，则不再去初始化
                        // var myEchart = echarts.init(document.getElementById('main'));
                        let el=document.getElementById(idNode[0].id);

                        let myChart = echarts.getInstanceByDom(el);
                        if (!myChart) {
                            myChart = echarts.init(el);
                        }
                        myChart.clear();

                        let dataStr=idNode[0].attributes.chartdata.value;
                        if(dataStr==undefined|| dataStr==null|| dataStr==""){
                            return;
                        }


                        let rowChartData=JSON.parse(dataStr);
                        let series = [];
                        if(rowChartData==undefined|| rowChartData==null){
                            return;
                        }

                        rowChartData.series.forEach(s => {
                            series.push({ type: 'line', smooth: true, showSymbol: false, ...s })
                        });

                        var xAxis = { ...rowChartData.xAxis };
                        xAxis.type = "category";
                        //xAxis.boundaryGap=false;
                        xAxis.show = false;
                        xAxis.boundaryGap = false;

                        myChart.setOption({
                            legend: {
                                show: false,
                            },
                            grid: {
                                left: "0",
                                top: "1",
                                right: "6",
                                bottom: "1",
                                containLabel: false,
                            },
                            xAxis: xAxis,
                            yAxis: {
                                type: 'value',
                                show: false,
                            },
                            series: series
                        });

                        let width =el.clientWidth;
                        let height= el.clientHeight;

                        myChart.resize({width,height});

                    });

                    self.echartsLoading = false
                }, 1000)
            },
            resizableChange({ $rowIndex, column, columnIndex, $columnIndex, $event }){
                this.loadRowEcharts();
            }
        },
    }
</script>

<style lang="scss" scoped>

        /*滚动条整体部分*/
        .mytable-scrollbar20221212 ::-webkit-scrollbar {
          width: 10px;
          height: 10px;
        }
        /*滚动条的轨道*/
        .mytable-scrollbar20221212 ::-webkit-scrollbar-track {
          background-color: #FFFFFF;
        }
        /*滚动条里面的小方块，能向上向下移动*/
        .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
          background-color: #bfbfbf;
          border-radius: 5px;
          border: 1px solid #F1F1F1;
          box-shadow: inset 0 0 6px rgba(0,0,0,.3);
        }
       .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
          background-color: #A8A8A8;
        }
        .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
          background-color: #787878;
        }
        /*边角，即两个滚动条的交汇处*/
       .mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
          background-color: #FFFFFF;
        }

        // 图片大小
        .mytable-scrollbar20221212  .images20221212{
          max-width: 150px;max-height: 150px;
          width:40px !important;
          height:40px  !important;
        }

        // 图片张数标记
        .mytable-scrollbar20221212 .badgeimage20221212 .el-badge__content.is-fixed{
            top:10px;
        }

        /*  工具箱位置  */
        .vxetoolbar20221212{
            position:absolute ;
            top: 5px;
            right: 0px;
            padding-top:0;
            padding-bottom:0;
            z-index: 999;
            background-color: rgb(255 255 255 / 0%);
            display: flex;
            align-items: center;
        }

        .vxetoolbar20221212 ::v-deep .vxe-custom--wrapper{
            margin-left:0px !important;
        }

        .vxetableheadercell-left-20221216
        {
            text-align: left;
        }

        .vxetableheadercell-center-20221216
        {
            text-align: center;
        }

        .vxetableheadercell-right-20221216
        {
            text-align: right;
        }

        ::v-deep .vxe-table .vxe-cell--sort{
            min-width: 10px !important;
        }
        ::v-deep .vxe-table--render-default .vxe-cell{
            padding-right: 0 !important;
            padding-left: 0 !important;
        }

        .copyhover{
        display: none;
        }
        .relativebox{
        width: 80%;
        -webkit-line-clamp: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        }
        .relativebox:hover{
        width: 80%;
        }
        .textover{
        -webkit-line-clamp: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        }
        .relativebox:hover .textover{
        -webkit-line-clamp: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        }

        .relativebox:hover .copyhover{
        display: block;
        position: absolute;
        top: 50%;
        left: 75%;
        margin: 0 10px;
        z-index: 99;
        transform: translate(-50%,-50%);
        color: #409EFF;
        font-weight: 600;
        }

        .vxe-icon-ellipsis-h:hover {
            color: #409EFF;
            margin-left: 2px;
            background-color: #F1F1F1;
        }

        .vxe-icon-ellipsis-h {
            color: #999;
            font-size: 15px;
        }
        .custom-button.text-like-button {
        background-color: transparent;
        border: none;
        padding: 0;
        cursor: pointer;
        user-select: text;
        color: #409EFF;
        }

        .beforeBox{
          position:relative;
          height:40px;
          .beforeBox_item1{
            position:absolute;
            bottom:0;
            left:5px;
          }
          .beforeBox_item2{
            position:absolute;
            top:0;
            right:5px;
          }
        }
        .alicenter{
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .imgstyle{
            max-height: 50px !important;
        }

        .el-icon-warning-outline{
            color: red;
            margin-left: 5px;
        }
        .tag-with-border {
          border: 1px solid rgb(163, 149, 149) !important;
        }
        .tagpattern{
          display: block;
          word-break: break-all;
          -webkit-line-clamp: 2;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .tagpattern_item{
          flex: 1;
          margin-right: 5px;
        }
        .cursor-pointer {
          cursor: pointer;
        }
        .tag{margin-right:5px;}

        ::v-deep .vxe-body--column {
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;
        user-select: text !important;
}
::v-deep .vxe-footer--column{
    line-height: 12px !important;
}
.el-icon-full-screen{
    font-size: 20px;
}
.el-icon-s-grid{
    font-size: 20px;
    cursor: pointer;
}
.xTableFull{
    position: fixed;
    top: -5px;
    left: 0;
    height: 100vh !important;
    width: 100vw;
    z-index: 2000;
}
.xTableMin{
    height: 100%;
    width: 100%;
}
.fullClass{
    background-color: #fff;
    height: 28px;
    width: 28px;
    border-radius: 50%;
    border: 1px solid #ccc;
    line-height: 28px;
    text-align: center;
    box-sizing: border-box;
    cursor: pointer;
    margin-right: 5px;
}
.expendBtn{
    position: absolute;
    top: -5px;
    right: 20px;
    border: 1px solid #ccc;
    background-color: #fff;
    padding:0 5px;
    border-radius: 5px;
}
.ddTalk ::v-deep img {
    max-width: 20px !important;
    max-height: 20px !important;
    min-height: 20px !important;
    min-width: 20px !important;
    vertical-align: middle;
    cursor: pointer;
}

.nameBox {
    display: flex;
    align-items: center;

    .name {
        max-width: 40px;
    }
}

.userAvatar ::v-deep img {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50%;
}
</style>
<style lang="scss">
.vxe-toolbar{
    padding: 0px !important;
}
</style>
