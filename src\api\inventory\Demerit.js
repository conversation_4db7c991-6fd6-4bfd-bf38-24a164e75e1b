import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/Demerit/`
const apiImport = `${process.env.VUE_APP_BASE_API_ImportInventory}/Demerit/`

//添加行为扣分
export const AddDemerit = (params, config = {}) => { return request.post(apiPrefix + 'AddDemerit', params, config) }

//导出行为扣分
export const ExportDemerit = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportDemerit', params, config) }

//获取所有采购部门
export const GetDeptList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetDeptList', { params: params, ...config })
}

//获取所有采购职位
export const GetPostList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPostList', { params: params, ...config })
}
//获取所有采购人
export const GetUserList = (params, config = {}) => {
    return request.get(apiPrefix + 'GetUserList', { params: params, ...config })
}

//查询行为扣分
export const QueryDemerit = (params, config = {}) => { return request.post(apiPrefix + 'QueryDemerit', params, config) }

//导入行为扣分
export const ImportDemeritFile = (params, config = {}) => {
    return request.post(apiImport + 'ImportDemeritFile', params, config)
}
