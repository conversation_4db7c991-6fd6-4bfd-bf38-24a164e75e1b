<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <!-- elment样式 -->
    <link rel="stylesheet" href="/approvalform/html/api/elment.css">
    <!-- vue -->
    <script src="/approvalform/html/api/vue.min.js"></script>
    <!-- elment脚本 -->
    <script src="/approvalform/html/api/elment.js"></script>
    <!-- jquery -->
    <script src="/approvalform/html/api/jquery.min.js"></script>
    <title></title>
    <style>
        .el-input__inner {
            text-align: center !important;
        }
    </style>
</head>

<body>
    <div id="app" style="width: 100%;height: 100%; ">
        <!-- v-loading="thisLonding" -->
        <el-container direction="vertical"
            style="padding: 0px 10px 0px 10px ; border: 1px #ccc solid; width: 100%; padding: 40px 400px; box-sizing: border-box;">
            <template>
                <el-form ref="dialogContentAddFormData" :model="thisFormData" label-width="100px" label-position="left"
                    :disabled="true">
                    <el-row style="padding-top: 18px;" justify="center">
                        <el-col :span="12">
                            <el-form-item prop="goodsCode" label="商品编码">
                                <el-input v-model="thisFormData.goodsCode" placeholder="商品编码" style="width: 180px;" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item prop="styleCode" label="系列编码">
                                <el-input v-model="thisFormData.styleCode" placeholder="系列编码" style="width: 180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row style="background-color: #ccc; text-align: center;font-size: 16px; ">
                        <el-col :span="12" style="border-right: 1px solid white;">
                            旧
                        </el-col>
                        <el-col :span="12">
                            新
                        </el-col>
                    </el-row>

                    <!-- 长 -->
                    <el-row style="padding-top: 18px;">
                        <el-col :span="8">
                            <el-form-item prop="oldLengthNum" label="长(cm)">
                                <el-input-number v-model="thisFormData.oldLengthNum" placeholder="长(cm)" :precision="2"
                                    :min="0" :max="9999" controls-position="right" :controls="false" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-image :src="thisFormData.oldLengthImg" style="width: 50px;height: 50px;" lazy
                                fit="scale-down" :preview-src-list="previewSrcList1"></el-image>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item prop="newLengthNum" label="长(cm)">
                                <el-input-number v-model="thisFormData.newLengthNum" placeholder="长(cm)" :precision="2"
                                    :min="0" :max="9999" controls-position="right" :controls="false"
                                    :style="newLengthStyle" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-image :src="thisFormData.newLengthImg" style="width: 50px;height: 50px;" lazy
                                fit="scale-down" :preview-src-list="previewSrcList2"></el-image>
                        </el-col>
                    </el-row>
                    <!-- 宽 -->
                    <el-row style="padding-top: 18px;">
                        <el-col :span="8">
                            <el-form-item prop="oldWidthNum" label="宽(cm)">
                                <el-input-number v-model="thisFormData.oldWidthNum" placeholder="宽(cm)" :precision="2"
                                    :min="0" :max="9999" controls-position="right" :controls="false" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-image :src="thisFormData.oldWidthImg" style="width: 50px;height: 50px;" lazy
                                fit="scale-down" :preview-src-list="previewSrcList3"></el-image>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item prop="newWidthNum" label="宽(cm)">
                                <el-input-number v-model="thisFormData.newWidthNum" placeholder="宽(cm)" :precision="2"
                                    :min="0" :max="9999" controls-position="right" :controls="false"
                                    :style="newWidthStyle" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-image :src="thisFormData.newWidthImg" style="width: 50px;height: 50px;" lazy
                                fit="scale-down" :preview-src-list="previewSrcList4"></el-image>
                        </el-col>
                    </el-row>
                    <!-- 高 -->
                    <el-row style="padding-top: 18px;">
                        <el-col :span="8">
                            <el-form-item prop="oldHeightNum" label="高(cm)">
                                <el-input-number v-model="thisFormData.oldHeightNum" placeholder="高(cm)" :precision="2"
                                    :min="0" :max="9999" controls-position="right" :controls="false" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-image :src="thisFormData.oldHeightImg" style="width: 50px;height: 50px;" lazy
                                fit="scale-down" :preview-src-list="previewSrcList5"></el-image>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item prop="newHeightNum" label="高(cm)">
                                <el-input-number v-model="thisFormData.newHeightNum" placeholder="高(cm)" :precision="2"
                                    :min="0" :max="9999" controls-position="right" :controls="false"
                                    :style="newHeightStyle" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-image :src="thisFormData.newHeightImg" style="width: 50px;height: 50px;" lazy
                                fit="scale-down" :preview-src-list="previewSrcList6"></el-image>
                        </el-col>
                    </el-row>
                    <!-- 厚度 -->
                    <el-row style="padding-top: 18px;">
                        <el-col :span="8">
                            <el-form-item prop="oldThicknessNum" label="厚度(丝)">
                                <el-input-number v-model="thisFormData.oldThicknessNum" placeholder="厚度(丝)"
                                    :precision="2" :min="0" :max="9999" controls-position="right" :controls="false" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-image :src="thisFormData.oldThicknessImg" style="width: 50px;height: 50px;" lazy
                                fit="scale-down" :preview-src-list="previewSrcList7"></el-image>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item prop="newThicknessNum" label="厚度(丝)">
                                <el-input-number v-model="thisFormData.newThicknessNum" placeholder="厚度(丝)"
                                    :precision="2" :min="0" :max="9999" controls-position="right" :controls="false"
                                    :style="newThicknessStyle" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-image :src="thisFormData.newThicknessImg" style="width: 50px;height: 50px;" lazy
                                fit="scale-down" :preview-src-list="previewSrcList8"></el-image>
                        </el-col>
                    </el-row>
                    <!-- 克重 -->
                    <el-row style="padding-top: 18px;">
                        <el-col :span="8">
                            <el-form-item prop="oldWeightNum" label="克重(g)">
                                <el-input-number v-model="thisFormData.oldWeightNum" placeholder="克重(g)" :precision="2"
                                    :min="0" :max="9999" controls-position="right" :controls="false" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-image :src="thisFormData.oldWeightImg" style="width: 50px;height: 50px;" lazy
                                fit="scale-down" :preview-src-list="previewSrcList9"></el-image>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item prop="newWeightNum" label="克重(g)">
                                <el-input-number v-model="thisFormData.newWeightNum" placeholder="克重(g)" :precision="2"
                                    :min="0" :max="9999" controls-position="right" :controls="false"
                                    :style="newWeightStyle" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-image :src="thisFormData.newWeightImg" style="width: 50px;height: 50px;" lazy
                                fit="scale-down" :preview-src-list="previewSrcList10"></el-image>
                        </el-col>
                    </el-row>
                    <!-- 颜色 -->
                    <el-row style="padding-top: 18px;">
                        <el-col :span="8">
                            <el-form-item prop="oldColor" label="颜色">
                                <el-input v-model="thisFormData.oldColor" placeholder="颜色" :precision="2" :min="0"
                                    :max="9999" controls-position="right" :controls="false" style="width: 180px;" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-image :src="thisFormData.oldColorImg" style="width: 50px;height: 50px;" lazy
                                fit="scale-down" :preview-src-list="previewSrcList11"></el-image>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item prop="newColor" label="颜色">
                                <el-input v-model="thisFormData.newColor" placeholder="颜色" :precision="2" :min="0"
                                    :max="9999" controls-position="right" :controls="false" :style="newColorStyle"
                                    style="width: 180px;" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-image :src="thisFormData.newColorImg" style="width: 50px;height: 50px;" lazy
                                fit="scale-down" :preview-src-list="previewSrcList12"></el-image>
                        </el-col>
                    </el-row>
                    <!-- 视频 -->
                    <el-row style="padding-top: 18px;">
                        <el-col :span="12">
                            <el-form-item prop="oldVideo" label="视频">
                                <img src="/approvalform/html/images/vedio.jpg" @click="videoplay('oldVideo')"
                                    style="height: 50px; width: 50px;" mode="aspectFit" v-if="thisFormData.oldVideo" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item prop="newVideo" label="视频">
                                <img src="/approvalform/html/images/vedio.jpg" @click="videoplay('newVideo')"
                                    style="height: 50px; width: 50px;" mode="aspectFit" v-if="thisFormData.newVideo" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!-- 材质 -->
                    <el-row style="padding-top: 18px;">
                        <el-col :span="12">
                            <el-form-item prop="oldMaterial" label="材质">
                                <el-input v-model="thisFormData.oldMaterial" placeholder="材质" style="width: 180px;" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item prop="newMaterial" label="材质">
                                <el-input v-model="thisFormData.newMaterial" placeholder="材质" style="width: 180px;"
                                    :style="newMaterialStyle" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!-- 包装方式 -->
                    <el-row style="padding-top: 18px;">
                        <el-col :span="12">
                            <el-form-item prop="oldPackMethod" label="包装方式">
                                <el-input v-model="thisFormData.oldPackMethod" placeholder="包装方式"
                                    style="width: 180px;" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item prop="newPackMethod" label="包装方式">
                                <el-input v-model="thisFormData.newPackMethod" placeholder="包装方式" style="width: 180px;"
                                    :style="newPackMethodStyle" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!-- 货运方式 -->
                    <el-row style="padding-top: 18px;">
                        <el-col :span="12">
                            <el-form-item prop="oldTransportMethod" label="货运方式">
                                <el-input v-model="thisFormData.oldTransportMethod" placeholder="货运方式"
                                    style="width: 180px;" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item prop="newTransportMethod" label="货运方式">
                                <el-input v-model="thisFormData.newTransportMethod" placeholder="货运方式"
                                    style="width: 180px;" :style="newTransportMethodStyle" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!-- 相似度 -->
                    <el-row style="padding-top: 18px;">
                        <el-col :span="12">
                            <el-form-item prop="isApprovePrice" label="是否核价">
                                <el-input v-model="thisFormData.isApprovePrice" placeholder="是否核价"
                                    style="width: 180px;" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item prop="isChangeSupplier" label="是否更换厂家">
                                <el-input v-model="thisFormData.isChangeSupplier" placeholder="是否更换厂家"
                                    style="width: 180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row style="padding-top: 18px;">
                        <el-col :span="12">
                            <el-form-item prop="oldSupplierName" label="厂家名称">
                                <el-input v-model="thisFormData.oldSupplierName" placeholder="厂家名称"
                                    style="width: 180px;" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item prop="newSupplierName" label="厂家名称">
                                <el-input v-model="thisFormData.newSupplierName" placeholder="厂家名称"
                                    style="width: 180px;" :style="newTransportMethodStyle" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row style="padding-top: 18px;">
                        <el-col :span="24">
                            <el-form-item prop="similarity" label="相似度%">
                                <el-input v-model="thisFormData.similarity" placeholder="相似度%" style="width: 180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!-- 备注 -->
                    <el-row style="padding-top: 18px;">
                        <el-col :span="24">
                            <el-form-item prop="remark" label="备注">
                                <el-input v-model="thisFormData.remark" placeholder="备注" type="textarea" :row="4" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </template>
            <el-dialog title="视频播放" :visible.sync="dialogContent.visible" width="50%" :close-on-click-modal="false"
                v-dialogDrag>
                <div style=" width: 45.3125vw; height: 50.1vh;  position: relative;">
                    <video controls="" ref="video" id="video" controlslist="nodownload noremoteplayback"
                        :autoplay="false" oncontextmenu="return false;" style="width:100%;height:100%"
                        :src="dialogContent.videosrc">
                    </video>
                </div>
            </el-dialog>
        </el-container>
    </div>
    <script>
        var vm = new Vue({
            el: '#app',
            data: function () {
                return {
                    dialogContent: {
                        visible: false,
                        videosrc: "",
                    },
                    thisFormData: {},
                    thisLonding: false,
                    newLengthStyle: "",
                    newWidthStyle: "",
                    newHeightStyle: "",
                    newThicknessStyle: "",
                    newWeightStyle: "",
                    newColorStyle: "",
                    newMaterialStyle: "",
                    newPackMethodStyle: "",
                    newTransportMethodStyle: "",
                    previewSrcList1: [],
                    previewSrcList2: [],
                    previewSrcList3: [],
                    previewSrcList4: [],
                    previewSrcList5: [],
                    previewSrcList6: [],
                    previewSrcList7: [],
                    previewSrcList8: [],
                    previewSrcList9: [],
                    previewSrcList10: [],
                    previewSrcList11: [],
                    previewSrcList12: [],
                }
            },
            created() {
            },
            async mounted() {
                this.getList();
            },
            methods: {
                async getList() {
                    var me = this;
                    let searchURL = window.location.search;
                    searchURL = searchURL.substring(1, searchURL.length);
                    let targetPageId = searchURL.split("&")[0].split("=")[1];
                    me.thisLonding = true;
                    $.ajax({
                        type: 'GET',
                        async: false,
                        url: `/api/operatemanage/samplechangecheck/getSampleChangeCheckById`,
                        data: {
                            id: targetPageId
                        },
                        success: function (result) {
                            result.data.oldLengthImg = me.getImageUrl(result.data.oldLengthImg);
                            result.data.newLengthImg = me.getImageUrl(result.data.newLengthImg);
                            me.previewSrcList1.push(result.data.oldLengthImg);
                            me.previewSrcList2.push(result.data.newLengthImg);
                            if (result.data.oldLengthNum != result.data.newLengthNum) me.newLengthStyle = "border: 1px orange solid;"

                            result.data.oldWidthImg = me.getImageUrl(result.data.oldWidthImg);
                            result.data.newWidthImg = me.getImageUrl(result.data.newWidthImg);
                            me.previewSrcList3.push(result.data.oldWidthImg);
                            me.previewSrcList4.push(result.data.newWidthImg);
                            if (result.data.oldWidthNum != result.data.newWidthNum) me.newWidthStyle = "border: 1px orange solid;"

                            result.data.oldHeightImg = me.getImageUrl(result.data.oldHeightImg);
                            result.data.newHeightImg = me.getImageUrl(result.data.newHeightImg);
                            me.previewSrcList5.push(result.data.oldHeightImg);
                            me.previewSrcList6.push(result.data.newHeightImg);
                            if (result.data.oldHeightNum != result.data.newHeightNum) me.newHeightStyle = "border: 1px orange solid;"

                            result.data.oldThicknessImg = me.getImageUrl(result.data.oldThicknessImg);
                            result.data.newThicknessImg = me.getImageUrl(result.data.newThicknessImg);
                            me.previewSrcList7.push(result.data.oldThicknessImg);
                            me.previewSrcList8.push(result.data.newThicknessImg);
                            if (result.data.oldThicknessNum != result.data.newThicknessNum) me.newThicknessStyle = "border: 1px orange solid;"

                            result.data.oldWeightImg = me.getImageUrl(result.data.oldWeightImg);
                            result.data.newWeightImg = me.getImageUrl(result.data.newWeightImg);
                            me.previewSrcList9.push(result.data.oldWeightImg);
                            me.previewSrcList10.push(result.data.newWeightImg);
                            if (result.data.oldWeightNum != result.data.newWeightNum) me.newWeightStyle = "border: 1px orange solid;"

                            result.data.oldColorImg = me.getImageUrl(result.data.oldColorImg);
                            result.data.newColorImg = me.getImageUrl(result.data.newColorImg);
                            me.previewSrcList11.push(result.data.oldColorImg);
                            me.previewSrcList12.push(result.data.newColorImg);
                            if (result.data.oldColor != result.data.newColor) me.newColorStyle = "border: 1px orange solid;"

                            if (result.data.oldMaterial != result.data.newMaterial) me.newMaterialStyle = "border: 1px orange solid;"
                            if (result.data.oldPackMethod != result.data.newPackMethod) me.newPackMethodStyle = "border: 1px orange solid;"
                            if (result.data.oldTransportMethod != result.data.newTransportMethod) me.newTransportMethodStyle = "border: 1px orange solid;"


                            me.thisFormData = result.data;
                            me.thisLonding = false;
                        }
                    })
                },
                getImageUrl(imgcontext) {
                    let imgUrl = "/approvalform/html/images/nonupload.png";
                    if (imgcontext)
                        imgUrl = JSON.parse(imgcontext)[0].url
                    return imgUrl;
                },
                videoplay(type) {
                    if (type == "oldVideo") {
                        this.dialogContent.videosrc = this.thisFormData.oldVideo;
                        this.dialogContent.visible = true;
                    }
                    else if (type == "newVideo") {
                        this.dialogContent.videosrc = this.thisFormData.newVideo;
                        this.dialogContent.visible = true;
                    }
                },
            }
        });
    </script>
</body>

</html>