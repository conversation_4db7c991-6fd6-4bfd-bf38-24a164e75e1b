import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/machine/`
  
export const addAndUpdateGoodsFinishPart =(params,config) =>{return request.post(apiPrefix + 'AddAndUpdateGoodsFinishPartAsync',params,config)}
export const deleteGoodsFinishPart = (params, config = {}) => {return request.delete(apiPrefix + 'DeleteGoodsFinishPart?GroupId='+params,config)}
export const queryGoodsFinishedPart = (params, config = {}) => {return request.get(apiPrefix + 'QueryGoodsFinishedPartAsync?groupId='+params,config)}
export const pageGoodsFinishedPart = (params, config = {}) => {return request.get(apiPrefix + 'PageGoodsFinishedPartAsync', { params: params, ...config })}

export const exportGoodsFinishedPart =(params,config ={responseType: 'blob'}) =>{
    return request.post(apiPrefix + 'ExportGoodsFinishedPartAsync',params, config) }   

// 成品自动开单    
export const getGoodsFinishedPartAutoAsync =(params,config) =>{
    return request.post(apiPrefix + 'GetGoodsFinishedPartAutoAsync',params,config)}

// 设置成品开单信息    
export const updateGoodsFinishedPartAutoAsync =(params,config) =>{
    return request.post(apiPrefix + 'UpdateGoodsFinishedPartAutoAsync',params,config)}

// 设置成品开单半成品信息    
export const setGoodsFinishedPartAutoAsync =(params,config) =>{
    return request.post(apiPrefix + 'SetGoodsFinishedPartAutoAsync',params,config)}
    
// 成品开单半成品信息     
export const getGoodsFinishedPartAutoSetAsync =(params,config) =>{
    return request.post(apiPrefix + 'GetGoodsFinishedPartAutoSetAsync',params,config)}    
    
// 成品开单日志     
export const getGoodsFinishedPartAutoLogAsync =(params,config) =>{
    return request.post(apiPrefix + 'GetGoodsFinishedPartAutoLogAsync',params,config)} 
  
// 成品开单日志     
export const getGoodsFinishedPartAutoLogGroupAsync =(params,config) =>{
    return request.post(apiPrefix + 'GetGoodsFinishedPartAutoLogGroupAsync',params,config)}     

// 岗位信息     
export const getJob_PositionListAsync =(params,config) =>{
    return request.post(apiPrefix + 'GetJob_PositionListAsync',params,config)}   
    
// 成品开单日志详情     
export const getGoodsFinishedPartAutoLogDetailAsync =(params,config) =>{
    return request.post(apiPrefix + 'GetGoodsFinishedPartAutoLogDetailAsync',params,config)}   
   
// 成品开单日志详情     
export const pageGoodsFinishedPartDetailAsync =(params,config) =>{
    return request.post(apiPrefix + 'PageGoodsFinishedPartDetailAsync',params,config)}   
          