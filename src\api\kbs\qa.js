import request from '@/utils/request'

//const apiPrefix = `http://**************:9000/yunhan-gis-lift/kbsRetrieveLog/`
const apiPrefix = `${process.env.VUE_APP_JAVA_API_BLADEGATEWAY}/yunhan-gis-lift/kbsRetrieveLog/`

//最近沟通过的问题/热门答疑列表
export const latestList = (params,config ={}) =>{
    return request.post(apiPrefix+'latestList', params, config)
}

//热门知识库列表
export const hotList = (params,config ={}) =>{
    return request.post(apiPrefix+'hotList', params, config)
}

//提问
export const saveQuestion = (params,config ={}) =>{
    return request.post(apiPrefix+'save', params, config)
}

//回答提问
export const replyQuestion = (params,config ={}) =>{
    return request.post(apiPrefix+'replyQuestion', params, config)
}

//采纳/不采纳答案
export const adoptAnswer = (params,config ={}) =>{
    return request.post(apiPrefix+'adoptAnswer', params, config)
}

//up提问
export const upQuestion = (params,config ={}) =>{
    return request.post(apiPrefix+'upQuestion', params, config)
}

//提交问题维护
export const maintainQuestionSave = (params,config ={}) =>{
    return request.post(apiPrefix+'saveHotList', params, config)
}

//删除
export const deleteQuestion = (params,config ={}) =>{
    return request.post(apiPrefix+'remove?id='+params.id, params, config)
}

//申请查看图片
export const applyImg = (params,config ={}) =>{
  return request.post(apiPrefix+'applyImg', params, config)
}

//提问-问答列表
export const replyList = (params,config ={}) =>{
  return request.post(apiPrefix+'replyList', params, config)
}

//更新跳转标签
export const update = (params,config ={}) =>{
  return request.post(apiPrefix+'update', params, config)
}

//知识库-待回复总数
export const unRepliedCount = (params,config ={}) =>{
  return request.post(apiPrefix+'unRepliedCount', params, config)
}

//知识库-更新答案
export const updateReply = (params,config ={}) =>{
  return request.post(apiPrefix+'updateReply', params, config)
}

//知识库-更改接口
export const topsearch = (params,config ={}) =>{
  return request.post(apiPrefix+'search', params, config)
}
