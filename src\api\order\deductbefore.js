import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/deductbeforezr/`



//分页查询
export const PageDeductBeforeZrList = (params, config = {}) => {
    return request.post(apiPrefix + 'PageDeductBeforeZrList', params, config)
}
//导出
export const ExportDeductBeforeZrList = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportDeductBeforeZrList', params, config)
}


//趋势图
export const GetDeductChatData = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDeductChatData', params, config)
}

//导入快递签收信息
export const ImportExpressSignOrder = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportExpressSignOrder', params, config)
}
//导入快递签收信息
export const ImportExpressStatusOrder = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportExpressStatusOrder', params, config)
}

//获取扣款订单关键节点信息-效率备注统计饼图
export const DeductOrderNodeInfoPie = (params, config = {}) => {
    return request.post(apiPrefix + 'DeductOrderNodeInfoPie', params, config)
}

//获取扣款订单关键节点信息
export const PageDeductOrderNodeInfoList = (params, config = {}) => {
    return request.post(apiPrefix + 'PageDeductOrderNodeInfoList', params, config)
}

export const ExportDeductOrderNodeInfoList2 = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportDeductOrderNodeInfoList2', params, config)
}


export const ExportDeductOrderNodeInfoXiaoLv1 = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportDeductOrderNodeInfoXiaoLv1', params, config)
}

export const ExportDeductOrderNodeInfoXiaoLv2 = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportDeductOrderNodeInfoXiaoLv2', params, config)
}
//获取扣款订单关键节点设置仓库
export const GetDeductOrderNodeAllList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDeductOrderNodeAllList', params, config)
}

//获取扣款订单关键节点时长
export const GetDeductOrderNodeSetList = (params, config = {}) => { return request.get(apiPrefix + 'GetDeductOrderNodeSetList', { params: params, ...config }) }

//保存扣款订单关键节点时长
export const SaveDeductOrderNodeSetList = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveDeductOrderNodeSetList', params, config)
}

//重算违规
export const GetDeductOrderNodeInfo_Again = (params, config = {}) => { return request.get(apiPrefix + 'GetDeductOrderNodeInfo_Again', { params: params, ...config }) }

//趋势图
export const GetDeductNodeInfoChatData = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDeductNodeInfoChatData', params, config)
}


//违规统计-违规人报表
export const GetDeductNodeNoPassUserNameRpt = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDeductNodeNoPassUserNameRpt', params, config)
}

//违规统计-违规仓库报表
export const GetDeductNodeNoPassWmsNameRpt = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDeductNodeNoPassWmsNameRpt', params, config)
}

//违规统计-违规快递公司报表
export const GetDeductNodeNoPassExpressCompanyNameRpt = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDeductNodeNoPassExpressCompanyNameRpt', params, config)
}

//违规统计-违规环节报表
export const GetDeductNodeNoPassTypeRpt = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDeductNodeNoPassTypeRpt', params, config)
}


//批次设置列表
export const GetDeductOrderNodeBatchSetList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDeductOrderNodeBatchSetList', params, config)
}

//删除批次设置
export const DelDeductOrderNodeBatchSet = (params, config = {}) => { return request.get(apiPrefix + 'DelDeductOrderNodeBatchSet', { params, ...config }) }

//保存批次设置
export const SaveDeductOrderNodeBatchSet = (params, config = {}) => {
    return request.post(apiPrefix + 'SaveDeductOrderNodeBatchSet', params, config)
}

//导出违规统计报表
export const ExportDeductNodeNoPassRpt = (params, config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportDeductNodeNoPassRpt', params, config)}


