import Decimal from 'decimal.js'
/**
 * 
 * @param {Number} num1 第一个数
 * @param {Number} num2 第二个数
 * @param {Number} precision 保留几位小数
 * @param {String} type 运算类型 + - * / % pow(幂运算)
 * @returns 返回计算结果
 */
const decimal = (num1 = 0, num2 = 0, precision = 2, type = '+',) => {
    Decimal.set({ rounding: Decimal.ROUND_DOWN });//全局截断模式
    let result = new Decimal(num1)
    switch (type) {
        case '+': //加法
            result = result.plus(num2)
            break
        case '-': //减法
            result = result.minus(num2)
            break
        case '*': //乘法
            result = result.times(num2)
            break
        case '/': //除法
            //如果num2为0,则返回0
            if (num2 === 0) {
                return 0
            }
            result = result.div(num2)
            break
        case '%': //取余
            if (num2 === 0) {
                return 0
            }
            result = result.mod(num2)
            break
        case 'pow': //幂运算
            result = result.pow(num2)
            break
    }
    return result.toDecimalPlaces(precision).toNumber() //保留precision位小数,precision小数后面不四舍五入,直接截断
}
export default decimal