import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/IllegalWordProduct/`

//违禁词-查询
export const getIllegalWordProductLists = (params, config = {}) => {
  return request.post(apiPrefix + 'GetIllegalWordProductLists', params, config)
}

//违禁词-重置
export const resetIllegalWordProduct = (params, config = {}) => {
  return request.post(apiPrefix + 'ResetIllegalWordProduct', params, config)
}

//违禁词-导出
export const exportIllegalWordProductLists = (params, config = {}) => {
  return request.post(apiPrefix + 'ExportIllegalWordProductLists', params, config)
}
