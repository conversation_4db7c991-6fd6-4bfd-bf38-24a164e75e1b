import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/volumeGoods/`

//GetVolumeGoodsListAsync 获取商品市场体量列表
export const getVolumeGoodsListAsync = (params, config = {}) => request.post(apiPrefix + `GetVolumeGoodsListAsync`, params, config)

//根据商品编码获取商品市场体量明细数据 GetVolumeGoodsInfoByGoodsCode
export const getVolumeGoodsInfoByGoodsCode = (params, config = {}) => request.post(apiPrefix + `GetVolumeGoodsInfoByGoodsCode`, params, config)

//导出商品市场体量列表 ExportVolumeGoodsListAsync
export const exportVolumeGoodsListAsync = (params, config = { responseType: 'blob' }) => request.post(apiPrefix + `ExportVolumeGoodsListAsync`, params, config)

//保存商品市场体量明细数据 SaveVolumeGoodsInfo
export const saveVolumeGoodsInfo = (params, config = {}) => request.post(apiPrefix + `SaveVolumeGoodsInfo`, params, config)

//批量认领 BatchClaimVolumeGoods
export const batchClaimVolumeGoods = (params, config = {}) => request.post(apiPrefix + `BatchClaimVolumeGoods`, params, config)

//批量重置 BatchResetVolumeGoods
export const batchResetVolumeGoods = (params, config = {}) => request.post(apiPrefix + `BatchResetVolumeGoods`, params, config)

//同步 SyncVolumeGoods
export const syncVolumeGoods = (params, config = {}) => request.post(apiPrefix + `SyncVolumeGoods`, params, config)

//获取商品市场体量日志列表 GetVolumeGoodsChangeLogListAsync
export const getVolumeGoodsChangeLogListAsync = (params, config = {}) => request.post(apiPrefix + `GetVolumeGoodsChangeLogListAsync`, params, config)

//ExportVolumeGoodsChangeLog 导出商品市场体量日志
export const exportVolumeGoodsChangeLog = (params, config = { responseType: 'blob' }) => request.post(apiPrefix + `ExportVolumeGoodsChangeLog`, params, config)

//GetChangeLogType 获取修改日志类型
export const getChangeLogType = (params, config = {}) => request.post(apiPrefix + `GetChangeLogType`, params, config)

//根据系列编码获取商品编码 GetVolumeGoodsInfoByStyleCode
export const getVolumeGoodsInfoByStyleCode = (params, config = {}) => request.post(apiPrefix + `GetVolumeGoodsInfoByStyleCode`, params, config)

//导出商品市场体量价格 按系列编码导出 ExportVolumeGoodsListByStyleCodeAsync
export const exportVolumeGoodsListByStyleCodeAsync = (params, config = { responseType: 'blob' }) => request.post(apiPrefix + `ExportVolumeGoodsListByStyleCodeAsync`, params, config)