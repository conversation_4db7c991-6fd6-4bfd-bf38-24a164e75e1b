<template>
    <el-select v-model="innerValue" :clearable="clearable" filterable remote :style="cststyle" reserve-keyword :placeholder="placeholder" 
        :remote-method="remoteMethod" @remove-tag="removetag" @change="valueChanged($event)" @clear="clear" :loading="loading" multiple collapse-tags>
        <el-option v-for="(item, i) in options" :key="'userSelector'+item.value+ item.extData.defaultDeptId+i" :label="item.label" :value="item.value">
            <span>{{item.label}}</span>
            <span  style=" color: #8492a6; " v-show="item.extData.position||item.extData.empStatusText||item.extData.jstUserName">({{item.extData.position?item.extData.position:''}},{{item.extData.empStatusText?item.extData.empStatusText:''}}{{item.extData.jstUserName ? ","+item.extData.jstUserName:"" }})</span>           
            <span style=" color: #8492a6; "> {{item.extData.deptName?item.extData.deptName:''}}</span>
        </el-option>
        <!-- <el-option v-if="value && !options.find(x=>x.value==value)" :key="'userSelector_dft'+(value==null?'':value)" :label="text" :value="value">
        </el-option> -->
    </el-select>

</template>

<script>     

    import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'

    export default {
        name: 'YhUserelector',
        props: {
            placeholder: {
                type: String,
                default() {
                    return "请输入"
                }
            },
            rows: {
                type: Number,
                default: 50
            },
            value: {
                type: Array,
                default() {
                    return [];
                }
            },
            text: {
                type: String,
                default() {
                    return ""
                }
            },
            clearable: {
                type: Boolean,
                default() { return true; }
            },
            cststyle: {
                type: Object,
                default() {
                    return {}
                }
            },
            textmap: {
                type: Array,
                default() {
                    return []
                }
            }
        },
        data() {
            return {
                loading: false,
                options: [],
                innerValue: [],
                innerText: "",
                orgOptions: [],
                nameArr: [],
                newoptions: [],
                changearr: []
            }
        },
        computed: {           
        },       
        async mounted() {   
            this.orgOptions = [...this.options];
            this.$nextTick(() => {
                //     if(this.value){
                //     this.options.push({label:this.text,value:this.value,extData:{defaultDeptId:null,position:null,empStatusText:null,jstUserName:null,deptName:null}});
                //     this.innerValue=this.value;
                // }
            });
        },
        methods: {
            showName(options){
                // this.options = [];
                // this.nameArr = [];
                
                // console.log(options,9999999999);
                // this.options = options;
                // this.innerValue = ids;
                // this.options.map((item2)=>{
                //     this.nameArr.push(item2.label)
                // })
                this.innerValue = [];
                this.newoptions = options;
                this.options = options;
                options.map((item)=>{
                    this.innerValue.push(item.value);
                });

            },
            clear(){
                this.innerValue = [];
                this.$emit("update:textmap", []);

                // this.$emit("update:value", null);
                // this.$emit("update:text", null);
                this.$emit("change", null);
            },
            removetag(data){
                setTimeout(() => {
                    let shengyu = []
                    this.changearr.map((item)=>{
                        if(item.value != data){
                            shengyu.push(item)
                        }
                    })
                    this.newoptions = shengyu;
                    this.$emit("update:textmap", shengyu);
                }, 50);
               
            },
            valueChanged(newValue) {
                // 去重函数
                function removeDuplicatesByValue(arr) {
                    const seenValues = new Set();
                    return arr.filter(item => {
                        if (!seenValues.has(item.value)) {
                            seenValues.add(item.value);
                            return true;
                        }
                        return false;
                    });
                }
                let findOpts=null;
                let labelValue = newValue;
               
                var towarr = this.newoptions;
                newValue.map((item)=>{
                    towarr = [...this.options.filter(itemm=> item == itemm.value), ...towarr]
                })


                

                const uniqueData = removeDuplicatesByValue(towarr);
                this.changearr = uniqueData;
                setTimeout(()=>{
                    this.$emit("update:value", newValue);
                    // this.$emit("update:text", '');
                    this.$emit("update:textmap", uniqueData);
                    this.$emit("change",findOpts);
                },0)
               
            },
            async remoteMethod(query) {
                if (query && query.length > 50) return this.$message.error("输入内容过长");
                this.loading = true;
                if (query !== '') {                  
                    let rlt= await QueryAllDDUserTop100({ keywords: query });
                    if (rlt && rlt.success) {
                        this.options = rlt.data?.map(item => {
                            return { label: item.userName, value: item.ddUserId, extData:item }
                        });
                    }
                } else {
                    this.options = [...this.orgOptions];
                }
                this.loading = false;
            }
        }
    }
</script>

