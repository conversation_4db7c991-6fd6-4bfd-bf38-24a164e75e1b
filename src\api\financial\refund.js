
   import request from '@/utils/request'
   const apiPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Refund/`
 
   export const deleteRefundDetail = (params, config = {}) => {return request.post(apiPrefix + 'DeleteRefundDetailAsync', params, config)}
   export const pageRefundDetail = (params, config = {}) => {return request.get(apiPrefix + 'PageRefundDetailAsync', { params: params, ...config })}
   // export const pageDetail2MonthSumfee = (params, config = {}) => {return request.get(apiPrefix + 'PageDetail2MonthSumfeeAsync', { params: params, ...config })}       
   // export const computDetail2 = (params, config = {}) => {return request.post(apiPrefix + 'ComputDetail2Async',  params, config)}
   export const pageRefund = (params, config = {}) => {return request.get(apiPrefix + 'PageRefundAsync', { params: params, ...config })}
   
   export const importRefundDetail = (params, config = {}) => {return request.post(apiPrefix + 'ImportRefundDetailAsync', params, config)}

   //首页退款趋势图
   export const getRefundDetailAnalysis = (params, config = {}) => {return request.get(apiPrefix + 'GetRefundDetailAnalysisAsync', { params: params, ...config })}

   export const pageRefundDetailAnalysis = (params, config = {}) => {return request.get(apiPrefix + 'PageRefundDetailAnalysisAsync', { params: params, ...config })}

