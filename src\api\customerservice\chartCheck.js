import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/UnPayOrder/`

// 售前
export const getUnpayOrderList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetUnPayOrderPageList', params, config)
}

// 售后
export const getAfterSalesOrderList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetUnPayOrderAfterSalesPageList', params, config)
}

// 发票
export const getInvoiceUnPayOrderList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetInvoiceUnPayOrderPageList', params, config)
}

// 获取审核的聊天数据
export const GetUnpayOrderSalesList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetUnpayOrderSalesList', params, config)
}

// 聊天记录
export const getChartList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetChatRecordsPageList', params, config)
}

// 售前导出
export const exportUnpay = (params, config = {}) => {
    return request.post(apiPrefix + 'UnPayOrderExport', params, config)
}

// 售后导出
export const exportAfter = (params, config = {}) => {return request.post(apiPrefix + 'UnPayOrderAfterSalesExport', params, config)}
export const exportInvoice = (params, config = {}) => {return request.post(apiPrefix + 'InvoiceUnPayOrderExport', params, config)}

// 初审
export const saveChatRecordReview = (params, config = {}) => {
    return request.post(apiPrefix + 'ChatRecordReview', params, config)
}

// 售后状态列表
export const getAfterStatusList = (params, config = {}) => {
  return request.get(apiPrefix + 'GetAfterSalesStateList', params, config)
}


//客服聊天记录——平台批判列表
export const getPlatformPunishPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPlatformPunishPageList', params, config)
  }
//客服聊天记录——平台批判——分组列表
export const getPlatformPunishQueryGroup = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPlatformPunishQueryGroup', params, config)
  }

  //客服聊天记录——平台批判——聊天记录
export const getPlatformPunishChatRecordsPageList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPlatformPunishChatRecordsPageList', params, config)
  }

//客服聊天记录——平台批判——判罚
  export const uploadCredentialsAsync = (params, config = {}) => {
    return request.post(apiPrefix + 'UploadCredentialsAsync', params, config)
  }

  // 客服聊天记录——平台批判——导出
export const platformPunishExport = (params, config = {responseType: 'blob'}) => {
  return request.post(apiPrefix + 'PlatformPunishExport', params, config)
}

//客服聊天记录——敷衍回复列表
export const getReplyCarelesslyPagehList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetReplyCarelesslyPagehList', params, config)
  }
//客服聊天记录——敷衍回复——分组列表
export const getReplyCarelesslyPagehQueryGroup = (params, config = {}) => {
    return request.post(apiPrefix + 'GetReplyCarelesslyPagehQueryGroup', params, config)
  }

//客服聊天记录——敷衍回复——聊天记录
export const getReplyCarelesslyChatRecordsPageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetReplyCarelesslyChatRecordsPageList', params, config)
}

  // 客服聊天记录——敷衍回复——导出
  export const replyCarelesslyExport = (params, config = {responseType: 'blob'}) => {
    return request.post(apiPrefix + 'ReplyCarelesslyExport', params, config)
  }

  //获取账号使用人
export const getUserNameList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetUserNameList', params, config)
}

//获取组名称
export const getGruopNameList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGruopNameList', params, config)
}

//获取组长
export const getGroupManagerList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGroupManagerList', params, config)
}

//敷衍回复判罚
export const uploadResponsibilityAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'UploadResponsibilityAsync', params, config)
}

//售前售后详情id
export const getUnPayOrderById = (params, config = {}) => {
  return request.get(apiPrefix + 'GetUnPayOrderById', { params: params, ...config })
}
//平台判罚详情id
export const getPlatformPunishById = (params, config = {}) => {
  return request.get(apiPrefix + 'GetPlatformPunishById', { params: params, ...config })
}

//敷衍回复Id的信息
export const getReplyCarelesslyById = (params, config = {}) => {
  return request.get(apiPrefix + 'GetReplyCarelesslyById',  { params: params, ...config })
}
 //售前售后审核统计分页数据，客服数据
 export const getUserNameDataStatisticsPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetUserNameDataStatisticsPageList', params, config) }

  //售前售后审核统计分页数据，客服数据
 export const getJudgmentDataStatisticsPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetJudgmentDataStatisticsPageList', params, config) }

 //售前售后审核统计页面，客服名称
 export const getUnPayOrderUserNameList = (params, config = {}) => { return request.post(apiPrefix + 'GetUnPayOrderUserNameList', params, config) }

 //售前售后审核统计页面，客服分组名
 export const getUnPayOrderGroupNameList = (params, config = {}) => { return request.post(apiPrefix + 'GetUnPayOrderGroupNameList', params, config) }

  //售前售后审核统计页面，稽查初审人名称
  export const getUnPayOrderInitialOperatorList = (params, config = {}) => { return request.post(apiPrefix + 'GetUnPayOrderInitialOperatorList', params, config) }

 //售前售后审核统计页面，稽查分组名
 export const getInitialOperatorGroupNameList = (params, config = {}) => { return request.post(apiPrefix + 'GetInitialOperatorGroupNameList', params, config) }

 //售后，责任人
 export const getPersonList = (params, config = {}) => { return request.post(apiPrefix + 'GetPersonList', params, config) }



//统计页面，客服数据导出
 export const userNameDataStatisticsExport = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'UserNameDataStatisticsExport', params, config)}

 //统计页面，稽查数据导出
 export const judgmentDataStatisticsExport = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'JudgmentDataStatisticsExport', params, config)}


 //正向聊天记录——正向聊天列表
export const getQualityChatOrderPageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetQualityChatOrderPageList', params, config)
}

//正向聊天记录——店铺下拉
export const getQualityChatOrderShopNameList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetQualityChatOrderShopNameList', params, config)
}
//正向聊天记录——聊天账号下拉
export const getQualityChatOrderChatAccountList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetQualityChatOrderChatAccountList', params, config)
}

//正向聊天记录——获取正向聊天账号使用人
export const getQualityChatOrderUserNameList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetQualityChatOrderUserNameList', params, config)
}

//正向聊天记录——获取正向聊天分组
export const getQualityChatOrderGroupNameList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetQualityChatOrderGroupNameList', params, config)
}


//正向聊天记录——获取正向聊天记录列表
export const getQualityChatRecordsPageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetQualityChatRecordsPageList', params, config)
}


//正向聊天——提取话术 保存关闭按钮
export const addQualityChatInfoAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'AddQualityChatInfoAsync', params, config)
}


//正向聊天  查看提取话术 获取正向聊天优质信息
export const getQualityChatInfoList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetQualityChatInfoList', params, config)
}


//正向聊天列表导出
// 售前导出
export const qualityChatOrderExport = (params, config = {}) => {
  return request.post(apiPrefix + 'QualityChatOrderExport', params, config)
}


//客服拉黑买家
export const getBlacklistingBuyersPageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetBlacklistingBuyersPageList', params, config)
}

//客服拉黑买家 添加
export const addOrUpdateBlacklistingBuyersAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'AddOrUpdateBlacklistingBuyersAsync', params, config)
}

//客服拉黑买家 删除
export const deleteBlacklistingBuyersAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'DeleteBlacklistingBuyersAsync', params, config)
}

//客服拉黑买家 列表下拉 店铺
export const getBlacklistingBuyersShopNameList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetBlacklistingBuyersShopNameList', params, config)
}
//客服拉黑买家 列表下拉 账号使用人
export const getBlacklistingBuyersUserNameList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetBlacklistingBuyersUserNameList', params, config)
}
//客服拉黑买家 列表下拉 分组
export const getBlacklistingBuyersGroupNameList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetBlacklistingBuyersGroupNameList', params, config)
}
//客服拉黑买家 列表下拉 最后操作人
export const getBlacklistingBuyersLastOperatorList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetBlacklistingBuyersLastOperatorList  ', params, config)
}

//客服拉黑买家 导出
export const blacklistingBuyersExport = (params, config = {responseType: 'blob'}) => {
  return request.post(apiPrefix + 'blacklistingBuyersExport', params, config)
}
 //退款原因统计，分页列表
export const getReasonForRefundStatisticsPageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetReasonForRefundStatisticsPageList', params, config)
}

//退款原因统计，分组列表
export const getReasonForRefundGroupNameList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetReasonForRefundGroupNameList', params, config)
}

//退款原因统计，趋势图
export const getReasonForRefundTrendChart = (params, config = {}) => {
  return request.post(apiPrefix + 'GetReasonForRefundTrendChart', params, config)
}


 //退款原因统计，导出
 export const reasonForRefundStatisticsExport = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'ReasonForRefundStatisticsExport', params, config)}

//聊天问题数据汇总——列表
export const getUnPayOrderAuditStatisticsPageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetUnPayOrderAuditStatisticsPageList', params, config)
}

//聊天问题数据汇总——分组
export const getUnPayOrderAuditUserNameList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetUnPayOrderAuditUserNameList', params, config)
}

//聊天问题数据汇总——账号使用人
export const getUnPayOrderAuditGroupNameList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetUnPayOrderAuditGroupNameList', params, config)
}

//聊天问题数据汇总——导出
export const unPayOrderAuditStatisticsExport = (params, config = {responseType: 'blob'}) => {
  return request.post(apiPrefix + 'UnPayOrderAuditStatisticsExport', params, config)
}

//聊天问题数据汇总——趋势图
export const getUnPayOrderAuditStatisticsTrendChart  = (params, config = {}) => { return request.post(apiPrefix + 'GetUnPayOrderAuditStatisticsTrendChart  ', params, config) }

//售前售后审核数据统计 审核总量  客服服务
export const getDataStatisticsPopupPageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetDataStatisticsPopupPageList', params, config)
}

//售前售后审核数据统计 审核总量  稽查数据
export const getJudgmentDataStatisticsPopupPageList  = (params, config = {}) => {
  return request.post(apiPrefix + 'GetJudgmentDataStatisticsPopupPageList', params, config)
}

//售前售后审核数据统计(拼多多) 审核总量  客服服务
export const getPddUserNameDataStatisticsPageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetPddUserNameDataStatisticsPageList', params, config)
}

//售前售后审核数据统计(拼多多) 审核总量  稽查数据
export const getPddJudgmentDataStatisticsPageList  = (params, config = {}) => {
  return request.post(apiPrefix + 'GetPddJudgmentDataStatisticsPageList', params, config)
}

//售前售后审核数据统计(拼多多)，客服数据导出
export const pddUserNameDataStatisticsExport   = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'PddUserNameDataStatisticsExport', params, config)}

//售前售后审核数据统计(拼多多)，稽查数据导出
export const pddJudgmentDataStatisticsExport = (params, config = {responseType: 'blob'}) => {return request.post(apiPrefix + 'PddJudgmentDataStatisticsExport', params, config)}

//聊天问题数据汇总 免责数字 历史弹框
// export const getUnPayOrderProblemPageList  = (params, config = {}) => {
//   return request.post(apiPrefix + 'GetUnPayOrderProblemPageList', params, config)
// }
export const getUnPayOrderProblemPageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetUnPayOrderProblemPageList', params, config)
}

//初审复审评判等责任客服数据获取
export const getGroupSnameList  = (params, config = {}) => {
  return request.post(apiPrefix + 'GetGroupSnameList', params, config)
}
//客服认责
export const UnpayOrderAcceptResponsibility = (params, config = {}) => {
  return request.post(apiPrefix + 'UnpayOrderAcceptResponsibility', params, config)
}
