import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_MsgCenter}/user/`
// axios.defaults.headers.post['Content-Type']='application/json;charset=UTF-8';


//获取聊天的用户列表
export const getHotSaleGoodChatUserListsAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'GetHotSaleGoodChatUserListsAsync', { params: params, ...config })
}

//获取聊天用户的双方信息
export const getHotSaleGoodChatUserInfosAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'GetHotSaleGoodChatUserInfosAsync', { params: params, ...config })
}

//获取用户单独聊天的消息记录
export const getHotSaleGoodChatByUserIdAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'GetHotSaleGoodChatByUserIdAsync', { params: params, ...config })
}

//发送消息
export const hotSaleGoodChatDDUserAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'HotSaleGoodChatDDUserAsync', params, config)
}

//更新消息已读状态
export const updateHotSaleGoodChatSendMesgAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'UpdateHotSaleGoodChatSendMesgAsync', { params: params, ...config })
}

//删除对话
export const deleteHotSaleGoodChat = (params, config = {}) => {
  return request.get(apiPrefix + 'DeleteHotSaleGoodChatAsync', { params: params, ...config })
}

//获取所有类型
export const getNoticeSubTypes = (params,config ={}) =>{
  return request.get(apiPrefix+'GetNoticeSubTypes', {params: params, ...config})
}

//获取登录用户消息订阅
export const getUserNoticeSub = (params,config ={}) =>{
  return request.get(apiPrefix+'GetUserNoticeSub', {params: params, ...config})
}

//更新用户消息订阅类型
export const updateNoticeSub = (params,config ={}) =>{
  return request.post(apiPrefix+'UpdateNoticeSub',  params, config)
}

//分页获取用户站内信
export const pageGetUserNotices = (params,config ={}) =>{
  return request.post(apiPrefix+'PageGetUserNotices',  params, config)
}

//用户站内信设置已读
export const readNotice = (params,config ={}) =>{
  return request.get(apiPrefix+'ReadNotice', {params: params, ...config})
}


//用户站内信设置已读
export const ReadNoticeBatch = (params,config ={}) =>{
  return request.post(apiPrefix+'ReadNoticeBatch',  params, config)
}



//用户站内信设置已读
export const ExecNotice = (params,config ={}) =>{
  return request.post(apiPrefix+'ExecNotice',  params, config)
}

//获取所有订阅的唯独条数 全部相加为总数
export const getUnreadCount = (params,config ={}) =>{
  return request.post(apiPrefix+'GetUnreadCount',  params, config)
}



//获取子系列在线人员
export const GetAppTypeOnlineUsers = (params,config ={}) =>{
  return request.get(apiPrefix+'GetAppTypeOnlineUsers', {params: params, ...config})
}

