import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/purchaseCostVerify/`
const apiimportPrefix = `${process.env.VUE_APP_BASE_API_ImportInventory}/purchase/`

// 采购运费核对-汇总-数据展示
export const getSummaryOrderQtyFreight = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSummaryOrderQtyFreight', params, config)
}

// 采购运费核对-汇总-数据列表
export const getSummaryListPage = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSummaryListPage', params, config)
}

// 采购运费核对-汇总-数据导出
export const exportSummaryList = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'ExportSummaryList', params, config)
}

// 采购运费核对-仓库数据-获取仓库数据
export const getPurchaseCostVerifyWarehousePage = (params, config = {}) => {
  return request.post(apiPrefix + 'GetPurchaseCostVerifyWarehousePage', params, config)
}

// 删除采购运费核对数据 批次号
export const delPurchaseCostVerifyWarehouseByBatchNo = (params, config = {}) => {
  return request.get(apiPrefix + 'DelPurchaseCostVerifyWarehouseByBatchNo', { params: params, ...config })
}

// 删除采购运费核对数据 勾选
export const delPurchaseCostVerifyWarehouseBySel = (params, config = {}) => {
  return request.post(apiPrefix + 'DelPurchaseCostVerifyWarehouseBySel', params, config)
}

// 采购运费核对-仓库数据-导出仓库数据
export const exportPurchaseCostVerifyWarehouse = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'ExportPurchaseCostVerifyWarehouse', params, config)
}

// 采购运费核对-仓库数据-导入仓库数据
export const importPurchaseCostVerifyWarehouse = (params, config = {}) => {
  return request.post(apiimportPrefix + 'ImportPurchaseCostVerifyWarehouse', params, config)
}

// 采购运费核对-仓库数据-删除仓库数据
export const delPurchaseCostVerifyWarehouseByBatchNumber = (params, config = {}) => {
  return request.post(apiPrefix + 'DelPurchaseCostVerifyWarehouseByBatchNumber', params, config)
}

// 采购运费核对-仓库数据-发起审批
export const sendPurchaseCostVerifyApply = (params, config = {}) => {
  return request.post(apiPrefix + 'SendPurchaseCostVerifyApply', params, config)
}

// 采购运费核对-采购数据-分页获取采购数据
export const getPurchaseCostVerifyPurchaseOrderPage = (params, config = {}) => {
  return request.post(apiPrefix + 'GetPurchaseCostVerifyPurchaseOrderPage', params, config)
}

// 采购运费核对-采购数据-导出采购数据
export const exportPurchaseCostVerifyPurOrder = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'ExportPurchaseCostVerifyPurOrder', params, config)
}

// 采购运费核对-采购数据-获取设置规则列表
export const getPurchaseCostVerifyPurSetList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetPurchaseCostVerifyPurSetList', { params, ...config })
}

// 采购运费核对-采购数据-保存设置规则
export const savePurchaseCostVerifyPurSet = (params, config = {}) => {
  return request.post(apiPrefix + 'SavePurchaseCostVerifyPurSet', params, config)
}

// 采购运费核对-采购数据-确认采购数据
export const confirmPurchaseCostVerifyPurOrder = (params, config = {}) => {
  return request.post(apiPrefix + 'ConfirmPurchaseCostVerifyPurOrder', params, config)
}

// 采购运费核对-采购数据-处理采购数据
export const handlePurchaseCostVerifyPurOrder = (params, config = {}) => {
  return request.post(apiPrefix + 'HandlePurchaseCostVerifyPurOrder', params, config)
}

// 采购运费核对-采购数据-确认采购数据-父级
export const confirmPurchaseCostVerifyPurOrderForParent = (params, config = {}) => {
  return request.post(apiPrefix + 'ConfirmPurchaseCostVerifyPurOrderForParent', params, config)
}

// 采购运费核对-采购数据-处理采购数据-父级
export const handlePurchaseCostVerifyPurOrderForParent = (params, config = {}) => {
  return request.post(apiPrefix + 'HandlePurchaseCostVerifyPurOrderForParent', params, config)
}

// 采购运费核对-采购数据-批量操作(确认/处理)
export const batchHandlePurchaseCostVerifyPurOrder = (params, config = {}) => {
  return request.post(apiPrefix + 'BatchHandlePurchaseCostVerifyPurOrder', params, config)
}

// 采购运费核对-采购数据-获取采购数据日志
export const getPurchaseCostVerifyPurOrderLogPage = (params, config = {}) => {
  return request.post(apiPrefix + 'GetPurchaseCostVerifyPurOrderLogPage', params, config)
}

// 采购运费核对-采购数据-获取审批弹窗数据
export const getPurchaseCostVerifyApplyData = (params, config = {}) => {
  return request.post(apiPrefix + 'GetPurchaseCostVerifyApplyData', params, config)
}

// 采购运费核对-仓库数据-上传凭证图片
export const importPurchaseCostVerifyPicture = (params, config = {}) => {
  return request.post(apiPrefix + 'ImportPurchaseCostVerifyPicture', params, config)
}


// 入库拍摄导入仓库数据
export const AddPurchaseCostVerifyForWarehouseOrderVideo = (params, config = {}) => {
    return request.post(apiPrefix + 'AddPurchaseCostVerifyForWarehouseOrderVideo', params, config)
}

//获取采购单号
export const GetPurchaseCostVerifyBuyNo = (params, config = {}) => {
    return request.get(apiPrefix + 'GetPurchaseCostVerifyBuyNo?buyNo='+params.buyNo, params, config)
}

//新增仓库数据
export const AddPurchaseCostVerify = (params, config = {}) => {
    return request.post(apiPrefix + 'AddPurchaseCostVerify', params, config)
}

//编辑采购运费核对-仓库数据
export const EditPurchaseCostVerify = (params, config = {}) => {
    return request.post(apiPrefix + 'EditPurchaseCostVerify', params, config)
}

//更新采购运费核对-仓库数据凭证
export const UpdatePurchaseCostVerifyProof = (params, config = {}) => {
    return request.post(apiPrefix + 'UpdatePurchaseCostVerifyProof', params, config)
}

//明细
export const GetPurchaseCostVerifyInfo = (params, config = {}) => {
    return request.post(apiPrefix + 'GetPurchaseCostVerifyInfo?id='+params.id, params, config)
}

//提交财务预审
export const submitFinancePriorAudit = (params, config = {}) => {
  return request.post(apiPrefix + 'SubmitFinancePriorAudit', params, config)
}

//分页财务预审列表
export const getFinancePriorAuditPage = (params, config = {}) => {
  return request.post(apiPrefix + 'GetFinancePriorAuditPage', params, config)
}

//导出仓库预审数据
export const exportFinancePriorAuditData = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'ExportFinancePriorAuditData', params, config)
}

//单个财务预审
export const financePriorAudit = (params, config = {}) => {
  return request.post(apiPrefix + 'FinancePriorAudit', params, config)
}

//批量财务预审
export const batchFinancePriorAudit = (params, config = {}) => {
  return request.post(apiPrefix + 'batchFinancePriorAudit', params, config)
}

//发起审批明细列表
export const getPurchaseCostVerifyApplyTableData = (params, config = {}) => {
  return request.post(apiPrefix + 'GetPurchaseCostVerifyApplyTableData', params, config)
}
