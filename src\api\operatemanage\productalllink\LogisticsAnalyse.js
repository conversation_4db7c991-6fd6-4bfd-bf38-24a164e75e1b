import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/AllLink/`
export const pageHotSaleGoodsAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsAsync', params, config) }
export const isDoHotSaleGoodsAsync = (params, config = {}) => { return request.post(apiPrefix + 'IsDoHotSaleGoodsAsync', params, config) }
export const importHotSaleGoodsAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportHotSaleGoodsAsync', params, config) }

//获取用户信息
export const getUserInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetUserInfo', { params: params, ...config }) }
//获取一个雪花ID
export const GetSnowflakeId = (params, config = {}) => { return request.get(apiPrefix + 'GetSnowflakeId', { params: params, ...config }) }
//获取 商品 GetProductByKeywords
export const GetProductByKeywords = (params, config = {}) => { return request.get(apiPrefix + 'GetProductByKeywords', { params: params, ...config }) }

export const getAllLinkPlantformsAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetAllLinkPlantformsAsync', { params: params, ...config }) }
export const getAllLinkCategoryNamesAsyncByParent = (params, config = {}) => { return request.get(apiPrefix + 'GetAllLinkCategoryNamesAsyncByParent', { params: params, ...config }) }

//按时间范围查看商品趋势图
export const getHotSaleGoodsEchartByDateAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleGoodsEchartByDateAsync', { params: params, ...config }) }


//分页查询已选品
export const pageHotSaleGoodsChooseAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsChooseAsync', params, config) }

//获取产品信息
export const getProductInfo = (id, config = {}) => {
    return request.get(apiPrefix + `GetProductInfo?productId=${id}`, { params: {}, ...config })
}
//获取时间范围内的趋势图
export const getProductSalesTrendByDate = (params, config = {}) => { return request.get(apiPrefix + 'GetProductSalesTrendByDate', { params: params, ...config }) }
//申请查询商品详情
export const goodsInfoQueryReqAsync = (params, config = {}) => { return request.get(apiPrefix + 'GoodsInfoQueryReqAsync', { params: params, ...config }) }


//获取所有 待查询商品记录
export const getAllHotGoodsWaitQueryAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetAllHotGoodsWaitQueryAsync', { params: params, ...config }) }

//申请查询参谋数据
export const reqGoodsCmRefInfoQueryAsync = (params, config = {}) => { return request.get(apiPrefix + 'ReqGoodsCmRefInfoQueryAsync', { params: params, ...config }) }

//查看具体竞品参谋数据
export const getHotGoodsCmRefInfoAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetHotGoodsCmRefInfoAsync', { params: params, ...config }) }



//申请竞品SKU
export const applyCompeteGoodSKU = (params, config = {}) => { return request.get(apiPrefix + 'ApplyCompeteGoodSKU', { params: params, ...config }) }
//手动添加SKU数据
export const addSkuListByUser = (params, config = {}) => { return request.post(apiPrefix + 'AddSkuListByUser', params, config) }

//获取已选品的竞品SKU数据
export const getHotSaleGoodsChooseSkuData = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleGoodsChooseSkuData', { params: params, ...config }) }
//保存已选品的竞品SKU数据
export const saveHotSaleGoodsChooseSkuData = (params, config = {}) => { return request.post(apiPrefix + 'SaveHotSaleGoodsChooseSkuData', params, config) }
//竞品参谋数据-拼多多
export const getHotGoodsCmRefInfoPddData = (params, config = {}) => { return request.get(apiPrefix + 'GetHotGoodsCmRefInfoPddData', { params: params, ...config }) }

//获取竞品操作日志
export const getHotSaleGoodsChooseLog = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleGoodsChooseLog', { params: params, ...config }) }

//根据id或标题获取竞品
export const getHotSaleGoodsByFilter = (params, config = {}) => { return request.get(apiPrefix + 'GetHotSaleGoodsByFilter', { params: params, ...config }) }

//竞品归档
export const SealCloseChooseAsync = (params, config = {}) => { return request.post(apiPrefix + 'SealCloseChooseAsync', params, config) }

//竞品-转让运营
export const tranOfHotSaleGoodsChoose = (params, config = {}) => { return request.get(apiPrefix + 'TranOfHotSaleGoodsChoose', { params: params, ...config }) }

//设置竞品利润状态
export const DingDingApproval_BuildGoodsDocBeforConfirm = (params, config = {}) => { return request.get(apiPrefix + 'DingDingApproval_BuildGoodsDocBeforConfirm', { params: params, ...config }) }

//获取SKU采样明细数据
export const getSkuSamplingListBySkuId = (params, config = {}) => { return request.get(apiPrefix + 'GetSkuSamplingListBySkuId', { params: params, ...config }) }

//竞品登记采样
export const registerSkuOrderAsync = (params, config = {}) => { return request.get(apiPrefix + 'RegisterSkuOrderAsync', { params: params, ...config }) }

//分页查询SKU采样数据
export const pageHotSaleGoodsChooseSkuOrderAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsChooseSkuOrderAsync', params, config) }

//获取SKU采样数据
export const getSkuOrderAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetSkuOrderAsync', { params: params, ...config }) }

//保存SKU采样数据
export const saveSkuOrderAsync = (params, config = {}) => { return request.post(apiPrefix + 'SaveSkuOrderAsync', params, config) }

//设置SKU禁用状态 SetSkuDisableState
export const setSkuDisableState = (params, config = {}) => { return request.get(apiPrefix + 'SetSkuDisableState', { params: params, ...config }) }
//设置所有SKU禁用状态 SetAllSkuDisableState
export const SetAllSkuDisableState = (params, config = {}) => { return request.get(apiPrefix + 'SetAllSkuDisableState', { params: params, ...config }) }

//竞品SKU单独登记采样 RegisterSkuOrderBySkuAsync
export const registerSkuOrderBySkuAsync = (params, config = {}) => { return request.post(apiPrefix + 'RegisterSkuOrderBySkuAsync', params, config) }

//获取选品建档数据
export const getBuildGoodsDocAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetBuildGoodsDocAsync', { params: params, ...config }) }

//获取选品建档数据
export const getBuildGoodsDocOtherAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetBuildGoodsDocOtherAsync', { params: params, ...config }) }

//保存选品建档
export const buildGoodsDocAsync = (params, config = {}) => { return request.post(apiPrefix + 'BuildGoodsDocAsync', params, config) }

//保存选品建档
export const buildGoodsDocUpdateCodeAsync = (params, config = {}) => { return request.post(apiPrefix + 'BuildGoodsDocUpdateCodeAsync', params, config) }

//保存选品建档-并审批通过
export const buildGoodsDocSyncJstAndDingDingPassAsync = (params, config = {}) => { return request.post(apiPrefix + 'BuildGoodsDocSyncJstAndDingDingPassAsync', params, config) }

///更新建编码当前审批流程信息
export const syncBuildGoodsDocDingDingCurAuditNode = (params, config = {}) => { return request.get(apiPrefix + 'SyncBuildGoodsDocDingDingCurAuditNode', { params: params, ...config }) }



// //申请竞品档案编码
// export const applyBuildGoodsDocCodeAsync = (params, config = {}) => { return request.get(apiPrefix + 'ApplyBuildGoodsDocCodeAsync', { params: params, ...config }) }

//分页查询选品建档
export const pageHotSaleGoodsBuildGoodsAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsBuildGoodsAsync', params, config) }

//分页查询选品建档
export const pageHotSaleGoodsBuildGoodsTreeAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsBuildGoodsTreeAsync', params, config) }

//【旧】分页查询选品建档
export const pageHotSaleGoodsBuildGoodsCodeAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsBuildGoodsCodeAsync', params, config) }
//【新】分页查询选品建档
export const pageHotSaleGoodsBuildGoodsCodeNewAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsBuildGoodsCodeNewAsync', params, config) }

//选品建档拍摄前获取数据
export const GetDataBeforeBuildGoodsMediaTask = (params, config = {}) => { return request.get(apiPrefix + 'GetDataBeforeBuildGoodsMediaTask', { params: params, ...config }) }


//选品建档发起申请
export const dingDingApproval_BuildGoodsDoc = (params, config = {}) => { return request.get(apiPrefix + 'DingDingApproval_BuildGoodsDoc', { params: params, ...config }) }


//发起SKU询价
export const EnquiryLaunchAsync = (params, config = {}) => { return request.post(apiPrefix + 'EnquiryLaunchAsync', params, config) }

//获取询价报价信息，运营
export const GetEnquiryFbInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetEnquiryFbInfo', { params: params, ...config }) }

//选品上架
export const SetOnSell = (params, config = {}) => { return request.get(apiPrefix + 'SetOnSell', { params: params, ...config }) }

//运营询价列表 PageChooseEnquiryAsync
export const pageChooseEnquiryAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageChooseEnquiryAsync', params, config) }
//运营询价统计列表 PageChooseEnquiryAsync
export const PageChooseEnquirySummaryAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageChooseEnquirySummaryAsync', params, config) }


//获取询价报价信息，采购
export const GetEnquiryInfoAndSelfFb = (params, config = {}) => { return request.get(apiPrefix + 'GetEnquiryInfoAndSelfFb', { params: params, ...config }) }
//选品报价
export const EnquiryFeedbackAsync = (params, config = {}) => { return request.post(apiPrefix + 'EnquiryFeedbackAsync', params, config) }
//采纳报价前校验 SetEnquiryFeedbackWinnerBefore
export const SetEnquiryFeedbackWinnerBefore = (params, config = {}) => { return request.get(apiPrefix + 'SetEnquiryFeedbackWinnerBefore', { params: params, ...config }) }

//询价报价统计报表
export const GetEnquiryFeedbakSummaryCharts = (params, config = {}) => { return request.post(apiPrefix + 'GetEnquiryFeedbakSummaryCharts', params, config) }

//分页查询SKU采样数据--采购模块
export const pageHotSaleGoodsChooseForSkuOrderAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleGoodsChooseForSkuOrderAsync', params, config) }

//评价任务-分页查询
export const pageCommentTaskAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageCommentTaskAsync', params, config) }
//获取单条评价
export const getCommentTaskFirstDetailByDtlId = (params, config = {}) => { return request.get(apiPrefix + 'GetCommentTaskFirstDetailByDtlId', { params: params, ...config }) }
//评价任务订单-分页查询
export const pageCommentTaskOrderAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageCommentTaskOrderAsync', params, config) }
// 获取产品
export const getCommentProductListByCode = (params, config = {}) => { return request.get(apiPrefix + 'GetCommentProductListByCode', { params: params, ...config }) }
//保存评价任务
export const saveCommentTaskAsync = (params, config = {}) => { return request.post(apiPrefix + 'SaveCommentTaskAsync', params, config) }
// 删除评价任务
export const deleteCommentTaskAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteCommentTaskAsync', { params: params, ...config }) }
// 删除评价任务-明细
export const deleteCommentTaskDtlAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteCommentTaskDtlAsync', { params: params, ...config }) }
// 发布/取消发布-评价任务
export const releaseCommentTaskAsync = (params, config = {}) => { return request.get(apiPrefix + 'ReleaseCommentTaskAsync', { params: params, ...config }) }
// 发布/取消发布-评价任务
export const sendJiuTanZi = (params, config = {}) => { return request.get(apiPrefix + 'SendJiuTanZi', { params: params, ...config }) }
// 认领-评价任务
export const receiveCommentTaskAsync = (params, config = {}) => { return request.get(apiPrefix + 'receiveCommentTaskAsync', { params: params, ...config }) }
//完成-评价任务
export const finishCommentTaskAsync = (params, config = {}) => { return request.post(apiPrefix + 'FinishCommentTaskAsync', params, config) }
//完成-评价任务-导入订单
export const importCommentTaskOrderAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportCommentTaskOrderAsync', params, config) }
//完成-评价任务-导入订单
export const deleteCommentTaskOrderAsync = (params, config = {}) => { return request.get(apiPrefix + 'DeleteCommentTaskOrderAsync', { params: params, ...config }) }
//完成-评价任务-下载
export const exportCommentTaskAsync = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportCommentTaskAsync', { params: params, ...config }) }
// 开启或关闭同步
export const openCloseCommentTaskDtlSync = (params, config = {}) => { return request.get(apiPrefix + 'OpenCloseCommentTaskDtlSync', { params: params, ...config }) }
//获取天猫淘宝主图
export const getTaoBaoOrTmallMainImage = (params, config = {}) => { return request.get(apiPrefix + 'GetTaoBaoOrTmallMainImage', { params: params, ...config }) }
//采纳评价
export const adoptCommentTaskDetail = (params, config = {}) => { return request.get(apiPrefix + 'AdoptCommentTaskDetail', { params: params, ...config }) }
//备用评价
export const spareCommentTaskDetail = (params, config = {}) => { return request.get(apiPrefix + 'SpareCommentTaskDetail', { params: params, ...config }) }
//获取下载进度
export const getCommentTaskDownloadProgressAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetCommentTaskDownloadProgressAsync', { params: params, ...config }) }
//获取下载进度
export const getCommentTaskDownloadProgress2Async = (params, config = {}) => { return request.post(apiPrefix + 'GetCommentTaskDownloadProgress2Async', params, config) }
//只修改采纳评价
export const updateCommentTaskDetail = (params, config = {}) => { return request.post(apiPrefix + 'UpdateCommentTaskDetail', params, config) }


//选品统计
export const GetChooseSummaryCharts = (params, config = {}) => { return request.post(apiPrefix + 'GetChooseSummaryCharts', params, config) }
//选品统计报表 明细导出
export const exportChooseSummaryChartsData = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'exportChooseSummaryChartsData', params, config) }




export const getShowJstSkuCode = (params, config = {}) => { return request.get(apiPrefix + 'GetShowJstSkuCode', { params: params, ...config }) }


// 生成采购计划
export const GeneratePurchasePlanByBuildDoc = (params, config = {}) => { return request.get(apiPrefix + 'GeneratePurchasePlanByBuildDoc', { params: params, ...config }) }
// 热销-采购计划列表
export const PageHotPurchasePlanAsync = (params, config = {}) => { return request.post(apiPrefix + 'PageHotPurchasePlanAsync', params, config) }
// 热销-采购计划明细
export const GetHotPurchasePlanDtlAsnyc = (params, config = {}) => { return request.get(apiPrefix + 'GetHotPurchasePlanDtlAsnyc', { params: params, ...config }) }
// 热销-保存建编码采购计划
export const SaveHotPurchasePlanDtlAsnyc = (params, config = {}) => { return request.post(apiPrefix + 'SaveHotPurchasePlanDtlAsnyc', params, config) }
// 热销-分配采购
export const SetHotPurchasePlanBrand = (params, config = {}) => { return request.get(apiPrefix + 'SetHotPurchasePlanBrand', { params: params, ...config }) }
// 热销-生成采购单
export const GeneratePurchaseOrder = (params, config = {}) => { return request.get(apiPrefix + 'GeneratePurchaseOrder', { params: params, ...config }) }
// 热销-采购计划进度列表
export const GetPurchasePlanProcessRateList = (params, config = {}) => { return request.post(apiPrefix + 'GetPurchasePlanProcessRateList', params, config) }

//获取进货单数量
export const validateStockInApplyCount = (params, config = {}) => { return request.get(apiPrefix + 'ValidateStockInApplyCount', { params: params, ...config }) }

//获取进货单数据
export const getStockInApplyInfo = (params, config = {}) => { return request.get(apiPrefix + 'GetStockInApplyInfo', { params: params, ...config }) }

//保存进货单数据
export const saveStockInApply = (params, config = {}) => { return request.post(apiPrefix + 'SaveStockInApply', params, config) }

//获取进货单列表
export const getStockInApplyList = (params, config = {}) => { return request.get(apiPrefix + 'GetStockInApplyList', { params: params, ...config }) }

//删除进货单
export const delStockInApply = (params, config = {}) => { return request.get(apiPrefix + 'DelStockInApply', { params: params, ...config }) }

//////////////////////
//采购推信-新
export const pageHotSaleBrandPushNew = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleBrandPushNew', params, config) }

//新增选品
export const saveHotSaleBrandPushNew = (params, config = {}) => { return request.post(apiPrefix + 'SaveHotSaleBrandPushNew', params, config) }

//获取编辑数据
export const getHotSaleBrandPushNewById = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewById?id=' + params.id, config) }

//获取计算利润数据
export const getHotSaleBrandPushNewGoodsChooseSkuData = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewGoodsChooseSkuData?hotSaleBrandPushNewId=' + params.hotSaleBrandPushNewId, config) }

//添加到已选品
export const addChooseByHotSaleBrandPushNew = (params, config = {}) => { return request.post(apiPrefix + 'AddChooseByHotSaleBrandPushNew', params, config) }

//获取类目
export const getBrandProductCategory = (params, config = {}) => { return request.post(apiPrefix + 'GetBrandProductCategory?level=' + params.level + '&parentName=' + params.parentName, config) }

//获取类目(全部)
export const getAllBrandProductCategorys = (params, config = {}) => { return request.post(apiPrefix + 'GetAllBrandProductCategorys', config) }
//获取类目(全部)
export const getAllBrandProductCategorysCascader = (params, config = {}) => { return request.post(apiPrefix + 'GetAllBrandProductCategorysCascader', config) }

//获取所有推荐人列表
export const getHotSaleBrandPushNewCreatedUserNameList = (params, config = {}) => { return request.post(apiPrefix + 'GetHotSaleBrandPushNewCreatedUserNameList', config) }

//保存计算利润
export const saveHotSaleBrandPushNewCalProfit = (params, config = {}) => { return request.post(apiPrefix + 'SaveHotSaleBrandPushNewCalProfit', params, config) }

//批量警用
export const setHotSaleBrandPushNewGoodsChooseAllSkuDisableState = (params, config = {}) => { return request.post(apiPrefix + 'SetHotSaleBrandPushNewGoodsChooseAllSkuDisableState?hotSaleBrandPushNewId=' + params.hotSaleBrandPushNewId + '&disState=' + params.disState, config) }

//单个警用
export const setHotSaleBrandPushNewGoodsChooseSkuDisableState = (params, config = {}) => { return request.post(apiPrefix + 'SetHotSaleBrandPushNewGoodsChooseSkuDisableState?skuId=' + params.skuId + '&remark=' + params.remark + '$disState=' + params.disState, config) }

//手动新增sku
export const addHotSaleBrandPushNewGoodsChooseSkuByUser = (params, config = {}) => { return request.post(apiPrefix + 'AddHotSaleBrandPushNewGoodsChooseSkuByUser', params, config) }

//获取新竞品操作日志
export const pageHotSaleBrandPushNewLog = (params, config = {}) => { return request.post(apiPrefix + 'PageHotSaleBrandPushNewLog', params, config) }

//导出
export const exportHotSaleBrandPushNew = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportHotSaleBrandPushNew', params, config)
}

// export const pageHotSaleBrandPushNewLog = (params, config = {}) => { return request.get(apiPrefix + 'PageHotSaleBrandPushNewLog', { params: params, ...config }) }
export const validateGoodsIdIsRepeat = (params, config = {}) => { return request.post(apiPrefix + 'ValidateGoodsIdIsRepeat', params, config) }

//删除
export const delSaleBrandPushNewById = (params, config = {}) => { return request.post(apiPrefix + 'DelSaleBrandPushNewById?id=' + params.id, params, config) }

//选品-采购推新(新)-获取趋势图
export const getAnalysisResponse = (params, config = {}) => { return request.post(apiPrefix + 'GetAnalysisResponse', params, config) }

//选品-采购推新(新)-竞品利润-删除
export const delHotSaleBrandPushNewGoodsChooseSku = (params, config = {}) => { return request.post(apiPrefix + 'DelHotSaleBrandPushNewGoodsChooseSku?skuId=' + params.skuId, config) }

//从采购推新跨境复制为国内
export const hotSaleBrandPushNewCopyForKuaJing = (params, config = {}) => { return request.post(apiPrefix + 'HotSaleBrandPushNewCopyForKuaJing', params, config) }
//从采购推新跨境复制为国内
export const hotSaleBrandPushNewCopyForGuoNei = (params, config = {}) => { return request.post(apiPrefix + 'HotSaleBrandPushNewCopyForGuoNei', params, config) }


//采购推新-新-询价  新增/编辑
export const saveHotSaleBrandPushNewXj = (params, config = {}) => { return request.post(apiPrefix + 'SaveHotSaleBrandPushNewXj', params, config) }
//删除
export const delSaleBrandPushNewXjById = (params, config = {}) => { return request.post(apiPrefix + 'DelSaleBrandPushNewXjById', params, config) }
//采购推新-新-询价  采购认领询价
export const renLingSaleBrandPushNewXjById = (params, config = {}) => { return request.post(apiPrefix + 'RenLingSaleBrandPushNewXjById', params, config) }
//采购推新-新-询价 指派
export const zhiPaiSaleBrandPushNewXjById = (params, config = {}) => { return request.post(apiPrefix + 'ZhiPaiSaleBrandPushNewXjById', params, config) }
//采购推新-新-询价 驳回
export const boHuiSaleBrandPushNewXjById = (params, config = {}) => { return request.post(apiPrefix + 'BoHuiSaleBrandPushNewXjById', params, config) }
//采购推新-新 分配
export const fenPeiSaleBrandPushNewXjById = (params, config = {}) => { return request.post(apiPrefix + 'FenPeiSaleBrandPushNewXjById', params, config) }
//采购推新-新 运营驳回
export const yunYingBoHuiSaleBrandPushNewXjById = (params, config = {}) => { return request.post(apiPrefix + 'YunYingBoHuiSaleBrandPushNewXjById', params, config) }
//获取采购部所有人
export const getBrandAllUserList = (params, config = {}) => { return request.post(apiPrefix + 'GetBrandAllUserList', config) }
//获取运营部所有人
export const getYunYingAllUserList = (params, config = {}) => { return request.get(apiPrefix + 'GetYunYingAllUserList', { params: params, ...config }) }
//采购推新-新- 采购归档
export const guiDangSaleBrandPushNewXjById = (params, config = {}) => { return request.post(apiPrefix + 'GuiDangSaleBrandPushNewXjById', params, config) }
//采购推新-新- 采购归档-恢复
export const guiDangHfSaleBrandPushNewXjById = (params, config = {}) => { return request.post(apiPrefix + 'GuiDangHfSaleBrandPushNewXjById', params, config) }

//采购推新-新-运营询价认领
export const directorClaimHotSaleBrandPushNew = (params, config = {}) => { return request.post(apiPrefix + 'DirectorClaimHotSaleBrandPushNew', params, config) }
