import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/ProductCategoryManager/`
 
export const getList = (params, config = {}) => {  return request.get(apiPrefix + 'GetList', { params: params, ...config })}
export const getbyid  = (id, config = {}) => {    return request.get(apiPrefix + `Get?id=${id}`, {params: {}, ...config })}

export const deletebyid = (id, config = {}) => {  return request.delete(apiPrefix + 'Delete', { params: id, ...config })}
export const deletebyids = (params, config = {}) => {  return request.delete(apiPrefix + 'Delete', { params: params, ...config })}
export const addoredit = (params, config = {}) => {  return request.post(apiPrefix + 'AddorEdit', params, config)}
export const importProductCategory = (params, config = {}) => {  return request.post(apiPrefix + 'ImportProductCategoryAsync', params, config)}

export const updatePlatformService = (params, config = {}) => {  return request.post(apiPrefix + 'UpdatePlatformService', params, config)}
export const getPlatformList = (params, config = {}) => {  return request.get(apiPrefix + 'GetPlatformList', { params: params, ...config })}
export const getCategoryPerformanceStatistics = (params, config = {}) => {return request.get(apiPrefix + 'GetCategoryPerformanceStatisticsAsync', { params, ...config })}
		
export const getCategoryLists = (params, config = {}) => {  return request.get(apiPrefix + 'GetCategoryLists', { params: params, ...config })}
export const getCategoryPSChart = (params, config = {}) => { return request.get(apiPrefix + 'GetCategoryPSChartAsync', { params, ...config })}

//经营类目
export const getBusinessCategorySelectData = (params, config = {}) => {  return request.post(apiPrefix + 'GetBusinessCategorySelectData', params, config)}
export const importPlatformBusinessCategory = (params, config = {}) => {  return request.post(apiPrefix + 'ImportPlatformBusinessCategory', params, config)}
export const exportPlatformBusinessCategory = (params, config = { responseType: 'blob' }) => {return request.post(apiPrefix + 'ExportPlatformBusinessCategory', params, config)}
export const getPlatformBusinessCategoryList = (params, config = {}) => {  return request.post(apiPrefix + 'getPlatformBusinessCategoryList', params, config)}

//查询类目（按父级查询）GetListByPId
export const getListByPId = (params, config = {}) => {  return request.get(apiPrefix + 'GetListByPId', { params: params, ...config })}

export const getProductCategorySyncLog = (params, config = {}) => {  return request.post(apiPrefix + 'GetProductCategorySyncLog', params, config)} 

export const syncProNoCategoryByFilter = (params, config = {}) => {  return request.post(apiPrefix + 'SyncProNoCategoryByFilter', params, config)} 

export const syncPlatformShopIdByWuHanShop = (params, config = {}) => {  return request.post(apiPrefix + 'SyncPlatformShopIdByWuHanShop', params, config)} 
