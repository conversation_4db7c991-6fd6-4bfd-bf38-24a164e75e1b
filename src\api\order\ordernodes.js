import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/OrderNodes/`
export const getOrderList = (params, config = {}) => {return request.get(apiPrefix + 'GetOrderList', { params: params, ...config })}
export const getOrderNodesAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderNodesAnalysisResponse', { params, ...config })}
export const getOrderNodesByNodeAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderNodesByNodeAnalysisResponse', { params, ...config })}
export const getOrderPositionByNodeAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderPositionByNodeAnalysisResponse', { params, ...config })}
export const getOrderPositionByPositionAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderPositionByPositionAnalysisResponse', { params, ...config })}
export const getOrderPositionByPositionlist = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderPositionByPositionlist', { params, ...config })}
export const getOrderNodesTimeAnalysishz = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderNodesTimeAnalysishz', { params, ...config })}

export const setParmPostion = (params,config ={}) =>{ return request.post(apiPrefix+'SetParmPostion', params, config)}
export const getParmPostion = (params, config = {}) => { return request.get(apiPrefix + 'GetParmPostion', { params, ...config })}

export const importOrderNodes =(params, config = {}) => { return request.post(apiPrefix + 'ImportOrderNodesAsync',params,config)}
export const ExportOrderNodesAsync = (params, config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportOrderNodesAsync',  params, config)}
export const getOrderAnalysisWrongHairList = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderAnalysisWrongHairList', { params, ...config })}

export const importOrderNodesPick =(params, config = {}) => { return request.post(apiPrefix + 'ImportOrderNodesPickAsync',params,config)}
export const getOrderNodesPickList = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderNodesPickList', { params, ...config })}
export const exportOrderNodesPick =(params,config ={responseType: 'blob'}) =>{return request.get(apiPrefix + 'ExportOrderNodesPickList',{params: params, ...config})}

