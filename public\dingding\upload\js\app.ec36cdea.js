(function(){"use strict";var t={1072:function(t,e,n){var a=n(8249),o=function(){var t=this,e=t._self._c;return e("div",{attrs:{id:"app"}},[e("div",{staticClass:"bodyupload"},[e("el-upload",{attrs:{drag:"",action:"","list-type":"text",multiple:"","auto-upload":!1,"show-file-list":!1,"on-change":t.changeFile}},[e("i",{staticClass:"el-icon-upload"}),e("div",{staticClass:"el-upload__text"},[t._v(" 点击按钮上传， "),e("em",[t._v("点击上传")])])])],1),e("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingshow,expression:"loadingshow"}],attrs:{fullscreen:!0,visible:t.loadingshow,width:"30%","element-loading-text":t.total>0?"上传进度"+(100*t.total/t.newnum).toFixed(0)+"%":"上传进度0%","element-loading-spinner":"el-icon-loading"},on:{"update:visible":function(e){t.loadingshow=e}}}),t.video?e("div",{staticClass:"uploadImg"},[e("video",{attrs:{src:t.video,controls:""}})]):t._e()],1)},i=[];n(7106);function r(t,e="base64"){return new Promise((n=>{let a=new FileReader;"base64"===e?a.readAsDataURL(t):"buffer"===e&&a.readAsArrayBuffer(t),a.onload=t=>{n(t.target.result)}}))}var l=n(4148),s=n.n(l),u=n(6021),d=n.n(u),c={name:"App",data(){return{loadingshow:!1,total:0,video:null,btn:!1,filenum:0,batchnumber:"",atfterUplaodData:"",chunkCount:0,jsondata:"",arraylist:[],newnum:0}},created(){var t=navigator.userAgent;t.indexOf("AlipayClient")>-1&&document.writeln('<script src="https://appx/web-view.min.js"><\/script>')},filters:{btnText(t){return t?"继续":"暂停"},totalText(t){return t>100?100:t}},mounted(){dd.onMessage=function(t){console.log(t),setTimeout((()=>{dd.postMessage({upfile:t})}),1e3)}},methods:{async changeFile(t){var e=this;if(e.loadingshow=!0,!t)return;t=t.raw,console.log("打印文件file",t),e.filenum=e.filenum+1;let n,a,o=await r(t,"buffer"),i=new(d().ArrayBuffer);i.append(o),n=i.end(),a=/\.([0-9a-zA-Z]+)$/i.exec(t.name)[1];const l=1048576;this.chunkCount=Math.ceil(t.size/l);let s=[],u=0;e.newnum=e.newnum+Number(e.chunkCount),console.log("打印总端数",e.newnum);for(var c=0;c<this.chunkCount;c++){let e={chunk:t.slice(u,u+l),filename:`${n}_${c}.${a}`};u+=l,s.push(e)}console.log("全部切片",s),this.partList=s,this.hash=n,this.sendRequest()},async sendRequest(){let t=[],e=this;this.partList.forEach(((n,a)=>{let o=()=>{let t=new FormData;return t.append("data",n.chunk),t.append("batchnumber",e.batchnumber?e.batchnumber:""),t.append("filename",n.filename),t.append("index",a+1),t.append("total",e.chunkCount),s().post("/api/uploadnew/file/XMTVideoUploadBlockAsync",t,{headers:{"Content-Type":"multipart/form-data"}}).then((t=>{if(t=t.data,console.log("data数值",t),t.data&&(e.batchnumber=t.data),1==t.code&&(e.total+=1,e.partList.splice(a,1)),t.data.relativePath){let n=JSON.stringify(t.data);e.arraylist.push(n);let a=e.arraylist.join("-");console.log("_this.arraylist.length888",e.arraylist.length),console.log("_this.filenum999",e.filenum),e.arraylist.length==e.filenum&&(console.log("最后一次循环",a),dd.postMessage({upfile:a}))}}))};t.push(o)})),console.log("结束打印",e.jsondata);let n=0,a=async()=>{let t=await s().get("/merge",{params:{hash:this.hash}});t=t.data,0==t.code&&(this.video=t.path)},o=async()=>{this.abort||(n>=t.length?a():(await t[n](),n++,o()))};o()},handleBtn(){if(this.btn)return this.abort=!1,void(this.btn=!1);this.btn=!0,this.abort=!0}}},f=c,h=n(2349),p=(0,h.Z)(f,o,i,!1,null,"24aaf772",null),g=p.exports,m=n(9359),v=n.n(m);a["default"].use(v()),a["default"].config.productionTip=!1,new a["default"]({render:t=>t(g)}).$mount("#app")}},e={};function n(a){var o=e[a];if(void 0!==o)return o.exports;var i=e[a]={exports:{}};return t[a](i,i.exports,n),i.exports}n.m=t,function(){var t=[];n.O=function(e,a,o,i){if(!a){var r=1/0;for(d=0;d<t.length;d++){a=t[d][0],o=t[d][1],i=t[d][2];for(var l=!0,s=0;s<a.length;s++)(!1&i||r>=i)&&Object.keys(n.O).every((function(t){return n.O[t](a[s])}))?a.splice(s--,1):(l=!1,i<r&&(r=i));if(l){t.splice(d--,1);var u=o();void 0!==u&&(e=u)}}return e}i=i||0;for(var d=t.length;d>0&&t[d-1][2]>i;d--)t[d]=t[d-1];t[d]=[a,o,i]}}(),function(){n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,{a:e}),e}}(),function(){n.d=function(t,e){for(var a in e)n.o(e,a)&&!n.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:e[a]})}}(),function(){n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"===typeof window)return window}}()}(),function(){n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}(),function(){n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}}(),function(){var t={143:0};n.O.j=function(e){return 0===t[e]};var e=function(e,a){var o,i,r=a[0],l=a[1],s=a[2],u=0;if(r.some((function(e){return 0!==t[e]}))){for(o in l)n.o(l,o)&&(n.m[o]=l[o]);if(s)var d=s(n)}for(e&&e(a);u<r.length;u++)i=r[u],n.o(t,i)&&t[i]&&t[i][0](),t[i]=0;return n.O(d)},a=self["webpackChunk_20200726"]=self["webpackChunk_20200726"]||[];a.forEach(e.bind(null,0)),a.push=e.bind(null,a.push.bind(a))}();var a=n.O(void 0,[998],(function(){return n(1072)}));a=n.O(a)})();
//# sourceMappingURL=app.ec36cdea.js.map