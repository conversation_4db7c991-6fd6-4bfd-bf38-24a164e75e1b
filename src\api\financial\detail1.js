import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Financial}/Detail1/`

export const pageTaoKeNot = (params, config = {}) => {return request.get(apiPrefix + 'PageTaoKeNotAsync', { params: params, ...config })}
export const pageCashRedTX = (params, config = {}) => {return request.get(apiPrefix + 'PageCashRedTXAsync', { params: params, ...config })}
export const pageResendAgent = (params, config = {}) => {return request.get(apiPrefix + 'PageResendAgentAsync', { params: params, ...config })}
export const pageDetail1MonthSumfee = (params, config = {}) => {return request.get(apiPrefix + 'PageDetail1MonthSumfeeAsync', { params: params, ...config })}

export const computDetail1 = (params, config = {}) => {return request.post(apiPrefix + 'ComputDetail1Async',  params, config)}

export const importCashRedTX = (params, config = {}) => {return request.post(apiPrefix + 'ImportCashRedTXAsync', params, config)}
export const importTaoKeNoMeter = (params, config = {}) => {return request.post(apiPrefix + 'ImportTaoKeNoMeter', params, config)}
export const importResendAgent = (params, config = {}) => {return request.post(apiPrefix + 'ImportResendAgentAsync', params, config)}


