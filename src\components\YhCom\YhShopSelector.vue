<template>
    <div>
        <el-popover placement="bottom-start" width="200" trigger="hover">
            <div v-if="checkedWarehouses.length > 0">
                <el-scrollbar>
                    <div v-for="item in checkedWarehouses" :key="item" style="line-height: 25px;"> {{ item }}</div>
                </el-scrollbar>
            </div>
            <div v-else>暂未选择店铺</div>

            <el-button slot="reference" @click="onSelect()" height="30" style="width: 110px;">
                {{ checkedWarehouses.length == 0 ? "请选择店铺" : checkedWarehouses[0].substr(0, 5) + "+" +
                checkedWarehouses.length }}
            </el-button>
        </el-popover>

        <el-dialog v-if="showCheckbox" title="店铺选择列表(多选)" :visible.sync="showWarehouseList" @close="closeDialog"
            v-dialogDrag :append-to-body="true" style="height:800px;">
            <el-checkbox v-model="checkAll" @change="handleCheckAllChange" style="margin-bottom: 10px;">全选</el-checkbox>
            <el-checkbox v-model="checkInvert" @change="handleCheckInvertChange"
                style="margin-bottom: 10px;">反选
            </el-checkbox>
            <el-input v-model="keywords" style="margin-left:30px;width:200px;" placeholder="请输入关键字进行检索"
                @keyup.enter.native="getShop(keywords)" clearable>
            </el-input>
                <div style="height:480px;" >
                    <div style="height:450px; overflow: auto;">
                <el-checkbox-group v-model="checkedWarehouses" @change="handleCheckWarehouseChange">
                    <el-row><!-- :justify="'start'" -->
                        <el-col v-for="item in listObject" :key="item.value" :span="8">
                            <el-checkbox :key="item.value" :label="item.label" :value="item.value">
                                <span :style="keywords && item.label.indexOf(keywords) >= 0 ? 'background-color:yellow' : ''">
                                    {{ item.label }}
                                </span>
                            </el-checkbox>
                        </el-col>
                    </el-row>
                </el-checkbox-group>
            </div>
                </div>
            <span style="position: absolute; bottom: 30px; right: 30px;">
                <el-button @click="GetCommonWarehouseAsync">选取常用店铺</el-button>
                <el-button @click="SetCommonWarehouseAsync">设置为常用店铺</el-button>
                <el-button @click="clearValue">清空</el-button>
                <el-button @click="cancle">取消</el-button>
                <el-button type="primary" @click="confirm">确定</el-button>
            </span>
        </el-dialog>

        <el-dialog v-else title="店铺选择列表(单选)" :visible.sync="showWarehouseList" @close="closeDialog" v-dialogDrag
            :append-to-body="true" style="height:800px;">
            <div style="height:450px;">
                <el-radio-group v-model="radioCheckedWarehouse">
                    <el-row><!-- :justify="'start'" -->
                        <el-col v-for="item in listObject" :key="item.value" :span="6">
                            <el-radio :key="item.id" :label="item.label" @change="handleRadioChange(item.label)"
                                style="margin-top: 5px;">{{ item.label }}
                            </el-radio>
                        </el-col>
                    </el-row>
                </el-radio-group>
            </div>

            <span style="position: absolute; bottom: 30px; right: 30px;">
                <el-button @click="clearValue">清空</el-button>
                <el-button @click="cancle">取消</el-button>
                <el-button type="primary" @click="confirm">确定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>

import { GetVxeTableColumnCacheAsync,SetVxeTableColumnCacheAsync } from '@/api/admin/business'
import {  getAllList as getshopList } from '@/api/operatemanage/base/shop'
import { number } from "echarts";

export default {
    name: 'YhShopSelector',
    props: {
        values: {
            type: Array,
            default() {
                return [];//默认选择店铺
            }
        },
        names: {
            type: Array,
            default() {
                return [];//默认选择店铺ID
            }
        },
        checkboxOrRadio: {
            type: String,
            default() {
                return 'checkbox';//单选框或多选框
            }
        },
        addAllWarehouseConcept: {
            type: Boolean,
            default() {
                return false;//默认不添加全仓概念
            }
        },
        platform: {
            type: String,
            default() {
                return '';//平台
            }
        },
        
    },
    watch: {
        checkboxOrRadio: {
            handler(newval, oldval) {
                this.showCheckbox = newval.includes("checkbox");
            },
            immediate: true, // 立即执行一次
        },
    },
    data() {
        return {
            keyId:'warehouseCurrnetIdCache2407281833',
            keywords: "",//检索关键字
            warehouselist: [],//店铺列表:value-label-所有店铺对象(未去重)
            listName: [],//店铺去重需要:label-所有店铺名
            listID: [],//店铺去重需要:value-所有店铺ID
            listObject: [],//店铺去重需要:value-label-所有店铺对象
            listForwardingWarehouse: [],//代发仓
            listNonIssuingWarehouse: [],//非代发仓

            checkedWarehouses: [],//已选店铺名(多选)
            checkedWarehouseIDs: [],//已选店铺ID(多选)
            radioCheckedWarehouse: '',//已选店铺名(单选)

            showWarehouseList: false,//店铺列表弹窗出现状态
            checkAll: false,//全选状态
            // isIndeterminate: false,//不确定状态
            checkInvert: false,//反选状态
            checkForwarding: false,//代发仓
            checkNonIssuing: false,//非代发仓

            // // placeholder: "",
            // props: ['names', 'values'],
            props: { names: 'names', values: 'values', checkboxOrRadio: 'chexboxOrRadio', addAllWarehouseConcept: 'addAllWarehouseConcept' },
            showCheckbox: true,//多选框或单选框

            remoteCfg:[],//常用店铺缓存
        };
    },
    mounted() {
        this.init();
    },
    methods: {
        //初始化
        async init() {
            
            const cfg =  await GetVxeTableColumnCacheAsync({ tableId: this.keyId });
            if(cfg && cfg.success && cfg.data){
                this.remoteCfg = JSON.parse(cfg.data);
            }

            const { data } = await getshopList({ Platforms: this.platform.split(','), CurrentPage: 1, PageSize: 100000 });

            this.warehouselist = data.map(item => { return { value: item.shopCode, label: item.shopName } });

            // //新增店铺概念---label:全仓, value:-1
            // if (this.addAllWarehouseConcept && !this.listName.includes("所有店铺")) {
            //     this.listObject.push({ label: "所有店铺", value: -1 });
            //     this.listName.push("所有店铺");
            //     this.listID.push(-1);
            // }
          
          
            //店铺去重
            for (let i = 0; i < this.warehouselist.length; i++) {
                if (!this.listName.includes(this.warehouselist[i].label)) {
                    this.listName.push(this.warehouselist[i].label);
                    this.listID.push(this.warehouselist[i].value);
                    this.listObject.push({ label: this.warehouselist[i].label, value: this.warehouselist[i].value });
                }
            }
            // if (this.addAllWarehouseConcept && !this.listName.includes("所有店铺")) {
            //     this.listObject.push({ label: "所有店铺", value: -1 });
            //     this.listName.push("所有店铺");
            //     this.listID.push(-1);
            // }

            //初始化选择店铺
            if (this.listObject.filter(item => this.names.includes(item.label)).length > 0 && this.names.length > 0) {
                if (this.showCheckbox) {//多选
                    this.checkedWarehouses = this.names;//初始设定店铺
                    this.checkedWarehouseIDs = this.values;//初始设定店铺ID
                    this.$emit('onChange', [this.checkedWarehouseIDs, this.checkedWarehouses, this.listObject.filter(item => this.checkedWarehouses.includes(item.label))]);//传递组件数据
                    this.checkAll = this.checkedWarehouses.length === this.listObject.length;//全选状态
                    // this.isIndeterminate = this.checkedWarehouses.length > 0 && this.checkedWarehouses.length < this.listObject.length;//部分选中状态
                    this.checkForwarding = this.listForwardingWarehouse.length === this.checkedWarehouses.filter(item => this.listForwardingWarehouse.includes(item)).length;//代发仓全选状态
                    this.checkNonIssuing = this.listNonIssuingWarehouse.length === this.checkedWarehouses.filter(item => this.listNonIssuingWarehouse.includes(item)).length;//非代发仓全选状态
                } else {//单选
                    this.radioCheckedWarehouse = this.listObject[0].label;//初始设定店铺
                    this.checkedWarehouses.push(this.listObject[0].label);//初始设定店铺名
                    this.checkedWarehouseIDs.push(this.listObject[0].value);//初始设定仓ID
                    this.$emit('onChange', [this.checkedWarehouseIDs, this.checkedWarehouses, this.listObject.filter(item => this.checkedWarehouses.includes(item.label))]);//传递组件数据
                }
            } else if (this.names.length != 0) {
                //this.$message.error("默认初始选择的店铺不存在！");
                console.log("默认初始选择的店铺不存在！");
            }
        },
        //选择店铺列表
        onSelect() {
            this.showWarehouseList = true;

            if (this.showCheckbox) {
                let checkedCount = this.checkedWarehouses.length;
                this.checkAll = checkedCount === this.listObject.length ? true : false;//全选状态
                // this.isIndeterminate = checkedCount > 0 && checkedCount < this.listObject.length;//获取长度方式---部分选中状态
                this.checkForwarding = (this.listForwardingWarehouse.length === this.checkedWarehouses.filter(item => this.listForwardingWarehouse.includes(item)).length);//代发仓全选状态
                this.checkNonIssuing = (this.listNonIssuingWarehouse.length === this.checkedWarehouses.filter(item => this.listNonIssuingWarehouse.includes(item)).length);//非代发仓全选状态
            }
        },
        //关闭弹窗
        closeDialog() {
            this.keywords = '';
            this.showWarehouseList = false;
            this.cancle();
        },
        //清空
        clearValue() {
            // this.showWarehouseList = false;//弹窗关闭状态
            this.keywords = '';
            this.checkedWarehouses = [];
            this.checkedWarehouseIDs = [];
            this.radioCheckedWarehouse = '';
            this.checkAll = false;//全选状态
            this.isIndeterminate = false;//不确定状态
            this.checkInvert = false;//反选状态
            this.checkForwarding = false;//代发仓状态
            this.checkNonIssuing = false;//非代发仓状态
            this.$emit('onChange', [this.checkedWarehouseIDs, this.checkedWarehouses, this.listObject.filter(item => this.checkedWarehouses.includes(item.label))]);//传递组件数据
        },
        //取消---返回上次选择的数据
        cancle() {
            this.keywords = '';
            this.showWarehouseList = false;//弹窗关闭状态
            this.checkedWarehouses = [];
            this.checkAll = false;//全选状态
            // this.isIndeterminate = false;//不确定状态
            this.checkInvert = false;//反选状态
            this.checkForwarding = false;//代发仓状态
            this.checkNonIssuing = false;//非代发仓状态

            if (this.showCheckbox) {//多选
                for (var i = 0; i < this.listID.length; i++) {
                    if (this.checkedWarehouseIDs.includes(this.listID[i])) {
                        this.checkedWarehouses.push(this.listName[i]);
                    }
                }
            }
            else {//单选
                for (var i = 0; i < this.listID.length; i++) {
                    if (this.checkedWarehouseIDs.includes(this.listID[i])) {
                        this.checkedWarehouses.push(this.listName[i]);
                        this.radioCheckedWarehouse = this.listName[i];
                        break;
                    }
                }
            }
            this.$emit('onChange', [this.checkedWarehouseIDs, this.checkedWarehouses, this.listObject.filter(item => this.checkedWarehouses.includes(item.label))]);//传递组件数据
        },
        //确定
        confirm() {
            this.keywords = '';
            this.showWarehouseList = false;//弹窗关闭状态
            this.checkedWarehouseIDs = [];//点击提交时才清空上次复选框选择状态
            for (var i = 0; i < this.listName.length; i++) {
                if (this.checkedWarehouses.includes(this.listName[i])) {
                    this.checkedWarehouseIDs.push(this.listID[i]);
                }
            }
            this.$emit('onChange', [this.checkedWarehouseIDs, this.checkedWarehouses, this.listObject.filter(item => this.checkedWarehouses.includes(item.label))]);//传递组件数据
        },
        //全选
        handleCheckAllChange(value) {
            this.checkedWarehouses = value ? this.listName : [];
            // this.isIndeterminate = false;//部分选中状态
            this.checkInvert = false;//反选状态
            this.checkForwarding = this.listForwardingWarehouse.length === this.checkedWarehouses.filter(item => this.listForwardingWarehouse.includes(item)).length;//代发仓全选状态
            this.checkNonIssuing = this.listNonIssuingWarehouse.length === this.checkedWarehouses.filter(item => this.listNonIssuingWarehouse.includes(item)).length;//非代发仓全选状态
        },
        //反选
        handleCheckInvertChange(value) {
            let warehouses = this.listName;
            let checkedWarehouses = this.checkedWarehouses;
            if (0 === checkedWarehouses.length) {
                this.checkedWarehouses = value ? warehouses : [];//店铺全选
            } else if (checkedWarehouses.length === warehouses.length) {
                this.checkedWarehouses = [];//店铺非全选
                this.checkAll = false;
            } else {
                let list = warehouses.concat(checkedWarehouses).filter((v, i, array) => {
                    return array.indexOf(v) === array.lastIndexOf(v);//返回只出现一次索引的元素
                });
                this.checkedWarehouses = list;//店铺反选
            }
            this.checkAll = this.checkedWarehouses.length === this.listObject.length;//全选状态
            // this.isIndeterminate = checkedWarehouses.length > 0 && checkedWarehouses.length < this.listObject.length;//部分选中状态
            this.checkForwarding = this.listForwardingWarehouse.length === this.checkedWarehouses.filter(item => this.listForwardingWarehouse.includes(item)).length;//代发仓全选状态
            this.checkNonIssuing = this.listNonIssuingWarehouse.length === this.checkedWarehouses.filter(item => this.listNonIssuingWarehouse.includes(item)).length;//非代发仓全选状态
        },
        //代发仓全选
        handleCheckForwardingWarehouseChange(value) {
            this.checkedWarehouses = this.checkedWarehouses.filter(item => !this.listForwardingWarehouse.includes(item));//先去除"代发"，然后再添加，避免重复
            this.checkedWarehouses = value ? this.checkedWarehouses.concat(this.listForwardingWarehouse) : this.checkedWarehouses;//添加全选代发仓
            this.checkAll = this.checkedWarehouses.length === this.listObject.length;//代发仓全选状态
            this.isIndeterminate = this.checkedWarehouses.length > 0 && this.checkedWarehouses.length < this.listObject.length;//部分选中状态
        },
        //非代发仓全选
        hadnleCheckNonIssuingWarehousechange(value) {
            this.checkedWarehouses = this.checkedWarehouses.filter(item => !this.listNonIssuingWarehouse.includes(item));//先去除"非代发"，然后再添加，避免重复
            this.checkedWarehouses = value ? this.checkedWarehouses.concat(this.listNonIssuingWarehouse) : this.checkedWarehouses;//添加全选非代发仓
            this.checkAll = this.checkedWarehouses.length === this.listObject.length;//非代发仓全选状态
            // this.isIndeterminate = this.checkedWarehouses.length > 0 && this.checkedWarehouses.length < this.listObject.length;//部分选中状态
        },
        //点击后：勾选检查(多选)
        handleCheckWarehouseChange() {
            this.checkAll = this.checkedWarehouses.length === this.listObject.length ? true : false;//全选状态
            // this.isIndeterminate = checkedCount > 0 && checkedCount < this.listObject.length;//获取长度方式---部分选中状态

            this.checkForwarding = this.listForwardingWarehouse.length === this.checkedWarehouses.filter(item => this.listForwardingWarehouse.includes(item)).length;//代发仓全选状态
            this.checkNonIssuing = this.listNonIssuingWarehouse.length === this.checkedWarehouses.filter(item => this.listNonIssuingWarehouse.includes(item)).length;//非代发仓全选状态
        },
        //点击后，勾选检测(单选)
        handleRadioChange(value) {
            this.checkedWarehouses = [value];
        },
        //输入框Enter事件监听
        getShop(keywords) {
            if (!keywords) return;
            for (let i = 0; i < this.listName.length; i++) {
                if (this.listName[i].includes(keywords) && !this.checkedWarehouses.includes(this.listName[i])) {
                    this.checkedWarehouses.push(this.listName[i]);
                    // this.checkedWarehouseIDs.push(this.listID[i]);
                    // if (!this.showCheckbox) {
                    //     this.radioCheckedWarehouse = this.listName[i];
                    //     break;
                    // }
                }
            }
            this.handleCheckWarehouseChange();
        },
        //固定店铺按钮点击事件监听
        btnOn(keywords) {
            this.keywords = keywords;
            this.getWarehouse(this.keywords);
        },
        //设置常用店铺
        async SetCommonWarehouseAsync() {
            this.checkedWarehouseIDs = [];//去除初始化时添加的店铺ID
            for (var i = 0; i < this.listName.length; i++) {
                if (this.checkedWarehouses.includes(this.listName[i])) {
                    this.checkedWarehouseIDs.push(this.listID[i]);
                }
            }
            this.remoteCfg=[this.checkedWarehouseIDs, this.checkedWarehouses, this.listObject.filter(item => this.checkedWarehouses.includes(item.label))];
            const cfg = await SetVxeTableColumnCacheAsync({tableId: this.keyId, ColumnConfig: JSON.stringify(this.remoteCfg)});//设置常用店铺缓存
            if (this.checkedWarehouses.length === 0) {
                this.$message.success("请注意，您未设置常用店铺!");
            } else if(cfg.success && cfg.data){
                this.$message.success("设置常用店铺成功!");
            } else if (!cfg.success) {
                this.$message.error("设置常用店铺失败!");
            } 
        },
        //选取常用店铺
        async GetCommonWarehouseAsync() {
            for ( let i = 0; i < this.remoteCfg[1].length; i++) {
                if (!this.checkedWarehouses.includes(this.remoteCfg[1][i])) {//避免一直点击设置常用店铺后，checkedWarehouses与checkedWarehouseIDs存在重复数据
                    this.checkedWarehouses.push(this.remoteCfg[1][i]);
                }
            }
            if (this.remoteCfg[1].length === 0) {
                this.$message.success("请注意，您未设置常用店铺!");
            } else if (this.remoteCfg[1].length > 0) {
                this.$message.success("选取常用店铺成功!");
            }
            this.handleCheckWarehouseChange();
        }
    },
};
</script>

<style scoped>
::v-deep .el-scrollbar__wrap {
    max-height: 300px;
    /* 控制 el-scrollbar 最大高度 */
}
</style>