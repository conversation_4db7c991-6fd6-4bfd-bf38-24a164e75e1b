<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="referrer" content="no-referrer" />
  <!-- elment样式 -->
  <link rel="stylesheet" href="/approvalform/html/api/elment.css">
  <!-- vue -->
  <script src="/approvalform/html/api/vue.min.js"></script>
  <!-- elment脚本 -->
  <script src="/approvalform/html/api/elment.js"></script>
  <!-- jquery -->
  <script src="/approvalform/html/api/jquery.min.js"></script>

  <script src="/approvalform/html/api/html2canvas.js"></script>

  <title>运营申报</title>
  <style type="text/css">
    .linebreak {
      overflow: hidden;
      /*超出部分隐藏*/

      text-overflow: ellipsis;
      /* 超出部分显示省略号 */
      white-space: normal;
      /*规定段落中的文本不进行换行 */
      width: 100%;
    }

    .linebreak1 {
      /* 超出隐藏,给省略号,换三行 */
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      width: 100%;
    }

    .imgcss img {
      min-width: 55px !important;
      min-height: 55px !important;
      width: 55px !important;
      height: 55px !important;
    }

    html,
    body {
      height: 100%;
      overflow-y: hidden;
    }
  </style>
</head>

<body>
  <div id="app" style="margin:0 auto;height: 100%;">
    <div ref="oneboxx" style="height: 98%;">
      <el-table ref="table" :data="list" row-key="id" border style="width: 100%" height="100%" show-overflow-tooltip>
        <el-table-column type="index" label="#" align="center" width="30"></el-table-column>
        <el-table-column label="图片" :show-overflow-tooltip="true" width="55" align="left">
          <teleport slot-scope="scope">
            <el-image :src="scope.row.images" :preview-src-list="scope.row.imageList" class="imgcss"
              style="margin-left: -10px;" fit="fill" :lazy="true"></el-image>
          </teleport>
        </el-table-column>
        <el-table-column label="商品信息" prop="styleCode" width="250" :show-overflow-tooltip="true">
          <template #header>
            <span class="grid-header">
              <span>
                <el-tooltip class="item" effect="dark" content="商品信息，款式编码，分仓" placement="top-end">
                  <span><i class="el-icon-question"></i></span>
                </el-tooltip>
              </span>
              <span>商品信息</span>
            </span>
          </template>
          <template slot-scope="scope">
            <el-tooltip class="item" effect="dark"
              :content=" scope.row.goodsName + ' ' + scope.row.styleCode + ' ' + scope.row.warehouseName"
              placement="top-start">
              <div style="height: 100px;overflow: hidden;">
                <div class="divline">
                  <span style="color: darkgray;" class="linebreak">{{
                    scope.row.goodsName
                    }}</span>
                </div>
                <div>
                  <span style="font-size: 15px">{{ scope.row.styleCode }}</span>
                </div>
                <div>
                  <span style="color: darkgray;">{{ scope.row.warehouseName }}</span>
                </div>
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="商品编码" prop="goodsCode" width="150" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span class="linebreak">
              {{ scope.row.goodsCode }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="商品标签" prop="goodsLable" width="250" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <div>
              <div style="color: darkgray;" class="linebreak1">{{ scope.row.goodsLable }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="商品分类 主营分类" width="150">
          <template #header>
            <span class="grid-header">
              <span>商品分类<br /> 主营分类</span>
            </span>
          </template>
          <teleport slot-scope="scope">
            <div>
              {{ scope.row.groupName == null ? '' : scope.row.groupName }}
            </div>
            <br />
            <div>
              {{ scope.row.maxRatioGroupName == null ? '' : scope.row.maxRatioGroupName }}
            </div>
          </teleport>
        </el-table-column>
        <el-table-column label="品牌" prop="brandId" width="130" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <div>
              {{ scope.row.brandName == null ? '' : scope.row.brandName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="库存周转天数" prop="inventoryDay" column-key="inventoryDay" width="150"
          :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column label="可售库存" prop="sellStock" width="130" :show-overflow-tooltip="true">
          <template #header>
            <span class="grid-header">
              <span>可售库存</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="日均销量/日均退货量" width="200">
          <teleport slot-scope="scope">
            <div>
              3日： {{ scope.row.avgDay3 }} / {{ scope.row.avgRefundDay3 }}
            </div>
          </teleport>
        </el-table-column>
        <el-table-column label="运营申报量" prop="applyQuantity" width="200" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column label="申报量周转天数" prop="applyTurnoverDays" width="auto" :show-overflow-tooltip="true">
        </el-table-column>
      </el-table>
    </div>
  </div>
  <script>
    var vm = new Vue({
      el: '#app',
      data: function () {
        return {
          thisLonding: true,
          list: [],
        }
      },
      created() {

      },
      async mounted() {
        this.getStyleSheetInfo();
      },
      methods: {
        async getStyleSheetInfo() {
          var me = this;
          let searchURL = window.location.search;
          searchURL = searchURL.substring(1, searchURL.length);
          let instanceId = searchURL.split("=")[1];
          me.thisLonding = true;
          $.ajax({
            type: 'GET',
            url: `/api/Inventory/PurchaseOrderNew/GetApplyStockGoodsByInstanceId?instanceId=` + instanceId,
            headers: {
              'Content-Type': 'application/json'
            },
            dataType: 'json',
            success: function (result) {
              me.thisLonding = false;
              if (result.success) {
                me.list = result.data.data
              }
            },
            error: function (err) {
              console.log(err);
            }
          })
        },
      }
    });
  </script>
</body>

</html>
