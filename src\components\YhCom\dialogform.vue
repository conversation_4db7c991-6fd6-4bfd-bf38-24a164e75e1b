<template >
    <el-dialog :title="title" :visible.sync="dialogFormVisible" :top="top" :width='width' :minheight="height"
        :close-on-click-modal="false" v-dialogDrag  append-to-body
        v-loading="false"  @closed="onClosed" >

        <component ref="form" :style="{height:height}" v-bind:is="currentView"
            @close="dialogFormVisible=false;" @afterSave="afterSave" @ok="onOk">
        </component>
            
    </el-dialog>
    
</template>

<script>
 //import MyContainer from "@/components/my-container";

export default {
    name:"dialogform",
   // components: { MyContainer },
    data() {
        return {
            dialogFormVisible:true,
            currentView:null,   
            title:'',
            width:'80%',
            top: '100px',
            height:'500px' ,
            cpmtArgs:{}    
        }
    },
    computed:{
        
    },
    created(){           
        let self=this;
        self.currentView=require(`@/views/${self.cmptPath}`).default;        
    },
    beforeMount(){        
       
    },
    mounted(){
        //console.log('创建showDialogform前：mounted！');
        let self=this;
        this.$nextTick(()=>{
            self.$refs.form.loadData(self.cpmtArgs);
        })
    },
    methods:{
        afterSave(data){
            if(this.callOk && typeof this.callOk==="function")  {
                this.callOk(data);
            }
        },
        onOk(data){
            if(this.callOk && typeof this.callOk==="function")  {
                this.callOk(data);
            }
        },
        onClosed(){  
            if(this.callCancel && typeof this.callCancel==="function")  {
                this.callCancel();
            }
            this.$destroy(true);//销毁组件
            //this.$el.parentNode.removeChild(this.$el);//在父元素中移除dom元素
        }
    }
}
</script>

<style>

</style>