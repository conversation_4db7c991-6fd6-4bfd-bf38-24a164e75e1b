import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Inventory}/openwebjushuitan/`


export const getTbWarehouseList =(params, config = {}) => { return request.get(apiPrefix + 'GetTbWarehouseList',{ params: params, ...config })}

//同步拣货未拣查询
export const getJstJhpcPageList = (params, config = {}) => {return request.post(apiPrefix + 'GetJstJhpcPageList', params, config) }

//实时库存分页查询
export const getJstKuCunPageList = (params, config = {}) => {return request.post(apiPrefix + 'GetJstKuCunPageList', params, config) }

//实时库存分页查询
export const getJstKuCunCheckPageList = (params, config = {}) => {return request.post(apiPrefix + 'GetJstKuCunCheckPageList', params, config) }

