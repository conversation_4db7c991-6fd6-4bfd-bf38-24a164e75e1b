import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_MonthBookKeeper}/finacialDetail/`

export const GetFinancialDetail_TxWx = (params, config = {}) => { return request.post(apiPrefix + 'GetFinancialDetail_TxWx', params, config) }
export const GetFinancialDetail_TgcWx = (params, config = {}) => { return request.post(apiPrefix + 'GetFinancialDetail_TgcWx', params, config) }
export const ExportFinancialDetail_TxWx = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportFinancialDetail_TxWx', params, config)
}
export const ExportFinancialDetail_TgcWx = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportFinancialDetail_TgcWx', params, config)
}


export const getFinancialDetail_ZFB = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialDetail_ZFB', { params, ...config }) }
export const ExportFinancialDetail_ZFB = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportFinancialDetail_ZFB', params, config)
}

export const getCaiNiaoYiZhanPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetCaiNiaoYiZhanPageList', { params, ...config }) }
export const getSaleThemeAnalysisPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetSaleThemeAnalysisPageList', { params, ...config }) }
export const exportGatherSaleThemeAnalysisList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + `ExportGatherSaleThemeAnalysisList`, { params, ...config }) }
// export const getSaleDetailPddPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetSaleDetailPddPageList', { params, ...config }) }
export const getSaleDetailPDD = (params, config = {}) => { return request.get(apiPrefix + 'GetSaleDetailPDD', { params, ...config }) }
export const ExportSaleDetailPddList = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportSaleDetailPddList', params, config)
}

export const ExportGatherSaleThemeAnalysisList2 = (params, config = {}) => { return request.post(apiPrefix + 'ExportGatherSaleThemeAnalysisList2', params, config) }
export const ExportSaleThemeAnalysisList2 = (params, config = {}) => { return request.post(apiPrefix + 'ExportSaleThemeAnalysisList2', params, config) }


export const SyncSaleThemeAnalysisEmptyProCode = (params, config = {}) => { return request.get(apiPrefix + 'SyncSaleThemeAnalysisEmptyProCode', { params, ...config }) }

export const SyncAlbbYanXuanOrders = (params, config = {}) => { return request.post(apiPrefix + 'SyncAlbbYanXuanOrders', params, config) }


export const getSaleDetailDY = (params, config = {}) => { return request.get(apiPrefix + 'GetSaleDetailDY', { params, ...config }) }
export const ExportSaleDetailDY = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportSaleDetailDY', params, config)
}
export const ExportFinancialDetailDY = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportFinancialDetailDY', params, config)
}


export const GetOtherDeduction_DY_Bfkc = (params, config = {}) => { return request.get(apiPrefix + 'GetOtherDeduction_DY_Bfkc', { params, ...config }) }
export const ExportOtherDeduction_DY_Bfkc = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportOtherDeduction_DY_Bfkc', params, config)
}

export const getFinancialDetailDY = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialDetailDY', { params, ...config }) }
export const getFinancialDetailJD = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialDetailJD', { params, ...config }) }
export const getFinancialDetailNoSaleJD = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialDetailNoSaleJD', { params, ...config }) }
export const ExportFinancialDetailJD = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportFinancialDetailJD', params, config)
}
export const ExportFinancialDetailNoSaleJD = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportFinancialDetailNoSaleJD', params, config)
}

export const getBaoCaiDetailJD = (params, config = {}) => { return request.get(apiPrefix + 'GetBaoCaiDetailJD', { params, ...config }) }
export const getWalletDetailJD = (params, config = {}) => { return request.get(apiPrefix + 'GetWalletDetailJD', { params, ...config }) }
export const exportWalletDetailJDList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + `ExportWalletDetailJDList`, { params, ...config }) }


export const GetVirtualExpressJD = (params, config = {}) => { return request.post(apiPrefix + 'GetVirtualExpressJD', params, config) }
export const GetVirtualOrderJD = (params, config = {}) => { return request.post(apiPrefix + 'GetVirtualOrderJD', params, config) }


export const getFinancialDetailPDD = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialDetailPDD', { params, ...config }) }
export const ExportFinancialDetailPddList = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportFinancialDetailPddList', params, config)
}




export const getRecoganizePddDic = (params, config = {}) => { return request.get(apiPrefix + 'GetRecoganizePddDic', { params, ...config }) }
export const getSaleAfterThemeAnalysisDetail = (params, config = {}) => { return request.get(apiPrefix + 'GetSaleAfterThemeAnalysisDetail', { params, ...config }) }
export const exportGatherSaleAfterThemeAnalysisList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + `ExportGatherSaleAfterThemeAnalysisList`, { params, ...config }) }
// export const getFinancialDetailPddPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialDetailPddPageList', { params, ...config }) }
// export const getQianNiuIdPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetQianNiuIdPageList', { params, ...config }) }
// export const getQianNiuDetailPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetQianNiuDetailPageList', { params, ...config }) }
export const getDataUploadList = (params, config = {}) => { return request.post(apiPrefix + 'GetDataUploadList', params, config) }
// export const getNoDataUploadList = (params, config = {}) => { return request.post(apiPrefix + 'GetNoDataUploadList', params, config) }
export const getDataCalcList = (params, config = {}) => { return request.post(apiPrefix + 'GetDataCalcList', params, config) }
// export const pageMonthReportDetail2Async = (params, config = {}) => { return request.get(apiPrefix + 'PageMonthReportDetail2Async', { params: params, ...config }) }
// export const getFinancialInAndOutDetailPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialInAndOutDetailPageList', { params, ...config }) }
// export const getFinancialResultPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialResultPageList', { params, ...config }) }

export const postCalcTask = (params, config = {}) => { return request.post(apiPrefix + 'PostCalcTask', params, config) }
export const postCalcTaskBatch = (params, config = {}) => { return request.post(apiPrefix + 'PostCalcTaskBatch', params, config) }
export const postCalcTaskPlatform = (params, config = {}) => { return request.post(apiPrefix + 'PostCalcTaskPlatform', params, config) }
export const postCalcTaskShopCode = (params, config = {}) => { return request.post(apiPrefix + 'PostCalcTaskShopCode', params, config) }
// export const exportFinacialResult = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + `ExportFinacialResult`, { params, ...config }) }
export const getFinancialResultIdPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialResultIdPageList', { params, ...config }) }
export const exportFinacialIdResult = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + `ExportFinacialResultId`, { params, ...config }) }

// //dayreport

// export const importFinacialZFBDay = (params, config = {}) => { return request.post(apiPrefix + 'ImportFinacialZFBDayAsync', params, config) }

// // 拼多多账单分页
// export const getBillFeePageList = (params, config = {}) => { return request.get(apiPrefix + 'GetBillFeePageList', { params, ...config }) }

// //月报计算（已上传）
export const exportDataUploadList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + `ExportDataUploadList`, { params, ...config }) }

export const getFinacialDeductionPage = (params, config = {}) => { return request.get(apiPrefix + 'GetFinacialDeductionPageList', { params, ...config }) }
export const exportFinacialDeductionRpt = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + `ExportFinacialDeductionRpt`, params, config ) }
export const getFinacialCustomerPage = (params, config = {}) => { return request.get(apiPrefix + 'GetFinacialCustomerPageList', { params, ...config }) }
export const exportFinacialCustomerRpt = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + `ExportFinacialCustomerRpt`, params, config ) }
export const getDeductionTypeDic = (params, config = {}) => { return request.get(apiPrefix + 'GetDeductionTypeDic', { params, ...config }) }

export const getMonthReportLoseProCodePageList = (params, config = {}) => { return request.get(apiPrefix + 'GetMonthReportLoseProCodePageList', { params, ...config }) }
export const exportMonthReportLoseProCodeList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + `ExportMonthReportLoseProCodeList`, { params, ...config }) }


//快手结算明细
export const getFinancialDetailKs = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialDetailKs', { params, ...config }) }
export const ExportFinancialDetailKs = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportFinancialDetailKs', params, config)
}
//快手退货补运费/违规赔付
export const getFinancialDetail_ReturnFreight_KS = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialDetail_ReturnFreight_KS', { params, ...config }) }
export const ExportFinancialDetail_ReturnFreight_KS = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportFinancialDetail_ReturnFreight_KS', params, config)
}

//快手保证金
export const getFinancialDetail_ReturnFreight_KS_Bond = (params, config = {}) => { return request.get(apiPrefix + 'GetFinancialDetail_ReturnFreight_KS_Bond', { params, ...config }) }
export const ExportFinancialDetail_ReturnFreight_KS_Bond = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportFinancialDetail_ReturnFreight_KS_Bond', params, config)
}
const apiPrefixImport = `${process.env.VUE_APP_BASE_API_MonthBookKeeper}/import/`
export const ImportKsBondAsync = (params, config = {}) => { return request.post(apiPrefixImport + 'ImportKsBondAsync', params, config) }

//快手资金明细
export const getFundDetail_KS = (params, config = {}) => { return request.get(apiPrefix + 'GetFundDetail_KS', { params, ...config }) }
export const ExportFundDetail_KS = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportFundDetail_KS', params, config)
}


//快手销售后台明细
export const getSaleDetail_Ks = (params, config = {}) => { return request.get(apiPrefix + 'GetSaleDetail_Ks', { params, ...config }) }
export const ExportSaleDetail_Ks = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportSaleDetail_Ks', params, config)
}
//分销快递费
export const getExpressFee_FenXiaoAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetExpressFee_FenXiaoAsync', { params, ...config }) }
//分销快递费-导入
export const importExpressFee_FenXiaoAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportExpressFee_FenXiaoAsync', params, config) }
//分销销售主题分析
export const getFenXiaoSaleThemeAnalysisPageList = (params, config = {}) => { return request.get(apiPrefix + 'GetFenXiaoSaleThemeAnalysisPageList', { params, ...config }) }
//分销售后主题分析
export const getFenXiaoSaleAfterThemeAnalysisDetail = (params, config = {}) => { return request.get(apiPrefix + 'GetFenXiaoSaleAfterThemeAnalysisDetail', { params, ...config }) }
//月报产品费用--大数据计算
export const computProductFreeToBigData = (params, config = {}) => { return request.post(apiPrefix + 'ComputProductFreeToBigData', params, config) }
//未结算账单-淘系
export const getTxWeiJieSuanPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetTxWeiJieSuanPageList', params, config) }
//未结算账单-抖音
export const getDyWeiJieSuanPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetDyWeiJieSuanPageList', params, config) }
//未结算账单-拼多多
export const getPddWeiJieSuanPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetPddWeiJieSuanPageList', params, config) }
export const ExportWeiJieSuanList = (params, config = {}) => { return request.post(apiPrefix + 'ExportWeiJieSuanList', params, config) }


//计算出仓成本/销退仓返还/产品运费/采购运费/未计算数据
export const computeOtherSave = (params, config = {}) => { return request.get(apiPrefix + 'ComputeOtherSave', { params, ...config }) }




export const GetFinancialDetailSph = (params, config = {}) => { return request.post(apiPrefix + 'GetFinancialDetailSph', params, config) }
export const GetFinancialDetailSphOther = (params, config = {}) => { return request.post(apiPrefix + 'GetFinancialDetailSphOther', params, config) }
export const ExportFinancialDetailSph = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportFinancialDetailSph', params, config)
}
export const ExportFinancialDetailSphOther = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportFinancialDetailSphOther', params, config)
}

export const SyncSphOrderNumberByGiftNumber = (params, config = {}) => { return request.post(apiPrefix + 'SyncSphOrderNumberByGiftNumber', params, config) }


export const XianCaiHouFuAlibabaSyncBefore = (params, config = {}) => { return request.post(apiPrefix + 'XianCaiHouFuAlibabaSyncBefore', params, config) }
export const XianCaiHouFuAlibabaSyncBefore_Platform = (params, config = {}) => { return request.post(apiPrefix + 'XianCaiHouFuAlibabaSyncBefore_Platform', params, config) }
export const SyncXhsItemIds = (params, config = {}) => { return request.post(apiPrefix + 'SyncXhsItemIds', params, config) }




export const GetXhsShopBalancePageList = (params, config = {}) => { return request.post(apiPrefix + 'GetXhsShopBalancePageList', params, config) }
export const GetXhsZfbWxPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetXhsZfbWxPageList', params, config) }
export const GetXhsFinacialDetailPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetXhsFinacialDetailPageList', params, config) }


export const ExportXhsShopBalanceList = (params, config = {}) => { return request.post(apiPrefix + 'ExportXhsShopBalanceList', params, config) }
export const ExportXhsZfbWxList = (params, config = {}) => { return request.post(apiPrefix + 'ExportXhsZfbWxList', params, config) }
export const ExportXhsFinacialDetailList = (params, config = {}) => { return request.post(apiPrefix + 'ExportXhsFinacialDetailList', params, config) }


export const GetMonthWareWagesPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetMonthWareWagesPageList', params, config) }
export const ExportMonthWareWagesList = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportMonthWareWagesList', params, config)
}
//出仓成本查询
export const GetMonthOutboundCostPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetMonthOutboundCostPageList', params, config) }
//出仓成本导出
export const exportMonthOutboundCostRpt = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportMonthOutboundCostRpt', params, config)
}


export const GetMonthWareWagesChildWarePageList = (params, config = {}) => { return request.post(apiPrefix + 'GetMonthWareWagesChildWarePageList', params, config) }
export const SaveMonthWareWagesChildWare = (params, config = {}) => { return request.post(apiPrefix + 'SaveMonthWareWagesChildWare', params, config) }
//月报仓库薪资-子仓导出
export const exportMonthWareWagesChildWareRpt = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportMonthWareWagesChildWareRpt', params, config)
}



export const GetSpecialOrdersPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetSpecialOrdersPageList', params, config) }
export const GetSpecialAmountPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetSpecialAmountPageList', params, config) }
export const GetSpecialOrdersAmount = (params, config = {}) => { return request.get(apiPrefix + 'GetSpecialOrdersAmount', { params, ...config }) }
//特殊单回款-拉取支付宝收入/支出
export const updateSpecialOrderIncomeAmountInfo = (params, config = {}) => { return request.post(apiPrefix + 'UpdateSpecialOrderIncomeAmountInfo', params, config) }
export const ExportSpecialAmountList = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportSpecialAmountList', params, config)
}

//京东自营入仓月报-月账单查询
export const getMonthBillPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetMonthBillPageList', params, config) }
//东自营入仓月报-月账单删除
export const deleteMonthBill = (params, config = {}) => { return request.post(apiPrefix + 'DeleteMonthBill', params, config) }
//京东自营入仓月报-月出库查询
export const getMonthOutboundPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetMonthOutboundPageList', params, config) }
//东自营入仓月报-月出库删除
export const deleteMonthOutbound = (params, config = {}) => { return request.post(apiPrefix + 'DeleteMonthOutbound', params, config) }
//京东自营入仓月报-月入仓成本查询
export const getMonthWarehouseEntryCostPageList = (params, config = {}) => { return request.post(apiPrefix + 'GetMonthWarehouseEntryCostPageList', params, config) }
//东自营入仓月报-月入仓成本删除
export const deleteMonthWarehouseEntryCost = (params, config = {}) => { return request.post(apiPrefix + 'DeleteMonthWarehouseEntryCost', params, config) }
//京东自营入仓出库数据同步
export const SetJdSelfWarehousOutToMonthbookkeeper = (params, config = {}) => { return request.post(apiPrefix + 'SetJdSelfWarehousOutToMonthbookkeeper', params, config) }
//京东自营入仓出库同步数据查询
export const GetJdSelfWarehousOutToMonthbookkeeper = (params, config = {}) => { return request.post(apiPrefix + 'GetJdSelfWarehousOutToMonthbookkeeper', params, config) }
//京东自营-商品编码期初期末查询
export const getJDSelfSkuPriceMonthRpt = (params, config = {}) => { return request.post(apiPrefix + 'getJDSelfSkuPriceMonthRpt', params, config) }
//京东自营-商品编码期初期末导出
export const exportJDSelfSkuPriceMonthRpt = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportJDSelfSkuPriceMonthRpt', params, config) }

export const postJdSelfCalcTask = (params, config = {}) => { return request.post(apiPrefix + 'PostJdSelfCalcTask', params, config) }

//月报计算-包装费
export const GetPackingFeeList = (params, config = {}) => { return request.post(apiPrefix + 'GetPackingFeeList', params, config) }

//平台计算结果
export const GetPlatformCalculationResults = (params, config = {}) => { return request.post(apiPrefix + 'GetPlatformCalculationResults', params, config) }
//平台计算结果导出
export const exportPlatformCalculationResults = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPlatformCalculationResults', params, config) }
//抖音销售明细-侨宝特殊单平台补贴计算
export const qiaobaoSpecialSinglePlatformSubsidyCalcTask = (params, config = {}) => { return request.post(apiPrefix + 'QiaobaoSpecialSinglePlatformSubsidyCalcTask', params, config) }

//仓库查询界面：可按主子仓单独搜索查询
export const GetPackageAndOutwarehousefeeWarehouseMain = (params, config = {}) => { return request.post(apiPrefix + 'GetPackageAndOutwarehousefeeWarehouseMain', params, config) }
//仓库查询界面：可按主子仓单导出
export const ExportPackageAndOutwarehousefeeWarehouseMain = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPackageAndOutwarehousefeeWarehouseMain', params, config) }


//ID+仓库查询界面：可按平台、商品ID、仓库单独搜索查询
export const GetPackageAndOutwarehousefeeProcode = (params, config = {}) => { return request.post(apiPrefix + 'GetPackageAndOutwarehousefeeProcode', params, config) }
//ID+仓库查询界面：可按平台、商品ID、仓库导出
export const ExportPackageAndOutwarehousefeeProcode = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPackageAndOutwarehousefeeProcode', params, config) }

//系列编码+仓库查询界面：可按平台、系列编码、仓库单独搜索查询
export const GetPackageAndOutwarehousefeeStylecode = (params, config = {}) => { return request.post(apiPrefix + 'GetPackageAndOutwarehousefeeStylecode', params, config) }
//系列编码+仓库查询界面：可按平台、系列编码、仓库导出
export const ExportPackageAndOutwarehousefeeStylecode = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPackageAndOutwarehousefeeStylecode', params, config) }

// 明细查询界面：可按平台、店铺、商品ID、内部单、线上单号（单号可以批量查询）查询
export const GetBaozhuangMonth = (params, config = {}) => { return request.post(apiPrefix + 'GetBaozhuangMonth', params, config) }
// 明细查询界面：可按平台、店铺、商品ID、内部单、线上单号（单号可以批量查询）导出
export const ExportBaozhuangMonth = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportBaozhuangMonth', params, config) }
