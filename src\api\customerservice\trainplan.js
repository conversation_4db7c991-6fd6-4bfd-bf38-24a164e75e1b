import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_CustomerService}/trainplan/`

export const gettrainplandata = (params, config = {}) => {
   
    return request.get(apiPrefix + 'GetTrainDataListAsync',{params: params, ...config})
}
export const addtrainplan = (params, config = {}) => {
    return request.post(apiPrefix + 'AddTrainDataAsync', params, config)
}
export const settrainstatus = (params, config = {}) => {
   
    return request.get(apiPrefix + 'SetTrainsStatusAsync',{params: params, ...config})
}
export const gethistrainplandata = (params, config = {}) => {
   
    return request.get(apiPrefix + 'GetHisTrainDataListAsync',{params: params, ...config})
}
 


export const trainfilesUpload = (params, config = {}) => {
    return request.post(apiPrefix + 'TrainfilesUpload', params, config)
}
export const getTrainResourceDataList = (params, config = {}) => {
   
    return request.get(apiPrefix + 'GetTrainResourceDataListAsync',{params: params, ...config})
}

export const disableTrainResourceData = (params, config = {}) => {
   
    return request.get(apiPrefix + 'DisableTrainResourceDataAsync',{params: params, ...config})
}