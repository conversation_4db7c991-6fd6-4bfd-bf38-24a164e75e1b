import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/productmanager/`
const apiPrefixCost = `${process.env.VUE_APP_BASE_API_OperateManage}/productpackcost/`
const apiPrefixOptMng = `${process.env.VUE_APP_BASE_API_OperateManage}/shopmanager/`

export const getUserInfo = (params, config = {}) => {
  return request.post(apiPrefixOptMng + 'GetDirectorList', params, config)
}
export const addorEditProduct = (params, config = {}) => {
  return request.post(apiPrefix + 'AddorEditProduct', params, config)
}
export const updateDamageLink = (params, config = {}) => {
  return request.post(apiPrefix + 'UpdateDamageLink', params, config)
}
export const getProductStyleTagList = (params, config = {}) => { return request.get(apiPrefix + 'GetProductStyleTagList', { params, ...config }) }
export const getProductPinPaiList = (params, config = {}) => { return request.get(apiPrefix + 'GetProductPinPaiList', { params, ...config }) }
export const getShopGoodsByProCode = (params, config = {}) => {
  return request.get(apiPrefix + 'GetShopGoodsByProCode', { params: params, ...config })
}
export const getProductByProCode = (id, config = {}) => {
  return request.get(apiPrefix + `GetProductByProCode?proCode=${id}`, { ...config })
}
export const getProductById = (id, config = {}) => {
  return request.get(apiPrefix + `GetProductById?id=${id}`, { ...config })
}
export const batchDeleteProduct = (params, config = {}) => {
  return request.post(apiPrefix + 'BatchDeleteProduct', params, config)
}
export const batchUpdateProduct = (params, config = {}) => {
  return request.post(apiPrefix + 'BatchUpdateProduct', params, config)
}
export const BatchProductIsClassCodeCalc = (params, config = {}) => {
  return request.post(apiPrefix + 'BatchProductIsClassCodeCalc', params, config)
}
export const batchUpdateBrand = (params, config = {}) => {
  return request.post(apiPrefix + 'BatchUpdateBrand', params, config)
}
export const batchUpdateTag = (params, config = {}) => {
  return request.post(apiPrefix + 'BatchUpdateTag', params, config)
}
export const batchUpdateProductCommission = (params, config = {}) => {
  return request.post(apiPrefix + 'BatchUpdateProductCommission', params, config)
}
export const batchUpdateProductMask = (params, config = {}) => {
  return request.post(apiPrefix + 'BatchUpdateProductMask', params, config)
}
export const batchUpdateProductCid = (params, config = {}) => {
  return request.post(apiPrefix + `BatchUpdateProductCid`, params, config)
}
export const batchIsProtectCornerId = (params, config = {}) => {
  return request.post(apiPrefix + `BatchIsProtectCornerId`, params, config)
}
export const batchUpdateProductBrandid = (params, config = {}) => {
  return request.post(apiPrefix + `BatchUpdateProductBrandid`, params, config)
}
export const saveProBianMa = (params, config = {}) => {
  return request.post(apiPrefix + 'SaveProBianMa', params, config)
}
export const getProductBianMaById = (id, config = {}) => {
  return request.get(apiPrefix + `getProductBianMa?productId=${id}`, {}, config)
}
export const getPageProductList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetPageProductList', params, config)
}
export const getProductShareDetailList = (proCodes, type, config = {}) => {
  return request.get(apiPrefix + `GetProductShareDetailList?proCodes=${proCodes}&type=${type}`, {}, config)
}
export const clearProductShareDetail = (proCodes, type, config = {}) => {
  return request.get(apiPrefix + `ClearProductShareDetail?proCodes=${proCodes}&type=${type}`, {}, config)
}
export const deleteProductShareDetailById = (id, config = {}) => {
  return request.get(apiPrefix + `DeleteProductShareDetailById?id=${id}`, {}, config)
}
export const batchInsertProductShare = (params, config = {}) => {
  return request.post(apiPrefix + 'BatchInsertProductShare', params, config)
}
export const updateProductShare = (params, config = {}) => {
  return request.post(apiPrefix + 'UpdateProductShare', params, config)
}
export const ImportProductShare = (params, config = {}) => {
  return request.post(apiPrefix + 'ImportProductShare', params, config)
}
export const getProductKeyValueByGroup = (params, config = {}) => {
  return request.get(apiPrefix + 'GetProductKeyValueByGroup', { params: params, ...config })
}
export const getGroupKeyValueByCategory = (params, config = {}) => {
  return request.get(apiPrefix + 'GetGroupKeyValueByCategory', { params: params, ...config })
}
export const importProductDirector = (params, config = {}) => {
  return request.post(apiPrefix + 'ImportProductDirector', params, config)
}
export const importProductBianMaBind = (params, config = {}) => {
  return request.post(apiPrefix + 'ImportProductBianMaBind', params, config)
}
export const exportProduct = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'ExportProductAsync', params, config)
}
export const getGroupKeyValue = (params, config = {}) => {
  return request.get(apiPrefix + 'GetGroupKeyValue', { params: params, ...config })
}
export const getProductCommission = (params, config = {}) => {
  return request.get(apiPrefix + 'GetProductCommissionAsync', { params: params, ...config })
}

//获取所有产品code-name的键值对
export const getAllProductList = (params, config = {}) => {
  return request.get(apiPrefix + 'GetAllProductList', { params, ...config })
}

//远程模糊搜索
export const filterProductAsync = (name, config = {}) => {
  return request.get(apiPrefix + `FilterProductAsync?name=${name}`, {}, config)
}

//导入快递包装费
export const ImportProductPackCost = (params, config = {}) => {
  return request.post(apiPrefixCost + 'ImportProductPackCostAsync', params, config)
}

//分页包装费
export const getProductPackCost = (params, config = {}) => {
  return request.get(apiPrefixCost + 'GetProductPackCostAsync', { params, ...config })
}
//历史包装费
export const getProductPackCostHistory = (params, config = {}) => {
  return request.get(apiPrefixCost + 'GetProductPackCostHistoryAsync', { params, ...config })
}

//分页包装费
export const getProductPack = (params, config = {}) => {
  return request.get(apiPrefixCost + 'GetProductPackAsync', { params, ...config })
}
//历史包装费
export const getProductPackHistory = (params, config = {}) => {
  return request.get(apiPrefixCost + 'GetProductPackHistoryAsync', { params, ...config })
}

//批量修改
export const batchAddOrUpdatePackCost = (params, config = {}) => {
  return request.post(apiPrefixCost + 'BatchAddOrUpdatePackCost', params, config)
}

//添加
export const batchAddPackCost = (params, config = {}) => {
  return request.post(apiPrefixCost + 'BatchAddPackCost', params, config)
}
//批量添加
export const batchAddProCodeSimilarity = (params, config = {}) => {
  return request.post(apiPrefixCost + 'BatchAddProCodeSimilarity', params, config)
}

export const getProductPackCostByCost = (params, config = {}) => {
  return request.get(apiPrefixCost + 'GetProductPackCostByCost', { params, ...config })
}

//批量修改
export const batchAddOrUpdatePack = (params, config = {}) => {
  return request.post(apiPrefixCost + 'BatchAddOrUpdatePack', params, config)
}

//添加
export const batchAddPack = (params, config = {}) => {
  return request.post(apiPrefixCost + 'BatchAddPack', params, config)
}

export const getProductPackCostBy = (params, config = {}) => {
  return request.get(apiPrefixCost + 'GetProductPackCostBy', { params, ...config })
}

//远程搜索包装费
export const getListByStyleCodeCost = (params, config = {}) => {
  return request.get(apiPrefixCost + 'GetListByStyleCodeCostAsync', { params, ...config })
}

//新品监控
export const pageProductNewAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'PageGuardProductNewAsync', { params, ...config })
}



//产品战报(新品待选)
export const pageProductBattlefieldReport = (params, config = {}) => {
  return request.post(apiPrefix + 'PageProductBattlefieldReportAsync', params, config)
}
//产品战报 新品
export const pageProductNewBattlefield = (params, config = {}) => {
  return request.post(apiPrefix + 'PageProductNewBattlefieldAsync', params, config)
}

//产品战报（任务）
export const pageProductTaskReport = (params, config = {}) => {
  return request.post(apiPrefix + 'PageProductTaskReportAsync', params, config)
}


//添加爆款/关注产品
export const editProductAttention = (params, config) => { return request.post(apiPrefix + 'EditProductAttentionAsync', params, config) }
export const editProductAttentionstate = (params, config) => { return request.post(apiPrefix + 'EditProductAttentionstateAsync', params, config) }
//发送发起人
export const sendMessageToCreateUserName = (params, config) => { return request.post(apiPrefix + 'ProductReportSendMessageToCreateUserName', params, config) }




//产品战报 爆款 关注
export const pageProductHotCakeAttention = (params, config = {}) => {
  return request.post(apiPrefix + 'PageProductHotCakeAttentionReportAsync', params, config)
}

//产品战报  关注
export const pageProductAttentionReport = (params, config = {}) => {
  return request.post(apiPrefix + 'PageProductAttentionReportAsync', params, config)
}

//产品战报-上传资料
export const productReportfilesUpload = (params, config = {}) => {
  return request.post(apiPrefix + 'ProductReportfilesUpload', params, config)
}

//上传资料分页
export const getProductReportfiles = (params, config = {}) => {

  return request.get(apiPrefix + 'GetProductReportfilesAsync', { params: params, ...config })
}
//删除上传资料
export const deleteProductReportfiles = (params, config = {}) => { return request.delete(apiPrefix + 'DeleteProductReportfilesAsync', { params: params, ...config }) }



//负利润分析
export const pageLoseProfitAnalysis = (params, config = {}) => {
  return request.get(apiPrefix + 'PageLoseProfitAnalysisAsync', { params, ...config })
}

//新品监控（新媒体）
export const pageGuardProductNewMediaAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'PageGuardProductNewMediaAsync', { params, ...config })
}

//商品编码对应ID
export const getGoodsCodeID = (params, config = {}) => {
  return request.get(apiPrefix + 'GetGoodsCodeIDAsync', { params, ...config })
}


export const queryGuardProductNewsis = (params, config = {}) => {
  return request.get(apiPrefix + 'QueryGuardProductNewsisAsync', { params, ...config })
}

export const getProductStateName = (params, config = {}) => {
  return request.get(apiPrefix + 'GetProductStateName', { params, ...config })
}

export const addProductStateName = (params, config = {}) => {
  return request.post(apiPrefix + 'AddProductStateName', params, config)
}

export const addProductState = (params, config = {}) => {
  return request.post(apiPrefix + 'AddProductState', params, config)
}

export const deleteProductState = (params, config = {}) => {
  return request.post(apiPrefix + 'DeleteProductState', params, config)
}

//上新链接
export const getProductNewList = (params, config = {}) => {
  return request.get(apiPrefix + 'GetProductNewListAsync', { params, ...config })
}
///新品监控新增客服组
export const addProcodeCustomer = (params, config = {}) => {
  return request.post(apiPrefix + 'AddProcodeCustomer', params, config)
}

export const getProductAdveClick = (params, config = {}) => {
  return request.get(apiPrefix + 'GetProductAdveClick', { params, ...config })
}

//新品监控图片展示
export const getProductNewImgList = (params, config = {}) => {
  return request.get(apiPrefix + 'GetProductNewImgListAsync', { params, ...config })
}

// 查询运营工资比例
export const getOperatingGroupRatio = (params, config = {}) => {
  return request.get(apiPrefix + 'GetOperatingGroupRatio', { params: params, ...config })
}
//新增or编辑运营工资比例
export const editOperatingGroupRatioAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'EditOperatingGroupRatioAsync', params, config)
}
//远程搜索
export const getOperatingGroupPage = (params, config = {}) => {
  return request.get(apiPrefix + 'GetOperatingGroupPage', { params, ...config })
}
export const deleteOperatingGroupById = (params, config = {}) => { return request.delete(apiPrefix + 'DeleteOperatingGroupByIdAsync', { params: params, ...config }) }

export const queryGuardProductNewGoods = (params, config = {}) => {
  return request.get(apiPrefix + 'queryGuardProductNewGoodsAsync', { params, ...config })
}


//淘系自动下架商品过滤
export const getCanLowerShelfProductsAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'GetCanLowerShelfProductsAsync', { params, ...config })
}

//淘系自动下架趋势图过滤
export const queryCanLowerShelfProductsAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'QueryCanLowerShelfProductsAsync', { params, ...config })
}

//淘系产品批量下架
export const batchDownProducts = (params, config = {}) => {
  return request.get(apiPrefix + 'BatchDownProductsAsync', { params, ...config })
}

export const updateProductCoIdAsync = (params, config = {}) => {
  return request.post(apiPrefix + 'UpdateProductCoIdAsync', params, config)
}

export const getProductsByFilter = (params, config = {}) => {
  return request.get(apiPrefix + 'GetProductsByFilter', { params, ...config })
}

export const updateProductIsCompeteAsync = (params, config = {}) => {
  return request.get(apiPrefix + 'UpdateProductIsCompeteAsync', { params, ...config })
}

export const getProductChangeLogByFilter = (params, config = {}) => {
  return request.get(apiPrefix + 'GetProductChangeLogByFilter', { params, ...config })
}


export const getGoodsCodeDetail = (params, config = {}) => { return request.get(apiPrefix + 'GetGoodsCodeDetail', { params, ...config }) }
export const GoodsCodeDetailExport = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'GoodsCodeDetailExport', params, config) }
export const getQuarterlyPlanTx = (params, config = {}) => { return request.get(apiPrefix + 'GetQuarterlyPlanTx', { params, ...config }) }
export const getQuarterlyPlanDetailsTx = (params, config = {}) => { return request.get(apiPrefix + 'GetQuarterlyPlanDetailsTx', { params, ...config }) }
export const editQuarterlyPlanTx = (params, config = {}) => {
  return request.post(apiPrefix + 'EditQuarterlyPlanTx', params, config)
}
export const editQuarterlyPlanTxDetail = (params, config = {}) => {
  return request.post(apiPrefix + 'EditQuarterlyPlanTxDetail', params, config)
}

export const editCommissionerQuarterlyPlanDetailsTx = (params, config = {}) => {
  return request.post(apiPrefix + 'EditCommissionerQuarterlyPlanDetailsTx', params, config)
}
export const getCommissionerQuarterlyPlanDetailsTx = (params, config = {}) => { return request.get(apiPrefix + 'GetCommissionerQuarterlyPlanDetailsTx', { params, ...config }) }



export const editAssistantQuarterlyPlanDetailsTx = (params, config = {}) => {
  return request.post(apiPrefix + 'EditAssistantQuarterlyPlanDetailsTx', params, config)
}
export const getAssistantQuarterlyPlanDetailsTx = (params, config = {}) => { return request.get(apiPrefix + 'GetAssistantQuarterlyPlanDetailsTx', { params, ...config }) }




export const getReceivingInformationQuery = (params, config = {}) => { return request.post(apiPrefix + 'ReceivingInformationQuery', params, config) }

export const orderPartExport = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportOrderPart', params, config) }

export const importOrderPart = (params, config = {}) => { return request.post(apiPrefix + 'ImportOrderPart', params, config) }

export const exportReceivedInformationSelectedRow = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportReceivedInformationSelectedRow', params, config) }

// 增加手动同步店铺商品资料时间
export const addSyncShopGoodsJob_V2_TimeRange = (params, config = {}) => {
  return request.post(apiPrefix + 'AddSyncShopGoodsJob_V2_TimeRange', params, config)
}
// 增加自动同步店铺商品资料时间
export const addSyncShopGoodsJob_V2_TimeRange2 = (params, config = {}) => {
  return request.post(apiPrefix + 'AddSyncShopGoodsJob_V2_TimeRange2', params, config)
}

// 获取待同步列表
export const getSyncShopGoodsJob_V2_TimeRange = (params, config = {}) => {
  return request.post(apiPrefix + 'GetSyncShopGoodsJob_V2_TimeRange', params, config)
}


// 产品管理-一键建群
export const createGroupChatProductSendMsg = (params, config = {}) => {
  return request.post(apiPrefix + 'CreateGroupChatProductSendMsg', params, config)
}
// 分页查询产品一键通知日志
export const getCreateGroupChatLogPageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetCreateGroupChatLogPageList', params, config)
}

// 产品管理-一键建群-上传附件
export const createGroupChatProductFile = (params, config = {}) => {
  return request.post(apiPrefix + 'CreateGroupChatProductFile', params, config)
}

// 产品管理-一键通知
export const oneNoticeProductSendMsg = (params, config = {}) => {
  return request.post(apiPrefix + 'OneNoticeProductSendMsg', params, config)
}

// 分页查询产品一键通知日志
export const getProductNoticeLogPageList = (params, config = {}) => {
  return request.post(apiPrefix + 'GetProductNoticeLogPageList', params, config)
}

//分页查询产品一键通知日志导出
export const exportProductNoticeLogPageList = (params, config = { responseType: 'blob' }) => {
  return request.post(apiPrefix + 'ExportProductNoticeLogPageList', params, config)
}

//淘系运营人员业绩统计-价格力
export const getPriceForce = (params, config = {}) => {
  return request.get(apiPrefix + 'GetPriceForce', { params, ...config })
}

//查询价格力数据弹窗
export const getPriceForceDialog = (params, config = {}) => {
  return request.get(apiPrefix + 'GetPriceForceDialog', { params, ...config })
}

//查询价格力数据弹窗趋势图
export const getPriceForceChart = (params, config = {}) => {
  return request.get(apiPrefix + 'GetPriceForceChart', { params, ...config })
}

//价格力数据汇总弹窗趋势图
export const getPriceForceSumChart = (params, config = {}) => {
  return request.get(apiPrefix + 'GetPriceForceSumChart', { params, ...config })
}

//物流标签设置
export const getProductExpressLableSet = (params, config = {}) => {
  return request.get(apiPrefix + 'GetProductExpressLableSet', { params, ...config })
}

//物流标签设置  
export const saveProductExpressLableSet = (params, config = {}) => {
  return request.post(apiPrefix + 'SaveProductExpressLableSet', params, config)
}

//GetCustomerService 获取客服组
export const getCustomerService = (params, config = {}) => {
  return request.post(apiPrefix + 'GetCustomerService', params, config)
}
