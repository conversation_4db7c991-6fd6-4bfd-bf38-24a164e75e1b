<template>
  <div style="height:100%;padding:10px;overflow: auto;">
    <el-alert title="温馨提示:红色“!”代表当日有数据没有上传" type="warning" show-icon :closable="false">
      </el-alert>
     <el-calendar v-model="value">
        <template slot="dateCell" slot-scope="{ date, data }">
           <div class="cell-container">
            <div class="word-container">
              <div class="date">
                {{ data.day.split("-").slice(2).join("-") }}
              </div>
              <div class="solar-terms">
                <el-tooltip v-if="hasNoUpdate(data.day)==1" class="item" effect="dark" :content="getNoUpdateMsg(data.day)" placement="top-end">
                  <span><i class="el-icon-warning" style="color: red;"></i></span>
                </el-tooltip>
                <span v-else-if="hasNoUpdate(data.day)==2"><i class="el-icon-success" style="color: green;"></i></span>
              </div>
            </div>
          </div>
        </template>
     </el-calendar>
  </div>
</template>
<script>
 
import {queryImportModuleByDay} from '@/api/admin/business'
export default {
  name: 'importmodule',
  components: {},
  props:{
       id:null,
     },
  data() {
    return {
      value: new Date(),
      data:[]
    }
  },
 beforeCreate(){
    this.date=new Date();
  },
 mounted() {
   this.initdata();
 },
methods: { 
  //格式data:[{date:'2022-02-21',hasNoUpdate:true,noUpdateList:['','']}]
   async initdata(){
      const res = await queryImportModuleByDay({id:this.id,date:this.datetostr(this.value)})
      if (!res?.success)  return
      this.data=res.data;
    },
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    show(id){
        this.data=data;
     },
    getContent(day){
       var content="<div>";
       var daylist = this.data.filter(f=>f.date==day&&f.hasNoUpdate==true)
       if(daylist&&daylist.length>0){
         daylist[0].noUpdateList.forEach(element => {
           content+='<span>'+element+'</span>';
         });
       }
       content+="</div>";
       return content;
    },
    //0:无数据 1：有  2：无
    hasNoUpdate(day){
       var update=0;
       var daylist = this.data.filter(f=>f.date==day)
       if(daylist&&daylist.length>0) {
          if(this.data.filter(f=> f.hasNoUpdate==true).length>0) update=1;
          else update=2;
        }
       return update;
    },
    getNoUpdateMsg(day){
       var msg="";
       var daylist = this.data.filter(f=>f.date==day&&f.hasNoUpdate==true)
       if(daylist&&daylist.length>0)
          daylist[0].noUpdateList.forEach(element => { msg+=((!msg?"" :"、")+ element);});
       return msg;
    },
  }
}
</script>
<style lang="scss" scoped>
// 日历内容部分
.cell-container {
  height: 100%;
  width: 100%;
  position: relative;
}
.word-container {
  height: 100%;
  //font-size: 12px;
  text-align: center;
  .date {
    font-size: 16px;
    z-index: 4;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    //top: 20px;
  }
  .solar-terms {
    z-index: 4;
    position: relative;
    // left: 50%;
    //transform: translateX(-50%);
    //bottom: 20px;
  }
  .red-word {
    color: #ff4949;
    position: absolute;
    z-index: 4;
  }
}

::v-deep .el-calendar-table .el-calendar-day {
  margin: 4px;
  padding: 12px;
  border-radius: 8px;
  background: rgba(202, 249, 240, 0.4);
  text-align: center;
}
// 日历样式
::v-deep .el-calendar__header .el-calendar__title {
  //position: absolute;
  position: relative;
  width: 150px;
}

::v-deep .el-calendar__button-group {
  width: 100%;
}

::v-deep .el-button-group {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

::v-deep .el-button-group > .el-button:first-child:before {
  content: "<";
}

::v-deep .el-button-group > .el-button:last-child:before {
  content: ">";
}

::v-deep .el-button-group > .el-button:first-child span,
::v-deep .el-button-group > .el-button:last-child span {
  display: none;
}

//   .el-button {
//   border: 0;
//   background: transparent;
//   font-size: 18px;
//   font-weight: bold;
//   margin-bottom: 6px;
// }

::v-deep .el-calendar-table td {
  border: 0;
}
::v-deep .el-calendar-table tr td:first-child {
  border: 0;
}
::v-deep .el-calendar-table tr:first-child td {
  border: 0;
}
::v-deep .el-calendar-table td.is-selected {
  background: transparent;
}
::v-deep .el-calendar-table td.is-today {
  font-weight: bold;
}
// 不能预约的遮罩层
// .wrap {
//   position: absolute;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 100%;
//   background: rgba(253, 227, 228, 0.8);
//   border-radius: 8px;
// }
</style>
 
