<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="referrer" content="no-referrer" />
  <!-- elment样式 -->
  <link rel="stylesheet" href="/approvalform/html/api/elment.css">
  <!-- vue -->
  <script src="/approvalform/html/api/vue.min.js"></script>
  <!-- elment脚本 -->
  <script src="/approvalform/html/api/elment.js"></script>
  <!-- jquery -->
  <script src="/approvalform/html/api/jquery.min.js"></script>

  <script src="/approvalform/html/api/html2canvas.js"></script>

  <title>破损订单</title>
  <style type="text/css">
    .linebreak {
      overflow: hidden;
      /*超出部分隐藏*/

      text-overflow: ellipsis;
      /* 超出部分显示省略号 */
      white-space: normal;
      /*规定段落中的文本不进行换行 */
      width: 100%;
    }

    .linebreak1 {
      /* 超出隐藏,给省略号,换三行 */
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      width: 100%;
    }

    ::v-deep .custom-image img {
      max-width: 40px !important;
      max-height: 40px !important;
    }

    .circle-badge {
      position: absolute;
      top: 0px;
      right: 58px;
      background-color: red;
      color: white;
      font-size: 10px;
      width: 18px;
      height: 18px;
      line-height: 18px;
      text-align: center;
      border-radius: 50%;
    }

    .imgcss img {
      min-width: 55px !important;
      min-height: 55px !important;
      width: 55px !important;
      height: 55px !important;
    }

    html,
    body {
      height: 100%;
      overflow-y: hidden;
    }
  </style>
</head>

<body>
  <div id="app" style="margin:0 auto;height: 100%;">
    <div ref="oneboxx" style="height: 98%;">
      <el-table ref="table" :data="list" row-key="id" border style="width: 100%" height="100%" show-overflow-tooltip
        :header-cell-style="{'text-align':'center'}" :cell-style="{'text-align':'center'}">
        <el-table-column type="index" label="#" align="center" width="70"></el-table-column>
        <el-table-column label="责任图片" :show-overflow-tooltip="true" width="200" align="left">
          <teleport slot-scope="scope">
            <template v-if="scope.row.fileUrls && scope.row.fileUrls.length > 0">
              <div style="height: 100%;">
                <el-image :src="scope.row.fileUrls[0]" :preview-src-list="scope.row.fileUrls" class="imgcss"
                  style="margin-left: -10px; max-height: 100%; max-width: 100%;" fit="fill"></el-image>
                <span class="circle-badge">
                  {{ scope.row.fileUrls.length }}
                </span>
              </div>
            </template>
          </teleport>
        </el-table-column>
        <el-table-column label="订单号" prop="orderNo" width="300" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column label="系列编码" prop="styleCode" width="200" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span class="linebreak">
              {{ scope.row.styleCode }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="损耗类型" prop="zrType2" width="250" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <div>
              <div class="linebreak1">{{ scope.row.zrType2 }}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
  <script>
    var vm = new Vue({
      el: '#app',
      data: function () {
        return {
          thisLonding: true,
          list: [],
        }
      },
      created() {

      },
      async mounted() {
        this.getStyleSheetInfo();
      },
      methods: {
        async getStyleSheetInfo() {
          var me = this;
          let searchURL = window.location.search;
          searchURL = searchURL.substring(1, searchURL.length);
          let noticeId = searchURL.split("=")[1];
          me.thisLonding = true;
          $.ajax({
            type: 'GET',
            url: `/api/Customerservice/DamagedOrders/getDamagedOrderMemberNoticeData?noticeId=` + noticeId,
            headers: {
              'Content-Type': 'application/json'
            },
            dataType: 'json',
            success: function (result) {
              me.thisLonding = false;
              if (result.success) {
                me.list = result.data
                me.list.forEach(item => {
                  if (item.fileUrls) {
                    item.fileUrls = item.fileUrls.split(',').map(url => url.trim());
                  }
                });
              }
            },
            error: function (err) {
              console.log(err);
            }
          })
        },
      }
    });
  </script>
</body>

</html>
