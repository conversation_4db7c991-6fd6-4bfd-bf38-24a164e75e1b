import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_Order}/orderdeductmoney/`

//导入
export const importOrderDeductMoney = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportOrderDeductMoneyAsync', params, config)
}

//分页查询
export const pageOrderDeductMoney = (params, config = {}) => {
    return request.post(apiPrefix + 'PageOrderDeductMoneyAsync', params, config)
}

//导出
export const exportOrderDeductMoney = (params, config = { responseType: 'blob' }) => {
    return request.get(apiPrefix + 'exportOrderDeductMoneyAsync', { params: params, ...config })
}

//增改
export const addOrUpdate = (params, config = {}) => {
    return request.post(apiPrefix + 'AddOrUpdateAsync', params, config)
}

//删除
export const deleteData = (params, config = {}) => {
    return request.delete(apiPrefix + 'DeleteAsync', { params, ...config })
}

//获取单个
export const getById = (id, config = {}) => {
    return request.get(apiPrefix + `GetByIdAsync?id=${id}`, {}, config)
}

//分页查询 统计
export const getDeductMoneySum = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDeductMoneySumAsync', params, config)
}

//导出 统计
export const exportDeductMoneySum = (params, config = { responseType: 'blob' }) => {
    return request.get(apiPrefix + 'ExportDeductMoneySumAsync', { params: params, ...config })
}

//分页查询 明细
export const getDeductMoneyDetail = (params, config = {}) => {
    return request.post(apiPrefix + 'GetDeductMoneyDetailAsync', params, config)
}

//导出 明细
export const exportDeductMoneyDetail = (params, config = { responseType: 'blob' }) => {
    return request.get(apiPrefix + 'ExportDeductMoneyDetailAsync', { params: params, ...config })
}

//========================延迟发货等违规 Start
//导入 延迟发货等违规
export const importOrderIllegal = (params, config = {}) => {
    return request.post(apiPrefix + 'ImportOrderIllegalAsync', params, config)
}
//分页查询 延迟发货等违规 店铺统计
export const getOrderIllegalShopList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetOrderIllegalShopListAsync', params, config)
}

//导出 延迟发货等违规 店铺统计
export const exportOrderIllegalShop = (params, config = { responseType: 'blob' }) => {
    return request.get(apiPrefix + 'ExportOrderIllegalShopAsync', { params: params, ...config })
}
//分页查询 延迟发货等违规 仓库统计
export const getOrderIllegalWrhList = (params, config = {}) => {
    return request.post(apiPrefix + 'GetOrderIllegalWrhListAsync', params, config)
}

//导出 延迟发货等违规 仓库统计
export const exportOrderIllegalWrh = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportOrderIllegalWrhAsync', { params: params, ...config }) }

//获取 延迟发货扣款统计明细 的去重的订单号
export const getDeductMoneyDetailOrderNos = (params, config = {}) => { return request.post(apiPrefix + 'GetDeductMoneyDetailOrderNosAsync', params, config) }
//========================延迟发货等违规 End

//分页查询 违规扣款明细
export const GetOrderDeductAllViewListAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderDeductAllViewListAsync', params, config) }

//分页查询 违规扣款明细
export const getOrderWithholdList = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderWithholdListAsync', params, config) }

export const getOrderWithholdBrandRegionList = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderWithholdBrandRegionList', { params: params, ...config }) }
//采购页面，拼多多扣款明细
export const getOrderWithholdNewList = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderWithholdListNewAsync', { params: params, ...config }) }

//采购页面，淘系扣款明细
export const GetOrderWithholdListTx4Cg = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderWithholdListTx4Cg', params, config) }
//采购页面，抖音扣款明细
export const GetOrderWithholdListDou4Cg = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderWithholdListDou4Cg', params, config) }
//采购页面，快手扣款明细
export const GetOrderWithholdListOther4Cg = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderWithholdListOther4Cg', params, config) }

//采购维度扣款总额
export const getOrderWithholdListTotal = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderWithholdListTotal', params, config) }
//获取扣款详情-pdd-采购页面-导出
export const exportOrderWithholdListNew = (params, config = { responseType: 'blob' }) =>
{ return request.get(apiPrefix + 'ExportOrderWithholdListNewAsync', { params: params, ...config }) }
//获取扣款详情-tx||tgc-采购页面-导出
export const exportOrderWithholdListTx4Cg = (params, config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportOrderWithholdListTx4Cg', params, config)}
//获取扣款详情-抖音-采购页面-导出
export const ExportOrderWithholdListDou4Cg = (params, config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportOrderWithholdListDou4Cg', params, config)}
//获取扣款详情-快手-采购页面-导出
export const ExportOrderWithholdListOther4Cg = (params, config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportOrderWithholdListOther4Cg', params, config)}
//获取扣款详情-汇总-采购页面-导出
export const exportOrderWithholdListTotal = (params, config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportOrderWithholdListTotal', params, config)}


//分页查询 违规扣款明细V2
export const getOrderWithholdListV2 = (params, config = {}) => {
    return request.get(apiPrefix + 'GetOrderWithholdListV2Async', { params: params, ...config })
}
//分页查询
export const getOrderWithholdByYunYingPage = (params, config = {}) => {
    return request.post(apiPrefix + 'GetOrderWithholdByYunYingPage', params, config)
}

//导出 延迟发货等违规 详情
export const exportOrderWithhold = (params, config = {responseType: 'blob'}) => { return request.post(apiPrefix + 'ExportOrderWithholdAsync', params, config)}



//获取 订单扣款看板 详情
export const getWithholdSum = (params, config = {}) => { return request.post(apiPrefix + 'GetWithholdSumAsync', params, config) }
//获取 订单扣款看板--所有平台 详情
export const getWithholdSumAll = (params, config = {}) => { return request.post(apiPrefix + 'GetWithholdSumAllAsync', params, config) }


//获取 拼多多扣款看板 详情
export const getWithholdSumTable = (params, config = {}) => { return request.post(apiPrefix + 'GetWithholdSumTableAsync', params, config) }

//导入 拼多多扣款明细
export const importPinOrderIllegal = (params, config = {}) => { return request.post(apiPrefix + 'ImportPinOrderIllegalAsync', params, config) }

//分页查询 淘系违规扣款明细
export const getWithGroupTxSum = (params, config = {}) => { return request.get(apiPrefix + 'GetWithGroupTxSumAsync', { params: params, ...config }) }

export const getWithProCodeSum = (params, config = {}) => { return request.get(apiPrefix + 'GetWithProCodeSumAsync', { params: params, ...config }) }

export const queryWithholdAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'QueryWithholdAnalysisAsync', { params: params, ...config }) }

//订单违规扣款-拼多多扣款汇总-趋势图
export const QueryWithholdAnalysis4PddKkhz = (params, config = {}) => { return request.get(apiPrefix + 'QueryWithholdAnalysis4PddKkhz', { params: params, ...config }) }

//订单违规扣款-淘系扣款汇总-趋势图
export const QueryWithholdAnalysis4TxKkhz = (params, config = {}) => { return request.get(apiPrefix + 'QueryWithholdAnalysis4TxKkhz', { params: params, ...config }) }


export const getOrderWithhold = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderWithholdGoodsCodeAsync', { params: params, ...config }) }

export const editOrderWithhold = (params, config = {}) => { return request.post(apiPrefix + 'EditOrderWithholdAsync', params, config) }

export const getOrderWithholdCount = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderWithholdCountAsync', { params: params, ...config }) }

export const getOrderWithholdRecord = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderWithholdRecordAsync', { params: params, ...config }) }

//违规扣款分析
export const getWithholdboardList = (params, config = {}) => { return request.get(apiPrefix + 'GetWithholdboardListAsync', { params: params, ...config }) }

//违规扣款分析详情
export const GetWithholdboardDetail = (params, config = {}) => { return request.get(apiPrefix + 'GetWithholdboardDetailAsync', { params: params, ...config }) }

//违规扣款分析详情
export const getHomeWithholdboardList = (params, config = {}) => { return request.get(apiPrefix + 'GetHomeWithholdboardListAsync', { params: params, ...config }) }

//分页查询 淘系违规扣款明细
export const getOrderWithholdTXList = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderWithholdTXListAsync', params, config) }

//分页查询 淘系违规扣款明细看板表
export const getOrderWithholdTbaleTXList = (params, config = {}) => { return request.post(apiPrefix + 'GetOrderWithholdTbaleTXListAsync', params, config) }

//淘系违规扣款汇总
export const getWithProCodeTxSum = (params, config = {}) => { return request.get(apiPrefix + 'GetWithProCodeTxSumAsync', { params: params, ...config }) }
//淘系扣款汇总趋势图
export const queryWithholdAnalysisTx = (params, config = {}) => { return request.get(apiPrefix + 'QueryWithholdAnalysisTxAsync', { params: params, ...config }) }
//淘系订单扣款看板 详情
export const getWithholdTxSum = (params, config = {}) => { return request.post(apiPrefix + 'GetWithholdTxSumAsync', params, config) }
//淘系扣款看板详情趋势图
export const getOrderWithholdTxListChart = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderWithholdTxListChartAsync', { params: params, ...config }) }

//淘系扣款原因明细趋势图（首页）
export const queryTxWithholdAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'QueryTxWithholdAnalysis', { params, ...config }) }

//拼多多扣款原因明细趋势图（首页）
export const queryPddWithholdAnalysis = (params, config = {}) => { return request.get(apiPrefix + 'QueryPddWithholdAnalysis', { params, ...config }) }

//淘系扣款看板部门
export const getWithDeptTxSum = (params, config = {}) => { return request.get(apiPrefix + 'GetWithDeptTxSumAsync', { params: params, ...config }) }

//获取 淘系订单扣款快递公司看板 详情
export const GetWithholdSumTxAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetWithholdSumTxAsync', params, config) }

//获取 淘系订单扣款快递公司看板 详情
export const getWitholdCompanyTx = (params, config = {}) => { return request.post(apiPrefix + 'GetWitholdCompanyTxAsync', params, config) }
export const getOrderWithholdListChart = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderWithholdListChartAsync', { params: params, ...config }) }
export const getOrderWithholdAllListChart = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderWithholdAllListChartAsync', { params: params, ...config }) }

//平台扣款金额图表
export const getOrderAllListChartAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderAllListChartAsync', { params: params, ...config }) }

//拼多多责任扣款金额图表
export const getOrderAllDutyListChartAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderAllDutyListChartAsync', { params: params, ...config }) }
//淘系责任扣款金额图表
export const getOrderAllTXDutyListChartAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderAllTXDutyListChartAsync', { params: params, ...config }) }
//抖音责任扣款金额图表
export const GetOrderAllDouDutyListChartAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderAllDouDutyListChartAsync', { params: params, ...config }) }
//其他责任扣款金额图表
export const GetOrderAllOtherDutyListChartAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderAllOtherDutyListChartAsync', { params: params, ...config }) }

//违规扣款，快递分析
export const getHomeOrderExpressDeduct = (params, config = {}) => { return request.get(apiPrefix + 'GetHomeOrderExpressDeductAsync', { params: params, ...config }) }

//扣款分析列表
export const getOrderIllegalPurchase = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderIllegalPurchase', { params: params, ...config }) }

//获取开单数量-趋势图
export const getPurOrderCountTrendChart = (params, config = {}) => { return request.post(apiPrefix + 'GetPurOrderCountTrendChart', params, config) }

//点击
export const getOrderIllegalPurchaseDetailAsync = (params, config = {}) => { return request.get(apiPrefix + 'GetOrderIllegalPurchaseDetailAsync', { params: params, ...config }) }

//导入 抖音扣款
export const importDYOrderIllegal = (params, config = {}) => { return request.post(apiPrefix + 'ImportDYOrderIllegalAsync', params, config) }

//分页抖音扣款
export const GetDYWithholdDetail = (params, config = {}) => { return request.post(apiPrefix + 'GetDYWithholdDetailAsync', params, config) }


//导出 统计
export const ExportDYWithholdDetail = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportDYWithholdDetailAsync', { params: params, ...config }) }


//导入 快手扣款
export const ImportKWaiShopOrderIllegalAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportKWaiShopOrderIllegalAsync', params, config) }

//导入 京东扣款
export const ImportJingDongOrderOrderIllegalAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportJingDongOrderOrderIllegalAsync', params, config) }


//导入 视频号扣款
export const ImportWechatOrderIllegalAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportWechatOrderIllegalAsync', params, config) }



//分页快手及其他扣款
export const GetOtherWithholdDetailAsync = (params, config = {}) => { return request.post(apiPrefix + 'GetOtherWithholdDetailAsync', params, config) }


//导出 快手及其他统计
export const ExportOtherWithholdDetailAsync = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportOtherWithholdDetailAsync', { params: params, ...config }) }



//导出 扣款详情看板列表-所有平台
export const ExportOrderDeductAllViewList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportOrderDeductAllViewList', params, config) }


//导出 淘系扣款详情看板列表
export const exportOrderWithholdTbaleTXList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportOrderWithholdTbaleTXListAsync', params, config) }

//导出 拼多多扣款详情看板列表
export const exportOrderWithholdTbalePddList = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportOrderWithholdTbalePddListAsync', params, config) }

//导出采购扣款详情 统计
export const exportOrderIllegalPurchaseDetail = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportOrderIllegalPurchaseDetailAsync', { params: params, ...config }) }

//导出采购扣款 统计
export const exportOrderIllegalPurchase = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportOrderIllegalPurchaseAsync', { params: params, ...config }) }

//获聚聚水坛订单日志
export const jstGetOrderActionsByInnerNo = (params, config = {}) => { return request.post(apiPrefix + 'JstGetOrderActionsByInnerNo', params, config) }

//获聚聚水坛订单日志
export const jstGetOrderActionsByInnerNos = (params, config = {}) => { return request.post(apiPrefix + 'JstGetOrderActionsByInnerNos', params, config) }

//导出淘系扣款 统计
export const exportOrderWithholdTXList = (params, config = { responseType: 'blob' }) => { return request.get(apiPrefix + 'ExportOrderWithholdTXListAsync', { params: params, ...config }) }


//获取内部单号
export const GetOrderInnerNosByOrderNo = (params, config = { }) => { return request.get(apiPrefix + 'GetOrderInnerNosByOrderNo', { params: params, ...config }) }

//获取淘系订单日志
export const GetOrderActionsByTxOrderNo = (params, config = { }) => { return request.get(apiPrefix + 'GetOrderActionsByTxOrderNo', { params: params, ...config }) }

//获取淘系订单日志
export const GetLogisticsTrackByExpressNoFromDb = (params, config = { }) => { return request.get(apiPrefix + 'GetLogisticsTrackByExpressNoFromDb', { params: params, ...config }) }


//分页获取扣款责任申诉数据
export const PageDeductZrAppealList = (params, config = {}) => { return request.post(apiPrefix + 'PageDeductZrAppealList', params, config) }
//获取扣款责任申诉数据
export const GetDeductZrAppeal4CRUD = (params, config = { }) => { return request.get(apiPrefix + 'GetDeductZrAppeal4CRUD', { params: params, ...config }) }
//保存扣款责任申诉数据
export const SaveDeductZrAppeal = (params, config = {}) => { return request.post(apiPrefix + 'SaveDeductZrAppeal', params, config) }
//审核扣款责任申诉数据
export const AuditDeductZrAppeal = (params, config = { }) => { return request.get(apiPrefix + 'AuditDeductZrAppeal', { params: params, ...config }) }
//批量审核扣款责任申诉数据
export const BatchAuditDeductZrAppeal = (params, config = {}) => { return request.post(apiPrefix + 'BatchAuditDeductZrAppeal', params, config) }

//批量保存扣款责任申诉数据
export const BatchDeductZrAppeal = (params, config = {}) => { return request.post(apiPrefix + 'BatchDeductZrAppeal', params, config) }

//手动指派责任人
export const SetZrMemberCustomize = (params, config = {}) => { return request.post(apiPrefix + 'SetZrMemberCustomize', params, config) }


//重新计算责任
export const AddDeductPddOrder2Queue4Sql = (params, config = {}) => { return request.post(apiPrefix + 'AddDeductPddOrder2Queue4Sql', params, config) }


//导出扣款申诉数据
export const ExportDeductZrAppeal = (params, config = { responseType: 'blob' }) => {
    return request.get(apiPrefix + 'ExportDeductZrAppeal', { params: params, ...config })
}


//成员发起申诉
export const MemberAppealSubmit = (params, config = {}) => { return request.post(apiPrefix + 'MemberAppealSubmit', params, config) }
//责任成员初审-自审
export const AuditMemberAppealBySelf = (params, config = {}) => { return request.post(apiPrefix + 'AuditMemberAppealBySelf', params, config) }
//责任成员审核-管理
export const AuditMemberAppealByMng = (params, config = {}) => { return request.post(apiPrefix + 'AuditMemberAppealByMng', params, config) }
//成员责任申诉分页
export const PageDeductZrMemberAppealList = (params, config = {}) => { return request.post(apiPrefix + 'PageDeductZrMemberAppealList', params, config) }
//获取成员责任申诉详情
export const GetZrMemberAppealDtl = (params, config = { }) => { return request.get(apiPrefix + 'GetZrMemberAppealDtl', { params: params, ...config }) }



//初审核扣款责任申诉数据
export const FirstAuditDeductZrAppeal = (params, config = {}) => { return request.post(apiPrefix + 'FirstAuditDeductZrAppeal', params, config) }
//批量初审核扣款责任申诉数据
export const BatchFirstAuditDeductZrAppeal = (params, config = {}) => { return request.post(apiPrefix + 'BatchFirstAuditDeductZrAppeal', params, config) }

//初审核扣款责任申诉数据-同部门转派
export const FirstAuditDeductZrTrans2SameDept = (params, config = {}) => { return request.post(apiPrefix + 'FirstAuditDeductZrTrans2SameDept', params, config) }
//批量初审核扣款责任申诉数据-同部门转派
export const BatchFirstAuditDeductZrTrans2SameDept = (params, config = {}) => { return request.post(apiPrefix + 'BatchFirstAuditDeductZrTrans2SameDept', params, config) }

//自动初审
export const AutoSetZrAppealFirstAudit = (params, config = { }) => { return request.get(apiPrefix + 'AutoSetZrAppealFirstAudit', { params: params, ...config }) }
//获取管理扣款新责任部门初审代理人员
export const GetAllNewZrDeptAuditUserList = (params, config = { }) => { return request.get(apiPrefix + 'GetAllNewZrDeptAuditUserList', { params: params, ...config }) }
//管理扣款新责任部门初审代理人员
export const SaveNewZrDeptAuditUser = (params, config = {}) => { return request.post(apiPrefix + 'SaveNewZrDeptAuditUser', params, config) }


//责任申诉统计
export const SummaryZrAppeal = (params, config = {}) => { return request.post(apiPrefix + 'SummaryZrAppeal', params, config) }


//获取禁止申诉人员
export const GetAllDeductDisableApplyUserList = (params, config = { }) => { return request.get(apiPrefix + 'GetAllDeductDisableApplyUserList', { params: params, ...config }) }
//管理禁止申诉人员
export const SaveDeductDisableApplyUser = (params, config = {}) => { return request.post(apiPrefix + 'SaveDeductDisableApplyUser', params, config) }

//导入查询的订单和关键词
export const importSearchOrderKeyNodeAnalysisAsync = (params, config = {}) => { return request.post(apiPrefix + 'ImportSearchOrderKeyNodeAnalysisAsync', params, config) }

//获取平台原因系列编码
export const GetStyleOrderIllegalByPlatfomrReason = (params, config = { }) => { return request.post(apiPrefix + 'GetStyleOrderIllegalByPlatfomrReason', params, config) }
//导出平台原因系列编码
export const ExportStyleOrderIllegalByPlatfomrReason = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportStyleOrderIllegalByPlatfomrReason', params, config) }
//获取平台原因系列编码明细
export const GetStyleOrderIllegalByPlatfomrReasonDetail  = (params, config = {}) => { return request.post(apiPrefix + 'GetStyleOrderIllegalByPlatfomrReasonDetail', params, config) }
//导出平台原因系列编码明细
export const ExportStyleOrderIllegalByPlatfomrReasonDetail= (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportStyleOrderIllegalByPlatfomrReasonDetail', params, config) }
//获取平台原因系列编码趋势图
export const GetStyleOrderIllegalAnalysisByPlatfomrReason  = (params, config = {}) => { return request.post(apiPrefix + 'GetStyleOrderIllegalAnalysisByPlatfomrReason', params, config) }

//获取拼多多平台原因聊天记录列表
export const GetPinIllegalDetailChatRecord = (params, config = { }) => { return request.post(apiPrefix + 'GetPinIllegalDetailChatRecord', params, config) }

//获取拼多多平台原因聊天记录
export const GetPinIllegalDetailChatRecordDetail = (params, config = { }) => { return request.post(apiPrefix + 'GetPinIllegalDetailChatRecordDetail', params, config) }

//采购部首页-获取排序设置
export const getDeductionAnalysisSortSet  = (params, config = {}) => { return request.post(apiPrefix + 'GetDeductionAnalysisSortSet', params, config) }

//采购部首页-保存排序设置
export const saveDeductionAnalysisSortSet  = (params, config = {}) => { return request.post(apiPrefix + 'SaveDeductionAnalysisSortSet', params, config) }

//平台聊天记录导出 ExportPinIllegalDetailChatRecord
export const ExportPinIllegalDetailChatRecord = (params, config = { responseType: 'blob' }) => { return request.post(apiPrefix + 'ExportPinIllegalDetailChatRecord', params, config) }