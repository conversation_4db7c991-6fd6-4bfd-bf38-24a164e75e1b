import Vue from 'vue'
import Router from 'vue-router'
import { Message } from 'element-ui'
import defaultSettings from '@/settings'
import { getToken } from '@/utils/auth'
import { getLoginInfo } from '@/api/admin/auth'
import Layout from '@/layout'
import store from '@/store'
import usermessage from '@/store/modules/user'
import { login, logout } from '@/utils/is4'
import { isExternalLink } from '@/utils/validate'
import axios from 'axios'
import Cookies from 'js-cookie'
import { authUShield } from '@/utils/ushieldauth'

const _import = require('./_import_' + process.env.NODE_ENV)
/**
 * 重写路由的push方法
 */
const routerPush = Router.prototype.push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(error => error)
}
Vue.use(Router)
const constantRoutes = [
  {
    path: '/login',
    component: _import('/account/login'),
    hidden: true,
    meta: { title: '登录' }
  },
  {
    path: '/rebotlogin',
    component: _import('/account/rebotlogin'),
    hidden: true,
    meta: { title: '登录' }
  },
  {
    path: '/callback',
    component: _import('/account/login-callback'),
    hidden: true
  },
  {
    path: '/refresh',
    component: _import('/account/refresh-token'),
    hidden: true
  },
  {
    path: '/msgshow',
    name: 'msgshow',
    component: _import('/media/shooting/messageshow'),
    hidden: true
  },
  {
    path: '/msgshow1',
    name: 'msgshow1',
    component: _import('/media/shooting/messageshow1'),
    hidden: true
  },
  {
    path: '/mobilemsg',
    name: 'mobilemsg',
    component: _import('/media/shooting/mobileshopmsg'),
    hidden: true
  },
  {
    path: '/resultContrast',
    name: 'resultContrast',
    component: _import('/media/shooting/fuJianmanage/resultContrast'),
    hidden: true
  },
  {
    path: '/shooting/directImgView',
    name: 'directImgViews',
    component: _import('/media/shooting/directImg/directImgViewshow'),
    hidden: true
  },
  {
    path: '/shooting/packdesginView',
    name: 'packdesginViews',
    component: _import('/media/shooting/packdesgin/packdesginViewshow'),
    hidden: true
  },
  {
    path: '/resultmatter',
    name: 'resultmatter',
    component: _import('/media/shooting/fuJianmanage/resultmatter'),
    hidden: true
  },
  {
    path: '/resultmatterno',
    name: 'resultmatterno',
    component: _import('/media/shooting/fuJianmanage/resultmatterNo'),
    hidden: true
  },
  {
    path: '/seereference',
    name: 'seereference',
    component: _import('/media/shooting/fuJianmanage/seereference'),
    hidden: true
  },
  {
    path: '/pddbgmanage/pddbiddingsel',
    name: 'pddbiddingsel',
    component: _import('/operatemanage/pddbgmanage/pddbiddingsel'),
    hidden: true
  },

  //日常改图上传
  {
    path: '/changeimguploadfile',
    name: 'changeimguploadfile',
    component: _import('/media/shooting/changeImg/new/changeimgfileupload'),
    hidden: true
  },
  //直通车图上传
  {
    path: '/directimguploadfile',
    name: 'directimguploadfile',
    component: _import('/media/shooting/directImg/new/directImgfileupload'),
    hidden: true
  },
  //微详情视频上传
  {
    path: '/microvediofileupload',
    name: 'microvediofileupload',
    component: _import('/media/shooting/microvedio/new/microvediofileupload'),
    hidden: true
  },
  //店铺装修上传上传
  {
    path: '/shopdecorationfileupload',
    name: 'shopdecorationfileupload',
    component: _import('/media/shooting/shopdecoration/new/shopdecorationfileupload'),
    hidden: true
  },
  {
    path: '/shooting/shopdecorationViewshow',
    name: 'shopdecorationViewshow',
    component: _import('/media/shooting/shopdecoration/shopdecorationViewshow'),
    hidden: true
  },
  //包装设计上传
  {
    path: '/packdesginuploadfile',
    name: 'packdesginuploadfile',
    component: _import('/media/shooting/packdesgin/packdesginuploadaction'),
    hidden: true
  },
  //短视频剪辑独立
  {
    path: '/VedioCutInfo',
    name: 'VedioCutInfo',
    component: _import('/media/video/maintable/videotaskcankao'),
    hidden: true
  },
  //拼多多日常改图上传
  {
    path: '/pddchangeimguploadfile',
    name: 'pddchangeimguploadfile',
    component: _import('/media/shooting/pddchangeImg/new/pddchangeimgfileupload'),
    hidden: true
  },
  //包装设计人才库设计弹窗
  {
    path: '/packdesgintalentmanage',
    name: 'packdesgintalentmanage',
    component: _import('/media/shooting/packdesgin/maintasklist/packdesgintalentmanagement'),
    hidden: true
  },
  //视觉设计部新首页
  {
    path: '/media/shooting/monthlyReport/index',
    name: '/media/shooting/monthlyReport/index',
    component: _import('/media/shooting/monthlyReport/index'),
    hidden: true
  },
  {
    path: '/ljxqdialog',
    name: 'ljxqdialog',
    component: _import('/operatemanage/pddactualtimedatanew/dialogfile/ljxqdialog'),
    hidden: true
  },
  {
    path: '/recruitmentPositionOnGoingSelect',
    name: 'recruitmentPositionOnGoingSelect',
    component: _import('/profit/PersonnelRecruiting/recruitmentPositionOnGoingSelect'),
    hidden: true
  },
  // {
  //采购数据汇总-专员明细
  {
    path: '/purchaseSpecialistDetails',
    name: 'purchaseSpecialistDetails',
    component: _import('/inventory/procurementDataAggregation/jumpPage/purchaseSpecialistDetails'),
    hidden: true
  },
  //采购数据汇总-小组明细
  {
    path: '/purchaseGroupDetails',
    name: 'purchaseGroupDetails',
    component: _import('/inventory/procurementDataAggregation/jumpPage/purchaseGroupDetails'),
    hidden: true
  },
  //   path: '/packdesgintalentmanage',
  //   name: 'packdesgintalentmanage',
  //   component: _import('/media/shooting/packdesgin/maintasklist/packdesgintalentmanagement'),
  //   hidden: true
  // },
]

// this.$router.push({path: '/pddchangeimguploadfile'});

const createRouter = () =>
  new Router({
    // IIS发布使用默认的hash模式
    // IIS使用history请参考 https://router.vuejs.org/zh/guide/essentials/history-mode.html#internet-information-services-iis
    mode: 'history',
    // scrollBehavior: () => ({ y: 100 }),
    routes: constantRoutes
  })

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

// async getLoginInfo({ commit }) {
//     const user = await getLoginInfo()
//     console.log("请求用户数据",user)
//   },
// 生成路由
function generateRoutes(menus = []) {
  const routes = {
    path: '/',
    component: Layout,
    children: [],
    name: '',
    meta: {}
  }, username = usermessage.state.userName;
  const user = getLoginInfo();

  menus.forEach(m => {
    if (m.viewPath) {
      try {
        /////////////
        if (!m.newWindow) {
          const newWindoww = isExternalLink(m.path)
          if (newWindoww == true) {
            var array = store.state.rameRouter;
            if (!array.includes(m.path)) {
              array.push(m.path);
            }
          } else {
            const nowrouter = m.path;

            var reg = RegExp(/http/);
            if (reg.test(nowrouter)) {
              m.pathh = m.path;
              const newpath = '/' + m.id;
              m.path = newpath;
              ////////////
              // const bbspath = m.pathh.slice(1);
              // const password = /*Obfuscated by JShaman.com*/0x1e240;
              // var reg = RegExp(bbspath);
              // if(reg.test(m.pathh))
              // axios({
              //            url: bbspath+"api/login/signin",
              //            method: "POST",
              //            data: {
              //             captchaId: 'n55JY16DKjHFFb9ZTrdj',
              //             captchaCode: '',
              //             username,
              //             password,
              //             ref: '',
              //            },
              //            transformRequest: [function (data) {
              //              let ret = ''
              //              for (let it in data) {
              //                ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
              //              }
              //              return ret
              //            }],
              //            headers: {
              //              'Content-Type': 'application/x-www-form-urlencoded'
              //            }
              //        }).then((res)=>{
              //            Cookies.set('bbsToken', res.data.data.token);
              //            // console.log("22222token",res.data.data.token)
              //        })
            }
          }
        } else {
          const nowrouter = m.path;
          const start = nowrouter.indexOf("http");
          const routerr = nowrouter.slice(start);
          m.path = routerr;
        }
        /////////////
        const route = {
          name: m.path,
          path: m.path,
          // 如果IIS导入组件还是有问题，可以尝试直接使用 require('@/views' + m.viewPath + '.vue').default 导入
          component: _import(m.viewPath),
          meta: {
            title: m.label,
            icon: m.icon,
            closable: m.closable,
            path: m.path,
            pathh: m.pathh,
            viewId: m.viewId,
            isNeedUShield: m.isNeedUShield
          }
        }
        // Vue.$nextTick(item=>{routes.children.push(route)})
        // Cookies.get('userToken')
        // setTimeout(item=>{routes.children.push(route)},1000);
        routes.children.push(route)
      } catch (error) {
        Message.error(`导入组件${m.viewPath}.vue失败`)
      }
    }
  })

  routes.children.push({
    path: '/unushieldauth',
    name: 'unushieldauth',
    component: _import('/error/unushieldauth'),
    hidden: true
  })

  console.log(routes)

  // 修复无首页时不显示404
  routes.children.push({
    path: '',
    hidden: true
  })

  routes.children.push({
    path: '*',
    component: _import('/error/404'),
    hidden: true
  })
  return [routes]
}

// 添加路由
export function addRoutes(menus = []) {
  // 生成动态路由
  if (menus && menus.length > 0) {
    const dynamicRoutes = generateRoutes(menus)
    resetRouter()
    router.addRoutes(dynamicRoutes)
  }
}

const title = defaultSettings.title || 'ERP后台管理系统'
// 获取页面标题
function getPageTitle(pageTitle) {
  if (pageTitle) {
    return `${pageTitle} - ${title}`
  }
  return title
}

// 登出
export function toLogout() {
  store.dispatch('user/logout')
  if (defaultSettings.is4) {
    logout()
  } else {
    router.push('/login')
  }
}

// 登录
function toLogin(to, next) {
  // 自动登录判断
  // next({ path: '/' })

  console.log("tologin", to.path);

  if (defaultSettings.is4) {
    if (to.path === '/callback' || to.path === '/refresh') {
      next()
    } else {
      login()
    }
  } else {
    if (to.path === '/login' || to.path === '/qrlogin' || to.path === '/rebotlogin') {
      next()
    } else {
      next(`/login?redirect=${to.path}`)
    }
  }
}

// 设置缓存视图
function setCachedViews() {
  setTimeout(() => {
    if(window.changetabs )
      window.changetabs()
  }, 500)
  let sessionStorageTabs = sessionStorage.getItem('tabs')
  sessionStorageTabs = sessionStorageTabs ? JSON.parse(sessionStorageTabs) : []
  const cachedViews = sessionStorageTabs.map(t => t.name)
  store.commit('tabsView/set_cached_view', cachedViews)
}

let first = true
// 路由全局前置守卫
router.beforeEach(async (to, from, next) => {
  document.title = getPageTitle(to.meta.title)
  const token = getToken()

  setCachedViews()
  // console.log("beforeEach token",token);
  ///////////////////////////////////////
  next(vm => {
    console.log("打印vm参数", vm);
  })
  if (store.getters.menus && store.getters.menus.length > 0) {
    const cloneMenus = store.getters.menus;
    var a = cloneMenus.forEach(m => {
      if (!m.newWindow) {
        const newWindoww = isExternalLink(m.path)
        if (newWindoww == true) {
          var array = store.state.rameRouter;
          if (!array.includes(m.path)) {
            array.push(m.path);
          }
        } else {
          var reg = RegExp(/http/);
          if (reg.test(nowrouter)) {
            m.pathh = m.path;
            const newpath = '/' + m.id;
            m.path = newpath;
          }
        }
      } else {
        const nowrouter = m.path;
        const start = nowrouter.indexOf("http");
        const routerr = nowrouter.slice(start);
        m.path = routerr;
      }
    })
  }

  // U盾鉴权
  if(to.meta && to.meta.isNeedUShield==true){
    var result = await authUShield(to.meta.viewId);
    if(!result) next("/unushieldauth");
  }

  /////////////////////////
  const routeriframe = store.state.rameRouter.toString();
  const iframearray = routeriframe.split(",");

  const nowrouter = to.path;
  const start = nowrouter.indexOf("http");
  const routerr = nowrouter.slice(start);

  const exit = iframearray.indexOf(routerr);
  if (exit != -1) {
    store.commit('setRouterNow', routerr)
    // router.go(0);
  }

  ////////////////////////////////
  // store.commit('setRouterNow',this.$route.path);
  if (token) {
    if (to.path === '/login' || to.path === '/rebotlogin') {
      toLogin(to, next)
    } else {
      const hasPermission = store.getters.menus && store.getters.menus.length > 0
      if (hasPermission) {
        next()
      } else {
        // 仅执行一次
        if (first) {
          first = false
          const res = await store.dispatch('user/getLoginInfo')
          if (res && res.success) {
            next({ ...to, replace: true })
          } else {
            toLogin(to, next)
          }
        }
      }
    }
  } else {
    toLogin(to, next)
  }
})

export default router
